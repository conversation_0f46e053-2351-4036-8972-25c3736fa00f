/* eslint-disable @typescript-eslint/no-require-imports, no-undef */
const { mergeConfig } = require('@react-native/metro-config')
const { getMetroConfig } = require('@jdtaro/plugin-platform-jdrn/dist/supporter')

const defaultAssetExts = require("metro-config/src/defaults/defaults").assetExts;
const defaultSourceExts = require("metro-config/src/defaults/defaults").sourceExts;


module.exports = (async function () {
  return mergeConfig({
    resolver: {
      // 注：sourceExts中json文件类型加上
      sourceExts: ['rn.js', 'rn.ts', 'rn.jsx', 'rn.tsx', ...defaultSourceExts, 'jsx', "scss", "sass"],
      assetExts: defaultAssetExts, // 配置支持的静态资源文件类型
    },
  }, await getMetroConfig())
})()

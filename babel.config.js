// babel-preset-taro 更多选项和默认值：
// https://github.com/NervJS/taro/blob/next/packages/babel-preset-taro/README.md
/** @type {import('@babel/core').TransformOptions} */
// module.exports = {
//   presets: [
//     ['taro', {
//       framework: 'react',
//       ts: true,
//     }]
//   ]
// }

const hyphenateRE = /\B([A-Z])/g;

const hyphenate = function(str) {
    return str.replace(hyphenateRE, '-$1').toLowerCase();
};

const ltfeLibMap = {
    AddressList: 'address-new/address-list',
    AddressEdit: 'address-new/address-edit',
    CouponList: 'coupon-list/coupon-list',
    GetCoupon: 'coupon-list/get-coupon',
    CountryItem: 'country-list/country-item',
    SearchInput: 'country-list/search-input',
    PassengerList: 'passenger/passenger-list',
    PassengerAdd: 'passenger/passenger-add',
    ContactList: 'passenger/contact-list',
    ContactAdd: 'passenger/contact-add',
    NameDesc: 'passenger/name-desc',
    InvoiceDetail: 'invoice/invoice-detail',
    utils: 'utiles',
    fetch: 'utiles/fetch',
    SearchInputN: 'search-input'
};

const jdreactLibMap = {
    MapView: 'JDMapView/MapView',
    JDRadio: 'JDRadio/JDRadio',
    JDRadioGroup: 'JDRadio/JDRadioGroup',
    JDNativeToast: 'JDToast/JDNativeToast',
    JDBlurView: 'NativeViews/JDBlurView',
    JDImageView: 'NativeViews/JDImageView',
    JDLottieView: 'NativeViews/JDLottieView',
    JDNativeViewPager: 'NativeViews/JDNativeViewPager/JDNativeViewPager',
    JDReactShadowView: 'NativeViews/JDReactShadowView',
    JDReactWebView: 'NativeViews/JDReactWebView',
    JDTextInput: 'NativeViews/JDTextInput/JDTextInput',
    JDVideoPlayer: 'NativeViews/JDVideoPlayer'
};

module.exports = function (api) {
  api.cache(false);

  const prodPlugins = []
  // console.log('process.env.BABEL_ENV', process.env.BABEL_ENV)
  // 如果是发布阶段,处于生产环境,就向prodPlugins数组里添加一个插件
  if (process.env.NODE_ENV !== 'development') {
    prodPlugins.push('transform-remove-console')
  }

  return {
    presets: [['taro', {
      framework: 'react',
      ts: true,
      useBuiltIns: process.env.TARO_ENV === 'h5' ? 'usage' : false
    }]],
    plugins: [
      [
        'babel-plugin-import',
        {
          libraryName: '@ltfe/ltfe-core-lib',
          camel2DashComponentName: false, // 是否需要驼峰转短线
          customName: (name) => {
            if (ltfeLibMap[name]) {
              return `@ltfe/ltfe-core-lib/lib/${ltfeLibMap[name]}`;
            }

            return `@ltfe/ltfe-core-lib/lib/${hyphenate(name)}`;
          }
        },
        '@ltfe/ltfe-core-lib'
      ],
      [
        'babel-plugin-import',
        {
          libraryName: '@jdreact/jdreact-core-lib',
          camel2DashComponentName: false, // 是否需要驼峰转短线
          customName: (name) => {
            if (jdreactLibMap[name]) {
              return `@jdreact/jdreact-core-lib/Libraries/${jdreactLibMap[name]}`;
            }

            return `@jdreact/jdreact-core-lib/Libraries/${name}`;
          }
        },
        '@jdreact/jdreact-core-lib'
      ],
      ...prodPlugins
    ]
  }
}

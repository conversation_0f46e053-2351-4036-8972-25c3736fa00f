Taro JDRN 项目模板
====

这是一个使用 Taro 开发 JDRN 微应用的项目模板，同时也支持编译到小程序、H5、鸿蒙等多个平台。

开发文档及注意事项：https://joyspace.jd.com/pages/p4X4SQo0GHRsYgzGZ0Xo

## 📋 开发前准备

### 依赖环境
- node.js v18+
- yarn v1.22+

> **P.S. 由于开发 JDRN 依赖的 npm 包并不支持 pnpm，所以请使用 yarn 安装依赖。**

## 🎯 开发

### 安装依赖
```shell
$ yarn install
```
注意uuid的版本"uuid": "3.4.0", 如果高于这个版本构建发布会报错导致一直loading

### 启动 Bundle
```shell
# 打包 js bundle 及静态资源
$ yarn dev:jdrn
```

### 其他常用命令
```shell
# reset cache and start bundler
$ yarn dev:jdrn --reset-cache
```

## 相关资料
- [Taro JDRN 开发文档](https://taro.jd.com/docs/intro/dev/jdrn/config/)

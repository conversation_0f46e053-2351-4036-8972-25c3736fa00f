import { isEmpty } from '@/utils/isType'
import cloneDeep from 'lodash-es/cloneDeep'
import { Observable } from 'zen-observable-ts'


type TPayload = {
    value: BaseMoreModel
}

export default class BaseMoreModel {
  
    private subscribers: ZenObservable.Observer<number>[] = [];
    constructor() {

    }

    createObs() {
      return new Observable(observer => {
        // Add the observer to our list of subscribers
        this.subscribers.push(observer);
      
        // Return a function to unsubscribe
        return {
          unsubscribe: () => {
            // Remove the observer from our list of subscribers
            const index = this.subscribers.indexOf(observer);
            if (index > -1) {
              this.subscribers.splice(index, 1);
            }
          }
        };
      })
    }

    /**
     * 更新通知函数。
     * @param options 选项对象，包括快照、操作等。
     * action: 自定义，用于业务做制定动作的扩展参数，比如我更新了A字段，action: 'updateA'，此时订阅方可以根据action做具体操作
     */
    notifyUpdate(options?: { snapshot?: boolean, action?: string, [key: string]: any }) {
        if (isEmpty(this.subscribers)) { return }
        const { snapshot, ...others } = options || {}
        this.subscribers.forEach(subscriber => {
          subscriber?.next({
              value: options?.snapshot === false ? this : cloneDeep(this),
              ...others
          });
        })
    }

    subscribe(cb?: (v: TPayload) => void) {
        const obs = this.createObs()
        return obs.subscribe((newValue) => {
            console.log('xxxx', newValue, newValue.value === this)
            typeof cb === 'function' && cb(newValue)
        })
    }
}
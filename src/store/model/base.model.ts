import cloneDeep from 'lodash-es/cloneDeep'
import { Observable } from 'zen-observable-ts'


type TPayload = {
    value: BaseModel
}

export default class BaseModel {

    private _observable: Observable<TPayload>
    private _observer//: ZenObservable.SubscriptionObserver<TPayload>

    constructor() {
        this._observable = new Observable(observer => {
            this._observer = observer;
        })
    }

    /**
     * 更新通知函数。
     * @param options 选项对象，包括快照、操作等。
     * action: 自定义，用于业务做制定动作的扩展参数，比如我更新了A字段，action: 'updateA'，此时订阅方可以根据action做具体操作
     */
    notifyUpdate(options?: { snapshot?: boolean, action?: string, [key: string]: any }) {
        if (!this._observable) { return }
        const { snapshot, ...others } = options || {}
        this._observer?.next({
            value: options?.snapshot === false ? this : cloneDeep(this),
            ...others
        });
    }

    subscribe(cb?: (v: TPayload) => void) {
        return this._observable.subscribe((newValue) => {
            // console.log('xxxx', newValue, newValue.value === this)
            typeof cb === 'function' && cb(newValue)
        })
    }
}
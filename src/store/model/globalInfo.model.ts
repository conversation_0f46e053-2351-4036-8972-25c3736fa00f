import { getBaseInfo } from "@/common/hotelBaseInfo";
import BaseModel from "./base.model";
import { store } from '@ltfe/ltfe-core-lib/lib/utiles';

// 用于存储全局信息
export class GlobalInfoModel extends BaseModel {
    staticParams: any // open协议上的参数

    constructor(data?: GlobalInfoModel) {
        super()
        if (data) {
            Object.assign(this, data)
        }
    }

    /**
     * 全局 hotelBaseSearchParam 缓存，由酒店Tab先日历选择同步时间或者缓存同步
     */
    private _hotelBaseSearchParam;

    /**
     * 设置酒店基本搜索参数。
     * @param baseInfo 基本信息
     */
    public setHotelBaseInfo(baseInfo) {
        this._hotelBaseSearchParam = baseInfo;
        store.asyncSave('hotelRoomNum', baseInfo).catch(console.error)
        store.asyncSave('hotelCheckInDate', baseInfo.checkInDate).catch(console.error)
        store.asyncSave('hotelCheckOutDate', baseInfo.checkOutDate).catch(console.error)
    }

    /**
     * 获取酒店基本搜索参数。
     * 如果未缓存，则从服务器获取。
     * @returns 酒店基本搜索参数。
     */
    public async getHotelBaseInfo() {
        if (!this._hotelBaseSearchParam) {
            const baseInfo = await getBaseInfo().catch(() => { });
            this._hotelBaseSearchParam = baseInfo;
        }
        return this._hotelBaseSearchParam;
    }

}


export default new GlobalInfoModel();
// import BaseModel from "./base.model";
import BaseMoreModel from "./baseMore.model";
/**
 * 地址模型类
 * 订阅： addressModel.subscribe((result) => {
            console.log(result.value)
        });
  addressModel.latitude = 'test1111'
  通知更新(订阅的入参返回深拷贝的数据)：addressModel.notifyUpdate()
  addressModel.longitude = 'test2222'
  通知更新(订阅的入参返回源数据)：addressModel.notifyUpdate({ snapshot: false })
 */
export class AddressModel extends BaseMoreModel {
    longitude: string
    latitude: string
    posAreaId: string
    virtualLocation: number
    getAddress: Promise<any>

    constructor(address?: AddressModel) {
        super()
        if (address) {
            Object.assign(this, address)
        }
    }
}


export default new AddressModel();


// console.log('11', 11)
// // 创建装饰器函数
// function withObserver(options?: any) {
//     return function (target: any) {
//         console.log('target', target)
//         // target.prototype.subscribe = (obj) => {
            

//         // }
//     };
// }
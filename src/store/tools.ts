import { StoreApi, UseBoundStore } from 'zustand'

export type WithSelectors<S> = S extends { getState: () => infer T }
  ? S & { use: { [K in keyof T]: () => T[K] } }
  : never

/**
 * 创建并返回一个带有选择器的存储对象。
 * @param _store 用于创建选择器的存储对象。
 * @returns 带有选择器的存储对象。
 * 用法：https://zustand-cn.js.org/guides/auto-generating-selectors
 */
export const createSelectors = <S extends UseBoundStore<StoreApi<object>>>(
  _store: S,
) => {
  let store = _store as WithSelectors<typeof _store>
  store.use = {}
  for (let k of Object.keys(store.getState())) {
    ;(store.use as any)[k] = () => store((s) => s[k as keyof typeof s])
  }

  return store
}

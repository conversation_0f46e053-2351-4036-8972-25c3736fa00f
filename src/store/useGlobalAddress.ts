import { create } from 'zustand'
import addressModel, { AddressModel } from './model/address.model'
import locationPermissionModel, { LocationPermissionModel } from './model/locationPermission.model'
import isEqual from 'lodash-es/isEqual'

import { createSelectors } from './tools';

interface GlobalState {
    address: AddressModel,
    locationPermission: LocationPermissionModel,
    updateAddress: (newAddr: {longitude: string; latitude: string; posAreaId: string; virtualLocation: number}, replace?: boolean) => void,
    updateLocationPermisson: (newPer: {locationPermission: boolean}) => void,
}

// 地址、定位权限
const useGlobalState = create<GlobalState>()((set) => ({
    address: {} as AddressModel,
    locationPermission: {} as LocationPermissionModel,
    updateAddress: (newAddr: AddressModel) => set((state) => {
        console.log('zmm---newAddr', newAddr, state.address)
        const equalFlag = isEqual(newAddr, state.address)
        let newState = { address: newAddr }
        Object.assign(addressModel, newState.address)
        // 地址变化时才触发通知
        if (!equalFlag) {
            addressModel.notifyUpdate()
        }
        return newState;
    }),
    updateLocationPermisson: (locationPermission: LocationPermissionModel) => set((state) => {
        const newState = { locationPermission: Object.assign(state.locationPermission, locationPermission) }
        Object.assign(locationPermissionModel, newState.locationPermission)

        locationPermissionModel.notifyUpdate()
        return newState;
    }),
}))

const globalState = createSelectors(useGlobalState)

export default globalState



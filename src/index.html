<!DOCTYPE html>
<html>

<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
    <meta content="width=device-width,initial-scale=1,user-scalable=no" name="viewport">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="format-detection" content="telephone=no,address=no">
    <meta name="apple-mobile-web-app-status-bar-style" content="white">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>京东旅行搜索</title>
    <style>
        div:focus,
        input:focus {
            outline: none;
        }
    </style>
    <script>
        const isAndroid = /Android/.test(navigator.userAgent);
        const isIOS = /iPhone|iPad|iPod/.test(navigator.userAgent);

        if (isAndroid) {
            document.documentElement.style.setProperty('--os-type', 'android');
        } else if (isIOS) {
            document.documentElement.style.setProperty('--os-type', 'ios');
        }
    </script>
    <script src="https://wl.jd.com/unify.min.js"></script>
    <!-- 通过cdn文件方式引入，放在页面<head>中 -->
    <script src="https://storage.360buyimg.com/webcontainer/js_security_v3_0.1.5.js?v=2406"></script>
    <script src="https://jrb.jr.jd.com/common/jssdk/jrbridge/3.0.1/jrbridge.js"></script>
    <script>
        function getQueryParams() {
            const params = {};
            const search = window.location.search.substring(1);
            const queries = search.split('&');

            for (let i = 0; i < queries.length; i++) {
                const pair = queries[i].split('=');
                params[pair[0]] = decodeURIComponent(pair[1]);
            }

            return params;
        }
        const queryParams = getQueryParams();
        // console.log(queryParams);
        if (queryParams.debugger == '1') {
            document.write('<script src="https://cdn.jsdelivr.net/npm/eruda"><\/script>');
        }
    </script>
    <script>
        if (window.eruda) {
            eruda.init();
        }

        if(!Array.prototype.flat){
            Array.prototype.flat = function(count) {
                let c = count || 1;
                let len = this.length;
                let exe = [];
                if (this.length == 0) return this;
                while (c--) {
                    let _arr = [];
                    let flag = false;
                    if (exe.length == 0) {
                        flag = true;
                        for (let i = 0; i < len; i++) {
                            if (this[i] instanceof Array) {
                                exe.push(...this[i]);
                            } else {
                                exe.push(this[i]);
                            }
                        }
                    } else {
                        for (let i = 0; i < exe.length; i++) {
                            if (exe[i] instanceof Array) {
                                flag = true;
                                _arr.push(...exe[i]);
                            } else {
                                _arr.push(exe[i]);
                            }
                        }
                        exe = _arr;
                    }
                    if (!flag && c == Infinity) {
                        break;
                    }
                }
                return exe;
            }
        }
        if (!Object.hasOwn) {
            Object.defineProperty(Object, 'hasOwn', {
                value: function (object, property) {
                    if (object == null) {
                        throw new TypeError('Cannot convert undefined or null to object');
                    }
                    return Object.prototype.hasOwnProperty.call(Object(object), property);
                },
                configurable: true,
                enumerable: false,
                writable: true,
            });
        }
    </script>
    <script><%= htmlWebpackPlugin.options.script %></script>
</head>

<body>
    <div id="app"></div>
</body>

</html>
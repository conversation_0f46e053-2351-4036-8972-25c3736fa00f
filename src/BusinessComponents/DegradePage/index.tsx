import {
    JDNoDataView
} from '@jdreact/jdreact-core-lib'
import NavBar from '@/BaseComponents/NavBar';
import { View } from '@/BaseComponents/atoms'
import Taro, { useDidShow } from '@tarojs/taro'
import { reportInfo } from '@/common/reporter';
import { errorCodeConstantMapping, ErrortType } from '@/common/reporter/errorMapping';


export function DegradePage() {

    useDidShow(() => {
        reportInfo({
            code: errorCodeConstantMapping?.LOADERROR_NOT_PROPERLY_PAGE_EXCEPTION,
            errorDetail: {
              errorType: ErrortType.Info,
            }
        })
    })

    return (
        <View style={{ flex: 1 }}>
            <NavBar goBack={() => Taro.navigateBack()}></NavBar>
            <JDNoDataView message={"抱歉，页面走丢了~~"}></JDNoDataView>
        </View>
    )
}
import { reportInfo } from '@/common/reporter';
import { errorCodeConstantMapping, ErrortType } from '@/common/reporter/errorMapping';
import React, { Component, ReactNode } from 'react';

interface Props {
    children: ReactNode;
    fallback?: ReactNode;
}

interface State {
    hasError: boolean;
}

class ErrorBoundary extends Component<Props, State> {
    constructor(props: Props) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error): State {
        // Update state so the next render will show the fallback UI.
        return { hasError: true };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        // You can also log the error to an error reporting service
        console.error(error, errorInfo);
        const childrenArray = React.Children.toArray(this.props.children);
        const firstChild = childrenArray[0];
        console.error('componentDidCatch', error, errorInfo)
        if (React.isValidElement(firstChild)) {
            const { displayName, name } = firstChild.type as any
            reportInfo({
                code: errorCodeConstantMapping?.LOADERROR_BUSINESS_IMMEDIATE_ATTENTION,
                errorDetail: {
                    errorType: ErrortType.Error,
                    customMsg: {
                        compName: displayName || name,
                        errorInfo: error?.message,
                        errorStack: error?.stack,
                    },
                }
            })
        } else {
            console.log('No valid child element found');
        }
    }

    render(): ReactNode {
        if (this.state.hasError) {
            return (
                this.props.fallback || null
            );
        }

        return this.props.children;
    }
}

export default ErrorBoundary;

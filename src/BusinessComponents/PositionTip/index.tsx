import { View, Text, Image } from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import { usePositionTip } from './index.service'
import { getImageUrl } from '@/assets/imgs'
import classNames from 'classnames'
import { STYLE_TYPE } from './index.model'
import { InView } from '@/BaseComponents/IntersectionObserver'

const ImgConfig = {
    warning: getImageUrl('warning'), // 警告
    wordDelete: getImageUrl('wordDelete'), // 删除
}

const PositionTip = (props) => {
    const {
        isShowTip,
        handleClose,
        handleOpen,
        styleType,
        handelExpo,
    } = usePositionTip(props)

    if (!isShowTip) {
        return null
    }

    return (
        <InView  onChange={(visible) => {
            handelExpo(visible)
          }} className={classNames({
            [styles.wrapper]: styleType === STYLE_TYPE.NORMAL,
            [styles.modelWrapper]: styleType === STYLE_TYPE.MODEL,
        })}>
            <View className={classNames({
                [styles.container]: true,
                [styles.flex1]: true
            })}>
                {
                     styleType === STYLE_TYPE.NORMAL && (
                        <Image className={styles.tipIcon} src={ImgConfig['warning']}></Image>
                     )
                }
                <Text className={classNames({
                    [styles.description]: styleType === STYLE_TYPE.NORMAL,
                    [styles.modelDescription]: styleType === STYLE_TYPE.MODEL,
                })} numberOfLines={1}>开启定位后，为您提供更优质的搜索体验</Text>
            </View>
            <View className={styles.container}>
                <View onClick={handleOpen} className={classNames({
                    [styles.btnContainer]: styleType === STYLE_TYPE.NORMAL,
                    [styles.modelBtnContainer]: styleType === STYLE_TYPE.MODEL,
                })}>
                    <Text className={classNames({
                        [styles.btn]: styleType === STYLE_TYPE.NORMAL,
                        [styles.modelBtn]: styleType === STYLE_TYPE.MODEL,
                    })}>开启</Text>
                </View>
                <View onClick={handleClose}>
                    <Image className={styles.closeIcon} src={ImgConfig['wordDelete']}></Image>
                </View>
            </View>
        </InView>
    )
}

export default PositionTip
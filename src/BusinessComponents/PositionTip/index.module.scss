@import "@/assets/theme.scss";

.wrapper {
    display: flex;
    // width: 100%;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    background-color: #FFF8EA;
    height: 36px;
    // margin-top: 6px;
    padding-left: 12px;
    padding-right: 12px;
}

.modelWrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 42px;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.80);
    margin: 0 12px;
    padding-left: 12px;
    padding-right: 8px;
}

.container {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.flex1 {
    flex: 1;
}

.tipIcon {
    width: 14px;
    height: 14px;
    margin-right: 4px;
    flex-grow: 0;
    flex-shrink: 0;
    margin-top: 1px;
}

.description {
    color: var(--primaryTextColor);
    font-size: 12px;
    flex-shrink: 1;
    flex-grow: 0;
}

.modelDescription {
    color: #fff;
    font-size: 14px;
    font-weight: 500;
}

.btn {
    // line-height: 23px;
    font-size: 12px;
    color: var(--primaryTextColor);
    text-align: center;
}

.btnContainer {
    height: 25px;
    width: 48px;
    border-radius: 4px;
    border: 0.8px solid var(--primarySubTextColor);
    // padding: 4px 8px;
    margin-right: 12px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}

.modelBtnContainer {
    // padding: 0 10px;
    border-radius: 4px;
    background-color: var(--primaryHLTextColor);
    color: #fff;

    margin-right: 4px;
    width: 42px;
    height: 24px;
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: center;
}

.modelBtn {
    color: #fff;
    text-align: center;
    font-size: 12px;
}

.closeIcon {
    width: 15px;
    height: 15px;
}

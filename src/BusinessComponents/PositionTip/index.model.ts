import { requestLocationPermission } from "@/common/LBS/getLBS"
import localStorage from "@/utils/LocalStorage";

export enum STYLE_TYPE {
    MODEL, // model形式定位在页面上
    NORMAL // 普通形式
}

export default class PositionTipModel {
    private mainDataKey = '_travelSearchPosTipHideTime'
    public styleType : STYLE_TYPE

    constructor (props) {
        const { styleType = STYLE_TYPE.NORMAL } = props
        this.styleType = styleType 
    }

    // 打开定位权限
    async requestLocationPermission () {
       return await requestLocationPermission()
    }

    // 获取localStorage中的tip时间
    async getIsShowTip () {
        const tipHideTime = await localStorage.getItem(this.mainDataKey)
        const now = new Date().getTime()
        // 时间存在并且没过期 或者没有时间
        if ((!!tipHideTime && now > tipHideTime) || !tipHideTime ) {
            return true
        } else {
            return false
        }
    }

    // 设置tip隐藏时间
    setPosTipHideTime (day) {
        const tipHideTime = new Date().getTime() + day * 24 * 60 * 60 * 1000
        localStorage.setItem(this.mainDataKey, tipHideTime)
    }

}
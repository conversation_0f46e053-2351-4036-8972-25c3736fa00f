import { useEffect, useRef, useState } from "react"
import PositionTipModel, { STYLE_TYPE } from "./index.model"
import { globalAddress } from "@/store"
import locationPermissionModel from "@/store/model/locationPermission.model"
import { isEmpty } from "@/utils/isType"
import { useDidShow } from "@tarojs/taro"
import { M_EVENTID, M_PAGE, M_PAGEID, mtaExposure, newMta } from "@/common/mta"
import IdleQueue from "@/utils/IdleQueue"
import {showToast} from '@/BaseComponents/atoms/utils/toast'

export function usePositionTip (props) {
    const model = new PositionTipModel(props)
    const [isShowTip, setIsShowTip] = useState(false)
    const locationPermission = useRef()
    const isFirst = useRef(true)
    const updateAddress = globalAddress.use.updateAddress()
    const updateLocationPermisson = globalAddress.use.updateLocationPermisson()
    const firstInView = useRef(false)

    useEffect(() => {
        // 订阅地址 如果地址返回了才判断是否展示tip
        const subs = locationPermissionModel.subscribe((result: any) => {
            // result?.value?.locationPermission 是否有定位权限 true 有 false 没有
            if (result?.value?.locationPermission == locationPermission.current
            ) {
                return
            }
            locationPermission.current = result?.value?.locationPermission
            handlePermission(result?.value?.locationPermission)
            isFirst.current = false

            
        });
        return () => {
            subs.unsubscribe()
        }
    }, [])

    // 刷新接口
    useDidShow(() => {
        if (!isFirst.current || !isEmpty(locationPermissionModel.locationPermission)) {
            handlePermission(locationPermissionModel.locationPermission)
        }
    })

    // 处理权限
    const handlePermission = (permission) => {
        model.getIsShowTip().then((showFlag) => {
            console.log('是否关闭过定位提示且没过期', showFlag)
            // true 没点过关闭tip
            if (showFlag) {
                console.log('是否开启了定位权限', permission)
                // permission true 开了权限不展示tip false 没开权限展示tip
                setIsShowTip(!permission) // true 开了权限不展示tip false 没开权限展示tip
            } else {
                setIsShowTip(showFlag)
            }
        }).catch((e) => {
            console.log('获取是否关闭过地址定位提示失败:', e)
            // 异常兜底规则 不展示提示
            setIsShowTip(false)
        })
    }

    // 点击关闭按钮的操作
    const handleClose = () => {
        // 关闭tip时将当前时间戳存起来
        setIsShowTip(false)
        const day = 1
        model.setPosTipHideTime(day)
    }

    // 点击开启
    const handleOpen = () => {
        if (model.styleType === STYLE_TYPE.NORMAL) {
            // 埋点
            IdleQueue.add(newMta, M_EVENTID.TravelSearchLocation, M_PAGEID.TravelSearch, M_PAGE.SearchIndex)
        }
        // 开启成功后更新操作
        model.requestLocationPermission().then((data: any) => {
            const address = data?.address
            const openLocationPermisson = data?.openLocationPermisson
            updateAddress(address)
            updateLocationPermisson({
                locationPermission: openLocationPermisson
            })
        }).catch((err) => {
            console.log('请求开启定位失败', err)
            if (err?.showToast) {
                showToast({
                    title: '请求开启定位失败,请稍后重试',
                    icon: 'none',
                    duration: 2000,
                });
            }
        })
    }

    // 曝光埋点
    const handelExpo = (visible) => {
        if (model.styleType === STYLE_TYPE.NORMAL && visible && !firstInView.current) {
            firstInView.current =  true
            IdleQueue.add(mtaExposure, M_EVENTID.TravelSearchLocationExpo, M_PAGEID.TravelSearch, M_PAGE.SearchIndex)
        }
    }

    return {
        isShowTip,
        handleClose,
        handleOpen,
        styleType: model.styleType,
        handelExpo,
    }
}
import { PropsWithChildren, useEffect } from 'react'
import { Platform } from 'react-native';
import { useDidHide, useDidShow, useLaunch } from '@tarojs/taro'
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { setGlobalConfig } from '@ltfe/ltfe-core-lib/lib/utiles'
import './app.scss'
import mixin from './common/mixin'
import initApiMiddleware from './common/init-api-middleware'
import initGlobalMiddleware from './common/init-global-middleware'
import { isWeb } from '@/common/common'
import { globalAddress } from "@/store"
import { getLatLngPos } from './common/LBS/getLBS';
import globalInfoModel from '@/store/model/globalInfo.model'
import { reportInfo } from './common/reporter';
import { errorCodeConstantMapping, ErrortType } from './common/reporter/errorMapping';
import addressModel from './store/model/address.model';
import ResizeObserver from 'resize-observer-polyfill';
import locationPermissionModel from "@/store/model/locationPermission.model"

if (Platform.OS == 'web') {
  if (!window.ResizeObserver) {
    window.ResizeObserver = ResizeObserver;
  }
}

setGlobalConfig({
  timezone: 'Asia/Shanghai'
})

const originalConsoleError = console.error;

console.error = (...args) => {
  // console.log('args', args)
  if (args.some(x => x?.includes?.('Invalid props.style key `$$css` supplied to'))) {
    // Do nothing, ignore the warning
  } else {
    originalConsoleError(...args);
  }
};

const LaunchInfo = { launched: false }
const UseEffectInfo = { hasRun: false }
function App(props: { children: { children: PropsWithChildren<any> } }) {
  const { children, forwardedRef, ...ohters } = props
  const updateAddress = globalAddress.use.updateAddress()
  const updateLocationPermisson = globalAddress.use.updateLocationPermisson()

  useLaunch(async (options) => {
    if (isWeb && options?.query?.rndegrade == '1') {
      reportInfo({
        code: errorCodeConstantMapping?.H5_PAGEERROR_PAGE_INITIALIZATION,
        errorDetail: {
          errorType: ErrortType.Info
        }
      })
    }
    reportInfo({
      code: errorCodeConstantMapping?.PAGEERROR_PAGE_INITIALIZATION,
      errorDetail: {
        errorType: ErrortType.Info
      }
    })
    LaunchInfo.launched = true;
    mixin([
      initGlobalMiddleware(),
      initApiMiddleware()
    ])(options.query)
      globalInfoModel.staticParams = options.query
    try {
      await fetchAddress()
    } catch (error) {
      reportInfo({
          code: errorCodeConstantMapping?.TRYCATCHERROR_CATCH_CAPTURE_CODE_EXCEPTION,
          errorDetail: {
            errorType: ErrortType.Info,
            customMsg: {
                  errorInfo: error?.message,
                  errorStack: error?.stack,
              },
          }
      })
    }
  })

  // 处理地址等
  const handleData = (data) => {
    const address = data?.address
    const openLocationPermisson = data?.openLocationPermisson
    updateAddress(address)
    updateLocationPermisson({
      locationPermission: openLocationPermisson
    })
  }

  useEffect(() => {
    if (!isWeb) {
      globalInfoModel.staticParams = ohters
    }
    // 因为useLaunch只在启动时执行一次，这里只是开发时要用
    console.log('useLaunch--2', LaunchInfo.launched)
    if (!LaunchInfo.launched && !UseEffectInfo.hasRun) {
      UseEffectInfo.hasRun = true
      console.log('useLaunch', props,)
      mixin([
        initGlobalMiddleware(),
        initApiMiddleware()
      ])(ohters)
      if (isWeb) {
        globalInfoModel.staticParams = ohters
      }
      fetchAddress()
    }

    return () => {
      reportInfo({
        code: errorCodeConstantMapping?.PAGEERROR_PAGE_EXIT,
        errorDetail: {
          errorType: ErrortType.Info
        }
      })
    }
  }, [])


  // useLayoutEffect(() => {
  //   Taro.eventCenter.on('__taroRouterChange', routerListener)
  //   return () => {
  //     Taro.eventCenter.off('__taroRouterChange', routerListener)
  //   }
  // }, [])


  // taro_page taro_page_show taro_page_stationed taro_page_shade

  useDidShow(() => {
    console.log('useDidShow')
    if (locationPermissionModel.locationPermission == false) {
      fetchAddress()
    }
  })

  const fetchAddress = async () => {
    try {
      const addressPromise = getLatLngPos()
      addressModel.getAddress = addressPromise
      const addressInfo = await addressPromise
      console.log('获取到的地址信息', addressInfo)
      handleData(addressInfo)
    } catch (error) {
      handleData(error)
      console.error('获取到的地址信息--catch:', error);
    }
  };

  return (
    <SafeAreaProvider style={isWeb && { height: '100%' }}>
      {props.children as any}
    </SafeAreaProvider>
  )
}

export default App

const routers = require('./router/index').routers;

const pages = routers.sort(x => x.root ? -1 : 1);


export default defineAppConfig({
  pages: pages.map(x => x.position),
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: 'Taro',
    navigationBarTextStyle: 'black',
    navigationStyle: "custom",
    allowSlidePopFirstPage: true
  },
  animation: false
})

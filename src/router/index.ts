const ROUTERS = {
    index: 'pages/index/index',
    result: 'pages/result/index',
    vresult: 'pages/VResult/index',
    search: 'pages/search/index',
    hotelSearch: 'pages/hotelSearch/index',
    businessSearch: 'pages/businessSearch/index',
    channel: 'pages/channel/index'
}
module.exports.ROUTERS = ROUTERS

// 路由修改有坑请注意！！！！
// 路由相关注意文档：https://joyspace.jd.com/pages/p4X4SQo0GHRsYgzGZ0Xo
// 该文档中的三端协议与已有页面路由跳转

module.exports.routers = [
    {
        root: true,
        position: ROUTERS.index,
        customRouter: '/search/index',
    }, {
        root: false,
        position: ROUTERS.result,
        customRouter: '/search/result',
    }, {
        root: false,
        position: ROUTERS.vresult,
        customRouter: '/search/vresult',
    }, {
        root: false,
        position: ROUTERS.search,
        customRouter: '/search/search',
    }, {
        root: false,
        position: ROUTERS.hotelSearch,
        customRouter: '/search/hotelSearch',
    }, {
        root: false,
        position: ROUTERS.businessSearch,
        customRouter: '/search/businessSearch',
    }, {
        root: false,
        position: ROUTERS.channel,
        customRouter: '/search/channel',
    }
];

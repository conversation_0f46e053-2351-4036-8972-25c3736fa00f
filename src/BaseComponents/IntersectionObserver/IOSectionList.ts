import { RefAttributes } from 'react';
import { SectionList, SectionListProps } from 'react-native';
import withIO, { IOComponentProps } from './withIO';

export type IOSectionListController = SectionList;

export type IOSectionListProps<ItemT = any> = IOComponentProps &
SectionListProps<ItemT>;

const IOSectionList = withIO(SectionList, [
  'flashScrollIndicators',
  'getNativeScrollRef',
  'getScrollResponder',
  'getScrollableNode',
  'scrollToEnd',
  'scrollToIndex',
  'scrollToItem',
  'scrollToOffset',
  'scrollToLocation'
]);

declare function IOSectionListFunction<ItemT = any>(
  props: IOSectionListProps<ItemT> & RefAttributes<IOSectionListController>
): JSX.Element;

export default IOSectionList as unknown as typeof IOSectionListFunction;

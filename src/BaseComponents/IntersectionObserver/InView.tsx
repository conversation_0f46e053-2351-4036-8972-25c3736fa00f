import React, {
  ComponentType,
  PureComponent,
  ReactElement,
  ReactNode,
  RefObject,
} from 'react';
import { LayoutChangeEvent, ViewProps } from 'react-native';
import { View } from '@/BaseComponents/atoms'
import IOContext, { IOCOntextValue } from './IOContext';
import { Element } from './IntersectionObserver';
import { ObserverInstance } from './IOManager';
import withClassName from '../atoms/utils/withClassName';

export interface RenderProps {
  inView: boolean;
  onChange: (inView: boolean) => void;
}

export interface Props {
  [key: string]: any;
}

export type InViewProps<T = Props> = T & {
  /**
   * Render the wrapping element as this element.
   * @default `'View'`
   */
  as?: ComponentType<any>;
  children: ReactNode | ((fields: RenderProps) => ReactElement<View>);
  /** Only trigger the inView callback once */
  triggerOnce?: boolean;
  /** Call this function whenever the in view state changes */
  onChange?: (inView: boolean) => void;
  onLayout?: (event: LayoutChangeEvent) => void;
  index?: number;
  flag?: any;
};

export type InViewWrapper = ComponentType<{
  ref?: RefObject<any> | ((ref: any) => void);
  onLayout?: (event: LayoutChangeEvent) => void;
}>;

class InView<T = ViewProps> extends PureComponent<InViewProps<T>> {
  static contextType = IOContext;

  static defaultProps: Partial<InViewProps> = {
    triggerOnce: false,
    as: View,
  };

  context: undefined | IOCOntextValue = undefined;

  mounted = false;

  protected element: Element;
  protected elementForParent: Element;

  protected instance: undefined | ObserverInstance;
  protected instanceForParent: undefined | ObserverInstance;

  protected view: any;

  constructor(props: InViewProps<T>) {
    super(props);

    this.measureLayout = this.measureLayout.bind(this);

    this.element = {
      inView: false,
      inParentView: false,
      layout: {
        x: 0,
        y: 0,
        width: 0,
        height: 0,
        flag: this.props.flag
      },
      index: props.index,
      measureLayout: this.measureLayout,
    };
    this.elementForParent = { ...this.element }
  }

  componentDidMount() {
    this.mounted = true;
    if (this.context?.manager) {
      this.instance = this.context.manager.observe(
        this.element,
        this.handleChange,
        { withParent: !!this.context?.parentManger, parent: this.elementForParent }
      );
    }
    if (this.context?.parentManger) {
      this.instanceForParent = this.context.parentManger.observe(
        this.elementForParent,
        this.handleChange,
        { isGrandson: true, child: this.element }
      );
    }
  }

  componentWillUnmount() {
    this.mounted = false;
    if (this.context?.manager && this.instance) {
      this.context.manager.unobserve(this.element);
    }
  }

  protected handleChange = (inView: boolean) => {
    if (this.mounted) {
      const { triggerOnce, onChange } = this.props;
      if (inView && triggerOnce) {
        if (this.context?.manager) {
          this.context?.manager.unobserve(this.element);
        }
      }
      if (onChange) {
        onChange(inView);
      }
    }
  };

  protected handleRef = (ref: any) => {
    this.view = ref;
  };

  protected handleLayout = (event: LayoutChangeEvent) => {
    const {
      nativeEvent: { layout },
    } = event;
    if (
      layout.width !== this.element.layout.width ||
      layout.height !== this.element.layout.height
    ) {
      if (this.element.onLayout) {
        this.element.onLayout();
      }
    }
    const { onLayout } = this.props;
    if (onLayout) {
      onLayout(event);
    }
  };

  stDidUpdate;
  // 当使用组件可复用的列表时，当组件复用了，重绘后，需要重新计算组件的位置：measureTarget
  componentDidUpdate(prevProps: Readonly<InViewProps<T>>, prevPState: Readonly<{}>): void {
    // console.log('isShow UNSAFE_componentWillUpdate', prevProps.index, this.props.index)
    if (this.props.index !== prevProps.index) {
      this.element.index = this.props.index;
      // 当使用组件可复用的列表时，当组件复用了，重绘后，需要重新计算组件的位置：measureTarget
      clearTimeout(this.stDidUpdate)
      this.stDidUpdate = setTimeout(() => {
        this.element.reMeasureLayout && this.element.reMeasureLayout(this.element);
      }, 20);
    }
  }

  measureInWindow = (...args: any) => {
    this.view.measureInWindow(...args);
  };

  measureLayout = (...args: any) => {
    this.view?.measureLayout?.(...args);
  };

  setNativeProps = (...args: any) => {
    this.view.setNativeProps(...args);
  };

  focus = (...args: any) => {
    this.view.focus(...args);
  };

  blur = (...args: any) => {
    this.view.blur(...args);
  };

  render() {
    const { as, children, ...props } = this.props;
    if (typeof children === 'function') {
      return null; // TODO: need?
    }
    const ViewComponent: InViewWrapper = (as || View) as InViewWrapper;
    return (
      <ViewComponent
        {...props}
        ref={this.handleRef}
        onLayout={this.handleLayout}
      >
        {children}
      </ViewComponent>
    );
  }
}

export default withClassName()(InView);
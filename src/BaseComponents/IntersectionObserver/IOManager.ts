import { Element } from './IntersectionObserver';

import IntersectionObserver, {
  IntersectionObserverOptions,
  IntersectionObserverEntry,
} from './IntersectionObserver';

export type ObserverInstanceCallback = (inView: boolean) => void;
export type ObserverInstanceOptions = {
  isGrandson?: boolean; // 告知父级使用：是否是嵌套双层Scrollview下InView
  withParent?: boolean; //告知：本身使用：是否是嵌套双层Scrollview下InView
  child?: Element
  parent?: Element
}

export interface ObserverInstance {
  readonly callback: ObserverInstanceCallback;
  readonly element: Element;
  readonly observerId: number;
  readonly observer: IntersectionObserver;
  st?: NodeJS.Timeout; // 用于记录延迟曝光的句柄
  options?: ObserverInstanceOptions;
  selfOptions?: ObserverInstanceOptions;
}

class IOManager {
  io: IntersectionObserver;
  observerId: number;
  instanceMap: Map<Element, ObserverInstance> = new Map();
  delay?: number;

  constructor(options: IntersectionObserverOptions) {
    this.io = new IntersectionObserver(this.handleChange, options);
    this.observerId = 0;
    this.delay = options.time || 0;
  }

  handleChange = (entries: IntersectionObserverEntry[]) => {
    for (let index = 0; index < entries.length; index += 1) {
      const { target, isIntersecting } = entries[index];
      const instance = this.instanceMap.get(target);
      if (instance) {
        if(!isIntersecting) {
          clearTimeout(instance.st);
          instance.callback(isIntersecting); // 这个callback就是InView传入的onChange
          continue;
        }

        // if(instance.element.layout.flag) {
        //   debugger
        // }
        if(instance.options?.isGrandson && !instance.options.child.inView) {
          continue;
        }

        if(instance.options?.withParent && !instance.options.parent.inView) {
          continue;
        }
        
        if(this.delay > 0) {
          instance.st = setTimeout(() => {
            instance.callback(isIntersecting); // 这个callback就是InView传入的onChange
          }, this.delay);
        } else {
          instance.callback(isIntersecting); // 这个callback就是InView传入的onChange
        }
      }
    }
  };

  observe(
    element: Element,
    callback: ObserverInstanceCallback,
    options?: ObserverInstanceOptions
  ): ObserverInstance {
    const existInstance = this.instanceMap.get(element);
    if (existInstance) {
      return existInstance;
    }
    this.observerId += 1;
    const instance: ObserverInstance = {
      callback,
      element,
      observerId: this.observerId,
      observer: this.io,
      options
    };
    this.instanceMap.set(element, instance);
    this.io.observe(element);
    return instance;
  }

  unobserve(element: any) {
    if (this.instanceMap.has(element)) {
      this.instanceMap.delete(element);
      this.io.unobserve(element);
    }
  }
}

export default IOManager;

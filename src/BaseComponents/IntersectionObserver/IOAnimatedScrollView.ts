import { ForwardRefExoticComponent, RefAttributes } from 'react';
import { Animated, ScrollView, ScrollViewProps } from 'react-native';
import withIO, { IOComponentProps } from './withIO';

type AnimatedScrollViewType = typeof Animated.ScrollView;

export type IOScrollViewController = AnimatedScrollViewType;

export type IOScrollViewProps = IOComponentProps & ScrollViewProps & { children?: React.ReactNode };



const IOAnimatedScrollView = withIO(Animated.ScrollView as any, [
  'scrollTo',
  'scrollToEnd',
  'getScrollResponder',
  'getScrollableNode',
  'getInnerViewNode',
]);

export default IOAnimatedScrollView as unknown as ForwardRefExoticComponent<
  IOScrollViewProps & RefAttributes<IOScrollViewController>
>;

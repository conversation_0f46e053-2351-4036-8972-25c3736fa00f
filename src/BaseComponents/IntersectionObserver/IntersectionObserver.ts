import { LayoutRectangle, NativeScrollEvent, Platform } from 'react-native';
import throttle from 'lodash/throttle';
import { ObserverInstanceOptions } from './IOManager';

export interface Root {
  node: any;
  innerNode: any;
  horizontal: boolean;
  current: NativeScrollEvent;
  /**
   * 布局回调（初始化挂载、内容高度变化等）
   */
  onLayout?: ({ force }?: { force: boolean }) => void;
  /**
   * 滚动回调
   */
  onScroll?: (event: NativeScrollEvent) => void;
}

export interface Element {
  inView: boolean;
  index?: number;
  inParentView: boolean;
  layout: LayoutRectangle;
  reMeasureLayout?: (target: Element) => void;
  /**
   * 计算布局
   */
  measureLayout: (
    node: any,
    callback: (x: number, y: number, width: number, height: number) => void
  ) => void;
  /**
   * 布局回调（内容高度变化等）
   */
  onLayout?: () => void;
  options?: ObserverInstanceOptions,
  selfOptions?: ObserverInstanceOptions
}

export interface IntersectionObserverEntry {
  target: Element;
  isIntersecting: boolean;
}

export interface RootMargin {
  left?: number;
  right?: number;
  top?: number;
  bottom?: number;
}

export interface IntersectionObserverOptions {
  root: Root;
  rootMargin?: RootMargin;
  thresholds?: number[];
  time?: number;
}

export type IntersectionObserverCallback = (
  entries: IntersectionObserverEntry[]
) => void;

export const defaultRootMargin: RootMargin = {
  left: 0,
  right: 0,
  top: 0,
  bottom: 0,
};

export const defaultThresholds = [0]

class IntersectionObserver {
  protected callback: IntersectionObserverCallback;
  protected options: IntersectionObserverOptions;
  protected targets: Element[];

  constructor(
    callback: IntersectionObserverCallback,
    options: IntersectionObserverOptions
  ) {
    this.callback = callback;
    this.options = options;
    this.targets = [];
    this.options.root.onLayout = this.handleLayout;
    this.options.root.onScroll = this.handleScroll;
  }

  protected measureTarget = (target: Element) => {
    let rootNode = Platform.OS === 'web' ? this.options.root.innerNode : this.options.root.node;
    rootNode = rootNode || this.options.root.node;

    if (rootNode) {
      target.measureLayout(rootNode, (x, y, width, height) => {
        // if (target.layout.flag) {
        //   console.log('flag x, y, width, height', x, y, width, height)
        // }
        target.layout = {
          x,
          y,
          width,
          height,
          flag: target.layout.flag,
          hasLayout: true
        };

        target.reMeasureLayout = this.measureTarget;

        this.handleScroll();
      });
    }

  };

  protected handleLayout = throttle(
    (options) => {
      const targets = options?.force ? this.targets : this.targets.filter(x => !x.layout.hasLayout)
      for (let index = 0; index < targets.length; index += 1) {
        this.measureTarget(targets[index]);
      }
    },
    20,
    { leading: false, trailing: true }
  ); // TODO: 优化节流

  // throttle(
  protected handleScroll =
    () => {
      const rootMargin = this.options?.rootMargin || defaultRootMargin;
      const thresholds = this.options?.thresholds || defaultThresholds;

      const threshold = thresholds[0];

      const {
        horizontal,
        current: {
          contentOffset, // 偏移量 容器滚动的距离，例如：scrollTop, scrollLeft
          contentSize,
          layoutMeasurement, // 布局大小
        },
      } = this.options.root;
      if (
        contentSize.width <= 0 ||
        contentSize.height <= 0 ||
        layoutMeasurement.width <= 0 ||
        layoutMeasurement.height <= 0
      ) {
        return;
      }
      const contentOffsetWithLayout = horizontal
        ? contentOffset.x + layoutMeasurement.width // 滚动容器宽度+滚动的距离
        : contentOffset.y + layoutMeasurement.height; // 滚动容器高度+滚动的距离
      const changedTargets: IntersectionObserverEntry[] = [];
      for (let index = 0; index < this.targets.length; index += 1) {
        const target = this.targets[index];
        const targetLayout: LayoutRectangle = target.layout;
        if (
          !targetLayout ||
          targetLayout.width === 0 ||
          targetLayout.height === 0
        ) {
          continue;
        }
        let isIntersecting = false;
        if (horizontal) {
          // if (target.layout.flag && isIntersecting) {
          //   debugger
          // }
          isIntersecting =
            Math.ceil(contentOffsetWithLayout - (rootMargin.right || 0)) > targetLayout.x + (targetLayout.width * threshold) // 右边漏出临界值
            &&
            contentOffset.x + (rootMargin.left || 0) < Math.ceil(targetLayout.x + (targetLayout.width * (1 - threshold))); // 左边漏出临界值
        } else {
          let targetLayoutHeight = threshold < 0 ? targetLayout.height : targetLayout.height * threshold;
          isIntersecting =
            Math.ceil(contentOffsetWithLayout - (rootMargin.bottom || 0)) > targetLayout.y + targetLayoutHeight // 下边漏出临界值
            &&
            contentOffset.y + (rootMargin.top || 0) < Math.ceil(targetLayout.y + (targetLayout.height * (1 - threshold))) // 上边露出临界值
        }

        if (target.inView !== isIntersecting) {
          target.inView = isIntersecting;
          changedTargets.push({ target, isIntersecting });
        }

      }

      this.callback(changedTargets);
    }
  // ,
  //   20,
  //   { leading: false, trailing: true }
  // )

  public observe(target: Element) {
    const index = this.targets.indexOf(target);
    if (index < 0) {
      target.onLayout = this.handleLayout;
      this.targets.push(target);
    }
  }

  public unobserve(target: Element) {
    const index = this.targets.indexOf(target);
    if (index >= 0) {
      target.onLayout = undefined;
      this.targets.splice(index, 1);
    }
  }
}

export default IntersectionObserver;

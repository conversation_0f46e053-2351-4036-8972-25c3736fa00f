import { JDDialog } from '@jdreact/jdreact-core-lib';
import FloatLayout from '@/BaseComponents/FloatLayout'


const Dialog = (props) => {
    const { children, dialogStyle, ...others } = props

    return (
        <FloatLayout>
            <JDDialog overlayStyle={{}} dialogStyle={{...dialogStyle, position: 'relative', top: '38%', margin: '0 auto'}} {...others}>{children}</JDDialog>
        </FloatLayout>
    )
}

export default Dialog

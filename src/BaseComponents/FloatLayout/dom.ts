// 定义 `getRoot` 函数，返回页面中最后一个带有特定属性的 "root" 元素或其新建的 "div" 子元素
const getRoot = (): Element | undefined => {
    // 获取所有 "root" 元素，选取最后一个
    const root = document.getElementById("app");

    // 如果不存在 "root" 元素，直接返回 undefined
    if (!root) {
        return undefined;
    }

    // 查找 "root" 元素中第一个具有属性 root="root" 的子元素
    let element = Array.from(root.children).find(node => node.getAttribute('root') === 'root');

    // 如果没有找到符合条件的子元素，创建一个新的 "div" 元素，设置属性，并添加到 "root" 元素中
    if (!element) {
        element = document.createElement("div");
        element.setAttribute("root", "root");
        root.appendChild(element);
    }

    // 返回找到或创建的元素
    return element;
};

export default getRoot;
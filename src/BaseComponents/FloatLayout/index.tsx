import React, { ReactNode, useEffect, useState  } from "react";
import { View } from "@tarojs/components";
import ReactDOM from "react-dom";
import getRoot from "./dom";

interface FloatLayoutProps {
    children?: ReactNode; // children 属性，可以是任何 React 节点
    rootEl?: Element // 自定义容器
}

// FloatLayout 组件，用于在 root 元素上渲染浮动布局
const FloatLayout: React.FC<FloatLayoutProps> = (props) => {
    const { children, rootEl } = props;
    const [root, setRoot] = useState<Element>(rootEl!); // 存储  root 元素的状态

    // 当组件挂载后，使用 getRoot 获取 root 元素并设置状态
    useEffect(() => {
        if (!rootEl) {
            setRoot(getRoot()!);
        }
    }, []);

    // 如果没有 root 元素，不渲染任何内容
    if (!root) {
        return null;
    }

    // renderChild 函数用于处理 children 的渲染逻辑
    const renderChild = (): React.ReactElement | null => {
        if (!children) return null;
        return typeof children === 'function' ? children() : children;
    };

    // 使用 ReactDOM.createPortal 将子元素渲染到 root 元素中
    return ReactDOM.createPortal(
        <View>
            {renderChild()}
        </View>,
        root
    );
};

export default FloatLayout;
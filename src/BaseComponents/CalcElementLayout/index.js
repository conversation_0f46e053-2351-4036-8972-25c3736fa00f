import React, { forwardRef, memo, useCallback, useImperativeHandle, useRef, useState } from 'react';
import { StyleSheet, View, Dimensions } from 'react-native';
import { useProcessActionQueue } from '@/utils/useProcessActionQueue';
import { isObject } from '@tarojs/shared';
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
const { width } = Dimensions.get('window')
function CalcElementLayout(props, ref) {
    const initState = { datas: [], renderRow: null, callback: null };
    const [state, setState] = useState(initState);
    const refLayoutList = useRef([]);
    const stRef = useRef()

    const refResolve = useRef(null);
    // const refLayoutCompeleted = useRef(false)

    const reCalc = useCallback(async (args) => {
        setState(args);
        const p = new Promise(resolve => {
            refResolve.current = resolve;
        });

        await p;
    }, []);

    const { processActionQueue } = useProcessActionQueue(reCalc);

    useImperativeHandle(ref, () => ({
        getLayoutList: async ({ datas, renderRow, callback }) => {
            if (stRef.current) {
                clearTimeout(stRef.current)
            }
            await processActionQueue({ datas, renderRow, callback });
            stRef.current = setTimeout(() => {
                setState(initState);
            }, 2)
        }
    }), []);

    let { datas, renderRow, callback } = state;
    renderRow = renderRow || props.renderRow;

    const _onLayoutCompelete = (e, index, item) => {
        try {
            item.layout= e.nativeEvent.layout
            refLayoutList.current[index] = item
            if (refLayoutList.current.filter(x => {
                return isObject(x) || x === undefined
            }).length === datas.length) {
                const tmpLayoutList = refLayoutList.current;
                refLayoutList.current = [];
                refResolve.current && refResolve.current();
                callback && callback(tmpLayoutList);
            }
        } catch (error) {
            reportInfo({
                code: errorCodeConstantMapping?.TRYCATCHERROR_CATCH_CAPTURE_CODE_EXCEPTION,
                errorDetail: {
                  errorType: ErrortType.Info,
                  customMsg: {
                        errorDescription: '计算文本错误',
                        errorInfo: error?.message,
                        errorStack: error?.stack,
                    },
                }
            })
            console.error('计算文本错误', error)
            refResolve.current && refResolve.current();
        }
    };

    try {
        return (
            <View style={[styles.container]}>
                {
                    datas?.map((item, index) => {
                        return (
                            <View style={[styles.txt, props.width && { width: props.width - ((width - 377) > 0 ? (width - 377) : 16) }]} key={`${Date.now()}_${index}`} onLayout={(e) => _onLayoutCompelete(e, index, item)}>
                                {
                                    renderRow && renderRow(item, index, true)
                                }
                            </View>
                        );
                    })
                }
            </View>
        );
    } catch (error) {
        console.error('error', error)
        refResolve.current && refResolve.current();
        return null
    }
}

export default memo(forwardRef(CalcElementLayout));

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        top: 0,
        left: 0,
        zIndex: 9999,
        width: 99999,
        flexGrow: 0,
        alignItems: 'center',
        flexDirection: 'column',
        // borderWidth: 1
        transform: [{ translateY: -199999 }]
    },
    txt: {
        // flexShrink: 0,
        // flexGrow: 0,
    }
});


import { usePrevious } from "@/utils/hooks";
import isEqual from 'lodash-es/isEqual'
import { forwardRef, useMemo, useRef } from "react";

/**
 * 高阶组件，用于处理样式变化, 只适用于React-Native组件
 * @param WrappedComponent 被包裹的组件
 * @returns 包装后的组件
 */
export default function withClassName() {
    return function (WrappedComponent) {
        return forwardRef<any, any>(function (props, ref) {
            const { style, ...others } = props;

            const refPrevStyle = usePrevious(style)

            const refDep = useRef<any[]>([])
            const isEqualStyle = isEqual(style, refPrevStyle)
            // if(props.isTest) {
            //     console.log('refPrevStyle, style', refPrevStyle, style, isEqualStyle)
            // }
            if (!isEqualStyle) { refDep.current = [Date.now()] }

            const newStyle = useMemo(() => {
                return style
            }, [refDep.current]) as any
            


            return <WrappedComponent ref={ref} {...others} style={newStyle} />;
        });
    };
}

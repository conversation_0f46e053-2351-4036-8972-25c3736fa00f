import { JDTextInput } from '@jdreact/jdreact-core-lib'
import { forwardRef } from 'react';
import withClassName from '../utils/withClassName';

const TextInput = (props, ref) =>  {
    const { style, onChangeText, value, clearButtonMode, placeholder, ...others } = props

    return (
        <JDTextInput ref={ref} style={style} onChangeText={onChangeText} value={value} clearButtonMode={clearButtonMode} placeholder={placeholder} {...others}/>
    )
}

export default withClassName()(forwardRef(TextInput));
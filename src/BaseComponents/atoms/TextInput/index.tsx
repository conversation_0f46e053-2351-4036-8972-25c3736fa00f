import { Input } from '@tarojs/components'
import { forwardRef } from 'react'
import withClassName from '../utils/withClassName'

const TextInput = (props, ref) => {
    const { className, onChangeText, value, autoFocus, placeHolder, onSubmitEditing, ...others } = props

    const onInput = (e) => {
        onChangeText(e.detail.value)
    }

    return (
        <Input ref={ref} onInput={onInput} focus={autoFocus} className={className} type='text' placeholder={placeHolder} value={value} onConfirm={onSubmitEditing} {...others}/>
    )
}

export default withClassName()(forwardRef(TextInput));


import { Image as RNImage } from "react-native";
import { JDImage } from '@jdreact/jdreact-core-lib';
import withClassName from "../utils/withClassName";

function Image(props) {

    let { rn, src, mode, defaultSource, onError, onLoad, style } = props;

    // 图片的截取设置
    const ModeList = { 'scaleToFill': 'contain', 'aspectFill': 'cover', 'aspectFit': 'stretch' };
    mode = ModeList[mode] || 'cover';
    // const mStyle = useMemo(() => ({ resizeMode: mode }), [mode])


    // mode的对应方式
    // const styleObj = [StyleSheet.flatten(style), mStyle as any]

    if (rn) {
        // @ts-ignore
        return <RNImage resizeMode={mode} source={typeof src == 'string' && src?.indexOf?.('http') == 0 ? { uri: src } : src} style={style as any} defaultSource={defaultSource} onError={onError} onLoad={onLoad} />
    }
    return <JDImage resizeMode={mode} source={typeof src == 'string' && src?.indexOf?.('http') == 0 ? { uri: src } : src} style={style} defaultSource={defaultSource} onError={onError} onLoad={onLoad} />
}


export default withClassName()(Image)
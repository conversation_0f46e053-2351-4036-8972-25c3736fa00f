import { forwardRef } from 'react';
import { View as RNVIew, TouchableWithoutFeedback } from "react-native";
import { RNViewProps } from './type';
import withStyleForWeb from '../utils/withStyleForWeb';
const View = forwardRef((props: RNViewProps, ref) => {
    const { style = {}, onClick, onLayout, ...others } = props;

    if (!onClick) {
        return (
            // @ts-ignore
            <RNVIew ref={ref} style={style} onLayout={onLayout} {...others}>
                {props.children}
            </RNVIew>
        )
    }
    return (
        <TouchableWithoutFeedback onPress={onClick}>
            {/* @ts-ignore */}
            <RNVIew ref={ref} style={style} onLayout={onLayout} {...others}>
                {props.children}
            </RNVIew>
        </TouchableWithoutFeedback>
    )
})

export default withStyleForWeb()(View);
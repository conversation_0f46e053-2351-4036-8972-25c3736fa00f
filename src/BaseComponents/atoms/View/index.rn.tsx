import React, { forwardRef, ForwardRefRenderFunction, useMemo } from 'react';
import { View as RNVIew, TouchableWithoutFeedback, StyleSheet } from "react-native";
import { RNViewProps } from './type';
import { isObject } from '@/utils/isType';
import withClassName from '../utils/withClassName';
// import { transformRNStyle } from '../common/utils';

const View: ForwardRefRenderFunction<React.FC<RNViewProps>, any> = (props, ref) => {
    const { style = {}, onClick, onLayout, ...others } = props;
    // let styleObj = transformRNStyle(style);

    // const styles = StyleSheet.flatten([className, style as any]);
    const styles = useMemo(() => {
        return isObject(style) ? StyleSheet.create({ style }).style : style;
    }, [style]) as any
    if (!onClick) {
        return (
            <RNVIew ref={ref} style={[myStyle.defaultView, styles]} onLayout={onLayout} {...others}>
                {props.children}
            </RNVIew>
        )
    }

    return (
        <TouchableWithoutFeedback onPress={onClick} hitSlop={ { top: 5, left: 5, bottom: 5, right: 5 } }>
            <RNVIew ref={ref} style={[myStyle.defaultView, styles]} onLayout={onLayout} {...others}>
                {props.children}
            </RNVIew>
        </TouchableWithoutFeedback>
    )

}
const myStyle = StyleSheet.create({
    defaultView: {
        display: 'flex'
    }
})
export default withClassName()(forwardRef(View));
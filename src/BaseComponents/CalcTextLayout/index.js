import React, { forwardRef, memo, useCallback, useImperativeHandle, useRef, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import { useProcessActionQueue } from '@/utils/useProcessActionQueue';
import {
    JDText
} from '@jdreact/jdreact-core-lib';
import { isObject } from '@tarojs/shared';

function CalcTextLayout(props, ref) {
    const initState = { textObjs: [], textStyle: null, callback: null };
    const [state, setState] = useState(initState);
    const refLayoutList = useRef([]);
    const stRef = useRef()

    const refResolve = useRef(null);
    // const refLayoutCompeleted = useRef(false)

    const reCalc = useCallback(async (args) => {
        setState(args);
        const p = new Promise(resolve => {
            refResolve.current = resolve;
        });

        await p;
    }, []);

    const { processActionQueue } = useProcessActionQueue(reCalc);

    useImperativeHandle(ref, () => ({
        getLayoutList: async ({ texts, textStyle, callback }) => {
            if (stRef.current) {
                clearTimeout(stRef.current)
            }
            await processActionQueue({ textObjs: texts, textStyle, callback });
            stRef.current = setTimeout(() => {
                setState(initState);
            }, 10000)
        }
    }), []);

    const { textObjs, textStyle, callback } = state;

    const _onLayoutCompelete = (e, index, textObj) => {
        try {
            if (typeof textObj === 'string') {
                refLayoutList.current[index] = e.nativeEvent.layout
            } else {
                refLayoutList.current[index] = {
                    ...textObj,
                    layout: {...e.nativeEvent.layout, width: e.nativeEvent.layout?.width + pt(14)}
                }
            }
            if (refLayoutList.current.filter(x => {
                return isObject(x) || x === undefined
            }).length === textObjs.length) {
                const tmpLayoutList = refLayoutList.current;
                refLayoutList.current = [];
                refResolve.current && refResolve.current();
                callback && callback(tmpLayoutList);
            }
        } catch (error) {
            console.error('计算文本错误', error)
            refResolve.current && refResolve.current();
        }
    };

    try {
        return (
            <View style={styles.container}>
                {
                    textObjs?.map((textObj, index) => {
                        const isString = typeof textObj === 'string';
                        const isObject = typeof textObj === 'object'
                        const text = isString ? textObj : isObject ? textObj.text : ''
                        const textStyle = isString ? state.textStyle : (textObj.textStyle || textStyle);
                        return (
                            <View style={styles.txt} key={index}>
                                <JDText style={textStyle} key={`${Date.now()}_${index}`} onLayout={(e) => _onLayoutCompelete(e, index, textObj)}>{text}</JDText>
                            </View>
                        );
                    })
                }
            </View>
        );
    } catch (error) {
        refResolve.current && refResolve.current();
        return null
    }
}

export default memo(forwardRef(CalcTextLayout));

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        top: 0,
        zIndex: 9999,
        width: 99999,
        alignItems: 'flex-start',
        transform: [{ translateY: -199999 }]
    },
    txt: {
        flexShrink: 0,
        flexGrow: 0
    }
});

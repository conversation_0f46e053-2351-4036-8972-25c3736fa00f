import { View, Text, Image } from '@/BaseComponents/atoms'
import { getImageUrl } from '@/assets/imgs'
import styles from './index.module.scss'

const ImgConfig = {
    backTop: getImageUrl('backTop'), // 回到顶部
}

const BackTop = (props) => {
    const { isShow, handleClick } = props

    const onClick = () => {
        handleClick()
    }

    return (
        !!isShow &&
            <View onClick={onClick.bind(this)} className={ styles.wrapper }>
                <Image className={styles.backTopIcon} src={ImgConfig['backTop']} mode="scaleToFill"></Image>
                {/* <Text className={styles.backText}>回顶部</Text> */}
            </View>
    )
}

export default BackTop

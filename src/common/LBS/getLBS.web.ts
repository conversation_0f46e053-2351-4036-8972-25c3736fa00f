import { isEmpty } from '@/utils/isType';
import jdlbs from '@jd/lbs-sdk';
import { defaultAddress } from '../common';
import { isIOS, isAndroid, compareVersion, clientVersion, isJDApp, isHarmony, isJRApp, getLatLngInJr } from '@ltfe/ltfe-core-lib/lib/utiles'
import { reportInfo } from '../reporter';
import { errorCodeConstantMapping, ErrortType } from '../reporter/errorMapping';
import { timeOutPromise } from './common'

// web获取精准定位
const getLatLngWebCurrent = (businessId, sceneId) => {
    return new Promise((resolve, reject) => {
        jdlbs.getAddress({
            appid: businessId,
            sceneId,
            success({ data }) {
                if (
                    isEmpty(data.srclng) || data.srclng === 0 ||
                    isEmpty(data.srclat) || data.srclat === 0 ||
                    isEmpty(data.cityid) ||
                    isEmpty(data.provinceid) ||
                    isEmpty(data.districtId) ||
                    isEmpty(data.townid)
                ) {
                    console.log({msg: `getAddress获取的实时经纬度四级地址中有不存在的情况,数据: ${JSON.stringify(data)}`});
                    // 捕获异常
                    reportInfo({
                        code: errorCodeConstantMapping?.ADDRESSERROR_REALTIME_IS_NULL,
                        errorDetail: {
                            errorType: ErrortType.Info,
                            customMsg: {
                                errorDescription: `getAddress获取的实时经纬度四级地址中有不存在的情况,数据: ${JSON.stringify(data)}`
                            },
                        }
                    })
                    // 获取缓存地址
                    getLatLngWebCache(businessId, sceneId)
                        .then((res) => resolve(res))
                        .catch((err) => reject(err))
                }
                resolve({
                    longitude: data.srclng,
                    latitude: data.srclat,
                    posAreaId: `${data.provinceid},${data.cityid},${data.districtid},${data.townid}`,
                    virtualLocation: 0, // 虚拟定位 1 是 0 否
                });
            },
            fail({ msg }) {
                // 捕获异常
                reportInfo({
                    code: errorCodeConstantMapping?.ADDRESSERROR_REALTIME_FAILED,
                    errorDetail: {
                        errorType: ErrortType.Info,
                        customMsg: {
                            errorInfo: msg,
                        },
                    }
                })
                reject(msg)
            }
        });
    });
};

// web获取定位如果实时获取不到就取缓存
const getLatLng = (businessId, sceneId) => {
    return new Promise((resolve, reject) => {
        jdlbs.getAddress({
            appid: businessId,
            sceneId,
            success({ data }) {
                if (
                    isEmpty(data.srclng) || data.srclng === 0 ||
                    isEmpty(data.srclat) || data.srclat === 0 ||
                    isEmpty(data.cityid) ||
                    isEmpty(data.provinceid) ||
                    isEmpty(data.districtId) ||
                    isEmpty(data.townid)
                ) {
                    console.log({msg: `getAddress获取的实时经纬度四级地址中有不存在的情况,数据: ${JSON.stringify(data)}`});
                    // 捕获异常
                    reportInfo({
                        code: errorCodeConstantMapping?.ADDRESSERROR_REALTIME_IS_NULL,
                        errorDetail: {
                            errorType: ErrortType.Info,
                            customMsg: {
                                errorDescription: `getAddress获取的实时经纬度四级地址中有不存在的情况,数据: ${JSON.stringify(data)}`
                            },
                        }
                    })
                    // 获取缓存地址
                    getLatLngWebCache(businessId, sceneId)
                        .then((res) => resolve(res))
                        .catch((err) => reject(err))
                }
                resolve({
                    longitude: data.srclng,
                    latitude: data.srclat,
                    posAreaId: `${data.provinceid},${data.cityid},${data.districtid},${data.townid}`,
                    virtualLocation: 0, // 虚拟定位 1 是 0 否
                });
            },
            fail({ msg }) {
                 // 捕获异常
                 reportInfo({
                    code: errorCodeConstantMapping?.ADDRESSERROR_REALTIME_FAILED,
                    errorDetail: {
                        errorType: ErrortType.Info,
                        customMsg: {
                            errorInfo: msg,
                        },
                    }
                })
                // 获取缓存地址
                getLatLngWebCache(businessId, sceneId)
                    .then((res) => resolve(res))
                    .catch((err) => reject(err))
            }
        });
    });
};

// web获取缓存定位
const getLatLngWebCache = (businessId, sceneId) => {
    return new Promise((resolve, reject) => {
        // jdlbs为老版本 获取缓存用的方法是getLocation 新版本的用的getLastAddress
        jdlbs.getLocation({
            appid: businessId,
            sceneId,
            success({ data }) {
                if (
                    isEmpty(data.srclng) || data.srclng === 0 ||
                    isEmpty(data.srclat) || data.srclat === 0 ||
                    isEmpty(data.cityid) ||
                    isEmpty(data.provinceid) ||
                    isEmpty(data.districtId) ||
                    isEmpty(data.townid)
                ) {
                    // 捕获异常
                    reportInfo({
                        code: errorCodeConstantMapping?.ADDRESSERROR_IS_NULL,
                        errorDetail: {
                            errorType: ErrortType.Info,
                            customMsg: {
                                errorDescription:  `getLatLngWebCache获取的实时经纬度四级地址中有不存在的情况,数据: ${JSON.stringify(data)}`
                            },
                        }
                    })
                    reject({msg: `getLatLngWebCache获取的实时经纬度四级地址中有不存在的情况,数据: ${JSON.stringify(data)}`});
                }
                resolve({
                    longitude: data.srclng,
                    latitude: data.srclat,
                    posAreaId: `${data.provinceid},${data.cityid},${data.districtid},${data.townid}`
                });
            },
            fail({ msg }) {
                // 捕获异常
                reportInfo({
                    code: errorCodeConstantMapping?.ADDRESSERROR_CACHE_FAILED,
                    errorDetail: {
                        errorType: ErrortType.Info,
                        customMsg: {
                            errorInfo: msg
                        },
                    }
                })
                reject(msg);
            }
        });
    });
};
// web授权场景弹框
const requestLocationPermissionWithScene = (businessId, sceneId) => {
    return new Promise((resolve, reject) => {
        jdlbs.manualReqLocPermWithScene({
            appid: businessId,
            sceneId,
            sceneContent: '京东需要位置权限',
            success() {
                resolve('');
            },
            fail({ msg }) {
                reject(msg);
            }
        });
    });
};
// web授权弹框
const requestLocationPermissionWeb = (businessId) => {
    return new Promise((resolve, reject) => {
        jdlbs.requestLocationPermission({
            appid: businessId,
            tipMsg: '',
            success() {
                resolve('');
            },
            fail({ msg }) {
                reject(msg);
            }
        });
    });
};

/**
 * 获取经纬度
 *
 * @category API
 * @param {number} businessId 业务id
 * @param {number} sceneId 场景id，基本业务：basicShoppingProcess，同城业务：locService，营销活动：marketingActivities
 * @returns {object} lon: 经度，lat: 纬度
 *
 */
const _getLatLngPos = (isNeedGetPermission, businessId = '51a7a5642016df0d006663133e1ae2f5', sceneId = 'basicShoppingProcess') => {
    return new Promise((resolve, reject) => {
      try {
        if (isJRApp && compareVersion(clientVersion, '6.5.0') >= 0) {
          getLatLngInJr().then(res => {
            if (res.status === '1') { // 权限状态 '0' -用户未授权或者拒绝授权定位权限 '1' -用户已授权定位权限 '2' - 用户打开设置页；'3' -其他异常情况
              const { latitude, longitude } = res
              jdlbs.getAddress({
                appid: businessId,
                sceneId,
                lat: latitude,
                lng: longitude,
                success ({ status, param, data, msg }) {
                  resolve({
                    openLocationPermisson: true,
                    address: {
                      longitude,
                      latitude,
                      virtualLocation: 0, // 虚拟定位 1 是 0 否
                      posAreaId: `${data.provinceid},${data.cityid}`,
                    }
                  })
                },
                fail ({ status, param, data, msg }) {
                  reject({
                    openLocationPermisson: false,
                    address: defaultAddress
                  })
                }
              })
            }
          }).catch(err => {
            console.log('金融APP获取地址信息失败:', err, '方法: getLatLngInJr >> jdlbs.getAddress')
            reject({
              openLocationPermisson: false,
              address: defaultAddress
            })
          })
        } else if (isJDApp && jdlbs) { // 京东app
          if (
            isHarmony ||
              (isAndroid && (compareVersion(clientVersion, '11.3.8') === 0 || compareVersion(clientVersion, '11.6.5') >= 0)) ||
              (isIOS && (compareVersion(clientVersion, '11.8.1') >= 0))
          ) {
            jdlbs.hasLocationPermissionWithScene({
              appid: businessId,
              sceneId,
              success ({ data }) {
                if (data.result) {
                  getLatLng(businessId, sceneId)
                    .then(res => resolve({
                      openLocationPermisson: true,
                      address: res
                    }))
                    .catch(err => {
                      console.log('获取地址信息失败:', err, '方法: jdlbs.hasLocationPermissionWithScene >> 有data.result >> getLatLng')
                      reject({
                        openLocationPermisson: true,
                        address: defaultAddress
                      })
                    })
                } else {
                  getLatLngWebCache(businessId, sceneId)
                    .then(res => resolve({
                      openLocationPermisson: false,
                      address: res
                    }))
                    .catch(err => {
                      console.log('获取地址信息失败:', err, '方法: jdlbs.hasLocationPermissionWithScene >> 无data.result >> getLatLngWebCache')
                      reject({
                        openLocationPermisson: false,
                        address: defaultAddress
                      })
                    })
                }
              },
              fail () {
                if (isNeedGetPermission) {
                  requestLocationPermissionWithScene(businessId, sceneId)
                    .then(() => {
                      getLatLng(businessId, sceneId)
                        .then(res => resolve({
                          openLocationPermisson: true,
                          address: res
                        }))
                        .catch(err => {
                          console.log('获取地址信息失败:', err, '方法: jdlbs.hasLocationPermissionWithScene >> 有data.result >> getLatLng')
                          reject({
                            openLocationPermisson: true,
                            address: defaultAddress
                          })
                        })
                    })
                    .catch(err => {
                      console.log('获取地址信息失败:', err, '方法: requestLocationPermissionWithScene >> catch()')
                      getLatLngWebCache(businessId, sceneId)
                        .then(res => resolve({
                          openLocationPermisson: false,
                          address: res
                        }))
                        .catch(err => {
                          console.log('获取地址信息失败:', err, '方法: hasLocationPermissionWithScene >> catch() >> getLatLngWebCache')
                          reject({
                            openLocationPermisson: false,
                            address: defaultAddress
                          })
                        })
                    })
                }
                getLatLngWebCache(businessId, sceneId)
                  .then(res => resolve({
                    openLocationPermisson: false,
                    address: res
                  }))
                  .catch(err => {
                    console.log('获取地址信息失败:', err, '方法: jdlbs.hasLocationPermissionWithScene >> fail() >> getLatLngWebCache')
                    reject({
                      openLocationPermisson: false,
                      address: defaultAddress
                    })
                  })
              }
            })
          } else {
            requestLocationPermissionWeb(businessId)
              .then(() => {
                getLatLng(businessId, sceneId)
                  .then(res => resolve({
                    openLocationPermisson: true,
                    address: res
                  }))
                  .catch(err => {
                    console.log('获取地址信息失败:', err, '方法: requestLocationPermissionWeb >> getLatLng')
                    reject(err)
                  })
              })
              .catch(err => {
                console.log('获取地址信息失败:', err, '方法: requestLocationPermissionWeb >> catch')
                getLatLngWebCache(businessId, sceneId)
                  .then(res => resolve({
                    openLocationPermisson: false,
                    address: res
                  }))
                  .catch(err => {
                    console.log('不满足版本等信息获取地址信息失败:', err, '方法: 不满足判断条件 >> getLatLngWebCache')
                    reject({
                      openLocationPermisson: false,
                      address: defaultAddress
                    })
                  })
              })
          }
        } else {
          if (jdlbs) {
            getLatLngWebCurrent(businessId, sceneId)
              .then(res => {
                return resolve({
                  openLocationPermisson: true,
                  address: res
                })
              })
              .catch(err => {
                console.log('获取地址信息失败:', err, '方法: if(jdlbs) >> getLatLngWeb')
                getLatLngWebCache(businessId, sceneId)
                  .then(res => resolve({
                    openLocationPermisson: false,
                    address: res
                  }))
                  .catch(err => {
                    console.log('获取地址信息失败:', err, '方法: 不满足判断条件 >> getLatLngWebCache')
                    reject({
                      openLocationPermisson: false,
                      address: defaultAddress
                    })
                  })
              })
          } else {
            reject({
              openLocationPermisson: true,
              address: defaultAddress
            })
          }
        }
      } catch (e) {
        console.log('获取地址异常catch', e)
        reject({
          openLocationPermisson: true,
          address: defaultAddress
        })
      }
    })
  }
  

export const requestLocationPermission = (businessId = '51a7a5642016df0d006663133e1ae2f5', sceneId = 'basicShoppingProcess') => {
    return new Promise((resolve, reject) => {
        try {
            if (isJDApp && jdlbs) { // 京东app
                if (
                    isHarmony ||
                    (isAndroid && (compareVersion(clientVersion, '11.3.8') === 0 || compareVersion(clientVersion, '11.6.5') >= 0)) ||
                    (isIOS && (compareVersion(clientVersion, '11.8.1') >= 0))
                ) {
                     requestLocationPermissionWithScene(businessId, sceneId)
                        .then(() => {
                            getLatLng(businessId, sceneId)
                                .then(res => resolve({
                                    openLocationPermisson: true,
                                    address: res
                                }))
                                .catch(err => {
                                    console.log('获取地址信息失败:', err, '方法: requestLocationPermissionWithScene >> getLatLngWeb')
                                    reject(err)
                                });
                        })
                        .catch(err => reject(err));
                } else {
                    requestLocationPermissionWeb(businessId)
                        .then(() => {
                            getLatLng(businessId, sceneId)
                                .then(res => resolve({
                                    openLocationPermisson: true,
                                    address: res
                                }))
                                .catch(err => {
                                    console.log('获取地址信息失败:', err, '方法: requestLocationPermissionWeb >> getLatLngWeb')
                                    reject(err)
                                });
                        })
                        .catch(err => reject(err));
                }
            } else {
                if (jdlbs) {
                    getLatLngWebCurrent(businessId, sceneId)
                        .then(res => resolve({
                            openLocationPermisson: true,
                            address: res
                        }))
                        .catch(err => {
                            console.log('获取地址信息失败:', err, '方法: if(jdlbs) >> getLatLngWeb')
                            reject(err)
                        });
                } else {
                    reject({msg: '非京东app内且jdlbs不存在'})
                }
            }
        } catch (e) {
            console.log('开启定位异常catch', e)
            reject(e);
        }
    });
};


export const getLatLngPos = (isNeedGetPermission = false) => {
    if (isNeedGetPermission) {
        return _getLatLngPos(isNeedGetPermission)
    }
    return Promise.race([
      timeOutPromise(),
      _getLatLngPos(isNeedGetPermission)
    ])
  }



import { reportInfo } from '@/common/reporter'
import { errorCodeConstantMapping, ErrortType } from '@/common/reporter/errorMapping'

// 业务线编码，酒店-hotel，门票-scenic，机票-flight，火车票-train
export async function getLbsCity(apiFetch, addressInfo, businessCode) {
  const latitude = (addressInfo?.latitude)?.toString()
  const longitude = (addressInfo?.longitude)?.toString()
  const virtualLocation = addressInfo?.virtualLocation
  const [err, res] = await apiFetch('TRAVEL_GETGEOINFO', {
    latitude,
    longitude,
    businessCode,
  }, true)

  console.log('getLbsCity res',latitude,longitude, err, res)
  if (err) {
    try {
      reportInfo({
        code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_CITY_REQUEST_ERROR,
        errorDetail: {
          errorType: ErrortType.Info,
          customMsg: {
            errorInfo: err?.message,
            errorStack: err?.stack,
            errorDescription: `${JSON.stringify(err)}`,
          },
        }
      })
    } catch (error) {
      console.log(error)
    }
    return {}
  }
  const { result, code } = res || {}
  if (result && +code === 0) {
    return {
      latLng: {
        latitude,
        longitude,
      },
      virtualLocation,
      ...result
    }
  } else {
    return {}
  }
};

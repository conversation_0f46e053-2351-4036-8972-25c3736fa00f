import { JDNativeSystem, JDLBS } from '@jdreact/jdreact-core-lib';
import { compareVersion } from '@ltfe/ltfe-core-lib/lib/utiles'

const useLbsAuthorized = (businessId = '51a7a5642016df0d006663133e1ae2f5', sceneId = 'basicShoppingProcess') => {
    return new Promise((resolve) => {
        JDNativeSystem.getClientVersion()
            .then(version => {
                if (compareVersion(version, '12.4.1') >= 0) {
                    JDLBS.hasLocationPermissionWithSceneV2({ businessId, sceneId })
                        .then(() => {
                            resolve({
                                authorized: true
                            })
                        }).catch((err) => {
                            console.log(err)
                            resolve({
                                authorized: false
                            })
                        });
                } else {
                    resolve({
                        authorized: true
                    })
                }
            }, (err) => {
                console.log(err)
                resolve({
                    authorized: true
                })
            });
    });
};

export { useLbsAuthorized }
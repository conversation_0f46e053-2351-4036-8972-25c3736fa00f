import { sleeping } from '@/utils'

import { defaultAddress } from '../common'
import { reportInfo} from '../reporter'
import { errorCodeConstantMapping, ErrortType } from '../reporter/errorMapping'

export const timeOutPromise = async () => {
  await sleeping(1 * 1000)

  reportInfo({
    code: errorCodeConstantMapping?.RESPONSEERROR_ADDRESSERROR_NO_CALLBACK,
    errorDetail: {
        errorType: ErrortType.Info,
        customMsg: {
            errorDescription: '地址请求未回调'
        }
    }
})

  return Promise.reject({
    openLocationPermisson: true,
    address: defaultAddress
  })
}

import { JDNativeSystem, JDLBS } from '@jdreact/jdreact-core-lib';
// import { utils } from '@ltfe/ltfe-core-lib'
import { isIOS, compareVersion } from '@ltfe/ltfe-core-lib/lib/utiles'
import { getAddress } from './getLatLngPos';

// const {compareVersion} = utils

export const requestLocationPermission = (businessId = '51a7a5642016df0d006663133e1ae2f5', sceneId = 'basicShoppingProcess') => {
    // console.log('请求授权定位')
    return new Promise((resolve, reject) => {
        JDNativeSystem.getClientVersion()
            .then(version => {
                // console.log('获取版本信息', version)
                if (compareVersion(version, '12.4.1') >= 0) {
                    JDLBS.manualRequestLocationPermissionWithScene({ businessId, sceneId, sceneContent: '京东需要位置权限' })
                        .then(() => {
                            getAddress(businessId, sceneId)
                                .then(res => resolve({
                                    openLocationPermisson: true,
                                    address: res
                                }))
                                .catch(err => {
                                    console.log('开启定位成功获取地址失败', err)
                                    reject(err)
                                });
                        }).catch((error) => {
                            console.log('开启定位失败manualRequestLocationPermissionWithScene', error)
                            reject(error);
                        });
                } else {
                    console.log('异常获取地址信息失败版本小于12.4.1')
                    reject({msg: '异常获取地址信息失败版本小于12.4.1'})
                }
            }, (e) => {
                console.log('授权定位获取版本错误', e)
                reject({showToast: true, error: e});
            });
    });
};

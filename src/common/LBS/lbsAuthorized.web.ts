import jdlbs from '@jd/lbs-sdk'
import { clientVersion, compareVersion, isAndroid, isHarmony, isIOS } from '@ltfe/ltfe-core-lib/lib/utiles'

const useLbsAuthorized = (businessId = '51a7a5642016df0d006663133e1ae2f5', sceneId = 'basicShoppingProcess') => {
    return new Promise((resolve) => {
        if (isHarmony ||
            (isAndroid && (compareVersion(clientVersion, '11.3.8') === 0 ||
                compareVersion(clientVersion, '11.6.5') >= 0)) ||
            (isIOS && (compareVersion(clientVersion, '11.8.1') >= 0))
        ) {
            jdlbs.hasLocationPermissionWithScene({
                appid: businessId,
                sceneId,
                success() {
                    resolve({
                        authorized: true
                    })
                },
                fail() {
                    resolve({
                        authorized: false
                    })
                }
            });
        } else {
            resolve({
                authorized: true
            })
        }

    });
};

export { useLbsAuthorized }
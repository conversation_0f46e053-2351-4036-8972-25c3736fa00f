import { isEmpty } from '@/utils/isType';
import { JDNativeSystem, JDLBS, JDAddress } from '@jdreact/jdreact-core-lib';
// import { utils } from '@ltfe/ltfe-core-lib'
import { isIOS, compareVersion } from '@ltfe/ltfe-core-lib/lib/utiles'
import { defaultAddress } from '../common';
import { reportInfo } from '../reporter';
import { errorCodeConstantMapping, ErrortType } from '../reporter/errorMapping';
import { timeOutPromise } from './common'

// 获取实时定位 如果实时定位获取失败 获取缓存地址
export const getAddress = (businessId, sceneId) => {
    return new Promise((resolve, reject) => {
        JDLBS.getAddress({ businessId, sceneId }, location => {
            console.log('实时定位地址返回的数据结构:', location)
            if (
                (isEmpty(location.longitude) && isEmpty(location.longi)) ||
                (location.longitude === 0 && location.longi === 0) ||
                (isEmpty(location.latitude) && isEmpty(location.lati)) ||
                (location.latitude === 0 && location.lati === 0) ||
                isEmpty(location.cityId) ||
                isEmpty(location.districtId) ||
                isEmpty(location.provinceId) ||
                isEmpty(location.townId)
            ) {
                console.log({ msg: `getAddress获取的实时经纬度四级地址中有不存在的情况,数据: ${JSON.stringify(location)}` });
                // 捕获异常
                reportInfo({
                    code: errorCodeConstantMapping?.ADDRESSERROR_REALTIME_IS_NULL,
                    errorDetail: {
                        errorType: ErrortType.Info,
                        customMsg: {
                            errorDescription: `getAddress获取的实时经纬度四级地址中有不存在的情况,数据: ${JSON.stringify(location)}`
                        },
                    }
                })
                getCacheAddress(sceneId)
                    .then((res) => resolve(res))
                    .catch((err) => reject(err))
            }
            resolve({
                longitude: isIOS ? location.longitude : location.longi,
                latitude: isIOS ? location.latitude : location.lati,
                posAreaId: `${location.provinceId},${location.cityId},${location.districtId},${location.townId}`,
                virtualLocation: 0, // 虚拟定位 1 是 0 否
            });
        }, (e) => {
            console.log('获取实时定位地址失败:', e)
            // 捕获异常
            reportInfo({
                code: errorCodeConstantMapping?.ADDRESSERROR_REALTIME_FAILED,
                errorDetail: {
                    errorType: ErrortType.Info,
                    customMsg: {
                        errorInfo: e?.message,
                        errorStack: e?.stack,
                    },
                }
            })
            // 获取缓存地址
            getCacheAddress(sceneId)
                .then((res) => resolve(res))
                .catch((err) => reject(err))
        }, null);
    });
};

// 获取缓存地址
const getCacheAddress = (sceneId) => {
    return new Promise((resolve, reject) => {
        JDAddress.getAddress(sceneId).then((location) => {
            console.log('缓存地址返回的数据结构:', location)
            if (
                (isEmpty(location.longitude) && isEmpty(location.longi)) ||
                (location.longitude === 0 && location.longi === 0) ||
                (isEmpty(location.latitude) && isEmpty(location.lati)) ||
                (location.latitude === 0 && location.lati === 0) ||
                isEmpty(location.cityId) ||
                (isEmpty(location.areaId) && isEmpty(location.countyId)) ||
                isEmpty(location.provinceId) ||
                isEmpty(location.townId)
            ) {
                // 捕获异常
                reportInfo({
                    code: errorCodeConstantMapping?.ADDRESSERROR_IS_NULL,
                    errorDetail: {
                        errorType: ErrortType.Info,
                        customMsg: {
                            errorDescription: `获取的缓存经纬度四级地址中有不存在的情况,数据: ${JSON.stringify(location)}`
                        },
                    }
                })
                reject({ msg: `获取的缓存经纬度四级地址中有不存在的情况,数据: ${JSON.stringify(location)}` });
            }
            // 兜底地址
            const isDouDi = ((location.latitude == defaultAddress.latitude || location.lati == defaultAddress.latitude) && (location.longitude == defaultAddress.longitude || location.longi == defaultAddress.longitude)) ? 1 : 0

            resolve({
                longitude: location.longi || location.longitude,
                latitude: location.lati || location.latitude,
                posAreaId: `${location.provinceId},${location.cityId},${location.areaId || location.countyId},${location.townId}`,
                virtualLocation: isDouDi, // 虚拟定位 1 是 0 否
            })
        }).catch((e) => {
            console.log('获取缓存地址失败:', e)
            // 捕获异常
            reportInfo({
                code: errorCodeConstantMapping?.ADDRESSERROR_CACHE_FAILED,
                errorDetail: {
                    errorType: ErrortType.Info,
                    customMsg: {
                        errorInfo: e?.message,
                        errorStack: e?.stack,
                    },
                }
            })
            reject(e)
        })
    })
}

export const _getLatLngPos = (isNeedGetPermission, businessId = '51a7a5642016df0d006663133e1ae2f5', sceneId = 'basicShoppingProcess') => {
    return new Promise((resolve, reject) => {
        JDNativeSystem.getClientVersion()
            .then(version => {
                if (compareVersion(version, '12.4.1') >= 0) {
                    JDLBS.hasLocationPermissionWithSceneV2({ businessId, sceneId })
                        .then(() => {
                            getAddress(businessId, sceneId)
                                .then(res => resolve({
                                    openLocationPermisson: true,
                                    address: res
                                }))
                                .catch(err => {
                                    // console.log('获取地址信息失败:', err, '方法: getAddress')
                                    reject({
                                        openLocationPermisson: true,
                                        address: defaultAddress
                                    })
                                });
                        }).catch((err) => {
                            // console.log('获取定位权限失败:', err, '方法: hasLocationPermissionWithSceneV2')
                            if (isNeedGetPermission) {
                                JDLBS.requestLocationPermissionWithScene({ businessId, sceneId, sceneContent: '京东需要位置权限' })
                                    .then(() => {
                                        getAddress(businessId, sceneId)
                                            .then(res => resolve({
                                                openLocationPermisson: true,
                                                address: res
                                            }))
                                            .catch(err => {
                                                console.log('获取地址信息失败:', err, '方法: getAddress')
                                                reject({
                                                    openLocationPermisson: true,
                                                    address: defaultAddress
                                                })
                                            })
                                    }).catch((error) => {
                                        getCacheAddress(sceneId)
                                            .then(res => resolve({
                                                openLocationPermisson: false,
                                                address: res
                                            }))
                                            .catch(err => {
                                                console.log('获取地址信息失败:', err, '方法: getCacheAddress')
                                                reject({
                                                    openLocationPermisson: false,
                                                    address: defaultAddress
                                                })
                                            })
                                    })
                            } else {
                                getCacheAddress(sceneId)
                                    .then(res => resolve({
                                        openLocationPermisson: false,
                                        address: res
                                    }))
                                    .catch(err => {
                                        console.log('获取地址信息失败:', err, '方法: getCacheAddress')
                                        reject({
                                            openLocationPermisson: false,
                                            address: defaultAddress
                                        })
                                    });
                            }
                        });
                } else {
                    console.log('异常获取地址信息失败版本小于12.4.1')
                    reject({
                        openLocationPermisson: true,
                        address: defaultAddress
                    })
                }
            }, (e) => {
                console.log('获取APP版本失败', e, '方法: getClientVersion')
                reject({
                    openLocationPermisson: true,
                    address: defaultAddress
                })
            });
    });
};


export const getLatLngPos = (isNeedGetPermission = false) => {
    if (isNeedGetPermission) {
        return _getLatLngPos(isNeedGetPermission)
    }
    return Promise.race([
         timeOutPromise(),
        _getLatLngPos(isNeedGetPermission)
    ])
}
import { useCallback, useEffect, useRef } from "react"
import { isAndroid, isIOS, isWeb } from "./common"
import { AppState, NativeAppEventEmitter } from "react-native"
import { isFunction } from "@/utils/isType"


export function useListenBackFromOthers(callback, toBackground?) {
    const refCurrentState = useRef<any>(AppState.currentState)

    const handleAppStateChange = useCallback((nextAppState) => {
        // alert('nextAppState' + isIOS + ',' + isAndroid + nextAppState + JSON.stringify(refCurrentState.current))
        if ((isWeb && document.visibilityState === 'visible') ||
            (!isWeb && isAndroid && refCurrentState.current.match(/inactive|background/) && nextAppState === 'active') ||
            (!isWeb && isIOS)
        ) {
            // console.log('执行handleAppStateChange')
            // alert('执行handleAppStateChange')
            isFunction(callback) && callback();
        } else if (!isWeb && isAndroid && nextAppState.match(/inactive/)) {
            // Android压后台
            isFunction(toBackground) && toBackground();
        }
    }, [])


    useEffect(() => {
        let subscription
        if (isWeb) {
            document.addEventListener('visibilitychange', handleAppStateChange, false);
        } else if (isIOS) {
            subscription = NativeAppEventEmitter.addListener('JDReactNativeRefresh', handleAppStateChange);
        }
        // else if (isAndroid) {
        //     subscription = AppState.addEventListener('change', handleAppStateChange);
        // }

        return () => {
            if (isWeb) {
                document.removeEventListener('visibilitychange', handleAppStateChange);
            } else if (isIOS) {
                subscription.remove?.();
            }
            // else if (isAndroid) {
            //     subscription.remove?.();
            // }
        }
    })
}
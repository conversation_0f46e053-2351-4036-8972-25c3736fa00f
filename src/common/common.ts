import Taro, { getEnv } from "@tarojs/taro";
import mitt from "mitt";
import { Observable } from "zen-observable-ts";
import { isIOS as _isIOS, isAndroid as _isAndroid } from '@ltfe/ltfe-core-lib/lib/utiles'


export const env = getEnv();
export const isRn = env === 'RN';
export const isWeb = env === 'WEB';

export const isIOS = _isIOS;
export const isAndroid = _isAndroid;

/**
 * 获取当前页面对象
 * @returns 当前页面对象
 */
export function getCurrentPageInstance() {
    const currentPage = Taro.getCurrentInstance();
    return currentPage
}

/**
 * 获取当前页面栈
 * @returns 当前页面栈
 */
export function getCurrentPagesStack() {
    return Taro.getCurrentPages()
}


type T_Payload = {
    action: string,
    payload: any
}


/**
 * 获取一个观察者对象
 * @returns 观察者对象，包含observable和observer
 */
export function getWatcher() {
    const watcher = {} as {
        observable: Observable<T_Payload>,
        observer?: ZenObservable.SubscriptionObserver<T_Payload>
    }
    watcher.observable = new Observable<T_Payload>(observer => {
        watcher.observer = observer;
    });

    return watcher;
}


// 兜底地址：朝阳公园
export const defaultAddress = {
    latitude: "39.944093",
    longitude: "116.482276",
    posAreaId: "1,72,55674,0",
    virtualLocation: 1, // 虚拟定位 1 是 0 否
}

/** 事假总线 */
export const EventBus = mitt();


// 超时
export const isNetworkTimeOut = (code) => {
    if (code == '-9999' || code == "ENSURLERRORDOMAIN-1009" || code == "ENSURLERRORDOMAIN-1005" || code == "ENSURLERRORDOMAIN-1001") {
        return true
    }
    return false
}
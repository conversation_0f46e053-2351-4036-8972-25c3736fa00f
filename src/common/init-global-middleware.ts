
export default (param = {}) => {
    return (props) => {
        // 设置默认值
        const {
            _fromType = 'HOMEicon',
            _sourceType,
            _isWXMP,
            env = 'production'
        } = props;

        // 来源_渠道（HOMEicon: 首页icon，Search: 搜索直达）
        global.fromType = _fromType;

        // 是否RN/H5（1：是；0：否)
        global.sourceType = [0, 1].includes(+_sourceType) ? +_sourceType : 1;

        // 是否小程序环境（1：是；0：否）
        global.isWXMP = [0, 1].includes(+_isWXMP) ? +_isWXMP : 0;

        // 环境，yufa：预发，test：测试，production：生产
        global.env = ['yufa', 'test', 'production'].includes(env) ? env : 'production';

        return {
            ...param,
            ...props
        };
    };
};

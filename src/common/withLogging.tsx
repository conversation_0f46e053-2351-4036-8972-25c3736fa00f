import { reportInfo } from "./reporter";
import { ErrortType } from "./reporter/errorMapping";


function logBefore(...args) {
    reportInfo({
        code: 20003,
        errorDetail: {
            errorType: ErrortType.Info,
            customMsg: args
        }
    })
}

/**
 * 给原始函数添加日志功能
 * @param originalFunction 原始函数
 * @returns 带有日志功能的函数
 */
export function withLogging(originalFunction) {
    return function (...args) {
        console.log(`Calling ${originalFunction.name} with arguments:`, args);
        logBefore(...args)
        const result = originalFunction(...args);
        console.log(`Result of ${originalFunction.name}:`, result);
        return result;
    };
}


/**
 * 异步记录函数调用和结果的高阶函数。
 * @param originalFunction 要包装的原始函数
 * @returns 包装后的异步函数
 */
export function withAsyncLogging(originalFunction) {
    return async function (...args) {
        console.log(`Calling ${originalFunction.name} with arguments:`, args);
        const result = await originalFunction(...args);
        console.log(`Result of ${originalFunction.name}:`, result);
        return result;
    };
}
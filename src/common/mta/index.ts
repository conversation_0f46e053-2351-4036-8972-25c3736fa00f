import addressModel from '@/store/model/address.model'
import globalInfoModel from '@/store/model/globalInfo.model'
import {isObject} from '@/utils/isType'
import {
    mta as _mta,
    mtaExposure as _mtaExposure,
    newMta as _newMta,
    mtaPv as _mtaPv,
    mtaEp as _mtaEp, // 使用曝光方法进行H5曝光处理
} from '@ltfe/ltfe-core-lib/lib/utiles'

// pageId 集合
export enum M_PAGEID {
    TravelSearch = 'TravelSearch',
    TravelSearcResult = 'TravelSearcResult',
    BusinessSearch = "HotelTravel_Search",
    HotelSearch = 'HotelRN_List',
    Channel = 'Itravel_Subsidy',
}

// 筛选面板映射集合
export enum M_PANEL_NAME {
    main_filter = '筛选',
    scenic_category = '景点类型',
    scenic_city = '所在地',
    scenic_filter = '筛选',
    location_distance = "位置距离",
    price_star = "价格星级",
    hotel_filter = "筛选"
}

// 升序降序映射
export enum ORDER_TYPE {
    desc = 1,
    asc = 2
}

// 事件id
export enum M_EVENTID {
    TravelSearchClear = 'TravelSearch_Clear', // 删除历史搜索
    TravelSearchHotWordExpo = 'TravelSearch_HotWordExpo', // 搜索热词曝光
    TravelSearchHotWord = 'TravelSearch_HotWord', // 搜索热门词
    TravelSearchHistoryWordExpo = 'TravelSearch_HistoryWordExpo', // 搜索历史曝光
    TravelSearchHistoryWord = 'TravelSearch_HistoryWord', // 搜索历史点击
    TravelSearchSearchIcon = 'TravelSearch_SearchIcon', // 搜索按钮
    TravelSearchSingleDelete = 'TravelSearch_SingleDelete', // 单个删除
    TravelSearchLocationExpo = 'TravelSearch_LocationExpo', // 开启定位曝光
    TravelSearchLocation = 'TravelSearch_Location', // 开启定位
    TravelSearchAutoWordExpo = 'TravelSearch_AutoWordExpo', // sug曝光
    TravelSearchAutoWord = 'TravelSearch_AutoWord', // sug点击
    TravelSearcResultHotel = 'TravelSearcResult_Hotel', // 酒店tab酒店点击
    TravelSearcResultDiscountLayerOrder = 'TravelSearcResult_DiscountLayer_Order', // 酒店优惠跳转点击
    TravelSearcResult_RecDiscountLayer_Order = 'TravelSearcResult_RecDiscountLayer_Order', // 酒店优惠跳转点击
    TravelSearcResultHotelExpo = 'TravelSearcResult_HotelExpo', // 酒店tab酒店曝光
    TravelSearcResultScenic = 'TravelSearcResult_Scenic', // 景点tab景点点击
    TravelSearcResultScenicExpo = 'TravelSearcResult_ScenicExpo', // 景点tab景点曝光
    TravelSearcResultTrafficExpo = 'TravelSearcResult_TrafficExpo', // 交通方式曝光
    TravelSearcResultTraffic = 'TravelSearcResult_Traffic', // 交通方式点击
    TravelSearcResultRecScenicExpo = 'TravelSearcResult_RecScenicExpo', // 推荐景点曝光
    TravelSearcResultRecScenic = 'TravelSearcResult_RecScenic', // 推荐景点点击
    TravelSearcResultRecHotelExpo = 'TravelSearcResult_RecHotelExpo', // 推荐酒店曝光
    TravelSearcResultRecHotel = 'TravelSearcResult_RecHotel', // 推荐酒店点击
    TravelSearcResultOrder = 'TravelSearcResult_Order', //排序点击
    TravelSearcResultFilter = 'TravelSearcResult_Filter', //筛选项点击
    TravelSearcResultFilterClear = 'TravelSearcResult_FilterClear', //筛选清空
    TravelSearcResultFilterConfirm = 'TravelSearcResult_FilterConfirm', //筛选确认
    TravelSearcResultOrderEntrance = 'TravelSearcResult_OrderEntrance', //排序点击入口
    TravelSearcResultOrderExpo = "TravelSearcResult_OrderExpo", // 筛选项曝光
    TravelSearcResultDiscount = "TravelSearcResult_Discount", // 酒店优惠点击
    TravelSearcResult_RecDiscount = "TravelSearcResult_RecDiscount", // 推荐酒店优惠点击
    HotelRNListRecHotelPic = 'HotelRN_List_RecHotelPic', // 垂搜推荐酒店图片点击
    HotelRNListRecHotel = 'HotelRN_List_RecHotel', // 垂搜推荐卡片点击
    HotelRNListHotelPic = 'HotelRN_List_HotelPic', // 垂搜卡片图片点击
    HotelRNListHotel = 'HotelRN_List_Hotel', // 垂搜卡片点击
    HotelRN_List_RecHotelExpo = 'HotelRN_List_RecHotelExpo', // 垂搜推荐卡片曝光
    HotelRN_List_HotelMesExpo = 'HotelRN_List_HotelMesExpo', // 垂搜卡片曝光
    HotelRN_List_RecHotelPicExpo = 'HotelRN_List_RecHotelPicExpo', // 垂搜推荐卡片图片曝光
    HotelRN_List_HotelPicExpo = 'HotelRN_List_HotelPicExpo', // 垂搜卡片图片曝光
    TravelSearchRankExpo = "TravelSearch_RankExpo", // 榜单曝光
    TravelSearchRank = "TravelSearch_Rank", // 榜单点击
    

    TravelSearcResult_ResultExpo = 'TravelSearcResult_ResultExpo',
    /**
     * 搜索结果页Tab曝光
     */
    TravelSearcResult_TabExpo = 'TravelSearcResult_TabExpo',
    /**
     * 搜索结果页Tab点击
     */
    TravelSearcResult_Tab = 'TravelSearcResult_Tab',
    /**
     * 酒店快筛曝光
     */
    TravelSearcResult_QuickFilterExpo = 'TravelSearcResult_QuickFilterExpo',
    /**
     * 酒店快筛栏点击
     */
    TravelSearcResult_QickFilter = 'TravelSearcResult_QickFilter',
    /**
     * 扩大搜索范围曝光
     */
    TravelSearcResult_TipExpo = 'TravelSearcResult_TipExpo',
    /**
     * 扩大搜索范围点击
     */
    TravelSearcResult_Tip = 'TravelSearcResult_Tip',
    /**
     * 日历、房间点击
     */
    TravelSearcResult_RoomEntrance = 'TravelSearcResult_RoomEntrance',
    /**
     * 酒店相关城市点击
     */
    TravelSearcResult_City = 'TravelSearcResult_City',
    /**
     * 更多酒店相关城市
     */
    TravelSearcResult_CityMore = 'TravelSearcResult_CityMore',
    /**
     * 酒店相关城市
     */
    TravelSearcResult_RecCity = 'TravelSearcResult_RecCity',
    /**
     * 酒店-用户基础信息确认
     */
    TravelSearcResult_RoomConfirm = 'TravelSearcResult_RoomConfirm',
    /**
     * 推荐筛选项曝光
     */
    TravelSearcResult_RecFilterExpo = 'TravelSearcResult_RecFilterExpo',
    /**
     * 推荐筛选项点击
     */
    TravelSearcResult_RecFilter = 'TravelSearcResult_RecFilter',


    // 垂搜结果页
    HotelRNListSearchExpo= "HotelRN_List_SearchExpo", // 垂搜结果页顶部输入框关键词曝光

    // 差旅搜索中奖页
    HotelTravelSearchClear = "HotelTravel_Search_Clear", // 差旅搜索中间页，删除历史搜索
    HotelTravelSearchSearch = "HotelTravel_Search_Search", // 差旅搜索中间页，搜索按钮
    HotelTravelSearchHistory = "HotelTravel_Search_History", // 差旅搜索中间页，搜索历史点击
    HotelTravelSearchHistoryExpo = "HotelTravel_Search_HistoryExpo", // 差旅搜索中间页，搜索历史曝光
    HotelTravelSearchFilter = "HotelTravel_Search_Filter", // 差旅搜索中间页，筛选项点击
    HotelTravelSearchFilterExpo = "HotelTravel_Search_FilterExpo", // 差旅搜索中间页，筛选项曝光
    HotelTravelSearchAutoWordExpo = "HotelTravel_Search_AutoWordExpo", // 差旅搜索中间页，sug曝光
    HotelTravelSearchAutoWord = "HotelTravel_Search_AutoWord", // 差旅搜索中间页，sug点击

}

// 页面名称
export enum M_PAGE {
    SearchIndex = 'SearchIndex', // 搜索中间页面
    SearchSug = 'SearchSug', // 搜索sug
    SearchResult = 'SearchResult', // 搜索结果页面
    BusinessSearch = 'BusinessSearch', // 差旅搜素中间页
    HotelSearch = 'HotelSearch', // 垂搜结果页面
    Channel = 'Channel', // 百亿补贴页面
}

export const ModuleName = 'JDReactLifeTravelSearch'

export const MTA_NONE = '-100'
export const MTA_NONE_NUMBER = -100


// 点击埋点
export const newMta = async (eventId, pageId, page = '', jsonParam = {}, ...args) => {
    try {
        const params = await handleParams(jsonParam)
        _newMta(eventId, pageId, page, params, ...args)
    } catch (error) {
        console.log('点击埋点上报失败, eventId', eventId, 'error:', error)
    }
}

// 曝光埋点
export const mtaExposure = async (eventId, pageId, page, jsonParam = {}, ...args) => {
    try {
        const params = await handleParams(jsonParam)

        _mtaExposure(eventId, pageId, page, params, ...args)
    } catch (error) {
        console.log('曝光埋点上报失败, eventId', eventId, 'error:', error)
    }
}

// 曝光埋点  使用曝光方法进行H5曝光处理
export const mtaEp = async (eventId, pageId, page, jsonParam = {}, ...args) => {
    try {
        const params = await handleParams(jsonParam)

        _mtaEp(eventId, pageId, page, params, ...args)
    } catch (error) {
        console.log('曝光埋点上报失败, eventId', eventId, 'error:', error)
    }
}

// pv
export const mtaPv = async (pageId, page, pageParam = {}) => {
    try {
        const params = await handleParams(pageParam)

        _mtaPv(pageId, page, params)

    } catch (error) {
        console.log('pv上报失败, pageId', pageId, 'error:', error)
    }
}

// 处理参数
const handleParams = async (params) => {
    let addressInfo
    try {
        addressInfo = await addressModel.getAddress
    } catch (error) {
        addressInfo = error
    }
    const staticParams = globalInfoModel.staticParams
    if (!isObject(params)) {
        return
    }

    const { longitude, latitude, posAreaId} = addressInfo.address || {}
    const result = Object.assign({}, {
        o2o_coordinates: `${longitude},${latitude}` || MTA_NONE,
        fouraddrid: posAreaId || MTA_NONE,
        // fix: channel取值兼容, 传参问题修复
        channel: staticParams?.channel || staticParams?.channelId || MTA_NONE
    }, params)

    return result
}

import { trimTime } from '@/utils';
import { isEmpty, isNumber, isObject, isString } from '@/utils/isType';
import { store, getTimeZone } from '@ltfe/ltfe-core-lib/lib/utiles';
import dayjs from 'dayjs';
import { addSharedData, querySharedDataByName } from './JDShareDataUtil';
import { JDShareData } from '@jdreact/jdreact-core-lib';
import { isWeb } from './common';
import { getJsonObjectOrRawValue } from '@/utils/Json';
import { reportInfo } from '@/common/reporter';
import { errorCodeConstantMapping, ErrortType } from '@/common/reporter/errorMapping';


/**
 * 异步初始化基本信息（本地缓存的人数，房间数，住离时间等信息）
 * @param dateNow 当前日期
 * @returns 返回合并后的基本信息对象
 */
export async function getBaseInfo(options?: { needFixDate?: boolean }) {

    // 加载酒店房间数量、入住日期和退房日期
    let baseInfo;
    let checkInDate;
    let checkOutDate;
    // console.log('[ isAndroid ] >', isAndroid)
    // console.log('[isIOS] >', isIOS)
    if (!isWeb) {
        baseInfo = await JDShareData.querySharedDataByName('hotelRoomNum').catch(console.log);
        checkInDate = await JDShareData.querySharedDataByName('hotelCheckInDate').catch(console.log);
        checkOutDate = await JDShareData.querySharedDataByName('hotelCheckOutDate').catch(console.log);
        // try {
        //     reportInfo({
        //         code: errorCodeConstantMapping?.PAGE_RESULT_SEARCH_HOTEL_JDSHARE_PARAMS,
        //         errorDetail: {
        //             errorType: ErrortType.Info,
        //             customMsg: {
        //                 errorDescription: `大搜结果页面酒店获取JDShare: baseInfo: ${JSON.stringify(baseInfo)}, checkInDate: ${JSON.stringify(checkInDate)}, checkOutDate: ${JSON.stringify(checkOutDate)} `
        //             },
        //         }
        //     })
        // } catch (error) {

        // }
        addSharedData({ hotelRoomNum: isObject(baseInfo) ? JSON.stringify(baseInfo) : baseInfo }).catch(console.log);
        addSharedData({ hotelCheckInDate: checkInDate }).catch(console.log);
        addSharedData({ hotelCheckOutDate: checkOutDate }).catch(console.log);

        baseInfo = isString(baseInfo) ? getJsonObjectOrRawValue(baseInfo) : baseInfo;

    }
    // console.log('[checkInDate  ] >', checkInDate, checkOutDate)
    if (isEmpty(checkInDate) || isEmpty(checkOutDate)) {
        baseInfo = await store.asyncLoad('hotelRoomNum').catch(console.error);
        checkInDate = await store.asyncLoad('hotelCheckInDate').catch(console.error);
        checkOutDate = await store.asyncLoad('hotelCheckOutDate').catch(console.error);
    }

    if (isEmpty(baseInfo)) {
        baseInfo = await store.asyncLoad('hotelRoomNum').catch(console.error);
    }


    if (checkInDate === "NaN-NaN-NaN" || isNumber(checkInDate)) {
        checkInDate = undefined
    }
    if (checkOutDate === "NaN-NaN-NaN" || isNumber(checkOutDate)) {
        checkOutDate = undefined
    }

    // 打印基本信息
    // console.log("baseInfo", baseInfo);

    // 构建基本信息对象
    const info = {
        ...baseInfo,
        checkInDate: checkInDate || getTimeZone().format('YYYY-MM-DD'),
        checkOutDate: checkOutDate || getTimeZone().add(1, 'days').format('YYYY-MM-DD'),
    };

    if (options?.needFixDate === false && !isEmpty(checkInDate) && !isEmpty(checkOutDate)) {
        return info;
    }
    // try {
    //     reportInfo({
    //         code: errorCodeConstantMapping?.PAGE_RESULT_SEARCH_HOTEL_HANDLE_PARAMS,
    //         errorDetail: {
    //             errorType: ErrortType.Info,
    //             customMsg: {
    //                 errorDescription: `大搜结果页面酒店获取JDShare后处理好的入离店等信息: ${JSON.stringify(info)}`
    //             },
    //         }
    //     })
    // } catch (error) {

    // }
    // 修正入住和退房日期
    // const { fromDate, arriveDate } = await fixCheckDate(info, dateNow);
    //
    // const newCheckInDate = getDate(0, 'YYYY-MM-DD', dayjs(fromDate).toDate());
    // const newCheckOutDate = getDate(0, 'YYYY-MM-DD', dayjs(arriveDate).toDate());
    //
    // store.asyncSave('hotelCheckInDate', newCheckInDate).catch(console.error)
    // store.asyncSave('hotelCheckOutDate', newCheckOutDate).catch(console.error)

    // 返回更新后的基本信息对象
    return info
}

async function fixCheckDate(baseInfo, dateNow: Date) {
    const { checkInDate, checkOutDate } = baseInfo; // 解构基本信息中的入住日期和退房日期
    let fromDate; // 定义起始日期变量
    let arriveDate; // 定义到达日期变量
    const today = trimTime(dateNow); // 获取当天日期
    // console.log('today', today)

    const checkInDateTime = dayjs(checkInDate).toDate().getTime(); // 将入住日期转换为时间戳
    const checkOutDateTime = dayjs(checkOutDate).toDate().getTime(); // 将退房日期转换为时间戳

    if (checkInDateTime < today) {
        fromDate = today; // 如果起始日期早于今天，则修正为今天
    }

    if (fromDate >= checkOutDateTime || checkInDateTime >= checkOutDateTime) {
        arriveDate = (fromDate || checkInDateTime) + 1 * 864e5; // 如果起始日期晚于到达日期，则修正到达日期为起始日期加一天
    }

    return {
        fromDate: fromDate || checkInDateTime, // 返回修正后的起始日期
        arriveDate: arriveDate || checkOutDateTime // 返回修正后的到达日期
    }
}

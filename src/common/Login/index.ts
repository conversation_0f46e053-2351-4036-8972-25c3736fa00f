
import { JDLogin } from '@jdreact/jdreact-core-lib';

/**
 * 判断用户是否已登录
 * @returns 返回一个布尔值，表示用户是否已登录
 */
export default async function hasLogin() {

    try {
        await JDLogin.isLogin()
        return true;
    } catch (error) {
        return false
    }
}

/**
 * 异步执行登录操作
 * @returns 登录成功返回true，失败返回false
 */
export async function doLogin() {
    try {
        await JDLogin.doLogin();
        return true;
    } catch (error) {
        return false
    }
} 
import { reportNLog, errorCode } from '@ltfe/ltfe-core-lib/lib/utiles';


// 如果上报的code在公共集合中没有，可以自定义errorCode进行上报
const customErrorCodes = {
    1110001: {
        errorMsg: '缓存经纬度或四级地址为null',
        crashReason: 'addressError'
    }
};

const errorMapping = Object.assign({}, customErrorCodes, errorCode);

export const reportError = (pageName, code, errorDetail) => {

    const errorMap = errorMapping[code];

    const crashReason = errorMap.crashReason + code + ':' + errorMap.errorMsg;

    const crashInfo = {
        'code': code,
        'pageName': pageName,
        'pageArgs': {},
        'errorMsg': errorMap.errorMsg,
        'errorDetail': errorDetail,
        "buildTime": process.env.BUILD_TIME,
    };

    // moduleName需要业务方自己配置
    reportNLog({ moduleName: 'JDReactLifeTravelSearch', errorDetail: { crashInfo, crashReason } });
};
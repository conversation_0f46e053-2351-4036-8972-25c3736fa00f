import { ErrorBoundary } from'@ltfe/ltfe-core-lib';
import { getUuid } from "@/utils";
import { getCurrentPages, useDidShow } from "@tarojs/taro";
import { forwardRef, useCallback, useEffect, useRef } from "react";
import { DegradePage } from "@/BusinessComponents/DegradePage";
import { createContext } from "react";
import { env, getCurrentPageInstance, isWeb } from "./common";
import { isFunction, isObject } from "@/utils/isType";
import { MTA_NONE } from "./mta";
import { useListenBackFromOthers } from "./useAppState";

type BasePageInfo = {
    pvId: string;
    debugger?: string;
}
type T_BASE_PAGE_CONTEXT = {
    uniquePageId: string
    getBackParams: () => any
    setBackParams: (args: any) => void
    clearBackParams: () => void
    registerDidShow: (fn: () => void) => void;
    offDidShow: (fn: () => void) => void;
    basePageInfo: BasePageInfo,
    getPageParams: () => any
    getCommonMtaParams: () => any
}
export const BasePageContext = createContext<T_BASE_PAGE_CONTEXT>({} as T_BASE_PAGE_CONTEXT);

const GLOBAL_BACK_PARAMS = {}
export const setBackParams = (uniquePageId: string) => {
    return GLOBAL_BACK_PARAMS[uniquePageId]
}


const hiddenPageClassList = ['taro_page', 'taro_page_show', 'taro_page_stationed', 'taro_page_shade']
function resetPageClassNames() {
    const pageNodeList = document.querySelectorAll('.taro_page') || []
    if (pageNodeList.length === 0) {
        setTimeout(() => {
            resetPageClassNames()
        }, 40);
        return;
    }
    // console.log('pageNodeList-Len' + pageNodeList.length)
    // console.log('找到了 __taroRouterChange  2222', pageNodeList)
    pageNodeList.forEach((el, index) => {
        // const page = getCurrentInstance();
        const pages = getCurrentPages()
        const page = pages[pages.length - 1]
        // console.log('__taroRouterChange el.id---1', el.id, getComputedStyle(el).display, el.classList)
        if (!el.id.startsWith(page.route!) || index !== pageNodeList.length - 1) {
            hiddenPageClassList.forEach(className => {
                if (!el.classList.contains(className)) {
                    el.classList.add(className);
                }
            });
        }
    })

}

/**
 * 高阶函数，用于包装React组件，添加页面信息和生命周期方法
 * @param options - 选项对象，包含页面名称和是否使用ref
 * @returns 处理后的组件函数
 */
export default function withPage(options: { pageName: string, ref?: boolean }) {
    const { pageName, ref } = options;
    return function (WrappedComponent) {
        function NewComponent(props, ref) {

            const refIsFirst = useRef(true)
            const basePageInfo = useRef<BasePageInfo>({
                pvId: getUuid()
            }).current
            const uniquePageId = useRef(getUuid()).current
            const didShowFnArr = useRef<(() => void)[]>([]).current

            // 获取返回参数
            const getBackParams = useCallback(() => {
                return GLOBAL_BACK_PARAMS[contextValue.uniquePageId]
            }, [])

            // 设置返回参数
            const setBackParams = useCallback((args) => {
                const currentPage = getCurrentPageInstance();
                if (currentPage.router?.params?.uniquePageId) {
                    GLOBAL_BACK_PARAMS[currentPage.router?.params.uniquePageId] = args
                }
            }, [])

            /**清楚下游页注入的返回值 */
            const clearBackParams = useCallback(() => {
                delete GLOBAL_BACK_PARAMS[contextValue.uniquePageId];
            }, [])

            const registerDidShow = useCallback((fn: () => void) => {
                didShowFnArr.push(fn)
            }, [])

            const offDidShow = useCallback((fn: () => void) => {
                const index = didShowFnArr.findIndex(x => x === fn)
                didShowFnArr.splice(index, 1)
            }, [])


            /** 获取页面入参 */
            const getPageParams = useCallback(() => {
                const currentPage = getCurrentPageInstance();
                const params = currentPage.router?.params
                if (isWeb) {
                    const newParams: any = {};
                    if (isObject(params)) {
                        Object.keys(params).forEach(key => {
                            try {
                                newParams[key] = decodeURIComponent(params[key]!)
                            } catch (error) {
                                console.error('error', error)
                                newParams[key] = params[key]!
                            }
                        })
                    }
                    return newParams;
                }
                return params;
            }, [])

            const getCommonMtaParams = useCallback(() => {
                const { displayName, realName } = getPageParams();
                const params = {
                    displayName: displayName || MTA_NONE,
                    keyword: realName || MTA_NONE
                }
                return params;
            }, [])

            const contextValue = useRef({
                uniquePageId,
                getBackParams,
                setBackParams,
                clearBackParams,
                registerDidShow,
                offDidShow,
                basePageInfo,
                getPageParams,
                getCommonMtaParams
            }).current

            useEffect(() => {
                const params = getPageParams() || {}
                console.log('debugger', params.debugger);
                basePageInfo.debugger = params.debugger

                return () => {
                    clearBackParams()
                }
            }, [])

            useDidShow(() => {

                // console.log('useDidShow - 1', refIsFirst.current, pageName, basePageInfo.pvId)
                if (refIsFirst.current) {
                    refIsFirst.current = false;
                    return
                }
                basePageInfo.pvId = getUuid();
                // console.log('useDidShow - 2', pageName, basePageInfo.pvId)
            })


            
            const execDidshowFns = () => {
                didShowFnArr.forEach(fn => {
                    isFunction(fn) && fn();
                })
            }

            useListenBackFromOthers(execDidshowFns)

            useDidShow(() => {
                // console.log('useDidShow = == =3', env)
                // alert('useDidShow = == =3')
                execDidshowFns()

                if (!isWeb) { return }
                resetPageClassNames()
            })


            // useDidHide(() => {
            //     // console.log('useDidHide')
            // })

            return (
                <BasePageContext.Provider value={contextValue}>
                    <ErrorBoundary moduleName={'JDReactLifeTravelSearch'} displayName={pageName} ErrorComponent={() => <DegradePage></DegradePage>}>
                        <WrappedComponent
                            ref={ref}
                            getBackParams={getBackParams}
                            setBackParams={setBackParams}
                            getPageParams={getPageParams}
                            basePageInfo={basePageInfo}
                            {...props}
                        />
                    </ErrorBoundary>
                </BasePageContext.Provider>
            )
        }
        if (ref) {
            return forwardRef<any, any>(NewComponent)
        }
        return NewComponent
    };
}

/**
 * 对原始函数添加延迟执行功能的高阶函数
 * @param originalFunction 原始函数
 * @returns 带有延迟执行功能的函数
 */
export function withBasePageInfo(originalFunction) {
    return function () {
        setTimeout(() => {
            originalFunction();
        }, 0);
    };
}
export enum PERFORMANCE {
    FIRSTSCREEN_TIME_KEY = 'firstScreenTime', // 首屏耗时
    INTERFACE_TIME_KEY = 'interfaceTime', // 接口耗时
    FUNCTIONID_KEY = 'functionId', // 接口ID
    ADDRESS_TIME_KEY = 'addressTime', // 地址耗时
    ADDRESS_TYPE_KEY = 'addressType' // 地址类型
}

export enum PERFORMANCE_LABEL {
    FIRSTSCREEN_TIME_KEY = 'firstScreenTime', // 首屏耗时label
    FUNCTIONID_HOTEL_LIST_KEY = 'hotelList', // 主接口请求label
    FUNCTIONID_HOTEL_LIST_RESPONSE_KEY = 'hotelListResponse', // 主接口解析label
    ADDRESS_TIME_KEY = 'addressTime', // 地址耗时label
}

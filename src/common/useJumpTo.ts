import { useContext } from "react";
import { BasePageContext } from "./withPage";
import { jumpConfig } from "@/utils/jump";
import { isEmpty, isObject } from "@/utils/isType";


export default function useJumpTo() {
    const basePageContext = useContext(BasePageContext)
    const uniquePageId = basePageContext.uniquePageId;
    const basePageInfo = basePageContext.basePageInfo;

    const _jumpTo = (jumpData: { to: string, params?: any }) => {
        const { to, params } = jumpData || {}
        if (!isEmpty(params) && !isObject(params)) {
            throw new Error('params must be object!')
        }
        jumpConfig({
            to,
            params: Object.assign({}, params, { uniquePageId, debugger: basePageInfo.debugger })
        })
    }

    return _jumpTo
}

import searchBar from '@/pages/VResult/searchBar';
import { errorCode, errorCodeConstant } from '@ltfe/ltfe-core-lib/lib/utiles';

export const codeMapping: errorCode = {
    ...(errorCode || {}),
    151305001: {
        errorMsg: '走到H5页面',
        crashReason: 'h5_initialization'
    },
    151305002101: {
        errorMsg: '垂搜结果页顶部bar获取到的参数',
        crashReason: 'vertical_searchbar_get_params'
    },
    151305002102: {
        errorMsg: '垂搜顶部弹窗点完成接收弹窗给的参数',
        crashReason: 'vertical_searchbar_get_popup_params'
    },
    151305002103: {
        errorMsg: '垂搜点完成给页面传的信息',
        crashReason: 'vertical_searchbar_give_page_data'
    },
    151305002104: {
        errorMsg: '垂搜跳中间页带的参数',
        crashReason: 'vertical_searchbar_to_city_params'
    },
    151305002105: {
        errorMsg: '垂搜页面接口异常',
        crashReason: 'vertical_list_fetch_error'
    },
    151305002106: {
        errorMsg: '垂搜初始JDSHARE异常',
        crashReason: 'vertical_list_init_load_jd_share_data'
    },
    151305002107: {
        errorMsg: '垂搜初始请求参数',
        crashReason: 'vertical_list_init_fetch_data'
    },
    151305002108: {
        errorMsg: '垂搜初始请求参数',
        crashReason: 'vertical_list_init_fetch_data_get'
    },
    151305002109: {
        errorMsg: '垂搜DEEP_MARGE参数',
        crashReason: 'vertical_list_deep_merge_data'
    },
    151305002110: {
        errorMsg: '垂搜SET_SHARE_INFO参数',
        crashReason: 'vertical_list_set_share_info_data'
    },
    151305002111: {
        errorMsg: '垂搜返回页面刷新',
        crashReason: 'vertical_list_refresh'
    },
    151305003112: {
        errorMsg: '大搜结果页请求入参',
        crashReason: 'result_search_fetch'
    },
    151305003113: {
        errorMsg: '大搜结果页面路由参数',
        crashReason: 'result_search_route'
    },
    151305002112: {
        errorMsg: '垂搜获取url的参数',
        crashReason: 'vertical_list_init_url_params'
    },
    151305002113: {
        errorMsg: '垂搜获取url的参数失败',
        crashReason: 'vertical_list_init_url_params_error'
    },
    151305002114: {
        errorMsg: '垂搜请求参数',
        crashReason: 'vertical_list_fetch_params'
    },
    151305002115: {
        errorMsg: '垂搜获取lbs定位失败',
        crashReason: 'vertical_list_location_error'
    },
    151305002116: {
        errorMsg: '垂搜获取lbs定位失败 try/catch',
        crashReason: 'vertical_list_location_error_try_catch'
    },
    151305002117: {
        errorMsg: '垂搜接口异常',
        crashReason: 'vertical_list_fetch_list_error'
    },
    151305002118: {
        errorMsg: '页面参数为空',
        crashReason: 'vertical_list_param_empty_error'
    },
    151305002119: {
        errorMsg: '获取JDShare数据异常',
        crashReason: 'vertical_list_get_jdshare_data_error'
    },
    151305002120: {
        errorMsg: 'reload酒店数据异常',
        crashReason: 'vertical_list_reload_data_if_need_error'
    },
    151305002121: {
        errorMsg: 'iframe回调异常',
        crashReason: 'vertical_list_iframe_callback_error'
    },
    // 差旅搜索中间页
    151305004001: {
        errorMsg: '差旅搜索中间页接口失败',
        crashReason:'business_search_fetch_fail'
    },
    151305004002: {
        errorMsg: '差旅搜索中间页接收参数失败',
        crashReason:'business_search_params_error'
    },
    151305003114: {
        errorMsg: '大搜结果页面获取的hotel JDShare内容',
        crashReason: 'result_search_hotel_jdshare'
    },
    151305003115: {
        errorMsg: '大搜结果页面获取的hotel 获取到的入离店信息处理后的数据',
        crashReason: 'result_search_hotel_jdshare_handle'
    },
    151305003116: {
        errorMsg: '垂搜storageRef为空',
        crashReason: 'vertical_list_storage_not_found'
    },
    151305003117: {
        errorMsg: '垂搜接口异常',
        crashReason: 'vertical_list_storage_promise_error'
    },
    151305003118: {
        errorMsg: '地址请求未回调',
        crashReason: 'responseerror_addresserror_no_callback'
    },
    151305005101: {
        errorMsg: '酒店搜索页入参异常',
        crashReason: 'channel_search_params_error'
    },
    151305005102: {
        errorMsg: '酒店搜索页请求异常',
        crashReason: 'channel_search_fetch_list_error'
    },
    151305005103: {
        errorMsg: '获取JDShare数据异常',
        crashReason: 'channel_list_get_jdshare_data_error'
    },
    151305003119: {
        errorMsg: '获取用户身份接口异常',
        crashReason: 'vertical_list_fetch_user_identity_error'
    },
    151305003120: {
        errorMsg: '垂搜获取城市信息异常',
        crashReason: 'city_request_error'
    },
    151305003121: {
        errorMsg: '获取地址异常',
        crashReason: 'use_default_address'
    }
};

// 如果上报的code在公共集合中没有，可以自定义errorCode进行上报
// 自定义监控 15
// 项目编号 13
// 业务编号 01:酒旅首页，02:酒店，03:机票，04:门票, 05:大搜
// 页面编号 首页 001 垂搜 002 大搜结果 003  004 差旅搜索中间页
// 页面具体上报的错误吗
// 101: 垂搜顶部接收页面的参数 102: 垂搜顶部弹窗点完成接收弹窗给的参数  103: 垂搜点完成给页面传的信息 104: 垂搜跳中间页带的参数
// 105: 垂搜页面接口异常 106: 垂搜初始JDSHARE异常  107: 垂搜初始请求参数  111:  垂搜返回页面刷新
// 112: 大搜结果页请求入参 113: 大搜结果页面获取的hotel JDShare内容 114: 大搜结果页面路由参数
// 115: 大搜结果页面获取的hotel 获取到的入离店信息处理后的数据
// export const HOME_PREHEAT_ERR = '151303001';
export const errorCodeConstantMapping: errorCodeConstant = {
    ...(errorCodeConstant || {}),
    H5_PAGEERROR_PAGE_INITIALIZATION: 151305001, // 走到H5页面
    PAGE_VERTICAL_SEARCHBAR_GET_PARAMS: 151305002101, // 垂搜顶部接收页面的参数
    PAGE_VERTICAL_SEARCHBAR_GET_POPUP_PARAMS: 151305002102, // 垂搜顶部弹窗点完成接收弹窗给的参数
    PAGE_VERTICAL_SEARCHBAR_GIVE_PAGE_DATA: 151305002103, // 垂搜点完成给页面传的信息
    PAGE_VERTICAL_SEARCHBAR_TO_CITY_PARAMS: 151305002104, // 垂搜跳中间页带的参数
    PAGE_VERTICAL_LIST_FETCH_ERROR: 151305002105, // 垂搜页面接口异常
    PAGE_VERTICAL_LIST_INIT_LOAD_JD_SHARE_DATA: 151305002106, // 垂搜初始JDSHARE异常
    PAGE_VERTICAL_LIST_INIT_FETCH_DATA: 151305002107, // 垂搜初始请求参数
    PAGE_VERTICAL_LIST_INIT_FETCH_DATA_GET: 151305002108, // 垂搜初始获取参数
    PAGE_VERTICAL_LIST_DEEP_MERGE_DATA: 151305002109, // 垂搜DEEP_MARGE参数
    PAGE_VERTICAL_LIST_SET_SHARE_INFO_DATA: 151305002110, //  垂搜SET_SHARE_INFO参数
    PAGE_VERTICAL_LIST_REFRESH: 151305002111, //  垂搜返回页面刷新
    PAGE_VERTICAL_LIST_INIT_URL_PARAMS: 151305002112, //  垂搜获取url的参数
    PAGE_VERTICAL_LIST_INIT_URL_PARAMS_ERROR: 151305002113, //  垂搜获取url的参数失败
    PAGE_VERTICAL_LIST_FETCH_PARAMS: 151305002114, //  垂搜获取url的参数失败
    PAGE_VERTICAL_LIST_LOCATION_ERROR: 151305002115, //  垂搜获取lbs定位失败
    PAGE_VERTICAL_LIST_LOCATION_ERROR_TRY_CATCH: 151305002116, //  垂搜获取lbs定位失败 try/catch
    PAGE_VERTICAL_LIST_FETCH_LIST_ERROR: 151305002117, // 垂搜接口异常
    PAGE_VERTICAL_LIST_PARAM_EMPTY_ERROR: 151305002118, // 页面参数为空
    PAGE_VERTICAL_LIST_GET_JDSHARE_DATA_ERROR: 151305002119, // 获取JDShare数据异常
    PAGE_VERTICAL_LIST_RELOAD_DATA_IF_NEED_ERROR: 151305002120, // reload酒店数据异常
    PAGE_VERTICAL_LIST_IFRAME_CALLBACK_ERROR: 151305002121, // iframe回调异常
    PAGE_RESULT_SEARCH_FETCH_PARAMS: 151305003112, // 大搜结果页请求入参
    PAGE_RESULT_SEARCH_ROUTE_PARAMS: 151305003113, // 大搜结果页面路由参数
    PAGE_RESULT_SEARCH_HOTEL_JDSHARE_PARAMS: 151305003114, // 大搜结果页面获取的hotel JDShare内容
    PAGE_RESULT_SEARCH_HOTEL_HANDLE_PARAMS: 151305003115, // 大搜结果页面获取的hotel 获取到的入离店信息处理后的数据
    PAGE_BUSINESS_SEARCH_FETCH_FAIL: 151305004001, // 差旅搜索中间页接口失败
    PAGE_BUSINESS_SEARCH_PARAMS_FAIL: 151305004002, // 差旅搜索中间页接口失败
    PAGE_VERTICAL_LIST_STORAGE_NOT_FOUND: 151305003116, // 垂搜storageRef为空
    PAGE_VERTICAL_LIST_STORAGE_PROMISE_ERROR: 151305003117, //垂搜接口异常
    RESPONSEERROR_ADDRESSERROR_NO_CALLBACK: 151305003118, // 地址请求未回调
    PAGE_CHANNEL_SEARCH_PARAMS_ERROR: 151305005101, // 酒店搜索页入参异常
    PAGE_CHANNEL_SEARCH_FETCH_LIST_ERROR: 151305005102, // 酒店搜索页请求异常
    PAGE_CHANNEL_LIST_GET_JDSHARE_DATA_ERROR: 151305005103, // 获取JDShare数据异常
    PAGE_VERTICAL_LIST_FETCH_USER_IDENTITY_ERROR: 151305003119, // 获取用户身份接口异常
    PAGE_VERTICAL_LIST_CITY_REQUEST_ERROR: 151305003120, // 垂搜获取城市信息异常
    PAGE_VERTICAL_LIST_USE_DEFAULT_ADDRESS: 151305003121, // 垂搜使用默认地址
}

export type T_REPORT_INFO = {
    code: keyof typeof codeMapping,
    errorDetail: {
        functionId?: string,
        errorType: ErrortType,
        customMsg?: any,
    }
}

//错误级别
export enum ErrortType {
    Error = 1,
    Warn = 2,
    Info = 3,
}

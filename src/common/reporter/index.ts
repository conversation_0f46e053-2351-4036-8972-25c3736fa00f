// import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@jdreact/jdreact-core-lib';
import { reportNLog } from '@ltfe/ltfe-core-lib/lib/utiles';
import { getCurrentPageInstance } from '../common';

import { codeMapping, T_REPORT_INFO } from './errorMapping';

const errorMapping = Object.assign({}, codeMapping);
// const errorMap = errorMapping[code];

export const reportInfo = (info: T_REPORT_INFO) => {
    const { code, errorDetail } = info
    const page = getCurrentPageInstance()
    const mapping = errorMapping[code];
    if (!mapping) {
        console.error('code 异常', code);
        return;
    }
    const crashInfo = {
        "code": code,
        "pageName": page.router?.path,
        "pageArgs": page.router?.params,
        "errorMsg": mapping.errorMsg,
        "errorDetail": errorDetail,
        "buildTime": process.env.BUILD_TIME,
        "customMsg": errorDetail.customMsg
        // "address": useAddressInfoStore.getState(),
        // "requestInfo": page.requestInfo,
    }
    const functionid = errorDetail.functionId;
    const crashReason = functionid
        ? `${mapping.crashReason}${code}:${mapping.errorMsg}:${functionid}`
        : `${mapping.crashReason}${code}:${mapping.errorMsg}`;


    // moduleName需要业务方自己配置
    reportNLog({ moduleName: 'JDReactLifeTravelSearch', errorDetail: { crashInfo, crashReason } });
}
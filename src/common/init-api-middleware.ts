import {resetConfig} from '@ltfe/ltfe-core-lib/lib/utiles/fetch'


const host = {
    beta: 'https://beta-api.m.jd.com'
}


export default () => {
    return () => {
        resetConfig({
            appid: 'JDLifeTravelSearch',
            appId: '20379',
            isSign: true,
            host: host[process.env.NET_ENV!],
            apis: {
                isUserFollow: {type: 'post', id: 'isUserFollow'},
                QUERY_COUPON_AD_Batch: {
                    type: 'post',
                    id: 'coupon_sc_ad_activity_batch_query'
                },
                QUERY_COUPON_AD: {
                    type: 'post',
                    id: global.env === 'yufa'
                        ? 'coupon_yf_ad_activity_query'
                        : 'coupon_sc_ad_activity_query'
                },
                abTest: {type: 'post', id: 'pdsm_abTest'},
                lbsCity: {type: 'post', id: 'geo_outer_regionLocation'}, // 定位
                TRIP_SEARCH_MIDDLE: {type: 'post', id: 'trip_search_midPage'}, // 搜索中间页面
                searchSugList: {type: 'post', id: 'trip_search_sug'},
                getLocation: {type: 'post', id: 'locationAndDistanceFilterComponents.data'},
                TRIP_SEARCH_INDEX: {type: 'post', id: 'trip_search_index'}, // 搜索结果页面接口
                HOTEL_LIST: {type: 'post', id: 'trip_search_hotel_vertical'}, // 垂搜结果页
                GET_DUCC: {type: 'post', id: 'trip_search_config_query'},
                MID_LOCATION: {type: 'post', id: 'trip_search_hotel_booking_midPage'},
                CHANNEL_LIST: {type: 'post', id: 'trip_baibu_hotel_list'},
                USER_IDENTITY_LIST: {type: 'post', id: 'identity_getUserIdentity'},
                TRAVEL_GETGEOINFO: { type: 'post', id: 'travel_getGeoInfo' }, // 通过业务id获取城市
            }
        })
    }
}

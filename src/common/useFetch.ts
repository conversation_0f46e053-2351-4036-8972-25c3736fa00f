import { getUuid, p2Arry, sleeping } from '@/utils';
import { apiFetch as _apiFetch } from '@ltfe/ltfe-core-lib/lib/utiles/fetch';
import addressModel from "@/store/model/address.model"
import { useContext } from 'react';
import { BasePageContext } from './withPage';
import { reportInfo } from './reporter';
import { errorCodeConstantMapping, ErrortType } from './reporter/errorMapping';
import { isEmpty } from '@/utils/isType';
import { isAndroid, isIOS, isWeb } from './common';
import globalInfoModel from '@/store/model/globalInfo.model'

export enum ERROR_CODE {
    NETWORK_TIMEOUT = -9999 // 网络错误
}

export const PLAT_FORM_CODE = isWeb ? 'h5' : isIOS ? 'apple' : isAndroid ? 'android' : '' // 客户端请求来源
export const BIZ_VERSION = '1.87' // TODO：每次发包前需要修改该版本号，该版本号对应着mpass上的版本

const useFetch = () => {
    const basePageContext = useContext(BasePageContext)

    const apiFetch = async (apiName, params, rej, options?: object) => {
        const { isTest, isEncryptBody, ignoreAddress = false, customPvId } = (options || {}) as any
        let addressInfo
        if (!ignoreAddress) {
            try {
                addressInfo = await addressModel.getAddress

            } catch (error) {
                addressInfo = error
            }
        }
        // 获取地址信息
        const address = {
            latitude: (addressInfo?.address?.latitude)?.toString(),
            longitude: (addressInfo?.address?.longitude)?.toString(),
            posAreaId: addressInfo?.address?.posAreaId,
            virtualLocation: addressInfo?.address?.virtualLocation, // 虚拟定位 1 是 0 否
        }
        try {
            const uuid = getUuid()
            const pvId = basePageContext?.basePageInfo?.pvId
            const staticParams = globalInfoModel.staticParams
            // 组装入参
            params = Object.assign({}, address, {
                bizVersion: BIZ_VERSION,
                platformCode: PLAT_FORM_CODE, // 客户端请求来源
                pvId: isEmpty(customPvId) ? pvId : customPvId,
                logId: uuid,
                channelId: staticParams.channel
            }, params)
            console.log('请求接口', apiName)

            console.log('请求入参', params)
            if (isEmpty(params.latitude) || isEmpty(params.longitude) || isEmpty(params.posAreaId) || isEmpty(params.logId) || isEmpty(params.pvId)) {
                if (!ignoreAddress) {
                    reportInfo({
                        code: errorCodeConstantMapping?.REQUESTERROR_REQUEST_PARAMETER_MISSING,
                        errorDetail: {
                          errorType: ErrortType.Info,
                          functionId: apiName,
                          customMsg: {
                                errorDescription: `入参：${JSON.stringify(params)}`
                            },
                        }
                    })
                }
            }

            /**
             * 异步函数，延迟10秒后返回一个拒绝的Promise，表示请求超时
             */
            const timeOutPromise = async () => {
                await sleeping(10 * 1000)
                return Promise.reject({ code: -9999, msg: '请求超时' })
            }

            // 请求接口
            let [error, res] = await p2Arry(Promise.race([
                timeOutPromise(),
                _apiFetch(apiName, params, rej, isTest, isEncryptBody)
            ]))
            console.log('请求返回', error, res)

            // 处理返回内容
            if (error) {
                if (error?.message === 'Network timeout' || error?.code == "ENSURLERRORDOMAIN-1009" || error?.code == "ENSURLERRORDOMAIN-1005" || error?.code == "ENSURLERRORDOMAIN-1001") {
                    reportInfo({
                        code: errorCodeConstantMapping?.RESPONSEERROR_NETWORK_EXCEPTION_DISCONNECTED,
                        errorDetail: {
                          errorType: ErrortType.Info,
                          functionId: apiName,
                          customMsg: {
                                errorInfo: error?.message,
                                errorStack: error?.stack,
                            },
                        }
                    })
                    return [{ code: ERROR_CODE.NETWORK_TIMEOUT, msg: '请求超时' }, res]
                }
                reportInfo({
                    code: errorCodeConstantMapping?.RESPONSEERROR_CODE_NON_ZERO_EXCEPTION,
                    errorDetail: {
                      errorType: ErrortType.Info,
                      functionId: apiName,
                      customMsg: {
                            errorDescription: '请求失败p2Arry返回error',
                            errorInfo: error?.message,
                            errorStack: error?.stack,
                        },
                    }
                })
                return [error, res]
            }

            const { code } = res;

            if (+code === 0) {
                return [null, res]
            } else {
                reportInfo({
                    code: errorCodeConstantMapping?.RESPONSEERROR_CODE_NON_ZERO_EXCEPTION,
                    errorDetail: {
                      errorType: ErrortType.Info,
                      functionId: apiName,
                      customMsg: {
                            res: res,
                        },
                    }
                })
                return [{ code: -1, msg: '接口返回异常-1' }, null]
            }
        } catch (error) {
            reportInfo({
                code: errorCodeConstantMapping?.RESPONSEERROR_CODE_NON_ZERO_EXCEPTION,
                errorDetail: {
                  errorType: ErrortType.Info,
                  functionId: apiName,
                  customMsg: {
                        errorDescription: '请求失败tryCatch',
                        errorInfo: error?.message,
                        errorStack: error?.stack,
                    },
                }
            })
            return [{ code: -999, error: error }, null]
        }

    }

    return {
        apiFetch
    }
}
export default useFetch

import { reportInfo } from "@/common/reporter";
import { errorCodeConstantMapping, ErrortType } from "@/common/reporter/errorMapping";

/**
 * 通过可能是json的字符串获取值，如果是json则转成json对象，如果不是，直接返回传入的值
 * @param {*} str 字符串
 */
export function getJsonObjectOrRawValue(str) {
    try {
        const val = JSON.parse(str);
        if (typeof val === "object") {
            return val;
        }
        // 兼容下上一版本问题
        const v = JSON.parse(val);
        if (typeof v === "object") {
            return v;
        }
        return str;
    } catch (e) {
        return str;
    }
}


/**
 * JSON.stringify值
 * @param {any} obj 需要被JSON.stringify的值
 */
export function stringifyValue(obj) {
    if (typeof obj === 'string') {
        return obj;
    }
    try {
        // 如果是object类型，优先序列化，避免走catch逻辑
        if (typeof obj === 'object') {
            return JSON.stringify(obj);
        }
        // 不排除传入的出了 object、string类型之外的值，使用catch兜底
        return JSON.stringify(obj);
    } catch (error) {
        // 捕获异常
        reportInfo({
            code: errorCodeConstantMapping?.PARSEERROR_JSON_PARSE_EXCEPTION,
            errorDetail: {
                errorType: ErrortType.Info,
            }
        })
        console.error('stringifyValue', error);
        return obj;
    }
}
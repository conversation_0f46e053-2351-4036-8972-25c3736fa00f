import {JDShareData} from '@jdreact/jdreact-core-lib'
import _ from 'lodash'

export const shareData = (keys) => {
    const dataList = keys.map(key => JDShareData.querySharedDataByName(key))
    return new Promise((res, rej) => {
        Promise.allSettled(dataList).then(resData => {
            res(resData)
        }).catch(error => {
            rej(error)
        })
    })
}

export const shareDataByKey = (key) => {
    return JDShareData.querySharedDataByName(key)
}

export const getEnvCallBack = (evnCb) => {
    return _.get(evnCb, 'rn')
}

export const setShareData = (keysList) => {
    const dataList = Object.keys(keysList).map(key => {
        const data = _.get(keysList, key, "")
        return JDShareData.addSharedData({[key]: typeof data === 'string' ? data : JSON.stringify(data)})
    })

    return new Promise((res, rej) => {
        Promise.all(dataList).then(resData => {
            res(resData)
        }).catch(error => {
            rej(error)
        })
    })
}

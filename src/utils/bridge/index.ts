import {safeRun} from '@/Components/Filter/utils'
import _ from 'lodash'

export const getEnvCallBack = (evnCb)=> {
    return _.get(evnCb, 'taro')
}

export const shareData = (key) => {
    return new Promise((res, rej) => {
        console.warn('EVN Tora dont had jd share！')
        rej()
    })
}

export const shareDataByKey = (key)=> {
    return new Promise((res, rej) => {
        console.warn('EVN Tora dont had jd share！')
        rej()
    })
}

export const setShareData = (key)=> {
    return new Promise((res, rej) => {
        console.warn('EVN Tora dont had jd share！')
        rej()
    })
}

import _ from 'lodash'
import qs from 'query-string'
import {enCodeDebBase64ParseSafe} from '@/Components/utils'
import {location, window} from '@tarojs/runtime'

export const getEnvCallBack = (evnCb) => {
    return _.get(evnCb, 'web')
}

export const shareData = (key) => {
    return new Promise((res, rej) => {
        console.warn('EVN Web dont had jd share！')
        rej()
    })
}

export const shareDataByKey = (key) => {
    return new Promise((res, rej) => {
        console.warn('EVN Tora dont had jd share！')
        rej()
    })
}

// 垂搜搜索条件持久化
export const setShareData = (setObjs = {}) => {
    console.log(setObjs, 'aabbcc')
    const curSearch = qs.parse(location.search)
    return new Promise((res, rej) => {
        try {
            Object.keys(setObjs).forEach(key => {
                _.set(curSearch, key, enCodeDebBase64ParseSafe(_.omit(setObjs[key], ['filterMetaData'])))
            })

            let searchParams = ""

            Object.keys(curSearch).forEach((key,index)=> {
                let firstFlag = index ? "&" : "?"
                searchParams += `${firstFlag}${key}=${curSearch[key]}`
            })

            const newState = {...window.history.state, search: `${searchParams}`}
            window.history.replaceState(newState, '', `${location?.origin}${location.pathname}${searchParams}`)
        } catch (e) {
            console.warn('shareData web:', e)
            rej()
        }
    })
}

import React, { useRef, useEffect } from 'react'
import { Text } from '@/BaseComponents/atoms'
import { isRn } from '@/common/common'

interface MonitoredTextProps {
  registerTextElement?: (ref: React.RefObject<any>) => void
  children: React.ReactNode
  [key: string]: any
}

/**
 * 自动注册到首屏监控器的Text组件
 * 当传入registerTextElement函数时，会自动注册text元素的ref
 */
const MonitoredText: React.FC<MonitoredTextProps> = ({ 
  registerTextElement, 
  children, 
  ...props 
}) => {
  const textRef = useRef<any>(null)

  useEffect(() => {
    // 仅在RN环境下且有注册函数时注册
    if (isRn && registerTextElement && textRef.current) {
      registerTextElement(textRef)
    }
  }, [registerTextElement, textRef.current])

  return (
    <Text ref={textRef} {...props}>
      {children}
    </Text>
  )
}

export default MonitoredText

# 首屏渲染耗时监控

## 功能说明

这个工具用于监控React Native应用中的首屏渲染耗时。它通过检测页面中的Text元素来判断首屏是否渲染完成。

## 实现方案

### 检测策略
1. **页面初始化开始**：在页面组件mount时开始计时
2. **遍历页面元素**：定时检测页面中的Text元素
3. **位置过滤**：
   - 忽略页面头部20%区域的Text元素（通常是导航栏等）
   - 忽略页面尾部25%区域的Text元素（通常是底部导航等）
4. **成功条件**：检测到>=2个有效区域内的Text元素
5. **检测频率**：每50ms检测一次
6. **超时机制**：总共检测10秒，超时则认为监控失败

### 使用方法

```typescript
import { useSimpleFirstScreenMonitor } from '@/utils/FirstScreenMonitor/SimpleMonitor'

const MyPage = () => {
  const { startMonitoring } = useSimpleFirstScreenMonitor()
  
  useEffect(() => {
    if (isRn) {
      startMonitoring((duration, success) => {
        console.log(`首屏渲染${success ? '完成' : '超时'}，耗时: ${duration}ms`)
        // 上报到埋点系统
      })
    }
  }, [])
  
  return <YourPageContent />
}
```

## 集成位置

当前已集成到以下页面：
- `src/pages/hotelSearch/index.tsx` - 酒店搜索页面

## 注意事项

1. **仅在RN环境下工作**：Web环境会自动跳过监控
2. **模拟检测逻辑**：当前使用模拟的检测逻辑，实际项目中需要根据具体情况调整
3. **性能影响**：监控过程对性能影响很小，每50ms执行一次轻量级检测
4. **埋点上报**：监控结果可以上报到现有的埋点系统进行数据分析

## 输出示例

```
[SimpleFirstScreenMonitor] 开始首屏渲染监控
[SimpleFirstScreenMonitor] 第1轮检测: 检测到0个有效Text元素
[SimpleFirstScreenMonitor] 第2轮检测: 检测到0个有效Text元素
...
[SimpleFirstScreenMonitor] 第12轮检测: 检测到3个有效Text元素
[SimpleFirstScreenMonitor] 首屏渲染完成，耗时: 600ms
[酒店搜索] 首屏渲染完成，耗时: 600ms
```

## 扩展方向

1. **真实元素检测**：替换模拟逻辑为真实的RN视图层级检测
2. **自定义条件**：支持自定义成功条件（如检测特定组件）
3. **更多指标**：添加更多性能指标（如首次内容绘制、最大内容绘制等）
4. **可视化调试**：添加调试模式，可视化显示检测到的元素

import React from 'react'
import { isRn } from '@/common/common'

/**
 * 首屏渲染耗时监控器
 * 用于检测RN页面首屏渲染完成时间
 */
export class FirstScreenMonitor {
  private startTime: number = 0
  private endTime: number = 0
  private isMonitoring: boolean = false
  private checkTimer: NodeJS.Timeout | null = null
  private checkCount: number = 0
  private maxCheckCount: number = 200 // 10秒 / 50ms = 200次
  private checkInterval: number = 50 // 50ms检测间隔
  private onComplete?: (duration: number, success: boolean) => void
  private textElementRefs: Array<React.RefObject<any>> = []

  constructor() {
    // 构造函数中不需要参数，通过registerTextElement方法注册Text元素
  }

  /**
   * 注册Text元素引用
   * @param ref Text元素的ref
   */
  registerTextElement(ref: React.RefObject<any>): void {
    if (ref && !this.textElementRefs.includes(ref)) {
      this.textElementRefs.push(ref)
    }
  }

  /**
   * 清空已注册的Text元素
   */
  clearTextElements(): void {
    this.textElementRefs = []
  }

  /**
   * 开始监控首屏渲染
   * @param onComplete 完成回调函数，参数为耗时(ms)和是否成功
   */
  start(onComplete?: (duration: number, success: boolean) => void): void {
    if (!isRn) {
      console.warn('FirstScreenMonitor only works in React Native environment')
      return
    }

    this.startTime = Date.now()
    this.isMonitoring = true
    this.checkCount = 0
    this.onComplete = onComplete

    console.log('[FirstScreenMonitor] 开始首屏渲染监控')
    
    // 开始检测
    this.startChecking()
  }

  /**
   * 停止监控
   */
  stop(): void {
    if (this.checkTimer) {
      clearTimeout(this.checkTimer)
      this.checkTimer = null
    }
    this.isMonitoring = false
    console.log('[FirstScreenMonitor] 停止首屏渲染监控')
  }

  /**
   * 开始定时检测
   */
  private startChecking(): void {
    if (!this.isMonitoring) return

    this.checkTimer = setTimeout(() => {
      this.checkCount++
      
      // 执行检测
      this.performCheck()
        .then((success) => {
          if (success) {
            // 检测成功，计算耗时
            this.endTime = Date.now()
            const duration = this.endTime - this.startTime
            console.log(`[FirstScreenMonitor] 首屏渲染完成，耗时: ${duration}ms`)
            this.stop()
            this.onComplete?.(duration, true)
          } else if (this.checkCount >= this.maxCheckCount) {
            // 超时
            this.endTime = Date.now()
            const duration = this.endTime - this.startTime
            console.log(`[FirstScreenMonitor] 首屏渲染超时，耗时: ${duration}ms`)
            this.stop()
            this.onComplete?.(duration, false)
          } else {
            // 继续下一轮检测
            this.startChecking()
          }
        })
        .catch((error) => {
          console.error('[FirstScreenMonitor] 检测过程出错:', error)
          if (this.checkCount >= this.maxCheckCount) {
            this.endTime = Date.now()
            const duration = this.endTime - this.startTime
            this.stop()
            this.onComplete?.(duration, false)
          } else {
            this.startChecking()
          }
        })
    }, this.checkInterval)
  }

  /**
   * 执行一次检测
   * @returns Promise<boolean> 是否检测成功
   */
  private async performCheck(): Promise<boolean> {
    try {
      // 获取屏幕尺寸
      const screenInfo = await this.getScreenInfo()
      if (!screenInfo) {
        return false
      }

      // 获取所有已注册Text元素的位置信息
      const textElementLayouts = await this.getTextElementLayouts()
      
      // 过滤符合条件的Text元素
      const validTextElements = this.filterValidTextElements(textElementLayouts, screenInfo)
      
      console.log(`[FirstScreenMonitor] 第${this.checkCount}轮检测: 总Text元素${textElementLayouts.length}个，有效Text元素${validTextElements.length}个`)
      
      // 判断是否满足条件：有效Text元素 >= 2个
      return validTextElements.length >= 2
    } catch (error) {
      console.error('[FirstScreenMonitor] performCheck error:', error)
      return false
    }
  }

  /**
   * 获取所有已注册Text元素的布局信息
   */
  private async getTextElementLayouts(): Promise<Array<{x: number, y: number, width: number, height: number}>> {
    const layouts: Array<{x: number, y: number, width: number, height: number}> = []
    
    const measurePromises = this.textElementRefs.map(async (ref) => {
      if (ref.current) {
        try {
          const layout = await this.measureElement(ref.current)
          if (layout) {
            layouts.push(layout)
          }
        } catch (error) {
          console.error('[FirstScreenMonitor] 测量Text元素失败:', error)
        }
      }
    })

    await Promise.all(measurePromises)
    return layouts
  }

  /**
   * 测量元素的布局信息
   */
  private measureElement(element: any): Promise<{x: number, y: number, width: number, height: number} | null> {
    return new Promise((resolve) => {
      try {
        if (element?.measureInWindow) {
          element.measureInWindow((x: number, y: number, width: number, height: number) => {
            // 过滤无效值
            if (x >= 0 && y >= 0 && width > 0 && height > 0) {
              resolve({ x, y, width, height })
            } else {
              resolve(null)
            }
          })
        } else {
          resolve(null)
        }
      } catch (error) {
        console.error('[FirstScreenMonitor] measureElement error:', error)
        resolve(null)
      }
    })
  }

  /**
   * 获取屏幕信息
   */
  private async getScreenInfo(): Promise<{width: number, height: number} | null> {
    try {
      const { Dimensions } = require('react-native')
      const { width, height } = Dimensions.get('window')
      return { width, height }
    } catch (error) {
      console.error('[FirstScreenMonitor] getScreenInfo error:', error)
      return null
    }
  }

  /**
   * 过滤有效的Text元素
   * 排除头部20%和尾部25%区域的元素
   */
  private filterValidTextElements(
    textElementLayouts: Array<{x: number, y: number, width: number, height: number}>, 
    screenInfo: {width: number, height: number}
  ): Array<{x: number, y: number, width: number, height: number}> {
    const { height } = screenInfo
    const topThreshold = height * 0.4 // 头部20%
    const bottomThreshold = height * 0.75 // 尾部25%（从75%开始）

    return textElementLayouts.filter((layout) => {
      const { y, height: elementHeight } = layout
      const elementBottom = y + elementHeight
      
      // 检查元素是否在有效区域内（不在头部20%和尾部25%）
      const isInValidArea = y >= topThreshold && elementBottom <= bottomThreshold
      
      return isInValidArea
    })
  }
}

/**
 * React Hook for FirstScreenMonitor
 */
export function useFirstScreenMonitor() {
  const monitorRef = React.useRef<FirstScreenMonitor | null>(null)

  React.useEffect(() => {
    monitorRef.current = new FirstScreenMonitor()
    
    return () => {
      if (monitorRef.current) {
        monitorRef.current.stop()
        monitorRef.current.clearTextElements()
      }
    }
  }, [])

  const registerTextElement = React.useCallback((ref: React.RefObject<any>) => {
    if (monitorRef.current) {
      monitorRef.current.registerTextElement(ref)
    }
  }, [])

  const startMonitoring = React.useCallback((onComplete?: (duration: number, success: boolean) => void) => {
    if (monitorRef.current) {
      monitorRef.current.start(onComplete)
    }
  }, [])

  const stopMonitoring = React.useCallback(() => {
    if (monitorRef.current) {
      monitorRef.current.stop()
    }
  }, [])

  const clearTextElements = React.useCallback(() => {
    if (monitorRef.current) {
      monitorRef.current.clearTextElements()
    }
  }, [])

  return {
    registerTextElement,
    startMonitoring,
    stopMonitoring,
    clearTextElements
  }
}

/**
 * 增强的Text组件，自动注册到首屏监控器
 */
interface MonitoredTextProps {
  monitor?: FirstScreenMonitor
  children: React.ReactNode
  style?: any
  [key: string]: any
}

export const MonitoredText: React.FC<MonitoredTextProps> = React.forwardRef<any, MonitoredTextProps>(
  ({ monitor, children, ...props }, forwardedRef) => {
    const internalRef = React.useRef<any>(null)
    const ref = forwardedRef || internalRef

    React.useEffect(() => {
      if (monitor && ref && typeof ref === 'object' && 'current' in ref) {
        monitor.registerTextElement(ref as React.RefObject<any>)
      }
    }, [monitor, ref])

    // 动态导入Text组件
    const Text = React.useMemo(() => {
      try {
        if (isRn) {
          const { Text: RNText } = require('react-native')
          return RNText
        } else {
          // Web环境，可以使用项目中的Text组件
          const { Text: ProjectText } = require('@/BaseComponents/atoms')
          return ProjectText
        }
      } catch (error) {
        console.error('[MonitoredText] 导入Text组件失败:', error)
        return 'div' // 降级方案
      }
    }, [])

    return React.createElement(Text, { ref, ...props }, children)
  }
)

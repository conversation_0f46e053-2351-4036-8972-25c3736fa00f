import { reportInfo } from "@/common/reporter";
import { errorCodeConstantMapping, ErrortType } from "@/common/reporter/errorMapping";
import { useCallback, useEffect, useRef } from "react";


export function useProcessActionQueue(callBack) {
    const isProcessing = useRef(false)
    const refArgs = useRef<any[]>([])
    const refQueueStatus = useRef<Promise<any> | undefined>()
    // 顺序执行渲染sectionItems
    const _processActionQueue = useCallback(async () => {
        // console.log('refArgs.current', refArgs.current.length, refArgs.current)
        if (refArgs.current.length > 0 && !isProcessing.current) {
            isProcessing.current = true;
            let { args, ...others } = refArgs.current.shift();
            try {
                await callBack(args, others)
                // console.log('refArgs.current', refArgs.current.length, '任务执行完了')
            } catch (error) {
                console.error('processActionQueue error', error)
                reportInfo({
                    code: errorCodeConstantMapping?.TRYCATCHERROR_CATCH_CAPTURE_CODE_EXCEPTION,
                    errorDetail: {
                      errorType: ErrortType.Info,
                      customMsg: {
                            errorInfo: error?.message,
                            errorStack: error?.stack,
                        },
                    }
                })
            }
            isProcessing.current = false;
            await _processActionQueue();
        }
    }, [])

    /**
     * option
     *  reProcess 是否重新执行
     *  请求序号
     */
    const processActionQueue = useCallback(async (args, option?: { reProcess: boolean } & Record<string, any>) => {
        // console.log('processActionQueue', args, option)
        if (option?.reProcess) {
            refArgs.current = []
            isProcessing.current = false;
        }
        refArgs.current.push({ args, ...(option || {}) });
        refQueueStatus.current = _processActionQueue();
        await refQueueStatus.current
    }, [])

    useEffect(() => {
        return () => {
            refArgs.current = []
        }
    }, [])

    return {
        processActionQueue,
        getArgsQueue: () => refArgs.current,
        getQueuePendingStatus: () => refQueueStatus.current
    }
}
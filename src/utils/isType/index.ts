const TYPE_UNDEFINED = 'undefined';
const TYPE_NULL = 'null';

/**
 * 获取类型
 * @param {*} v
 * @returns {string}
 */
const getTypeOf = (v: unknown) => {
    if (v === undefined) {
        return TYPE_UNDEFINED
    }
    if (v === null) {
        return TYPE_NULL
    }
    return v.constructor.name.toLowerCase()
}

const isTypeOf = (v, t) => getTypeOf(v) === t;


const isArray = (arg: unknown): arg is Array<unknown> => {
    return Object.prototype.toString.call(arg) == "[object Array]"
}
const isObject = (arg: unknown): arg is Record<string, unknown> => {
    return Object.prototype.toString.call(arg) == "[object Object]"
}
const isString = (arg: unknown): arg is string => {
    return Object.prototype.toString.call(arg) == "[object String]"
}
const isBoolean = (arg: unknown): arg is boolean => {
    return Object.prototype.toString.call(arg) == "[object Boolean]"
}
const isNumber = (arg: unknown): arg is number => {
    return Object.prototype.toString.call(arg) == "[object Number]"
}

// 数字字符串
const isStringNumber = (v) => /^[0-9]+.?[0-9]*$/.test(v);
const isFunction = (v) => isTypeOf(v, 'function');
const isUndefined = (v) => isTypeOf(v, TYPE_UNDEFINED);
const isNull = (v) => isTypeOf(v, TYPE_NULL);
const isDate = (v) => isTypeOf(v, 'date');
const isRegex = (v) => isTypeOf(v, 'regexp');
const isNil = (v) => isUndefined(v) || isNull(v);

/**
 * 判断对象 or 数组是否为空
 * @param o
 * @returns {boolean}
 */
const isEmpty = (value) => {
  if (isNil(value) || (isString(value) && value === '')) {
    return true;
  } if (isArray(value)) {
    return value.length === 0;
  } if (isObject(value)) {
    return Object.keys(value).length === 0;
  } 
    return false;
  
};

export {
    isArray,
    isObject,
    isString,
    isBoolean,
    isNumber,
    isStringNumber,
    isFunction,
    isUndefined,
    isNull,
    isDate,
    isRegex,
    isNil,
    isEmpty
}
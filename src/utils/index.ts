import { v4 as uuidv4 } from 'uuid';

/**
 * 返回指定范围内的随机整数。
 * @param min - 最小值
 * @param max - 最大值
 * @returns 介于最小值和最大值之间的随机整数
 */
export function getRandomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 生成随机颜色的函数
 */
export function generateRandomColor() {
  const letters = '0123456789ABCDEF';
  let color = '#';
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
}


/**
 * 将数组中指定索引的元素移动到数组最前端，不改变原数组，返回新数组。
 * @param arr - 输入数组
 * @param index - 要移动到最前端的元素索引
 * @returns 移动后的新数组
 */
export function moveElementToFrontWithoutMutation(arr, index) {
  if (index !== -1) {
    const result = [arr[index]];
    for (let i = 0; i < arr.length; i++) {
      if (i !== index) {
        result.push(arr[i]);
      }
    }
    return result;
  } else {
    return [...arr];
  }
}

/**
 * 将指定元素移动到数组前部而不改变原数组。
 * @param arr - 输入数组
 * @param elementsToMove - 需要移动到前部的元素
 * @returns 移动元素到前部后的新数组
 */
export function moveElementsToFrontWithoutMutation(arr, elementsToMove) {
  const result = [...elementsToMove];
  for (let i = 0; i < arr.length; i++) {
    if (!elementsToMove.includes(arr[i])) {
      result.push(arr[i]);
    }
  }
  return result;
}


/**
 * 使函数等待指定的毫秒数后继续执行。
 * @param ms - 要等待的毫秒数。
 * @returns 一个 Promise，在指定的时间后解析。
 */
export function sleeping(ms: number): Promise<void> {
  return new Promise(resolve => {
    setTimeout(resolve, ms);
  });
}

/**
 * 处理好promise，返回数组[error,value]
 * 当error是null时，表示promise返回正常
 * @param {Promise}} promise
 */
export function p2Arry(promise: Promise<any>) {
  return promise.then(data => {
    return [null, data];
  }).catch(err => [err, null]);
}


/**
 * 获取一个去除连字符的 UUID 字符串
 */
export const getUuid = () => {
  const uuid = uuidv4()
  return uuid.replace(/-/g, '')
}


/**
 * 根据给定的偏移天数和日期格式返回日期字符串
 * @param daysOffset - 要偏移的天数
 * @param dateFormat - 日期格式，支持 'YYYY-MM-DD', 'YYYY-MM', 'MM-DD'
 * @returns 格式化后的日期字符串
 */
export function getDate(daysOffset: number, dateFormat: string, dateNow?: Date): string {
  const date = dateNow ? new Date(dateNow.getTime()) : new Date();
  date.setDate(date.getDate() + daysOffset);

  const year = date.getFullYear().toString();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');

  let formattedDate = '';
  switch (dateFormat) {
    case 'YYYY-MM-DD':
      formattedDate = `${year}-${month}-${day}`;
      break;
    case 'YYYY-MM':
      formattedDate = `${year}-${month}`;
      break;
    case 'MM-DD':
      formattedDate = `${month}-${day}`;
      break;
    default:
      throw new Error(`Invalid date format: ${dateFormat}`);
  }

  return formattedDate;
}

/**
 * 将日期字符串转换为"月-日"格式的日期。
 * @param dateString 日期字符串
 * @returns 格式化后的日期字符串
 */
export function convertDateFormat(dateString) {
  const date = new Date(dateString);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${month}-${day}`;
}


/**
 * 将时间戳的时分秒部分去除，返回新的时间戳。
 */
export const trimTime = (ms: number | Date) => {
  const date = new Date(ms);
  const y = date.getFullYear();
  const m = date.getMonth();
  const d = date.getDate();
  return new Date(y, m, d).getTime();
};


/**
 * 查找两个数组中根据唯一键值 uniqueKey 找出的差异对象。
 * @param array1 第一个数组
 * @param array2 第二个数组
 * @param uniqueKey 唯一键值
 * @returns 差异对象数组
 */
export function findDifferences(array1: any[], array2: any[], uniqueKey: string) {
  const map1 = new Map();
  const map2 = new Map();

  // 将数组转换为 Map
  array1.forEach(obj => map1.set(obj[uniqueKey], obj));
  array2.forEach(obj => map2.set(obj[uniqueKey], obj));

  // 查找差异
  const differences: any[] = [];
  map1.forEach((obj, key) => {
    if (!map2.has(key)) {
      differences.push(obj);
    }
  });
  map2.forEach((obj, key) => {
    if (!map1.has(key)) {
      differences.push(obj);
    }
  });

  return differences;
}

/**
 * 查找两个对象数组中指定属性值相同的对象集合
 *
 * @param array1 - 第一个对象数组
 * @param array2 - 第二个对象数组
 * @param uniqueKey - 用于比较的属性键名
 * @returns 具有相同属性值的对象集合
 */
export function findIntersection(array1: any[], array2: any[], uniqueKey: string) {
  const set1 = new Set();
  const set2 = new Set();

  // 将数组转换为 Set
  array1.forEach(obj => set1.add(obj[uniqueKey]));
  array2.forEach(obj => set2.add(obj[uniqueKey]));

  // 查找交集
  const intersection = new Set([...(set1 as any)].filter(x => set2.has(x)));

  // 将交集转换回对象数组
  const intersectionObjects = Array.from(intersection).map(key => {
    const obj1 = array1.find(o => o[uniqueKey] === key);
    const obj2 = array2.find(o => o[uniqueKey] === key);
    return obj1 || obj2; // 如果两个对象不完全相同，返回任意一个
  });

  return intersectionObjects;
}

interface MyObject {
  [key: string]: any;
}

/**
 * 比较两个数组中对象的差异并返回新增和移除的对象。
 * @param A - 第一个对象数组(新数组)
 * @param B - 第二个对象数组（旧数组）
 * @param key - 用于比较对象的键
 * @returns 新增和移除的对象数组
 */
export function compareArrays<T extends MyObject>(
  A: T[],
  B: T[],
  key: string
): { added: T[], removed: T[] } {
  const bMap = new Map<string, T>();
  for (const obj of B) {
    bMap.set(obj[key], obj);
  }

  const added: T[] = [];
  const removed: T[] = [];

  for (const obj of A) {
    if (!bMap.has(obj[key])) {
      added.push(obj);
    }
  }

  for (const obj of bMap.values()) {
    if (!A.some((aObj) => aObj[key] === obj[key])) {
      removed.push(obj);
    }
  }

  return { added, removed };
}


/**
 * 合并两个数组，根据指定键去重
 */
export function unionArrays<T extends MyObject>(
  A: T[],
  B: T[],
  key: string
): T[] {
  const set = new Set<T>();

  for (const obj of A) {
    set.add(obj);
  }

  for (const obj of B) {
    if (!Array.from(set).some((sObj) => sObj[key] === obj[key])) {
      set.add(obj);
    }
  }

  return Array.from(set);
}


/**
 * 将指定键值在数组中的元素移至数组前部。
 * @param arr - 输入数组
 * @param keysToMove - 需要移动的键值数组
 * @param keyName - 键名
 * @returns 移动后的数组
 */
export function moveElementsToFront<T extends MyObject>(
  arr: T[],
  elementsToMove: any[],
  keyName: string
): T[] {
  const result = [...elementsToMove];
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    if (!elementsToMove.some(x => x[keyName] === item[keyName])) {
      result.push(arr[i]);
    }
  }
  return result;
}


/**
 * 检查两个字符串数组是否有差异
 * @param arr1 - 第一个字符串数组
 * @param arr2 - 第二个字符串数组
 * @returns 如果有差异返回true，否则返回false
 */
export function arraysHaveDifferences(arr1: string[], arr2: string[]): boolean {
  const set1 = new Set(arr1);
  const set2 = new Set(arr2);

  if (set1.size!== set2.size) {
    return true; // 如果两个数组的长度不同，则它们一定有差异
  }

  for (const item of set1) {
    if (!set2.has(item)) {
      return true; // 如果set1中有一个元素不在set2中，则它们有差异
    }
  }

  return false; // 如果迭代完set1后没有发现任何差异，则两个数组相同
}



export function toUnicode(str) {
    if (!str || typeof str !== 'string') {
        return '';
    }

    return str.split('').map(function(char) {
        return '\\u' + ('0000' + char.charCodeAt(0).toString(16)).slice(-4);
    }).join('');
}
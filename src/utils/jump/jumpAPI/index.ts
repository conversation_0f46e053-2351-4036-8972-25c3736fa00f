/** @format */

import Taro from '@tarojs/taro';
import stringify from '@/utils/stringify';
import { JDJumping } from '@jdreact/jdreact-core-lib';


/**
 * 扩展参数类型
 * isMthod: Boolean true: 执行的router协议为方法调用而非页面；用于通过router协议调用方法时，可以提高Android端的调用性能，对iOS无效
 * isSync: string 'yes'或者'', ios特有，router有同步回调无异步回调时开启
 * success: 收到开发者服务成功返回的回调函数
 * fail: 接口调用失败的回调函数
 * complete: 接口调用结束的回调函数（调用成功、失败都会执行）
 */
export interface jumpOption {
	isMethod?: boolean;
	isSync?: string;
	success?: Function;
	fail?: Function;
	complete?: Function;
}

/**
 * @description: 跳转Router协议
 * @param {string} url router协议
 * @param {*} params 协议参数
 * @param {*} opt 扩展参数
 * opt: {
 * isMthod: Boolean true: 执行的router协议为方法调用而非页面；用于通过router协议调用方法时，可以提高Android端的调用性能，对iOS无效
 * isSync: string 'yes'或者'', ios特有，router有同步回调无异步回调时开启
 * success: 收到开发者服务成功返回的回调函数
 * fail: 接口调用失败的回调函数
 * complete: 接口调用结束的回调函数（调用成功、失败都会执行）
 * }
 *
 *
 * @return {*}
 */
const jumpToRouter = (url: string, params, opt: jumpOption): any => {
	const {
		success = (res) => {
			console.log('~~~~~~jumpToRouter-success~~~~~~~~~~~~', res);
		},
		fail = (res) => {
			console.log('~~~~~~jumpToRouter-fail~~~~~~~~~~~~', res);
		}
	} = opt;

	const _toRouterParam ={
		url,
		arg: params
	}
	JDJumping.jumpJDRouter(_toRouterParam).then(() => {
		success()
	}).catch(() => {
		fail()
	})
};

/**
 * @description: 跳转OpenApp协议
 * @param {string} openAppUrl openApp协议（完整）链接
 * @param {*} otherParams 额外参数（暂未实现）
 * @param {*} opt 扩展参数
 * @return {*}
 */
 const jumpToOpenApp = (url: string, params = {}, opt: jumpOption = {}) => {
	if (url) {
		const {
			success = (res) => {
				console.log('~~~~~~jumpToOpenApp-success~~~~~~~~~~~~', res);
			},
			fail = (res) => {
				console.log('~~~~~~jumpToOpenApp-fail~~~~~~~~~~~~', res);
				
			}
		} = opt;

		//支持直接下发协议并跳转
		if (url.indexOf('openapp') != -1) {
			JDJumping.jumpToOpenapp(url).then(() => {
				success()
			}).catch((e) => {
				fail(e)
			})
		} else {
			const param = {
				category: 'jump',
				des: url,
				...params,
			};
	
			const encodeParam = encodeURI(JSON.stringify(param));
			const dataParam = `openapp.jdmobile://virtual?params=${encodeParam}`;

			JDJumping.jumpToOpenapp(dataParam).then(() => {
				success()
			}).catch(() => {
				fail()
			})
		}
	}
};

/**
 * @description: 通过OpenApp协议跳转webview容器，加载H5界面
 * @param {string} url H5落地页链接
 * @param {*} params 页面参数
 * @param {*} opt 扩展参数
 * @return {*}
 */
const jumpToWeb = (url: string, params = {}, opt: jumpOption = {}) => {
	if (url && url.indexOf('http') > -1) {
		const {
			success = (res) => {
				console.log('~~~~~~jumpToWeb-success~~~~~~~~~~~~', res);
			},
			fail = (res) => {
				console.log('~~~~~~jumpToWeb-fail~~~~~~~~~~~~', res);
			}
		} = opt;
		let jumpUrl = url;

		if (url.indexOf('?') > -1) {
			jumpUrl = url + stringify.stringify(params, { encode: true });
		} else {
			jumpUrl = `${url}?${stringify.stringify(params, { encode: true })}`;
		}

		JDJumping.jumpToWeb(jumpUrl).then(() => {
			success()
		}).catch(() => {
			fail()
		})
	}
};


//通用跳转方法
const jump = (url: string, params = {}, opt: jumpOption = {}) => {
	console.log(`+++ jump to path: ${url}`);
	if (!url || url.length <= 0) {
		return;
	}

	console.log('跳转信息', url, params, opt)

	if (url.search(/^router:\/\//i) == 0) {
		//'router://'开头 （忽略大小写）
		jumpToRouter(url, params, opt);
	} else if (url.search(/^http/i) == 0) {
		//'http'开头  （忽略大小写）
		jumpToWeb(url, params, opt);
	} else if (url.search(/^openapp/i) == 0) {
		//'openApp'或 'openapp' 开头 （忽略大小写）
		jumpToOpenApp(url, params, opt);
	} else if (url.search(/^\//i) == 0) {
		// '/'开头，一般为小程序内部页面
		//eg: '/pages/mergeOrder/mergeOrder?promotionId=211234690034'
		Taro.navigateTo({ url });
	}
};

const jumpAPI = {
	jump,
};

export default jumpAPI;

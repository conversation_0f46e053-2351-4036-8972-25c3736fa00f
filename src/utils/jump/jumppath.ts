/**
 * 跳转静态信息配置
 * id 代表跳转链接； 京东App内，有router和openApp协议；H5内，是https链接
 * version 代表跳转链接限制版本
 */


const configSetting = {
    lifeTravelIndex: {
        rn: {
            url: 'openapp.jdmobile://virtual?params={"category":"jump","des":"jdreactcommon","appname":"JDReactLifeTravel","modulename":"JDReactLifeTravel","ishidden":true,"needLogin":false,"param":{"initPath":"/index","transparentenable":true}}',
        },
        h5: {
            url: ''
        } 
    }
}


/**
 * @description: 获取跳转配置信息
 * @param {*} name 跳转页面key
 * @return {*}
 */

const getConfigSetting = (name: string) => {
    if (name) {
        const item = configSetting[name];
        if (process.env.TARO_ENV === 'rn') {
            return item ? item.rn : undefined;
        } 
        if (process.env.TARO_ENV === 'h5') {
            return item ? item.h5 : undefined;
        } 
        return item ? item.h5 : undefined;
        
    } 
        return undefined;   
}

export {
    getConfigSetting
};

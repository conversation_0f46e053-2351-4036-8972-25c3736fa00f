/**
 * 跳转协议配置文件，在这个文件中可以看到，全站跳转的所有链接
 */


// import { ROUTERS } from "@/router/ROUTES";

import jumpAPI from './jumpAPI';
import { getConfigSetting } from "./jumppath";
import { isEmpty } from "../isType";
import { ROUTERS } from '@/router/index'

const getQueryString = (params) => {
    if (isEmpty(params)) {
        return ''
    }
    return Object.keys(params).reduce((acc, key, index, array) => {
        acc += `${key}=${params[key]}`;
        if (index < array.length - 1) {
        acc += '&';
        }
        return acc;
    }, '')
}

export const jumpConfig = (jumpData) => {
    console.log('跳转信息 jumpConfig', jumpData)

    const { to, params } = jumpData || {};
    let _urlParams
    if (!to) return;
    try {
        switch (to) {
            case 'web':
                const { url } = params || {};
                if (!url) return;
                jumpAPI.jump(url);
                break;
            case 'index':
                _urlParams = getQueryString(params)
                if (_urlParams) {
                    jumpAPI.jump(`/${ROUTERS.index}?${_urlParams}`);
                } else {
                    jumpAPI.jump(`/${ROUTERS.index}`);
                }
                break;
            case 'result':
                _urlParams = getQueryString(params)
                if (_urlParams) {
                    jumpAPI.jump(`/${ROUTERS.result}?${_urlParams}`);
                } else {
                    jumpAPI.jump(`/${ROUTERS.result}`);
                }
                break;
            case 'lifeTravelIndex':
                const jumpLink = getConfigSetting('lifeTravelIndex')
                jumpAPI.jump(jumpLink.url);
                break;
            case 'businessSearch':
                _urlParams = getQueryString(params)
                if (_urlParams) {
                    jumpAPI.jump(`/${ROUTERS.businessSearch}?${_urlParams}`);
                } else {
                    jumpAPI.jump(`/${ROUTERS.businessSearch}`);
                }
            default:
                jumpAPI.jump(url)
        }
    } catch (e) {
        console.log('跳转异常', e)
    }
}
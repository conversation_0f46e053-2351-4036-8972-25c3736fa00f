/*
 * 获取上一次的props值，或是状态值
 */

import Taro from "@tarojs/taro";
import { useEffect, useRef, useCallback, useState } from "react";
import { ROUTERS } from '@/router/index'

// 自定义钩子 usePrevious 用于获取传入值的前一个值
export const usePrevious = (value) => {
    // 使用 useRef 创建一个 ref 对象，这个对象在组件的整个生命周期内持久化
    const ref = useRef();

    // 使用 useEffect 钩子来观察 value 的变化
    useEffect(() => {
        // 每当 value 发生变化时，将其当前值赋给 ref.current
        // 这样，ref.current 就总是持有 value 的前一个值
        ref.current = value;
    }, [value]); // 依赖数组中的 value 表示这个副作用仅在 value 变化时运行

    // 返回 ref.current，即上一次的 value 值
    return ref.current;
}


type Timer = ReturnType<typeof setTimeout> | null
type SomeFunction = (...args: any[]) => void


/**
 * @method react组件内函数防抖
 * @param fn {Function}
 * @param delay {number}
 * @returns (...arg) => void
 */
// 定义一个自定义的useDebounce钩子，它接受一个函数fn和一个延迟时间delay，默认为50毫秒
export const useDebounce = <T extends SomeFunction>(fn: T, delay: number = 50): (...arg: Parameters<T>) => void => {
    // 使用useRef创建一个可变的ref对象，存储函数和定时器ID
    const { current } = useRef<{ fn: T, timer: Timer }>({
        fn,
        timer: null
    });

    // 当fn变化时，更新ref对象中存储的函数，并在组件卸载时清除定时器
    useEffect(() => {
        current.fn = fn;

        return () => {
            if (!current.timer) return;
            clearTimeout(current.timer); // 清除定时器
        }
    }, [fn]);

    // 返回一个函数，它接收与fn相同的参数
    return (...args) => {

        if (current.timer) {
            clearTimeout(current.timer); // 如果定时器已经存在，清除它
        }
        current.timer = setTimeout(() => { // 设置一个新的定时器
            current.fn(...args); // 延迟执行fn函数，并将参数传递给它
        }, delay);
    }
}


// 定义一个接口，用于引用节流函数的相关信息
export interface ThrottleRefType {
    fn: Function, // 要节流的函数
    timer?: NodeJS.Timeout // 存储定时器的ID，用于控制节流
}

// 定义一个类型，用于描述useThrottle钩子的参数
export type ThrottlePropsType = [Function, number, Array<any>]

// 自定义的useThrottle钩子，用于在React组件中实现节流功能
export const useThrottle = (fn: Function, throttle: number, deps: Array<any> = []) => {
    // 使用useRef创建一个可变的ref对象，存储函数和定时器ID
    const { current } = useRef<ThrottleRefType>({ fn })

    // 当fn变化时，更新ref对象中存储的函数
    useEffect(() => {
        current.fn = fn
    }, [fn, current])

    // 使用useCallback创建一个记忆化的回调函数
    return useCallback(
        function (this: any, ...args: any[]) {
            // 如果当前没有定时器在运行
            if (!current.timer) {
                current.fn.apply(this, args)
                // 设置一个定时器，延迟执行fn函数
                current.timer = setTimeout(() => {
                    // 执行fn函数，并将参数传递给它
                    // current.fn.apply(this, args)
                    // 执行完毕后，删除定时器ID，允许下一次节流
                    clearTimeout(current.timer)
                    current.timer = undefined;
                }, throttle)
            }
        },
        // useCallback的依赖数组，包括传入的依赖项和ref对象本身
        [...deps, current, throttle]
    )
}




/**
 * useEffect 侦听组件依赖的状态发生改变，需要执行一次，但是初始化的时候不需要执行。类似于 class componentDidUpdate
 * @param {*} cb 组件非第一次加载需要执行的方法
 * @param {*} state 依赖的状态
 */
export function useUpdateOverFirst(cb, deps: any[] = []) {

    if (!cb || typeof cb !== 'function') {
        throw new Error('useUpdateOverFirst第一个参数请传入你需要侦听依赖项执行的函数');
    }

    if (!Array.isArray(deps) || !deps?.length) {
        throw new Error('useUpdateOverFirst第二个参数必须是依赖项组成的数组，且不能为空');
    }

    const mounting = useRef(true);
    useEffect(() => {
        if (mounting.current) {
            mounting.current = false;
            return;
        }
        cb && typeof cb == 'function' && cb();
    }, deps);
}

// 强制更新组件，类似于 class 里的 forceUpdate
export function useForceUpdate() {
    const [, setValue] = useState(0);
    return () => setValue(value => ++value);
}


export const getCurrentPageRoute = (): string | undefined => {
    const pages = Taro.getCurrentPages()
    const page = pages[pages.length - 1]
    if (page?.route) {
        return page.route
    }
    return undefined
}

/**
 *  判断传入的页面路由是否是当前显示的路由
 */
// pageName 支持ROUTERS 对象的key 值 或 支持传入页面的路由
export const useIsCurrentPage = (page) => { 
    let router = page;
    if (/(pages)*\/(\w)*/g.test(page)) {
        router = page;
    } else {
        router = ROUTERS[page];
    }
   
    if (!router) {
        return false;
    }
    return router == getCurrentPageRoute()
   
}
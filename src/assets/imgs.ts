import imgUrlList from './imgUrlList';
import { getSystemInfo } from '@/utils/taroApi';

export const getImageUrl = (key: keyof typeof imgUrlList) => {
    const sytemInfo = getSystemInfo()

    if (imgUrlList[key + `@${sytemInfo.pixelRatio}x`]) {
        return imgUrlList[key + `@${sytemInfo.pixelRatio}x`];
    } else if (imgUrlList[key + '@2x']) {
        return imgUrlList[key + '@2x'];
    } else if (imgUrlList[key]) {
        return imgUrlList[key];
    }
};
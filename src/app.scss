/*  #ifdef  h5 jd weapp */
@font-face {
    font-family: "JDZhengHT-Regular";
    src: url("https://storage.360buyimg.com/ltfe-static/JDZhengHT.ttf");
}
/*  #endif  */

@import "./assets/theme.scss";

.flex1 {
    flex: 1;
}

.row {
    flex-direction: row;
}

.wrap {
    flex-wrap: wrap;
}

.column {
    flex-direction: column;
}

.center {
    align-items: center;
    justify-content: center;
}

.w100 {
    width: 100%;
}

.h100 {
    height: 100%;
}

.relative {
    position: relative;
}

/*  #ifdef rn  */
.bold {
    @if $os-type == 'ios' {
        font-weight: 500;
    }

    @if $os-type == 'android' {
        font-weight: bold;
    }
}
/*  #endif  */

/*  #ifdef  H5 jd weapp */
.bold {
    /* default styles */
    font-weight: bold;
}
  
:root[style*="--os-type: android"] .bold {
  font-weight: bold;
}

:root[style*="--os-type: ios"] .bold {
  font-weight: 500;
}
/*  #endif  */



.primaryHLTextColor {
    color: var(--primaryHLTextColor);
}

.primaryTextColor {
    color: var(--primaryTextColor)
}
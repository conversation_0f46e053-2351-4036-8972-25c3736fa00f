import {useEffect, useState} from 'react'
import useFetch from '@/common/useFetch'
import _ from 'lodash'
import globalInfoModel from '@/store/model/globalInfo.model'

const EventCatch = (Comps) => (props) => {
    const [isLoad, setIsLoad] = useState(false)
    const [catchData, setCatchData] = useState<unknown>({})
    const {apiFetch} = useFetch()

    const {preLoad} = Comps

    const afterActions = (actions) => new Promise((res, rej) => {
        Promise.all(actions).then(res).catch(rej)
    })

    useEffect(() => {
        if (typeof preLoad === 'function') {
            const preLoadAction = preLoad({
                ...props,
                apiFetch,
                afterActions,
                urlParams: _.get(globalInfoModel, 'staticParams', {})
            })
            if (preLoadAction instanceof Promise) {
                preLoadAction.then((res) => {
                    setCatchData(res)
                    setIsLoad(true)
                }).catch(error => {
                    setCatchData(error)
                    setIsLoad(true)
                }).finally(() => {
                    setIsLoad(true)
                })
            } else {
                setIsLoad(true)
            }
        } else {
            setIsLoad(true)
        }
    }, [])

    return isLoad ? <Comps {...props} {...catchData}/> : null
}

export default EventCatch

import {loadPath, jointMatch} from './utils'

const Mapper = new Map()

const indexMapper = {}

Mapper.set('Mapper', indexMapper)

export const extendsMapper = (extendsMappers = {}, type = 'Mapper') => {
    const _Mapper = Mapper.get(type)
    Mapper.set(type, {
        ..._Mapper,
        ...extendsMappers
    })
}

export default (type: string) => {
    return type ? Mapper.get(type) : Mapper.get('Mapper')
}

const empty = hiddenEmpty => (hiddenEmpty ? <></> : <div style={{textAlign: 'center', padding: 100}}>敬请期待</div>)

export const DSLParse = (schema: any, config = {}) => {
    const {hiddenEmpty = true, MapperType = 'Mapper', Node = (comp) => <>{comp}</>} = config
    return Array.isArray(schema) && schema.length > 0
        ? schema.map(item => MapperComp(item, config)) : empty(hiddenEmpty)
}

export const MapperComp = (compsInfo: any, config?: any) => {
    const {MapperType = 'Mapper', actions, ...other} = config
    const {type, props, propsPaths, joint} = compsInfo

    if(Array.isArray(joint) && !jointMatch(joint, other.storage)) {
        return null
    }

    const _MapComps = Mapper.get(MapperType) || {}
    const MapComps = _MapComps[type]
    return <MapComps
        actions={actions}
        {...props}
        {...loadPath(other, propsPaths)}
    />
}

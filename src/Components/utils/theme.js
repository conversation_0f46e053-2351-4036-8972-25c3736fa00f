import BaseColor, { BaseFont } from '@ltfe/ltfe-core-lib/lib/utiles/theme';

// 首页
class IndexPageColor extends BaseColor {}
// 酒店列表页
class ListPageColor extends BaseColor {}
// 订单列表页
class OrderListPageColor extends BaseColor {}
// 定位城市页
class LoactionPageColor extends BaseColor {}
// 详情页
class DetailPageColor extends BaseColor {}
// 填单页
class OrderPageColor extends BaseColor {}
// 订单详情页
class OrderDetailPageColor extends BaseColor {}

export const THEME_ORDER_DETAIL = new OrderDetailPageColor();
export const THEME_DETAIL = new DetailPageColor();
export const THEME_ORDER = new OrderPageColor();
export const THEME_INDEX = new IndexPageColor();
export const THEME_LIST = new ListPageColor();
export const THEME_ORDERLIST = new OrderListPageColor();
export const THEME_LOCATION = new LoactionPageColor();
export const THEME_BASE = new BaseColor();
export const THEME_FONT = new BaseFont();

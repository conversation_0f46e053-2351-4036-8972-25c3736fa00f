import _ from 'lodash'

export default class LRU {
    constructor( info = {} ) {
        const {length = 20, defaultCatch = []} = info
        this.len = length
        this.catch = defaultCatch
    }

    // 操作缓存
    put = ( id, info, setKey = 'id' ) => {
        let findIndex = -1, res = info

        this.catch.some(( item, index ) => {
            if ( _.get(item, setKey) === id ) {
                findIndex = index
                res = item
            }
            return _.get(item, setKey) === id
        })

        // 如果存在
        if ( findIndex > -1 ) {
            // 将获取到的挪到第一位，并按len截取
            this.catch = [info].concat(this.catch.slice(0, findIndex)).concat(this.catch.slice(findIndex + 1, this.len))
        } else {
            // 头插并截取
            this.catch = [info].concat(this.catch).slice(0, this.len)
        }

        return {
            info: res,
            list: this.catch
        }
    }

    set = (setCatch)=> {
        this.catch = setCatch
    }
}

import { v4 } from 'uuid'
import _ from 'lodash'
import { Map } from 'immutable'
import dayjs from 'dayjs'
import { encrypt, getTimeZone } from '@ltfe/ltfe-core-lib/lib/utiles'

export const getTraceId = ( context ) => {
    return `${ context }${ v4(8) }`
}

/*
 *  loadPath会对props进行捞取
 *  默认storage为immutable类型
 *  immutable可以差量更新render
 * */
export const loadPath = ( value, valuePaths = {} ) => {
    const res = {}
    Object.keys(valuePaths).forEach(key => {
        const _valuePath = valuePaths[key], storage = _.get(value, 'storage')
        const isStorage = _valuePath[0] === 'storage' && Map.isMap(storage)
        if ( isStorage ) {
            _.set(res, key, storage.getIn(_valuePath.slice(1, Number.MAX_SAFE_INTEGER)))
        } else {
            _.set(res, key, _.get(value, _valuePath))
        }
    })
    return res
}

// 更新字段byKey
export const updateForStorage = ( target, value = {}, propsPaths = false ) => {
    let res = null
    // immutable更新
    if ( Map.isMap(target) ) {
        res = target.update(propsPaths, ( curValue ) => {
            // 是对象就合并，不是就返回值
            return typeof value === 'object' ? Object.assign({}, curValue, value) : value
        })
    }
    return res
}

// 更新字段byKeys
export const updateForStorageByObj = ( target, value = {} ) => {
    let res = target
    // immutable更新
    if ( Map.isMap(res) ) {
        Object.keys(value).forEach(key => {
            const _value = _.get(value, key)
            res = res.update(key, ( curValue ) => {
                if ( Array.isArray(curValue) || Array.isArray(_value) ) {
                    return _value
                } else {
                    // 是对象就合并，不是就返回值
                    return typeof _value === 'object' ? Object.assign({}, curValue, _value) : _value
                }
            })
        })
    }

    return res
}

// 浅合并
export const mergeForStorage = ( target, value = {} ) => {
    let res = null
    // immutable更新
    if ( Map.isMap(target) ) {
        res = target.merge(Map(value))
    }
    return res
}

// 深合并
export const mergeDeepForStorage = ( target, value = {} ) => {
    let res = null
    // immutable更新
    if ( Map.isMap(target) ) {
        res = target.mergeDeep(Map(value))
    }
    return res
}

// 数据格式化方法
export const format = ( params, format, pick ) => {
    let res = _.cloneDeep(params) || {}
    if ( Array.isArray(format) && format.length > 0 ) {
        format.forEach(key => {
            const {toType, value} = key
            const _value = _.isFunction(_.get(value, toType)) ? toType(value) : value
            if ( key.setKey ) {
                _.set(
                    res,
                    key.setKey,
                    _.get(params, key.formatValue, _value)
                )
            } else if ( key.setToObj ) { // 转成Obj
                res = {}
                _.set(
                    res,
                    key.setToObj,
                    params
                )
            } else {
                if ( typeof _.get(params, key.formatValue, _value) === 'object' ) {
                    res = {
                        ...res,
                        ..._.get(params, key.formatValue, _value)
                    }
                } else {
                    res = _.get(params, key.formatValue, _value)
                }
            }
        })
    }

    if ( Array.isArray(pick) && pick.length > 0 ) {
        return _.pick(res, pick)
    }

    return res
}

export const safeJsonParse = ( jsonStr ) => {
    let res = {}
    try {
        return JSON.parse(jsonStr)
    } catch ( error ) {
        console.warn(error)
    }

    return res
}

export const safeBase64Parse = ( base64Parse ) => {
    let res = {}
    try {
        return typeof encrypt.Base64Decode(base64Parse) === 'object' ? encrypt.Base64Decode(base64Parse) : {}
    } catch ( error ) {
        console.warn(error)
    }

    return res
}

export const debSafeBase64Parse = ( base64Parse ) => {
    let res = {}
    try {
        return typeof encrypt.Base64Decode(encrypt.Base64Decode(base64Parse)) === 'object' ? encrypt.Base64Decode(encrypt.Base64Decode(base64Parse)) : {}
    } catch ( error ) {
        console.warn(error)
    }

    return res
}

export const deCodeDebBase64ParseSafe = ( base64Parse ) => {
    let res = {}
    const _base64Parse = decodeURIComponent(base64Parse)
    try {
        return typeof encrypt.Base64Decode(encrypt.Base64Decode(_base64Parse)) === 'object' ? encrypt.Base64Decode(encrypt.Base64Decode(_base64Parse)) : {}
    } catch ( error ) {
        console.warn(error)
    }

    return res
}

export const enCodeDebBase64ParseSafe = ( jsonStr ) => {
    let res = ''

    try {
        return encodeURIComponent(encrypt.Base64Encode(encrypt.Base64Encode(jsonStr)))
    } catch ( error ) {
        console.warn(error)
    }

    return res
}


export const debBase64ParseSafe = ( jsonStr ) => {
    let res = ''

    try {
        return encodeURIComponent(encrypt.Base64Encode(encrypt.Base64Encode(jsonStr)))
    } catch ( error ) {
        console.warn(error)
    }

    return res
}

export const inZtoSixDay = ( time ) => {
    return dayjs(time).isValid() ? dayjs(time).isSame(dayjs(), 'day') && dayjs(time).isBefore(dayjs().startOf('day').add(6, 'hour')) : false
}

export const beforeSixHourAndDay = ( time ) => {
    return dayjs(time).isValid() ? (inZtoSixDay(time) || dayjs().isBefore(dayjs(time), 'day')) : false
}

export const checkValidDate = ( checkInDate ) => {
    const today = getTimeZone();
    const yesterday = getTimeZone().subtract(1, 'days')

    if ( today.hour() < 6 ) {
        if(yesterday.isAfter(checkInDate, 'day')) {
            return false
        } else {
          return true
        }
    } else {
        if(today.isAfter(checkInDate, 'day')) {
            return false
        } else {
          return true
        }
    }
}

export const getDefaultDate = () => {
    // 今早12点
    const today = getTimeZone()
    // 昨天
    const yesterday = getTimeZone().subtract(1, 'days')
    // 明天
    const tomorrow = getTimeZone().add(1, 'days')
    if ( today.hour() < 6 ) {
        return {
            checkInDate: yesterday.format('YYYY-MM-DD'),
            checkOutDate: today.format('YYYY-MM-DD')
        }
    } else {
        return {
            checkInDate: today.format('YYYY-MM-DD'),
            checkOutDate: tomorrow.format('YYYY-MM-DD')
        }
    }
}

export const isSameWithByKey = ( oldObj = {}, newObj = {}, keyPaths = [] ) => {
    let res = true
    keyPaths?.some(( key ) => {
        let oldValue = _.get(oldObj, key)
        let newValue = _.get(newObj, key)
        if ( Array.isArray(oldValue) && Array.isArray(newValue) ) {
            if ( !_.isEqual(oldValue.sort(), newValue.sort()) ) {
                res = false
                return true
            }
        } else {
            if ( changeValid(oldValue) !== changeValid(newValue) ) {
                res = false
                return true
            }
        }

    })
    return res
}

export const changeValid = ( value ) => {
    if ( value === undefined || value === null || value === '' ) {
        return false
    }
    return value
}

export const isStrValue = ( str ) => {
    return typeof str === 'string' && str !== '' || typeof str === 'number'
}

export const isObjValue = ( obj ) => {
    return typeof obj === 'object' && !Array.isArray(obj) && Object.keys(obj).length > 0
}

export const isDateValue = ( date ) => {
    return dayjs(date).isValid()
}

export const jointMatch = ( joint, _params ) => {
    const params = _.isFunction(_params.getIn) ? _params.toJS() : _params
    let res = true, nextLevelRes = true

    // 数组便利，或条件
    joint.some(jointItem => {
        const {keys, value, symbol, valueType} = jointItem
        const _joint = jointItem.joint
        // console.log('aabbcc', jointItem)
        // 递归校验下级joint，并条件。
        if ( Array.isArray(_joint) ) {
            nextLevelRes = jointMatch(_joint, params)
        }

        // 不相等
        if ( symbol === 'unequal' ) {
            if ( valueType ) {
                if ( valueType === 'string' ) {
                    res = !isStrValue(_.get(params, keys))
                    return res
                }
            } else {
                const flag = value !== _.get(params, keys)
                res = flag
                return flag
            }
        } else if ( symbol === '>' ) {   // 大于
            const flag = value < _.get(params, keys)
            res = flag
            return flag
        } else { // 等于
            if ( valueType ) {
                if ( valueType === 'string' ) {
                    res = isStrValue(_.get(params, keys))
                    return res
                }
            } else {
                const flag = value === _.get(params, keys)
                res = flag
                return flag
            }
        }
    })

    return res && nextLevelRes
}

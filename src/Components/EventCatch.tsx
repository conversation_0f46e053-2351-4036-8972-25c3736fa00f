import _ from 'lodash'
import {safeRun} from '@/Components/Filter/utils'
import {format as _format} from '@/Components/utils/index'
import React from 'react'

const EventCatch = (Comps) => React.forwardRef((props, ref) => {
    const {eventTypes, actions} = props

    const middleWare = (func, eventKey, args) => {
        const {format, pick, mta = {}} = _.get(eventTypes, eventKey)
        const {type, eventId} = mta
        // TODO 这里通知埋点
        if (type) {
            const {format, pick} = mta
            const eventData = _format(args, format, pick)
            safeRun(_.get(actions, type), {
                eventData,
                eventId: eventData?.eventId || eventId
            })
        }

        // 这里是做事件通知
        safeRun(func, _format(args, format, pick))
    }

    // 处理子组件event
    const getEventCatch = (eventTypes) => {
        const events = {}
        if (Array.isArray(eventTypes)) {
            events['onChange'] = (args) => middleWare(_.get(actions, eventTypes), 'onChange', args)
        } else {
            if (typeof eventTypes === 'object') {
                Object.keys(eventTypes).forEach(eventKey => {
                    if (Array.isArray(eventTypes[eventKey])) {
                        events[eventKey] = (args) => middleWare(_.get(actions, eventTypes[eventKey]), eventKey, args)
                    } else {
                        events[eventKey] = (args) => middleWare(_.get(actions, _.get(eventTypes, [eventKey, 'keys'])), eventKey, args)
                    }
                })
            }
        }
        return events
    }

    return <Comps ref={ref} {..._.omit(props, ['mta', 'mtaExp', 'format', 'pick'])}
                  mtaExpo={actions?.mtaExpo} {...getEventCatch(eventTypes)}/>
})

export default EventCatch

import _ from 'lodash'
import React from 'react'
import { dpr, isIOS } from '@ltfe/ltfe-core-lib/lib/utiles'
import filterImg from './filter.json'
import { Platform } from 'react-native'
import dayjs from 'dayjs'

export const objSetMapperObj = ( obj = {}, otherObj ) => {
    const res = {...otherObj}
    Object.keys(obj).forEach(key => {
        res[key] = _.get(otherObj, obj[key], '')
    })
    return res
}

export const objGetMapperObj = ( obj = {}, otherObj ) => {
    const res = {}
    Object.keys(obj).forEach(key => {
        res[key] = _.get(otherObj, obj[key], '')
    })
    return res
}

export const formatDistance = distance => {
    let text = ''

    distance = distance || 0

    if ( distance < 1000 ) {
        text = distance + '米'
    } else {
        text = parseFloat((distance / 1000).toFixed(2)) + '公里'
    }

    return text
}

export const filterGeoName = ( name, distance ) => {
    const textRightLen = distance?.length || 0
    const maxLen = 17 - textRightLen

    return name && name.length >= maxLen ? `${ name.slice(0, maxLen) }...` : name
}

export const rangeText = ( score, sourceMap ) => {
    let text = ''
    Object.keys(sourceMap).forEach(scoreRange => {
        if ( _.inRange(score, ...scoreRange.split('|')) ) {
            text = sourceMap[scoreRange]
        }
    })
    return text
}

export const safeRun = ( func, ...args ) => {
    if ( _.isFunction(func) ) {
        func(...args)
    } else {
        console.warn('Utils: safeRun func is not a function!')
    }
}

export const isComps = ( element ) => {
    return React.isValidElement(element) || _.isFunction(element)
}

export const safeParseJSON = ( jsonStr ) => {
    try {
        return JSON.parse(jsonStr)
    } catch ( e ) {
        console.log('safeParseJSON: run failed please check json')
        return {}
    }
}

export const textOverflow = ( text, count ) => {
    if ( text.length > count ) {
        return `${ text.slice(0, count - 2) }...`
    } else {
        return text
    }
}

export const formatFightTime = ( timeStr ) => {
    if ( typeof timeStr === 'string' ) {
        return timeStr.replace(/(\d{2})/, '$1:')
    } else {
        return '--:--'
    }
}

export const formatMinCount = ( minCount ) => {
    return `${ Math.floor(minCount / 60) > 0 ? `${ Math.floor(minCount / 60) }时` : '' }${ minCount % 60 }分`
}

export const calcListCount = ( list, fieldKey ) => {
    let res = 0
    for ( let i = 0 ; i < list.length ; i++ ) {
        if ( fieldKey ) {
            res += _.get(list, [i, fieldKey])
        } else {
            res += _.get(list, [i])
        }
    }
    return res
}

export const isSameDay = ( day, diffday ) => {
    return ~~((new Date(day) - new Date(diffday)) / 864e5)
}

export const complement = num => num < 10 ? '0' + num : num

export const formatDate = ( day, formatType ) => {
    if ( day === 'null' ) { return '' }
    // let dayS = typeof day === 'string' ? day.replace(/-/g, '/') : day;
    const dateF = new Date(day)
    let result

    const y = dateF.getFullYear()

    const m = dateF.getMonth() + 1

    const d = dateF.getDate()

    const h = dateF.getHours()

    const min = dateF.getMinutes()

    const ms = dateF.getSeconds()

    const getDay = dateF.getDay()

    const week = ['日', '一', '二', '三', '四', '五', '六']

    switch ( formatType ) {
        case '-':
            result = complement(m) + '-' + complement(d)
            break
        case '/':
            result = y + '/' + complement(m) + '/' + complement(d)
            break
        case 'A':
            result = `${ y }-${ complement(m) }-${ complement(d) } ${ h }:${ min }:${ ms }`
            break
        case '.':
            result = `${ y }.${ complement(m) }.${ complement(d) }`
            break
        case 'week':
            result = '周' + week[getDay]
            break
        case 'month':
            result = `${ complement(m) }-${ complement(d) }`
            break
        case 'hour':
            result = `${ complement(h) }:${ complement(min) }`
            break
        default:
            result = complement(m) + '月' + complement(d) + '日'
    }
    return result
}

export const hasValue = ( text ) => {
    return text !== null && text !== '' && text !== undefined
}

export const clear_PObj = ( obj ) => {
    Object.keys(obj).map(key => {
        obj[key] = null
    })
}

export const findInListKey = ( _list = [], value, marchKey = 'filterPanelCode', defaultValue = null ) => {
    let list = _.cloneDeep(_list)
    for ( let i = 0 ; i < list.length ; i++ ) {
        if ( _.get(list, [i, marchKey]) === value ) {
            return _.get(list, [i])
        }
    }
    // 兜底出null
    return defaultValue
}

export const fieldMap = ( before, mapper ) => {
    const res = {}
    res[mapper.label] = _.get(before, mapper.label)
    res[mapper.value] = _.get(before, mapper.value)
    return {
        ...before,
        ...res
    }
}

export const formatForOnlyCode = ( values = {} ) => {
    const res = {}
    const keyList = Object.keys(values)
    if ( keyList.length > 0 ) {
        keyList.forEach(key => {
            const selectInfo = values[key] || {}
            let formatInfo = {}
            Object.keys(selectInfo).forEach(selectInfoItemKey => {
                if ( Array.isArray(selectInfo[selectInfoItemKey]) ) {
                    formatInfo[selectInfoItemKey] = selectInfo[selectInfoItemKey].map(item => {
                        return item.itemId
                    })
                } else {
                    formatInfo = _.get(selectInfo, ['itemId'])
                }
            })
            res[key] = formatInfo
        })
    }
    return res
}

export const formatForArray = ( value = {} ) => {
    return Object.keys(value).reduce(( pre, cur ) => {
        return pre.concat(...Object.values(value[cur]))
    }, [])
}

const imgUrlList = filterImg// 所有图片

export const getImg = ( key ) => {
    if ( imgUrlList[key + `@${ dpr }x`] ) {
        return imgUrlList[key + `@${ dpr }x`]
    } else if ( imgUrlList[key + '@2x'] ) {
        return imgUrlList[key + '@2x']
    } else if ( imgUrlList[key] ) {
        return imgUrlList[key]
    }
}

export const getFirstIndex = ( children, value ) => {
    let res = 0

    children.forEach(( item = {}, index ) => {
        const subChildren = item.children || []
        subChildren.some(subItem => {
            const {filterType} = subItem
            const _value = _.get(value, filterType)
            if ( _value && Array.isArray(_value) && _value.length > 0 ) {
                res = index
                return true
            }
        })
    })

    return res
}

export const objToList = ( obj, removeKey = [] ) => {
    return Object.keys(obj).filter(( key ) => !removeKey.includes(key)).reduce(( pre, cur ) => {
        return pre.concat(Array.isArray(obj[cur]) ? obj[cur].map(subItem => {
            _.set(subItem, 'metaData.filterKey', cur)
            return {
                ...subItem,
                filterKey: cur
            }
        }) : [])
    }, [])
}

export const listGroupByToObj = ( list = [], setKey = 'hotel_filter' ) => {
    let res = {}
    res[setKey] = []
    if ( Array.isArray(list) ) {
        list.forEach(item => {
            res[setKey].push(item)
        })
    }
    return res
}

export const listToObjByKey = ( list, filterKey ) => {
    let res = {}
    if ( Array.isArray(list) ) {
        list.forEach(item => {
            const setKey = _.get(item, filterKey)
            const setValue = _.get(res, setKey, [])
            if ( setKey ) {
                setValue.push(item)
            }
            res[setKey] = setValue
        })
    }

    return res
}

export const findIndexByKey = ( list, value = {}, key, isBool = false ) => {
    let res = 0
    list.some(( item = {}, index ) => {
        if ( _.get(item, [key], '') === _.get(value, [key], '') ) {
            res = index
        }
        return _.get(item, [key], '') === _.get(value, [key], '')
    })

    return res
}

export const findIndexByKeyValue = ( list, value, key, isBool = false ) => {
    let res = 0
    list.some(( item = {}, index ) => {
        if ( _.get(item, key, '') === value ) {
            res = index
        }
        return _.get(item, key, '') === value
    })

    return res
}

export const hasValueInArrayByKey = ( list, _item, key ) => {
    return list.some(item => _.get(item, key, 'arrayItem') === _.get(_item, key, 'targetItem'))
}

export const scrollTo = ( refs, list, value, key = 'uuId', index ) => {
    try {
        refs.current.scrollToLocation({
            sectionIndex: typeof index === 'number' ? index : findIndexByKey(list, value, key), // 这是部分的索引
            itemIndex: Platform.OS === 'web' && isIOS ? 0 : 1, // 这是项目的索引
            viewPosition: 0, // 0是顶部，0.5是中间，1是底部
            animated: true, // 是否开启动画滚动
            viewOffset: isIOS ? 0 : 43
        })
    } catch ( error ) {
        console.log(error)
    }
}

export const arrayDiffByKey = ( array1, array2, key = 'sameKey' ) => {
    return !Array.isArray(array2) || !Array.isArray(array1) || array2.length !== array1.length || _.differenceBy(array1, array2, key).length > 0
}

export const duplicateByKey = ( list = [], byKey = 'sameKey' ) => {
    let res = {}

    if ( Array.isArray(list) ) {
        list.forEach(item => {
            _.set(res, [_.get(item, byKey)], item)
        })
    }

    return Object.values(res)
}

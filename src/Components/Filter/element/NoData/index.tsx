import { getImageUrl } from "@/assets/imgs";
import { View, Image, Text } from "@/BaseComponents/atoms";
import styles from './index.module.scss'
import classNames from "classnames";
import withClassName from "@/BaseComponents/atoms/utils/withClassName";
// import { useSafeAreaInsets } from "react-native-safe-area-context";

function NoData(props) {
    const { style, text = "未找到符合条件的结果，请更改条件重新搜索" } = props

    return (
        <View className={classNames("flex1 column center", styles.wr)} style={style}>
            <Image className={styles.emptyIcon} src={getImageUrl('emptyIcon')} />
            <Text className={styles.txt}>
                {text}
            </Text>
        </View>
    )
}

export default withClassName()(NoData)

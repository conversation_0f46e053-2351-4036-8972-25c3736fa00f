import React, { useEffect, useState, useRef} from 'react'
import { StyleSheet, View, ScrollView } from 'react-native'
import _ from 'lodash'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import Select from './FilterSelect'
import { duplicateByKey } from '../utils'

const filterChildren = ( _options, level ) => _options.filter(item => item.level === level)

const getSubChildren = ( _options, _selected, startLevel ) => _options.filter(item => {
    return _.get(_selected, [0, 'childrenSameKey'], '').indexOf(_.get(item, ['sameKey'])) > -1 && item.level > startLevel
})

const getSelectedValue = ( filterList, value, defaultValue ) => {
    if ( value ) {
        const getChildren = filterList.filter(item => item?.sameKey === value.parents?.sameKey)
        const getSubChildren = () => filterList.filter(item => _.get(item, 'childrenSameKey', '').indexOf(value.parents?.sameKey) > -1)
        if ( getChildren.length > 0 ) {
            return getChildren
        }
        if ( Array.isArray(getSubChildren()) && getSubChildren().length > 0 ) {
            return getSubChildren()
        }
        return defaultValue
    } else {
        return defaultValue
    }
}

const Cascader = ( props ) => {
    const {options, maxDeep = 1, startLevel, maxHeight, value} = props
    const [filterList, setFilterList] = useState(filterChildren(options, startLevel))
    const [selected, setSelectType] = useState(getSelectedValue(filterList, _.get(value, [0]), [_.get(filterList, [0])]))
    const [isTab, setIsTab] = useState(+maxDeep > 1)
    const rightRef = useRef(null)

    useEffect(() => {
        setFilterList(filterChildren(options, startLevel))
        setIsTab(+maxDeep > 1)
    }, [options])

    useEffect(() => {
        setSelectType(getSelectedValue(filterList, _.get(value, [0]), [_.get(filterList, [0])]))
    }, [filterList])

    const [subFilter, setSubFilter] = useState(getSubChildren(options, selected))

    useEffect(() => {
        setSubFilter(getSubChildren(options, selected, startLevel))
        // 判断selected是否发生变化
        // 每次选中后，都滚动到顶部
        if ( rightRef?.current?.scrollTo ) {
            rightRef.current.scrollTo({y: 0, animated: false})
        }
    }, [selected])

    return (
        <View style={ [styles.cascaderBox, {height: maxHeight}] }>
            <ScrollView style={ [isTab ? styles.tabBox : styles.selectBox, {height: maxHeight}] }
                        ref={rightRef}
                        showsVerticalScrollIndicator={ false } bounces={ false }
                        overScrollMode="never">
                <Select
                    options={ duplicateByKey(filterList) }
                    value={ isTab ? selected : props.value }
                    showSelectImage={ !isTab }
                    itemStyle={ {fontWeight: 400} }
                    onChange={ ( value ) => {
                        if ( isTab ) {
                            setSelectType([value])
                        } else {
                            props.onChange(value)
                        }
                    } }
                    showLine={ isTab }/>
            </ScrollView>
            {
                Array.isArray(subFilter) && subFilter.length > 0 ?
                    <ScrollView style={ [styles.cascaderSubBox, {height: maxHeight}] }
                                showsVerticalScrollIndicator={ false } bounces={ false }
                                overScrollMode="never">
                        <Select
                            options={ duplicateByKey(subFilter) }
                            value={ props.value }
                            showSelectImage={ true }
                            showLine={ false }
                            maxWordCount={ 8 }
                            itemStyle={ {fontWeight: 400, width: pt(118)} }
                            onChange={ props.onChange }/>
                    </ScrollView>
                    : null
            }
        </View>
    )
}

export default Cascader

const styles = StyleSheet.create({
    cascaderBox: {
        flexDirection: 'row',
        flexGrow: 0,
        display: 'flex',
        paddingLeft: pt(12),
        paddingRight: pt(12)
    },
    selectBox: {
        width: pt(263),
        backgroundColor: '#fff',
        maxHeight: pt(375),
    },
    tabBox: {
        width: pt(104),
        backgroundColor: '#fff',
        maxHeight: pt(375),
        position: 'relative'
    },
    cascaderSubBox: {
        width: pt(160),
        paddingLeft: pt(12),
        maxHeight: pt(375),
    }
})

import React, { useState, useRef, useEffect } from 'react'
import { SectionList, StyleSheet, View } from 'react-native'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import Select from './_FilterSelect'
import TagList from './FilterTagList'
import { scrollTo } from '../utils'
import _ from 'lodash'

const getSelectedValue = ( filterList, value, defaultValue ) => {
    if ( value ) {
        return filterList.filter(item => item?.sameKey === value.parents?.sameKey)
    } else {
        return defaultValue
    }
}

const getActValue = ( filterList, value, defaultValue ) => {
    if ( value ) {
        return filterList.filter(item => item?.sameKey === value?.sameKey)
    } else {
        return defaultValue
    }
}

const TabScroll = ( props ) => {
    const {filterInfo, value} = props
    const [filterList] = useState(filterInfo?.filter(item => item.level === 1) || [])
    const Selection = useRef(null)
    const [selectIndex, setSelectIndex] = useState(getSelectedValue(filterList, _.get(value, [0]), [_.get(filterList, [0])]))
    const [scrollFlag, setScrollFlag] = useState(false)
    const [sectionData] = useState(filterInfo?.filter(item => item.level === 2).map(item => {
        return {
            ...item,
            data: [item.data]
        }
    }) || [])

    const setSelect = ( value ) => {
        setScrollFlag(true)
        setSelectIndex([value])
        scrollTo(Selection, sectionData, value, 'sameKey')
        setTimeout(() => {
            setScrollFlag(false)
        }, 600)
    }

    useEffect(() => {
        // 当被展开时，该状态只能被渲染时获取
        const selectParent = _.get(value, [0, 'parents'])
        setTimeout(() => {
            scrollTo(Selection, sectionData, selectParent, 'sameKey')
        }, 300)
    }, [props.showFilterType])

    return (
        <View style={ styles.tabScroll }>
            <>
                <Select options={ filterList } showsVerticalScrollIndicator={ false } bounces={ false }
                        overScrollMode="never"
                        selected={ value }
                        value={ selectIndex }
                        scrollFlag={ scrollFlag }
                        onChange={ setSelect }/>

                <View style={ styles.tabScrollBox }>
                    <SectionList style={ styles.tagBox } ref={ Selection } showsVerticalScrollIndicator={ false }
                                 bounces={ false }
                                 overScrollMode="never"
                                 sections={ sectionData }
                                 onViewableItemsChanged={ ( {changed, viewableItems} ) => {
                                     const showItem = _.get(viewableItems, [0, 'section'])
                                     const findParentIndex = getActValue(filterList, showItem)
                                     if ( Array.isArray(findParentIndex) && findParentIndex.length > 0 && !scrollFlag ) {
                                         setSelectIndex(getActValue(filterList, showItem))
                                     }
                                 } }
                                 renderItem={ ( {item, section} ) => {
                                     const {info} = section
                                     return <TagList title={ section.title } { ...info } value={ value }
                                                     onChange={ ( value ) => {
                                                         props.onChange(value)
                                                     } } data={ item } width={ 270 }/>
                                 } }/>
                </View>
            </>

        </View>
    )
}

export default TabScroll

const styles = StyleSheet.create({
    tabScroll: {
        flexDirection: 'row',
        flexGrow: 0,
        display: 'flex',
        backgroundColor: '#fff',
        justifyContent: 'space-between'
    },
    tagBox: {
        padding: pt(8),
        maxHeight: pt(375),
        height: pt(375)
    }
})

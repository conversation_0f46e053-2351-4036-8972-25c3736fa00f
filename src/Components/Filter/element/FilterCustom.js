import React, { useEffect, useState } from 'react'
import { StyleSheet, View } from 'react-native'
import _ from 'lodash'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import { isEmpty } from '@/utils/isType'
import Price from './price/index.hook'
import FilterTagList from './FilterTagList'
import { THEME_BASE } from '../../utils/theme'

import { Loading } from '@/BaseComponents/atoms'
import { findIndexByKeyValue } from '../utils'
import JDText from '../../../BaseComponents/atoms/Text/index.rn'

const Select = ( props ) => {
    const {filterInfo, value = [], onChange, showFilterType} = props
    const [filterList] = useState(filterInfo?.filter(item => item.level === 1) || [])
    // const [price, stars] = filterList
    const price = filterList.find((item) => {
        return item.uuId.indexOf('hotel_price') !== -1
    }) || {}
    const stars = filterList.find((item) => {
        return item.uuId.indexOf('hotel_grade') !== -1
    }) || {}
    const [starInfo] = useState(filterInfo[findIndexByKeyValue(filterInfo, 'hotel_grade', ['metaData', 'groupCode'])])
    const [selfValue, setSelfValue] = useState(false)
    const [customValue, setValue] = useState({})
    useEffect(() => {
        if ( !selfValue ) return
        const newValue = Object.keys(selfValue).reduce(( pre, cur ) => pre.concat(selfValue[cur]), [])
        onChange(newValue)
    }, [selfValue])

    useEffect(() => {
        const _customValue = {
            price: [],
            grade: []
        }
        Array.isArray(value) && value.forEach(item => {
            let key = _.get(item, 'sameKey', '')
            if ( key.indexOf('price') > -1 ) {
                _.get(_customValue, 'price', []).push(item)
            } else {
                _.get(_customValue, 'grade', []).push(item)
            }
        })
        setValue(_customValue)
    }, [props.showFilterType, props.value])

    // TODO 这里写死了。因为Custom没有下发styleID，枚举都枚举不了，应该用sameKey + styleId枚举组件，数据映射。@xy
    return (
        <View style={ styles.selectBox }>
            {
                filterInfo ?
                    <>
                        {
                            !isEmpty(price) && 
                            <Price children={ filterInfo.filter(item => item.parents && item.parents.uuId === price.uuId) }
                                value={ _.get(customValue, 'price', []) }
                                showFilterType={ showFilterType }
                                onChange={ ( _value ) => {
                                    const [min, max] = _.get(_value, ['metaData', 'itemId'], ',').split(',')
                                    if ( ~~min === ~~max ) {
                                        setSelfValue({
                                            ...customValue,
                                            price: []
                                        })
                                    } else {
                                        setSelfValue({
                                            ...customValue,
                                            price: [_value]
                                        })
                                    }
                                } }/>
                        }
                        {
                            !isEmpty(stars) && 
                                <FilterTagList value={ _.get(customValue, 'grade', []) }
                                            title={ _.get(starInfo, ['metaData', 'filterName'], '星级/钻级') }
                                            linkWord={ _.get(starInfo, ['metaData', 'explain', 'text']) }
                                            link={ _.get(starInfo, ['metaData', 'explain', 'jumpUrl']) }
                                            exposeItemCount={ 6 }
                                            width={ 351 }
                                            onChange={ ( value ) => {
                                                setSelfValue({
                                                    ...customValue,
                                                    grade: value
                                                })
                                            } }
                                            descInfo={_.get(starInfo, ['metaData', 'desc', 'text'], false)}
                                            data={ filterInfo.filter(item => item.parents && item.parents.uuId === stars.uuId) }/>
                        }
                    </> : <View style={ styles.noDataBox }>
                        <Loading/>
                    </View>
            }
        </View>
    )
}

export default Select

const styles = StyleSheet.create({
    selectBox: {
        paddingLeft: pt(16),
        paddingRight: pt(16),
        marginTop: pt(8),
        width: '100%'
    },
    noDataBox: {
        height: pt(350),
        textAlign: 'center',
        padding: pt(100),
        flexDirection: 'row'
    },
    reloadWord: {
        color: THEME_BASE.highLightBlue
    }
})

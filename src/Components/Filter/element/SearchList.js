import React, { useState, useRef } from 'react'
import { ScrollView, SectionList, StyleSheet, View, Platform } from 'react-native'
import { pt, isIOS } from '@ltfe/ltfe-core-lib/lib/utiles'
import Select from './FilterSelect'
import TagList from './FilterTagList'
import { findInListKey, safeRun } from '../utils'
import _ from 'lodash'

const TabScroll = ( props ) => {
    const {filterInfo, value, onChange} = props
    const filterList = filterInfo.filter(item => item.level === 1)

    const _onChange = ( field, value, onlyCode ) => {
        safeRun(onChange, field, value, onlyCode)
    }

    return (
        <View style={ styles.tabScroll }>
            <ScrollView style={ styles.contentBox } showsVerticalScrollIndicator={false} bounces={ false }
                        overScrollMode='never'>
                {
                    Array.isArray(filterList) ? filterList.map(( item, index ) => {
                        const {info} = item
                        return <TagList
                            key={ index }
                            data={ filterInfo.filter(subItem => {
                                return subItem.parents && subItem.parents.uuId === item.uuId
                            }) }
                            // data={ filterInfo }
                            width={362}
                            onChange={ _onChange }
                            itemWidth={ 82 }
                            itemCount={ 4 }
                            title={ _.get(item, ['metaData', 'filterName']) }
                            value={ value }
                            { ...info }
                        />
                    }) : null
                }
            </ScrollView>
        </View>
    )
}

export default TabScroll

const styles = StyleSheet.create({
    tabScroll: {
        flexDirection: 'row',
        flexGrow: 0,
        display: 'flex',
        backgroundColor: '#fff',
        justifyContent: 'space-between',
        paddingTop: pt(12),
        paddingBottom: pt(10)
    },
    tabBox: {
        width: pt(88),
        backgroundColor: '#f6f7fa',
        maxHeight: pt(375)
    },
    tagBox: {
        padding: pt(8),
        maxHeight: pt(375)
    },
    contentBox: {
        paddingLeft: pt(12),
        paddingRight: pt(4),
    }
})

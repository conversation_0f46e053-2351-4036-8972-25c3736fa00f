import React from 'react'
import { StyleSheet, View, ScrollView, TouchableWithoutFeedback, Dimensions } from 'react-native'
import { JDText } from '@jdreact/jdreact-core-lib'
import Filter from '../index'
import { pt, deviceHeight, statusBarHeight } from '@ltfe/ltfe-core-lib/lib/utiles'
import _ from 'lodash'
import { THEME_BASE } from '../../utils/theme'
import { safeRun } from '../utils'
import Tag from './FilterTag'

const {height} = Dimensions.get('window')

const FilterPanel = ( props ) => {
    const {onChange, panelInfo, value, onClear, onOk, resInfo = "完成", address, showFilterType, handleInView} = props
    const {panelLock, showFooter, filterInfo, type, showTopClear, showSelectImage, toExpo, showValueList} = panelInfo
    const SubComp = _.get(Filter, type, null)

    const delTag = ( value, delItem ) => {
        const setValue = value.filter(item => item?.sameKey !== delItem?.sameKey)
        safeRun(onChange, setValue)
    }

    return (
        <View style={ styles.panelBox }>
            {
                panelLock ? <View style={ styles.panelContent }>
                    <SubComp filterInfo={ filterInfo } value={ value } showFilterType={ showFilterType }
                             handleInView={ handleInView }
                             showSelectImage={ showSelectImage } showTopClear={ showTopClear } toExpo={ toExpo }
                             address={ address }
                             onChange={ onChange }/>
                </View> : <ScrollView style={ styles.panelContent } showsVerticalScrollIndicator={ false } bounces={ false }
                                      overScrollMode='never'>
                    <SubComp filterInfo={ filterInfo } value={ value } showFilterType={ showFilterType }
                             handleInView={ handleInView }
                             showSelectImage={ showSelectImage } showTopClear={ showTopClear } toExpo={ toExpo }
                             address={ address }
                             onChange={ onChange }/>
                </ScrollView>
            }

            {
                showValueList && Array.isArray(value) && value.length > 0 ? <ScrollView horizontal={ true } style={ styles.tagBox }>
                    {
                        value.map(( item, index ) => {
                            return <Tag key={ index } showDel={ true } value={ item } style={ styles.tagItem }
                                        onChange={ ( delItem ) => delTag(value, delItem) }/>
                        })
                    }
                </ScrollView> : null
            }

            {
                showFooter ? <View style={ styles.panelFooter }>
                    <TouchableWithoutFeedback onPress={ () => onClear(showFilterType) }>
                        <View>
                            <JDText style={ styles.cancel }>
                                清空
                            </JDText>
                        </View>
                    </TouchableWithoutFeedback>
                    <TouchableWithoutFeedback onPress={ onOk }>
                        <View>
                            <JDText style={ styles.ok }>
                                { resInfo }
                            </JDText>
                        </View>
                    </TouchableWithoutFeedback>
                </View> : null
            }
        </View>
    )
}

export default FilterPanel

const styles = StyleSheet.create({
    panelBox: {
        position: 'relative',
    },
    panelContent: {
        overflow: 'scroll',
        paddingTop: pt(0),
        backgroundColor: '#fff'
    },
    panelFooter: {
        backgroundColor: '#fff',
        paddingTop: pt(10),
        paddingLeft: pt(16),
        paddingRight: pt(16),
        paddingBottom: pt(10),
        flexDirection: 'row',
        flexGrow: 0,
        display: 'flex',
        shadowOffset: {width: 0, height: pt(-3)},
        shadowOpacity: 0.02,
        shadowRadius: pt(2),
        shadowColor: '#000000',
        // Android 阴影
        elevation: 2.5,
        justifyContent: 'space-between'
    },
    cancel: {
        width: pt(164),
        height: pt(40),
        lineHeight: pt(38),
        color: '#5E6880',
        textAlign: 'center',
        borderWidth: pt(1),
        borderColor: '#5E6880',
        borderRadius: pt(6),
        fontSize: pt(16)
    },
    ok: {
        width: pt(164),
        height: pt(40),
        lineHeight: pt(38),
        color: '#fff',
        textAlign: 'center',
        borderWidth: pt(1),
        borderColor: '#013894',
        backgroundColor: '#013894',
        borderRadius: pt(6),
        overflow: 'hidden',
        fontSize: pt(16)
    },
    topClearBox: {
        position: 'absolute',
        right: pt(16),
        top: pt(22),
        zIndex: 10,
        alignItems: 'center',
        flexDirection: 'row',
    },
    clearIcon: {
        width: pt(14),
        height: pt(14),
        marginLeft: pt(3)
    },
    clearWord: {
        fontSize: pt(12),
        color: THEME_BASE.secondaryColor
    },
    tagBox: {
        flexDirection: 'row',
        display: 'flex',
        paddingBottom: pt(10),
        paddingLeft: pt(16),
        paddingRight: pt(40)
    },
    tagItem: {
        marginRight: pt(8)
    }
})

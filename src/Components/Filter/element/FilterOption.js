import React from 'react'
import { StyleSheet, Text, View, TouchableWithoutFeedback, Image } from 'react-native'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import { fieldMap, hasValue, safeRun } from '../utils'
import { JDText } from '@jdreact/jdreact-core-lib'
import { isAndroid } from '@ltfe/ltfe-core-lib/lib/utiles'
import _ from 'lodash'
import { isEmpty } from '@/utils/isType';

const Option = ( props ) => {
    const {
        tips,
        onChange,
        isActive,
        showLine,
        showSelectImage,
        showTipIcon,
        metaData,
        normalAct,
        style,
        itemStyle,
        index,
        maxWordCount = 18
    } = props
    const onPress = () => {
        const {metaData} = props
        safeRun(onChange, metaData)
    }

    const labelWord = () => {
        return _.get(metaData, ['metaData', 'filterName'], _.get(metaData, ['metaData', 'itemName'], _.get(metaData, ['metaData', 'groupName'], _.get(metaData, ['metaData', 'title'])))) || ""
    }

    const labelDesc = () => {
        return _.get(metaData,['metaData', 'desc']) || "用户选择"
    }
    const labelUserSelect = () => {
        return _.get(metaData,['metaData', 'userSelect']) || ""
    }

    const renderHotItem = () => {
        const title = labelWord()
        const desc = labelDesc() 
        const userSelect = labelUserSelect()
        if (isEmpty(title)) {
            return null
        }
        let showSubTitle = false
        if (!isEmpty(desc) && !isEmpty(userSelect)) {
            showSubTitle = true
        }
        if (!showSubTitle) {
            return (
            <View style={ itemStyle }>
                <JDText numberOfLines={ 2 }
                        style={ [isActive ? styles.labelAct : styles.label, labelWord()?.length > maxWordCount ? styles.labelTwoLine : '', tips ? styles.optionContentTips : '', normalAct ? styles.normalAct : ''] }>{ labelWord() }</JDText>
            </View>
            )
        }
        return (
            <View className={styles.hotContainer}>
                <View style={ {...itemStyle, marginTop: pt(-15)} }>
                    <JDText style={isActive ? styles.hotTitleSelected : styles.hotTitle}>{title}</JDText> 
                    <View style={styles.hotSubContainer}>
                        <JDText style={index <= 2 ? styles.hotSubTitle : styles.hotSubTitleNormal}>{userSelect}</JDText>
                        <JDText style={styles.hotSubDesc}>{desc}</JDText>
                    </View>
                </View>
            </View>
            )
    }

    return (
        <TouchableWithoutFeedback onPress={ onPress }>
            <View style={ [styles.optionBox, style] }>
                <View
                    style={ [isActive ? styles.optionContentAct : styles.optionContent, showLine ? styles.optionShowLint : '', labelWord()?.length > maxWordCount ? {paddingTop: pt(14)} : '', tips ? styles.optionContentTipsBox : ''] }>
                    {
                        showTipIcon ? <Image style={ styles.tipsIcon }
                                             source={ {uri: 'https://img11.360buyimg.com/imagetools/jfs/t1/198699/26/51873/302/67472468Fb3b524e8/2adc72f21ae70613.png'} }/> : null
                    }
                    {
                        renderHotItem()
                    }
                    {
                        showSelectImage && isActive ? <Image style={ styles.selectImage }
                                                             source={ {uri: 'https://img12.360buyimg.com/imagetools/jfs/t1/244299/16/22256/513/671cb1d8F087578c0/fedafe097dacb85f.png'} }/> : null
                    }
                </View>
                {
                    showLine && isActive ? <View style={ styles.selectIcon }/> : null
                }
                {
                    showLine ? <View style={ styles.maskBox }/> : null
                }
            </View>
        </TouchableWithoutFeedback>
    )
}

export default Option

const styles = StyleSheet.create({
    tipsIcon: {
        position: 'absolute',
        width: pt(4),
        height: pt(4),
        borderRadius: pt(2),
        overflow: 'hidden',
        top: pt(28),
        left: pt(8)
    },
    optionBox: {
        position: 'relative'
    },
    optionContent: {
        borderBottomWidth: pt(1),
        borderColor: '#F5F7FA',
        height: pt(60),
        flexGrow: 1,
        paddingTop: pt(23)
    },
    optionContentAct: {
        borderBottomWidth: pt(1),
        borderColor: '#F5F7FA',
        height: pt(60),
        flexGrow: 1,
        backgroundColor: '#fff',
        position: 'relative',
        paddingTop: pt(23)
    },
    optionContentTipsBox: {
        paddingTop: pt(10)
    },
    label: {
        fontSize: pt(14),
        lineHeight: pt(16),
        height: pt(16),
        marginBottom: pt(4),
    },
    labelAct: {
        fontSize: pt(14),
        lineHeight: pt(16),
        height: pt(16),
        color: '#006EEB',
        fontWeight: isAndroid ? 'bold' : 600
    },
    normalAct: {
        fontWeight: 500
    },
    labelTwoLine: {
        fontSize: pt(14),
        lineHeight: pt(16),
        height: pt(32),
        maxWidth: pt(240),
    },
    optionShowLint: {
        borderRightWidth: pt(1),
        borderRightColor: '#E3E7F0',
    },
    optionContentTips: {
        fontSize: pt(14),
        lineHeight: pt(0),
        height: pt(18),
        maxWidth: pt(240),
    },
    tipsBox: {
        flexDirection: 'row',
        marginBottom: pt(13),
    },
    hotContainer: {
        flex: 1,
        flexDirection: 'column',
        justifyContent: 'space-between'
    },
    hotTitle: {
        color: '#333333',
        fontSize: pt(14),
        marginRight: pt(6)
    },
    hotTitleSelected: {
        fontSize: pt(14),
        color: '#006EEB',
        fontWeight: 'bold'
    },
    hotSubContainer: {
        marginTop: pt(5),
        flexDirection: 'row',
        marginBottom: pt(13)
    },
    hotSubTitle: {
        fontSize: pt(12),
        color: '#FF0400',
        marginRight: pt(4)
    },
    hotSubTitleNormal: {
        fontSize:  pt(12),
        color:'#7C869C',
        marginRight: pt(4)
    },
    hotSubDesc: {
        fontSize: pt(12),
        color: '#7C869C'
    },
    tips: {
        fontSize: pt(12),
        height: pt(13),
        lineHeight: pt(13),
        color: '#7C869C'
    },
    selectIcon: {
        width: pt(10),
        height: pt(10),
        borderWidth: pt(1),
        borderColor: '#E3E7F0',
        position: 'absolute',
        right: pt(-5),
        top: pt(22),
        backgroundColor: '#fff',
        transform: [{rotate: '45deg'}],
        opacity: isAndroid ? 0.8 : 1
    },
    selectImage: {
        width: pt(18),
        height: pt(18),
        position: 'absolute',
        right: pt(12),
        top: pt(22)
    },
    maskBox: {
        position: 'absolute',
        backgroundColor: '#fff',
        height: pt(4),
        width: pt(12),
        right: pt(1),
        bottom: pt(-1)
    }
})

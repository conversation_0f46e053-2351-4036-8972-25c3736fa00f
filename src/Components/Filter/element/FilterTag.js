import React from 'react'
import { Image, StyleSheet, Text, TouchableWithoutFeedback, View } from 'react-native'
import _ from 'lodash'
import { JDText } from '@jdreact/jdreact-core-lib'
import { pt, px } from '@ltfe/ltfe-core-lib/lib/utiles'
import { safeRun, fieldMap, getImg } from '../utils'
import { THEME_BASE } from '../../utils/theme'

const Select = ( props ) => {
    const {width, field, onChange, isActive, fieldNames, showDel, value, style = {}, tagStyle = {}} = props

    const onPress = () => {
        safeRun(onChange, value)
    }

    const twoLine = _.get(value, ['metaData', 'itemName'], []).length > 6
    const twoStyle = [(typeof width === 'number') ? styles.selectBox : styles.selectBoxAuto, (typeof width === 'number') ? {width: pt(width)} : {}, style, isActive ? styles.selectedBox : '']

    return (
        <TouchableWithoutFeedback style={ twoStyle } onPress={ onPress }>
            <View
                style={ [twoStyle, isActive && styles.selectBor] }>
                <JDText
                    style={ [isActive ? styles.selected : styles.content, twoLine ? styles.twoLine : '', tagStyle] }
                    numberOfLines={ 2 }>
                    { _.get(value, ['metaData', 'itemName'], 'aabbcc') }
                </JDText>
                {
                    showDel ? <View style={ styles.iconBox }>
                        <Image style={ styles.delIcon }
                               source={ {uri: getImg('del')} }/>
                    </View> : null
                }
                {
                    _.get(props, ['data', 'metaData', 'itemDesc'], false) ? <JDText style={isActive ? styles.selectDescWord : styles.descWord}>
                        { _.get(props, ['data', 'metaData', 'itemDesc'], '') }
                    </JDText> : null
                }
                {/* {
                 isActive ? <Image style={ styles.selectIcon }
                 source={ {uri: getImg('select')} }/> : null
                 } */ }
            </View>
        </TouchableWithoutFeedback>
    )
}

export default Select

const styles = StyleSheet.create({
    descWord: {
        color: "#505259",
        fontSize: pt(10),
        marginTop: pt(4)
    },
    selectDescWord: {
        color: THEME_BASE.highLightBlue,
        fontSize: pt(10),
        marginTop: pt(4)
    },
    selectBox: {
        width: pt(110),
        borderRadius: pt(6),
        overflow: 'hidden',
        marginBottom: pt(8),
        padding: pt(1),
        marginRight: pt(8),
        position: 'relative',
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#F0F6FF',
        paddingTop: pt(4),
        paddingBottom: pt(4),
        paddingRight: pt(0),
        paddingLeft: pt(0),
        minHeight: pt(36),
        borderColor: '#fff',
        borderWidth: px(1.1),
        borderStyle: 'solid'
    },
    selectBoxAuto: {
        borderRadius: pt(6),
        overflow: 'hidden',
        position: 'relative',
        flex: 1,
        justifyContent: 'center',
        backgroundColor: '#f5f7fa',
        alignItems: 'center',
        paddingTop: pt(4),
        paddingBottom: pt(4),
        paddingRight: pt(0),
        paddingLeft: pt(0),
        minHeight: pt(36),
        borderColor: '#fff',
        borderWidth: px(2),
        borderStyle: 'solid'
    },
    selectedBox: {
        backgroundColor: '#F0F6FF',
    },
    selected: {
        fontSize: pt(14),
        textAlign: 'center',
        lineHeight: pt(16),
        color: THEME_BASE.highLightBlue,
        textAlignVertical: 'center'
    },
    content: {
        fontSize: pt(14),
        textAlign: 'center',
        lineHeight: pt(16),
        color: '#1A1A1A',
        textAlignVertical: 'center'
    },
    iconBox: {
        height: pt(30),
        paddingTop: pt(13),
        paddingLeft: pt(4)
    },
    delIcon: {
        width: pt(8),
        height: pt(8)
    },
    twoLine: {
        lineHeight: pt(16),
        fontSize: pt(14),
        padding: pt(2)
    },
    selectIcon: {
        width: pt(10),
        height: pt(10),
        position: 'absolute',
        right: pt(0),
        bottom: pt(0)
    },
    // 标签选中的边框
    selectBor: {
        borderColor: THEME_BASE.highLightBlue,
        borderWidth: px(1.1),
        borderStyle: 'solid'
    }
})

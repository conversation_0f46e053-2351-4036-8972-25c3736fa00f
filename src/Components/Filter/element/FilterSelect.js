import React from 'react'
import { ScrollView } from 'react-native'
import Option from './FilterOption'

import _ from 'lodash'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'

const checkTipIcon = ( option = {}, value = [] ) => {
    const childrenListStrList = _.get(option, 'childrenSameKey', '-----').split('|')
    return value.some(item => {
        return childrenListStrList.includes(_.get(item, 'sameKey', '--|--'))
    })
}

const checkTipIconParents = ( option = {}, value = [] , isTabs) => {
    const childrenListStr = _.get(option, 'childrenSameKey', '-----')
    return value.some(item => {
        return childrenListStr.indexOf(_.get(item, ['parents', 'sameKey'], '--|--')) > -1
    })
}

const Select = ( props ) => {
    const {
        options,
        field,
        fieldNames = {},
        mode = 'single',
        showLine,
        showSelectImage,
        value,
        selected,
        normalAct,
        itemStyle,
        maxWordCount,
    } = props

    return (
        <ScrollView showsVerticalScrollIndicator={false} bounces={ false }
                    overScrollMode='never'>
            {
                Array.isArray(options) ? options.map(( option, index ) => {
                    return <Option mode={ mode } metaData={ option } showLine={ showLine } key={ index } index={index}
                                   fieldNames={ fieldNames }
                                   showSelectImage={ showSelectImage }
                                   showTipIcon={ checkTipIconParents(option, selected) || checkTipIcon(option, selected) }
                                   isActive={ Array.isArray(value) && value.some(item => item?.sameKey === option?.sameKey) }
                                   field={ field }
                                   fieldKey={ option.key }
                                   fieldIndex={ index }
                                   normalAct={normalAct}
                                   itemStyle={itemStyle}
                                   maxWordCount={maxWordCount}
                                   onChange={ props.onChange }/>
                }) : null
            }
        </ScrollView>
    )
}

export default Select

// const styles = StyleSheet.create({})

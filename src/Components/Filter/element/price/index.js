import React, { PureComponent } from 'react'
// import PropTypes from 'prop-types';

import {
    View,
    StyleSheet
} from 'react-native'

import {
    JDTouchable,
    JDText
} from '@jdreact/jdreact-core-lib'

import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import Slider3 from './slider'

import { THEME_FONT, THEME_BASE } from '@ltfe/ltfe-core-lib/lib/utiles/theme'
import { safeRun } from '../../utils'
import _ from 'lodash'

const IMG = {
    slider_icon: getImg('slider_icon'),
    // arrow_down: getImageUrl('arrow_down')
}

const formatText = ( {
                         max, min, minimumValue, maximumValue, step
                     } ) => {
    return max === maximumValue + step && min === minimumValue ? '' : `${ minimumValue !== min ? `¥${ min }` : '' }${ (minimumValue !== min && max !== (maximumValue + step)) ? '-' : '' }${ maximumValue + step !== max ? `¥${ max }` : '' }${ minimumValue === min ? '以下' : `` }${ maximumValue + step === max ? '以上' : `` }`
}

class Index extends PureComponent {
    static displayName = 'Price'

    constructor( props ) {
        super(props)
        let _min = Number.MAX_SAFE_INTEGER, _max = Number.MIN_SAFE_INTEGER

        Array.isArray(props.children) && props.children.forEach(child => {
            const {metaData = {}} = child
            const {itemId = ''} = metaData
            const [childMin, childMax] = itemId.split(',')
            _min = Math.min(_min, ~~childMin)
            _max = Math.max(_max, ~~childMax)
        })

        const [selectMin, selectMax] = _.get(props, ['value', 0, 'metaData', 'itemId'], `${ _min },${ _max }`).split(',')

        const step = 50
        const __max = (selectMax === '' || +selectMax === 1000) ? +selectMax + step : +selectMax
        this.state = {
            min: +selectMin,
            max: __max,
            minimumValue: +_min,
            maximumValue: +_max,
            children: props.children || [],
            priceText: formatText({
                min: +selectMin,
                max: __max,
                minimumValue: +_min,
                maximumValue: +_max,
                step,
            }),
            step
        }
    }



    render() {
        const {minimumValue, maximumValue, step, min, max, children, priceText} = this.state

        return (
            <View style={ {marginBottom: pt(12)} }>
                <View style={ st.priceBox }>
                    <JDText style={ st.title_text }>价格</JDText>
                    <JDText style={ st.priceText }>{ priceText }</JDText>
                </View>

                <View style={ st.min_max }>
                    <JDText style={ [st.text_grey, st.min_left] }>&yen;{ minimumValue }</JDText>
                    <JDText style={ [st.text_grey, st.max_right] }>&yen;{ maximumValue }以上</JDText>
                </View>

                <View style={ st.slider_view }>
                    <View style={ {width: pt(343)} }>
                        <Slider3
                            value={ [min, max] }
                            onValueChange={ this.slicePrice }
                            { ...{minimumValue, maximumValue: maximumValue + step, step} }
                            minimumTrackTintColor={ '#0068FF' }
                            maximumTrackTintColor={ '#DADADA' }
                            thumbImage={ {uri: IMG.slider_icon} }
                            onSlidingComplete={ this.finish }
                        />
                    </View>
                </View>
                <View style={ st.lables }>
                    {
                        Array.isArray(children) ? children.map(( item, index ) => {
                                const {metaData = {}} = item
                                const {itemId = '', itemName} = metaData
                                const isActive = itemId === [min, max].join(',')
                                return <JDTouchable key={ index }
                                                    style={ [st.lable_view, isActive && st.active, ((index + 1) % 3 !== 0) && st.marginR] }
                                                    onPress={ () => this.choosePrice(item) }>
                                    <JDText
                                        style={ [st.lable_text, isActive && st.active_text] }>{ itemName }</JDText>
                                </JDTouchable>
                            }
                        ) : null
                    }
                </View>
            </View>
        )
    }

    slicePrice = ( price ) => {
        const [min, _max] = price
        const max = _max === '' ? maximumValue + step : _max
        const tempData = _.cloneDeep(_.get(this.props, ['children', 0]))

        _.set(tempData, ['metaData', 'itemId'], `${ min },${ _max === 1050 ? '' : _max }`)
        _.set(tempData, ['metaData', 'itemName'], formatText({
            ...this.state,
            min, max,
        }))

        // min取下限取以上  max取上限取以上  区间加-
        this.setState({
            min, max,
            priceText: formatText({
                ...this.state,
                min, max,
            })
        })

        safeRun(this.props.onChange, tempData)
    }

    choosePrice = ( _value ) => {
        const {maximumValue, step} = this.state
        const {metaData = {}} = _value

        const {itemId = '', itemName} = metaData
        const [min, max] = itemId.split(',')

        this.setState({
            min, max: max === '' ? maximumValue + step : max,
            priceText: itemName
        })

        safeRun(this.props.onChange, _value)
    }
}

const st = StyleSheet.create({
    priceBox: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 0
    },
    title_text: {
        paddingVertical: pt(4),
        color: '#000000',
        fontSize: pt(14),
        fontWeight: THEME_FONT.fontWeight.SemiBold,
        marginRight: pt(8)
    },
    priceText: {
        color: '#0068FF',
        fontSize: pt(16),
        fontFamily: 'JDZhengHT'

    },
    min_max: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: pt(14)
    },
    slider_view: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingTop: pt(8)
    },
    lables: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        // marginLeft: pt(16),
        // justifyContent: 'space-around',
        marginTop: pt(10)
    },
    lable_view: {
        width: pt(108),
        height: pt(30),
        backgroundColor: '#F5F7FA',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: pt(8),
        borderRadius: pt(4)
    },
    marginR: {
        marginRight: pt(8)
    },

    active: {
        backgroundColor: '#0068FF'
        // borderColor: '#FA2C19',
        // borderWidth: px(2)
    },
    lable_text: {
        fontSize: pt(14),
        color: THEME_BASE.secondaryColor,
        fontFamily: 'JDZhengHT'
    },
    active_text: {
        color: '#fff'
    },
    text_grey: {
        fontSize: pt(16),
        color: THEME_BASE.secondaryColor,
        fontFamily: 'JDZhengHT'
    },
    min_left: {
        marginRight: pt(8)
    },
    max_right: {
        marginLeft: pt(10)
    }
})

export default Index


import React, { useEffect, useState } from 'react'
import { StyleSheet, Text, View, Image, TouchableWithoutFeedback } from 'react-native'
import { pt, px } from '@ltfe/ltfe-core-lib/lib/utiles'
import { JDTouchable, JDText } from '@jdreact/jdreact-core-lib'
import Slider3 from './slider'
import { getImg, safeRun } from '../../utils'
import _ from 'lodash'
import { THEME_BASE, THEME_FONT } from '@ltfe/ltfe-core-lib/lib/utiles/theme'

const IMG = {
    slider_icon: getImg('slider_icon'),
    arrow_down: getImg('arrow_down')
}

const formatText = ( {
                         max, min, minimumValue, maximumValue, step
                     } ) => {
    return max === maximumValue + step && min === minimumValue ? '' : `${ minimumValue !== min ? `¥${ min }` : '' }${ (minimumValue !== min && max !== (maximumValue + step)) ? '-' : '' }${ maximumValue + step !== max ? `¥${ max }` : '' }${ minimumValue === min ? '以下' : `` }${ maximumValue + step === max ? '以上' : `` }`
}

const catchValue = new Map()

function FilterItem( props ) {

    const [initState, setInitState] = useState({
        min: 0,
        max: 1000,
        minimumValue: 0,
        maximumValue: 1000,
        step: 50,
        priceText: ''
    })

    useEffect(() => {
        let _min = Number.MAX_SAFE_INTEGER, _max = Number.MIN_SAFE_INTEGER
        Array.isArray(props.children) && props.children.forEach(child => {
            const {metaData = {}} = child
            const {itemId = ''} = metaData
            const [childMin, childMax] = itemId.split(',')
            _min = Math.min(_min, ~~childMin)
            _max = Math.max(_max, ~~childMax)
        })

        const [selectMin, selectMax] = _.get(props, ['value', 0, 'metaData', 'itemId'], `${ _min },${ _max }`).split(',')
        const __max = (selectMax === '' || +selectMax === 1000) ? +_max + step : +selectMax

        setInitState({
            min: +selectMin,
            max: __max,
            minimumValue: +_min,
            maximumValue: +_max,
            children: props.children || [],
            priceText: formatText({
                min: +selectMin,
                max: __max,
                minimumValue: +_min,
                maximumValue: +_max,
                step,
            }),
            step
        })
    }, [])

    useEffect(() => {
        if ( props.value.length > 0 ) {
            const [_min, _max] = _.get(props.value, [0, 'metaData', 'itemId'], '').split(',')
            const {step, maximumValue} = initState
            const min = +_min
            const max = (_max === '' || +min === maximumValue) ? +maximumValue + step : +_max
            setInitState({
                ...initState,
                min,
                max,
                children: props.children || [],
                priceText: formatText({
                    min,
                    max,
                    step,
                    minimumValue,
                    maximumValue
                }),
                step
            })
        } else {
            const {step, maximumValue, minimumValue} = initState
            setInitState({
                ...initState,
                min: minimumValue,
                max: maximumValue + step,
                children: props.children || [],
                priceText: '',
                step
            })
        }
    }, [props.value])

    const {priceText, minimumValue, maximumValue, step, children, min, max} = initState

    const slidePrice = ( price ) => {
        const [_min, _max] = price.map(( item, index ) => index && !item ? 1050 : +item)

        setInitState({
            ...initState,
            min: _min, max: _max,
            priceText: formatText({
                ...initState,
                min: _min, max: _max,
            })
        })

        const tempData = _.cloneDeep(_.get(props, ['children', 0]))
        const {min, max} = initState
        _.set(tempData, ['metaData', 'itemId'], `${ min },${ max === 1050 ? '' : max }`)
        _.set(tempData, ['metaData', 'itemName'], formatText({
            ...initState,
            min, max,
        }))
        _.set(tempData, ['sameKey'], `hotel_price_lowest-${ min },${ max === 1050 ? '' : max }`)
        catchValue.set('value', tempData)
    }

    const selectPrice = ( price, value ) => {
        const [_min, _max] = price.map(( item, index ) => index && !item ? 1050 : +item)
        const {min, max, minimumValue, maximumValue, step} = initState

        if ( [min, max].join(',') === [_min, _max].join(',') ) {
            setInitState({
                ...initState,
                min: minimumValue,
                max: maximumValue + step,
                priceText: ''
            })
            props.onChange(false)
        } else {
            setInitState({
                ...initState,
                min: _min, max: _max,
                priceText: formatText({
                    ...initState,
                    min: _min, max: _max,
                })
            })
            props.onChange(value)
        }
    }

    return (
        <View style={ {marginBottom: pt(12)} }>
            <View style={ st.priceBox }>
                <JDText style={ st.title_text }>价格</JDText>
                <JDText style={ st.priceText }>{ priceText }</JDText>
            </View>

            <View style={ st.min_max }>
                <JDText style={ [st.text_grey, st.min_left] }>&yen;{ minimumValue }</JDText>
                <JDText style={ [st.text_grey, st.max_right] }>&yen;{ maximumValue }以上</JDText>
            </View>

            <View style={ st.slider_view }>
                <View style={ {width: pt(343)} }>
                    <Slider3
                        value={ [min, max] }
                        onValueChange={ slidePrice }
                        { ...{minimumValue, maximumValue: maximumValue + step, step} }
                        minimumTrackTintColor={ '#0068FF' }
                        maximumTrackTintColor={ '#DADADA' }
                        thumbImage={ {uri: IMG.slider_icon} }
                        onSlidingComplete={ () => {
                            props.onChange(catchValue.get('value'))
                        } }
                    />
                </View>
            </View>
            <View style={ st.lables }>
                {
                    Array.isArray(children) ? children.map(( item, index ) => {
                            const {metaData = {}} = item
                            const {itemId = '', itemName} = metaData
                            const [itemMin, itemMax] = itemId.split(',')
                            const _itemId = [itemMin, itemMax === '' ? 1050 : itemMax].join(',')
                            const isActive = _itemId === [min, max].join(',')
                            return <View style={ {position: 'relative'} }><JDTouchable key={ index }
                                                                                       style={ [st.lable_view, isActive && st.active, ((index + 1) % 3 !== 0) && st.marginR] }
                                                                                       onPress={ () => selectPrice(itemId.split(','), item) }>
                                <JDText
                                    style={ [st.lable_text, isActive && st.active_text] }>{ itemName }</JDText>

                            </JDTouchable>
                                {/* {
                                    isActive ?
                                        <Image style={ [st.selectIcon, ((index + 1) % 3 === 0) ? {right: pt(0)} : {}] }
                                               source={ {uri: getImg('select')} }/> : null
                                } */}
                            </View>
                        }
                    ) : null
                }
            </View>
        </View>
    )
}

export default FilterItem

const st = StyleSheet.create({
    priceBox: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 0
    },
    title_text: {
        paddingVertical: pt(4),
        color: '#000000',
        fontSize: pt(14),
        fontWeight: THEME_FONT.fontWeight.SemiBold,
        marginRight: pt(8)
    },
    priceText: {
        color: '#0068FF',
        fontSize: pt(16),
        fontFamily: 'JDZhengHT'
    },
    min_max: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: pt(9)
    },
    slider_view: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingTop: pt(8)
    },
    lables: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        // marginLeft: pt(16),
        // justifyContent: 'space-around',
        marginTop: pt(10)
    },
    lable_view: {
        width: pt(108),
        height: pt(36),
        backgroundColor: '#F5F7FA',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: pt(8),
        borderRadius: pt(4),
        position: 'relative',
    },
    marginR: {
        marginRight: pt(8)
    },

    active: {
        backgroundColor: '#F0F6FF',
        borderColor: THEME_BASE.highLightBlue,
        borderWidth: px(1.5),
        borderStyle: 'solid'
    },
    lable_text: {
        fontSize: pt(14),
        color: THEME_BASE.secondaryColor,
        fontFamily: 'JDZhengHT'
    },
    active_text: {
        color: THEME_BASE.highLightBlue
    },
    text_grey: {
        fontSize: pt(14),
        color: THEME_BASE.secondaryColor,
        fontFamily: 'JDZhengHT'
    },
    min_left: {
        marginRight: pt(8)
    },
    max_right: {
        marginLeft: pt(10)
    },
    selectIcon: {
        width: pt(10),
        height: pt(10),
        position: 'absolute',
        right: pt(9),
        bottom: pt(9)
    }
})

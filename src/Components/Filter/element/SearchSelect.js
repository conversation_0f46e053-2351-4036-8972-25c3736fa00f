import React from 'react'
import {StyleSheet} from 'react-native'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import Option from './FilterOption'

import _ from 'lodash'
import { IOScrollView, InView } from '@/BaseComponents/IntersectionObserver'
import { safeRun } from '../utils'

const Select = ( props ) => {
    const {
        filterInfo,
        showLine,
        value,
        showSelectImage,
        handleInView,
        toExpo
    } = props

    const onChange = ( value ) => {
        props.onChange([value])
    }

    return (
        <IOScrollView contentContainerStyle={styles.scrollBox}>
            {
                Array.isArray(filterInfo) ? filterInfo.map(( option, index ) => {
                    return <InView onChange={ ( visible ) => toExpo ? safeRun(handleInView, visible, option, value) : "" }><Option
                        showLine={ showLine } key={ index }
                        metaData={ option }
                        isActive={ value.some(item => {
                            return item?.uuId === option?.uuId
                        }) }
                        showSelectImage={ showSelectImage }
                        tips={ _.get(option, 'metaData.tips', false) }
                        fieldKey={ option?.key }
                        fieldIndex={ index }
                        onChange={ onChange }/>
                    </InView>
                }) : null
            }
        </IOScrollView>
    )
}

export default Select

const styles = StyleSheet.create({
    scrollBox: {
        paddingLeft: pt(16),
        paddingRight: pt(16)
    }
})

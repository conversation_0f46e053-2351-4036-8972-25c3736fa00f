import React, { useEffect, useState } from 'react'
import _ from 'lodash'
import SearchTabPanel from './SearchTabPanel'
import Storage from '@/utils/LocalStorage'
import { StyleSheet, View } from 'react-native'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import { JDText, JDTouchable } from '@jdreact/jdreact-core-lib'
import { THEME_BASE } from '../../utils/theme'
import { Loading } from '../../../BaseComponents/atoms'

// 下次找映射关系找这里!!!!!!!!!!!!
export const MapObj = {
    sceneSortType: {
        type: 'Select',
        panelLock: 'true',
        field: 'sortType',
        showSelectImage: true,
        closeByChange: true,
        selectByOk: true,
        toExpo: true,
        filterList: [{
            filterName: '综合排序',
            filterType: 'default',
            sortType: 'default',
            orderType: 'desc',
            index: 1
        }, {
            filterName: '好评优先',
            filterType: 'good_comment_first',
            sortType: 'good_comment_first',
            orderType: 'desc',
            index: 2
        }, {
            filterName: '销量高→低',
            filterType: 'price_desc',
            sortType: 'price_desc',
            orderType: 'desc',
            index: 3
        }, {
            filterName: '距离近→远',
            filterType: 'distance_asc',
            sortType: 'distance_asc',
            orderType: 'desc',
            index: 4
        }]
    },
    hotelSortType: {
        type: 'Select',
        panelLock: 'true',
        field: 'sortType',
        showSelectImage: true,
        selectByOk: true,
        toExpo: true,
        filterList: [{
            filterName: '智能排序',
            filterType: 'default',
            sortType: 'default',
            orderType: 'desc',
            index: 1
        }, {
            filterName: '好评优先',
            filterType: 'good_comment_first',
            sortType: 'good_comment_first',
            orderType: 'desc',
            index: 2
        }, {
            filterName: '评价数多→少',
            filterType: 'comment_count_desc',
            sortType: 'comment_count_desc',
            orderType: 'desc',
            index: 3
        }, {
            filterName: '低价优先',
            filterType: 'price_asc',
            sortType: 'price_asc',
            orderType: 'asc',
            index: 4
        }, {
            filterName: '高价优先',
            filterType: 'price_desc',
            sortType: 'price_desc',
            orderType: 'desc',
            index: 5
        }, {
            filterName: '高星优先',
            filterType: 'star_desc',
            sortType: 'star_desc',
            orderType: 'desc',
            index: 6
        }, {
            filterName: '直线距离近→远',
            filterType: 'distance_asc',
            sortType: 'distance_asc',
            orderType: 'desc',
            index: 7
        }]
    },
    sortType: {
        type: 'Select',
        panelLock: 'true',
        field: 'sortType',
        showSelectImage: true,
        toExpo: true,
        children: [{
            label: '智能排序',
            value: 'default',
            orderType: 'desc'
        }, {
            label: '好评优先',
            value: 'good_comment_first',
            orderType: 'desc'
        }, {
            label: '评价数',
            tips: '多->少',
            value: 'comment_count_desc',
            orderType: 'desc'
        }, {
            label: '低价优先',
            value: 'price_asc',
            orderType: 'asc'
        }, {
            label: '高价优先',
            value: 'price_desc',
            orderType: 'desc'
        }, {
            label: '高星优先',
            value: 'star_desc',
            orderType: 'desc'
        }, {
            label: '直线距离近→远',
            value: 'distance_asc',
            orderType: 'desc'
        }]
    },
    location_distance: {
        type: 'Location',
        field: 'location_distance',
        showFooter: true,
    },
    price_star: {
        type: 'Custom',
        filterPanelCode: 'price_star',
        showFooter: true,
        field: 'price_star'
    },
    hotel_price: {
        type: 'Price'
    },
    hotel_grade: {
        type: 'TagList'
    },
    hotel_filter: {
        type: 'SearchTabListGroup',
        showFooter: true,
        panelLock: true,
        // showValueList: true,
        field: 'hotel_filter',
        history: true,
        groupByParent: true
    },
    'main_filter': {
        type: 'SearchTabScroll',
        showFooter: true,
        panelLock: true,
        field: 'main_filter',
        groupBy: true,
        showTopClear: true,
    },
    'scenic_city': {
        type: 'SearchTabSelect',
        showFooter: true,
        panelLock: true,
        field: 'scenic_city'
    },
    scenic_category: {
        type: 'SearchTabList',
        showFooter: true,
        panelLock: true,
        field: 'scenic_category'
    },
    scenic_filter: {
        type: 'SearchList',
        showFooter: true,
        panelLock: true,
        field: 'scenic_filter'
    }
}

const getAllChildrenSameKey = ( subItem ) => {
    return Array.isArray(subItem.itemList) ? subItem.itemList.map(item => `${ _.get(item, ['filterType']) }-${ _.get(item, ['itemId']) }`) : []
}

export const flattenGroupsHelper = ( groups, parents = null, result = [], level = 1, preFix = '', filterKey, _info ) => {
    if ( !Array.isArray(groups) ) return
    for ( let i = 0 ; i < groups.length ; i++ ) {
        const item = groups[i]
        const _uuId = `${ preFix }${ _.get(item, 'filterPanelCode', '') }_${ _.get(item, 'groupCode', '') }_${ _.get(item, 'filterType', '') }_${ _.get(item, 'itemId', '') }`
        const _isLeaf = !Array.isArray(item.itemList) && !Array.isArray(item.filterList)
        const _filterKey = item.filterPanelCode || filterKey
        const sameKey = `${ _.get(item, 'filterType') }-${ _.get(item, 'itemId') }`
        const exposeItemCount = _.get(item, 'exposeItemCount')
        const multi = _.get(item, 'multi')
        const info = typeof exposeItemCount === 'number' && typeof multi === 'number' && item.groupCode !== 'hotel_history' ? {
            multi,
            exposeItemCount
        } : _info
        const value = {
            uuId: `${ _uuId }`,
            metaData: {
                ...item,
                ...info,
                filterKey: _filterKey
            },
            checked: false,
            level,
            parents,
            filterKey: _filterKey,
            isLeaf: _isLeaf,
            childrenSameKey: item.childrenSameKey ? item.childrenSameKey : (Array.isArray(item.itemList) ? item.itemList.map(subItem => {
                return `${ _.get(subItem, 'filterType') }-${ _.get(subItem, 'itemId') }`
            }) : []).join('|'),
            sameKey: _isLeaf ? sameKey : _uuId,
            info
        }

        result.push(value)

        if ( Array.isArray(item.itemList) ) {
            flattenGroupsHelper(item.itemList, value, result, level + 1, `${ _uuId }|`, _filterKey, info)
        }

        if ( Array.isArray(item.filterList) ) {
            flattenGroupsHelper(item.filterList, value, result, level + 1, `${ _uuId }|`, _filterKey, info)
        }
    }

    return result
}

export const groupBy = ( list ) => {
    let groupObj = {}
    Array.isArray(list) && list.forEach(item => {
        const {groupName, groupCode, filterType, itemList} = item
        const setList = _.get(groupObj, [groupCode, 'itemList'])
        if ( Array.isArray(setList) ) {
            setList.push(_.omit(item, ['groupCode', 'groupName']))
            groupObj[groupCode].childrenSameKey = setList.reduce(( pre, cur ) => pre.concat(getAllChildrenSameKey(cur)), []).join('|')
        } else {
            groupObj[groupCode] = {
                groupName,
                groupCode,
                filterType,
                itemList: [item]
            }
        }
    })
    return Object.values(groupObj)
}

const genSchema = ( options, key ) => {
    let res = _.get(MapObj, key, {})
    Array.isArray(options) && options.some(item => {
        const {filterPanelCode, filterList} = item
        if ( filterPanelCode === key ) {
            const mapInfo = MapObj[filterPanelCode]
            if ( mapInfo && mapInfo.groupBy ) {
                mapInfo.filterList = groupBy(filterList)
            }
            if ( mapInfo && mapInfo.groupByHasSub ) {
                mapInfo.filterList = groupByHasSub(filterList)
            }
            if ( mapInfo && mapInfo.groupByChild ) {
                mapInfo.filterList = groupByChild(filterList)
            }
            res = {
                ...item,
                ...mapInfo,
            }
        }
        return filterPanelCode === key
    })

    return res
}

const getNewLengthNoSameList = ( list, count = 9 ) => {
    const mapCatch = new Map()
    list.forEach(item => {
        const {sameKey} = item
        mapCatch.set(sameKey, item)
    })

    return Array.from(mapCatch.values()).slice(0, count)
}

const groupByParent = ( list = [] ) => {
    const parentList = list.filter(item => item.level === 1), childrenList = list.filter(item => item.level === 2)
    let parentObj = {}, childrenObj = {}
    parentList.forEach(parent => {
        const groupCode = _.get(parent, ['metaData', 'groupCode'])
        if ( parentObj[groupCode] ) {
            parentObj[groupCode] = {
                ...parentObj[groupCode],
                childrenSameKey: _.get(parentObj, [groupCode, 'childrenSameKey']) + '|' + _.get(parent, ['childrenSameKey'])
            }
        } else {
            parentObj[groupCode] = {
                ...parent,
                metaData: {
                    ..._.get(parent, ['metaData']),
                    filterName: _.get(parent, ['metaData', 'groupName'])
                }
            }
        }
    })

    childrenList.forEach(child => {
        const parentSameKey = _.get(child, 'parents.sameKey')
        if ( childrenObj[parentSameKey] ) {
            childrenObj[parentSameKey] = {
                ..._.get(child, 'parent', {}),
                ...childrenObj[parentSameKey],
                level: 2,
                data: _.get(childrenObj, [parentSameKey, 'data'], []).concat(child),
            }
        } else {
            childrenObj[parentSameKey] = {
                ..._.get(child, 'parent', {}),
                data: [child],
                level: 2,
                sameKey: _.get(child, ['parents', 'sameKey']),
                info: {
                    exposeItemCount: _.get(child, ['parents', 'metaData', 'exposeItemCount']),
                    multi: _.get(child, ['parents', 'metaData', 'multi']),
                },
                title: _.get(child, 'parents.metaData.filterName')
            }
        }
    })

    return Object.values(parentObj).concat(Object.values(childrenObj))
}

const SearchPannel = ( props ) => {
    const {options, showFilterType, resInfo, value, address} = props
    const [_panelInfo, setPanelInfo] = useState(genSchema(_.cloneDeep(options), showFilterType))
    const [historyLoading, setHistoryLoading] = useState(true)
    useEffect(() => {
        const panelInfo = genSchema(_.cloneDeep(options), showFilterType)
        Storage.getItem(`filterHistory_${ showFilterType }`).then(historyData => {
            if ( historyData && historyData.length > 0 ) {
                panelInfo.filterList.unshift({
                    'multi': 0,
                    'groupCode': 'hotel_history',
                    'filterName': '历史筛选',
                    'exposeItemCount': 9,
                    'groupName': '历史筛选',
                    'itemList': historyData.map(item => item.metaData)
                })
            }
            _.set(panelInfo, ['filterInfo'], flattenGroupsHelper(panelInfo.filterList))
            if ( panelInfo && panelInfo.groupByParent ) {
                _.set(panelInfo, ['filterInfo'], groupByParent(_.get(panelInfo, 'filterInfo')))
            }
            setPanelInfo(panelInfo)
        }).finally(() => {
            setHistoryLoading(false)
        })
    }, [showFilterType])

    return props.showFilterType && !historyLoading ?
        <SearchTabPanel resInfo={ resInfo } showFilterType={ showFilterType } address={ address }
                        onChange={ ( value ) => {
                            if ( _panelInfo && _panelInfo.history ) {
                                Storage.getItem(showFilterType).then(historyData => {
                                    const _historyData = Array.isArray(historyData) ? historyData : []
                                    const newList = getNewLengthNoSameList((Array.isArray(value) ? value.reverse() : []).concat(_historyData))
                                    // console.log('aabbcc', newList)
                                    Storage.setItem(`filterHistory_${ showFilterType }`, newList)
                                })
                            }
                            if ( _panelInfo && _panelInfo.selectByOk ) {
                                props.onChange(showFilterType, value)
                                // TODO， 先用宏任务卡一下 useEffect没有回掉
                                setTimeout(() => {
                                    props.onOk()
                                })
                            } else {
                                props.onChange(showFilterType, value)
                            }
                        } } onOk={ props.onOk } value={ _.get(value, showFilterType, []) }
                        panelInfo={ _panelInfo } onClear={ props.clearData }
                        handleInView={ props.handleInView }/> :
        <View style={ {height: pt(375), backgroundColor: '#fff'} }>
            <View style={ styles.noDataBox }>
                <Loading/>
            </View>
        </View>
}

export default SearchPannel

const styles = StyleSheet.create({
    noDataBox: {
        textAlign: 'center',
        padding: pt(100),
        flexDirection: 'row'
    },
    reloadWord: {
        color: THEME_BASE.highLightBlue
    }
})


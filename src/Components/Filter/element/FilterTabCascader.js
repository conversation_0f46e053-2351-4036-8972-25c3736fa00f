import React, { useEffect, useState } from 'react'
import { ScrollView, StyleSheet, View } from 'react-native'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import Select from './FilterSelect'
import FilterCascader from './FilterCascader'
import {isEmpty} from '@/utils/isType'
import _ from 'lodash'

const getSubChildren = ( _options, _selected ) => _options.filter(item => {
    if ( _options.length === 0 || _selected.length === 0 ) return []
    return _.get(_selected, [0, 'childrenSameKey'], '').indexOf(_.get(item, ['sameKey'])) > -1 || _.get(_selected, [0, 'childrenSameKey'], '').indexOf(_.get(item, ['parents', 'sameKey'])) > -1
})

const getSelectedValue = ( filterList, value, defaultValue ) => {
    if ( value ) {
        const getChildren = filterList.filter(item => item?.sameKey === value.parents?.sameKey)
        const getSubChildren = () => filterList.filter(item => _.get(item, 'childrenSameKey', '').indexOf(value?.parents?.sameKey) > -1)
        return getChildren.length === 0 ? getSubChildren() : getChildren
    } else {
        const hot = filterList.filter(item => item?.metaData?.type === 'hot')
        if (!isEmpty(hot)) {
            return hot
        }
        return defaultValue
    }
}
const TabCascader = ( props ) => {
    const {options = [], value, maxHeight} = props
    const [filterList] = useState(options.filter(item => item.level === 1))
    // 热门 > 位置距离 > 首位
    let selectValue = [
        value?.find(item => item?.metaData?.type === 'hot'),
        value?.find(item => item?.metaData?.type === 'linearDistance'),
        _.get(value, [0])
    ].find(v => !isEmpty(v))
    const [selected, setSelectType] = useState(getSelectedValue(filterList, selectValue, [_.get(filterList, [0])]))
    const [subFilter, setSubFilter] = useState(getSubChildren(options, selected))

    useEffect(() => {
        setSubFilter(getSubChildren(options, selected))
    }, [selected])

    return (
        <View style={ styles.tabScroll }>
            <ScrollView style={ [styles.tabBox, {height: maxHeight}] } showsVerticalScrollIndicator={ false } bounces={ false }
                        overScrollMode='never'>
                <Select options={ filterList } value={ selected } selected={ value }
                        itemStyle={ {paddingLeft: pt(16)} }
                        isTabs={ _.get(selected, [0, 'maxDeep'], 1) === 2 }
                        onChange={ ( value ) => setSelectType([value]) }
                />
            </ScrollView>

            {
                Array.isArray(subFilter) && subFilter.length > 0 ? <View style={ styles.selectBox }>
                    <FilterCascader value={ value } maxHeight={ maxHeight }
                                    maxDeep={ _.get(selected, [0, 'maxDeep'], 1) } options={ subFilter }
                                    onChange={ props.onChange } startLevel={ 2 }/>
                </View> : null
            }
        </View>
    )
}

export default TabCascader

const styles = StyleSheet.create({
    tabScroll: {
        flexDirection: 'row',
        flexGrow: 0,
        display: 'flex',
        backgroundColor: '#fff',
        justifyContent: 'space-between'
    },
    tabBox: {
        width: pt(88),
        backgroundColor: '#f6f7fa',
        maxHeight: pt(375),
        overscrollBehaviorY: "none!important"

    },
    selectBox: {
        width: pt(287),
        maxHeight: pt(375),
        overscrollBehaviorY: "none!important"
    }
})

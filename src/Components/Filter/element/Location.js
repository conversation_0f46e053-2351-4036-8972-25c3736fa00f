import React, { useState, useEffect } from 'react'
import { Loading } from '@/BaseComponents/atoms'
import { StyleSheet, View } from 'react-native'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import { JDText, JDTouchable } from '@jdreact/jdreact-core-lib'
import _ from 'lodash'
import FilterTabCascader from './FilterTabCascader'
import { arrayDiffByKey } from '../utils'
import useFetch from '../../../common/useFetch'
import { THEME_BASE } from '../../utils/theme'

const catchData = new Map()

export const formatLocationData = ( locationList, parents = null, result = [], level = 1, maxDeep = 1 ) => {
    if ( !Array.isArray(locationList) ) return

    for ( let i = 0 ; i < locationList.length ; i++ ) {
        const item = locationList[i]
        const _maxDeep = _.get(item, ['maxDepth'], maxDeep)
        const {id, type, value} = item
        const _value = {
            metaData: _.omit(item, 'class'),
            level,
            parents,
            maxDeep: _maxDeep,
            sameKey: `${ type }-${ value }-${ id }`,
            childrenSameKey: item.childrenSameKey ? item.childrenSameKey : (Array.isArray(item.items) ? item.items.map(subItem => {
                return `${ _.get(subItem, 'type') }-${ _.get(subItem, 'value') }-${ _.get(subItem, 'id') }`
            }) : []).join('|')
        }

        result.push(_value)
        if ( Array.isArray(item.items) ) {
            formatLocationData(item.items, _value, result, level + 1, _maxDeep)
        }
    }

    return result
}

const TabScroll = ( props ) => {
    const {
        address = '',
        bizScene = 'wine-hotel',
        callerApp = 'wine-hotel-front',
        maxHeight,
    } = props
    const [filterList, setFilters] = useState([])
    const [selfValue, setSelfValue] = useState({})
    const [init, setInit] = useState(true)
    const [loading, setLoading] = useState(true)
    const {apiFetch} = useFetch()

    const getLocation = ( address ) => {
        setInit(true)
        const [provinceCode = '1', cityCode = '2814', districtCode, townCode] = address.split(',')
        apiFetch('getLocation', {
            bizScene,
            callerApp,
            districtInfo: {provinceCode, cityCode, districtCode, townCode}
        }, true).then(( [code, res] ) => {
            const filterList = formatLocationData(_.get(res, 'data.filters', []))
            setFilters(filterList)
            setLoading(false)
        })
    }

    useEffect(() => {
        getLocation(address)
    }, [])

    useEffect(() => {
        catchData.delete('catchData')
    }, [address])

    useEffect(() => {
        if ( !init ) {
            props.onChange(Object.values(selfValue).flat(1))
        }
        setInit(false)
    }, [selfValue])

    useEffect(() => {
        const selfValueList = Object.values(selfValue).reduce(( pre, cur ) => pre.concat(cur), [])
        if ( arrayDiffByKey(props.value, selfValueList) || arrayDiffByKey(selfValueList, props.value) ) {
            const _selfValue = {}
            Array.isArray(props.value) && props.value.forEach(item => {
                _selfValue[_.get(item, 'metaData.filterType')] = item
            })

            setSelfValue(_selfValue)
        }
    }, [props.value])

    if ( loading ) {
        return <View style={ styles.loadingBox }>
            <Loading/>
        </View>
    }
    return (
        <View style={ styles.tabScroll }>
            {
                Array.isArray(filterList) && filterList.length > 0 ? <FilterTabCascader options={ filterList }
                                                                                        value={ props.value }
                                                                                        maxHeight={ maxHeight }
                                                                                        onChange={ ( value = {} ) => {
                                                                                            setInit(false)
                                                                                            const {metaData} = value
                                                                                            const type = _.get(metaData, ['type'], '')
                                                                                            const _selfValue = props.value.length === 0 ? {} : selfValue
                                                                                            if ( type === 'linearDistance' ) {
                                                                                                setSelfValue({
                                                                                                    ..._selfValue,
                                                                                                    gis_distance: [{
                                                                                                        ...value,
                                                                                                        filterKey: 'gis_distance',
                                                                                                        metaData: {
                                                                                                            ...metaData,
                                                                                                            filterType: 'gis_distance',
                                                                                                            itemId: _.get(metaData, 'value', ''),
                                                                                                            filterName: _.get(metaData, 'title', ''),
                                                                                                            itemName: _.get(metaData, 'title')
                                                                                                        }
                                                                                                    }]
                                                                                                })
                                                                                            } else {
                                                                                                setSelfValue({
                                                                                                    ..._selfValue,
                                                                                                    gis_location: [{
                                                                                                        ...value,
                                                                                                        filterKey: 'gis_location',
                                                                                                        metaData: {
                                                                                                            ...metaData,
                                                                                                            filterType: 'gis_location',
                                                                                                            itemName: _.get(metaData, ['title']),
                                                                                                            itemId: JSON.stringify({
                                                                                                                ..._.omit(metaData, ['value', 'title', 'class', 'type']),
                                                                                                                category: _.get(metaData, ['type']),
                                                                                                                itemName: _.get(metaData, ['title']),
                                                                                                                geoHash: _.get(metaData, ['geohash'])
                                                                                                            }),
                                                                                                        }
                                                                                                    }]
                                                                                                })
                                                                                            }
                                                                                        } }/> :
                    <View>
                        <View style={ styles.noDataBox }>
                            <JDText>数据走丢了，点击</JDText>
                            <JDTouchable onPress={ () => getLocation(address) }>
                                <JDText style={ styles.reloadWord }>重新加载</JDText>
                            </JDTouchable>
                        </View>
                    </View>
            }
        </View>
    )
}

export default TabScroll

const styles = StyleSheet.create({
    loadingBox: {
        height: pt(375),
        paddingTop: pt(175)
    },
    textBox: {
        textAlign: 'center'
    },
    noDataBox: {
        textAlign: 'center',
        padding: pt(100),
        flexDirection: 'row'
    },
    reloadWord: {
        color: THEME_BASE.highLightBlue
    }
})

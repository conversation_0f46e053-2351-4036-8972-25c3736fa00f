import React from 'react';
import { StyleSheet, Text, View, Image, TouchableWithoutFeedback } from 'react-native';
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import { safeRun } from '../utils';

function FilterItem(props) {
    const { field, text, onChange, isActive, selectCount = 0 } = props;
    const onPress = () => {
        safeRun(onChange, field);
    };

    return (
        <TouchableWithoutFeedback onPress={ onPress }>
            <Text style={ isActive ? styles.filterItemAct : styles.filterItem }>
                {
                    <Text>{ text }
                        {
                            selectCount > 0
                                ? <View style={ styles.selectCount }>
                                    <Text style={ styles.selectCountNum }
                                    >{ selectCount }</Text>
                                </View> : null
                        }
                    </Text>
                }
                <View style={ styles.filterIconBox }>
                    <Image style={ styles.filterIcon }
                        source={ {
                            uri: isActive
                                ? 'https://img13.360buyimg.com/imagetools/jfs/t1/101578/38/51356/301/6715c251Fd3ba6f4c/05a704296ea25e49.png'
                                : 'https://img10.360buyimg.com/imagetools/jfs/t1/107306/36/55954/319/6715c257F32030f9b/f2ce587dc9b7f834.png'
                        } } />
                </View>
            </Text>
        </TouchableWithoutFeedback>
    );
}

export default FilterItem;

const styles = StyleSheet.create({
    filterItemAct: {
        width: pt(375 / 4),
        textAlign: 'center',
        height: pt(16),
        fontSize: pt(14),
        lineHeight: pt(16),
        verticalAlign: 'top',
        color: '#006EEB'
    },
    filterItem: {
        width: pt(375 / 4),
        textAlign: 'center',
        height: pt(16),
        fontSize: pt(14),
        lineHeight: pt(16),
        verticalAlign: 'top',
        color: '#1A1A1A'
    },
    filterIconBox: {
        paddingLeft: pt(6),
        fontWeight: '500',
        paddingTop: pt(9),
        height: pt(16)
    },
    filterIcon: {
        width: pt(7),
        height: pt(4)
    },
    selectCount: {
        width: pt(18),
        textAlign: 'center',
        color: '#fff',
        height: pt(14),
        paddingTop: pt(1.5),
        paddingLeft: pt(4)
    },
    selectCountNum: {
        color: '#fff',
        fontSize: pt(10),
        textAlign: 'center',
        lineHeight: pt(14),
        height: pt(14),
        backgroundColor: '#006eeb',
        borderRadius: pt(7),
        overflow: 'hidden'
    }
});

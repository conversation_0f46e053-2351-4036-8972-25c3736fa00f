import React, { useState, useRef } from 'react'
import { ScrollView, SectionList, StyleSheet, TouchableWithoutFeedback, View } from 'react-native'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import { JDText } from '@jdreact/jdreact-core-lib'
import Select from './_FilterSelect'
import TagList from './FilterTagList'
import _ from 'lodash'
import { Image } from '@tarojs/components'
import { getImg, scrollTo } from '../utils'
import { THEME_BASE } from '../../utils/theme'
import useJumpTo from '../../../common/useJumpTo'

const ENUM_ALL = `-909090`

const getSelectedValue = (filterList, value, defaultValue) => {
    if (value) {
        return filterList.filter(item => {
            return value?.parents?.sameKey.indexOf(item?.sameKey) > -1
        })
    } else {
        return defaultValue
    }
}

const TabScroll = (props) => {
    const {filterInfo = [], onChange, value, showTopClear, maxHeight} = props
    const [filterList] = useState(filterInfo.filter(item => item.level === 1))
    const [selectIndex, setSelectIndex] = useState(getSelectedValue(filterList, _.get(value, [0]), [_.get(filterList, [0])]))
    const Selection = useRef(null)
    const jumpTo = useJumpTo()

    const [sectionData, setSectionData] = useState(Array.isArray(filterInfo) ? filterInfo.filter(x => x.parents && x.parents?.sameKey === selectIndex[0]?.sameKey).map((item, index) => {
        return {
            title: _.get(item, 'metaData.filterName', ''),
            linkWord:_.get(item, ['metaData', 'explain', 'text']) ,
            link: _.get(item, ['metaData', 'explain', 'jumpUrl']) ,
            desText: _.get(item, ['metaData', 'desc', 'text']),
            data: [filterInfo.filter(subItem => {
                return subItem.level === 3 && _.get(subItem, ['parents', 'sameKey']) === item?.sameKey
            })],
            info: _.get(item, ['info'], {}),
            indexSection: index
        }
    }) : [])

    const selectItem = (value) => {
        setSectionData(filterInfo.filter(x => x.parents && x.parents?.sameKey === value?.sameKey).map((item, index) => {
            return {
                title: _.get(item, 'metaData.filterName', ''),
                data: [filterInfo.filter(subItem => {
                    return subItem.level === 3 && _.get(subItem, ['parents', 'sameKey']) === item?.sameKey
                })],
                info: _.get(item, ['info'], {}),
                indexSection: index
            }
        }))
        setSelectIndex([value])

        setTimeout(() => {
            scrollTo(Selection, null, null, undefined, 0)
        }, 300)
    }

    return (
        <View style={ [styles.tabScroll] }>
            <ScrollView style={ [styles.tabBox] } showsVerticalScrollIndicator={ false } bounces={ false }
                        overScrollMode="never">
                <Select options={ filterList }
                        selected={ value }
                        value={ selectIndex }
                        itemStyle={ {paddingLeft: pt(16)} }
                        onChange={ selectItem }/>
            </ScrollView>
            <SectionList style={ [styles.tagBox, {height: pt(375)}] } ref={ Selection }
                         initialNumToRender={ 6 }
                         sections={ sectionData }
                         showsVerticalScrollIndicator={ false } bounces={ false }
                         overScrollMode="never"
                         renderItem={ ({section}) => {
                             const {title, data, info = {}, indexSection, linkWord, link, desText} = section

                             return <View style={ styles.tagListItemBox } key={ indexSection }>
                                <TagList
                                 key={ indexSection } { ...info }
                                 title={ title }
                                 data={ _.get(data, 0, []) }
                                 onChange={ (valueList) => {
                                     const lastSelect = _.get(valueList, [0, 'metaData', 'itemId'])
                                     const type = _.get(selectIndex, [0, 'metaData', 'groupCode'])
                                    //  console.log('aabbcc', lastSelect)
                                     if (lastSelect === ENUM_ALL) {
                                         onChange(valueList.filter(item => {
                                             return (_.get(item, 'uuId', '').indexOf(type) < 0 || _.get(item, ['metaData', 'itemId']) === ENUM_ALL)
                                         }))
                                     } else {
                                         onChange(valueList.filter(item => {
                                             return (_.get(item, 'uuId', '').indexOf(type) < 0 || _.get(item, ['metaData', 'itemId']) !== ENUM_ALL)
                                         }))
                                     }
                                 } } width={ 279 }
                                 value={ value }/>
                                {
                                 !!desText && desText.length > 0 && <JDText style={styles.descText}>{desText}</JDText>
                                 }
                                 {
                                     showTopClear && indexSection === 0 ?
                                         <TouchableWithoutFeedback onPress={ () => {
                                             const type = _.get(selectIndex, [0, 'metaData', 'groupCode'])
                                             onChange(value.filter(item => {
                                                 return _.get(item, 'uuId', '').indexOf(type) < 0
                                             }))
                                         } }><View
                                             style={ styles.topClearBox }>
                                             <Image style={ styles.clearIcon } src={ getImg('clear') }/>
                                             <JDText style={ styles.clearWord }>清除选项</JDText>
                                         </View></TouchableWithoutFeedback> : null
                                 }
                                {
                                    indexSection !== 0 && !!linkWord && linkWord.length > 0 && !!link && link.length > 0 &&
                                         <TouchableWithoutFeedback onPress={ () => {
                                            console.log('link', link);
                                            if (link ) {
                                                jumpTo({to: 'web', params: {url: link}})
                                            }
                                         } }><View
                                             style={ styles.topClearBox }>
                                              <Image style={ styles.linkIcon }
                                                source={ {uri: 'https://img14.360buyimg.com/imagetools/jfs/t1/135893/18/49718/411/67164594F901f961b/03a5baab453a73c4.png'} }/>
                                             <JDText style={ styles.linkWord }>{linkWord}</JDText>
                                         </View></TouchableWithoutFeedback> 
                                 }
                             </View>
                         } }/>
        </View>
    )
}

export default TabScroll

const styles = StyleSheet.create({
    tabScroll: {
        flexDirection: 'row',
        flexGrow: 0,
        display: 'flex',
        backgroundColor: '#fff',
        justifyContent: 'space-between',
        position: 'relative',
    },
    tabBox: {
        width: pt(88),
        backgroundColor: '#f6f7fa',
    },
    tagBox: {
        padding: pt(8),
    },
    topClearBox: {
        position: 'absolute',
        right: pt(8),
        top: pt(-8),
        zIndex: 10,
        alignItems: 'center',
        left: pt(90),
        height: pt(30),
        flexDirection: 'row-reverse',
    },
    clearIcon: {
        width: pt(14),
        height: pt(14),
        marginLeft: pt(3)
    },
    clearWord: {
        fontSize: pt(12),
        color: THEME_BASE.secondaryColor
    },
    tagListItemBox: {
        position: 'relative',
        marginBottom: pt(2),
    },
    descText: {
        marginTop: -pt(20),
        marginBottom: pt(22),
        fontSize: pt(12),
        color: '#888B94'
    },
    linkWord: {
        fontSize: pt(12),
        color: '#006EEB'
    },
    linkIcon: {
        marginLeft: pt(4),
        width: pt(3),
        height: pt(8)
    },
})

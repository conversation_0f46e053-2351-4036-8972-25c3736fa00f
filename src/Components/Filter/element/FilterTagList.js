import React, { useEffect, useState } from 'react'
import { StyleSheet, View, Image, TouchableWithoutFeedback } from 'react-native'
import { JDText, JDTouchable } from '@jdreact/jdreact-core-lib'
import Tag from './FilterTag'
import _ from 'lodash'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import { hasValue, safeRun } from '../utils'
import { isAndroid } from '@ltfe/ltfe-core-lib/lib/utiles'
import useJumpTo from '../../../common/useJumpTo'

const Select = ( props ) => {
    const {
        data,
        width = 343,
        field,
        title,
        linkWord,
        value = [],
        fieldNames = {label: 'label', value: 'value'},
        exposeItemCount = 3,
        itemWidth,
        itemCount = 3,
        multi,
        descInfo,
        link
    } = props

    const [expended, setExpended] = useState(false)
    const jumpTo = useJumpTo()

    const onToggle = () => {
        setExpended(!expended)
    }

    const onChange = ( value ) => {
        const _value = props.value || []
        if ( props.value.some(subItem => subItem?.sameKey === value?.sameKey) ) {
            safeRun(props.onChange, _value.filter(subItem => subItem?.sameKey !== value?.sameKey))
        } else {
            const _multi = _.get(value, ['metaData', 'multi'], multi)

            if ( _multi ) {
                let removeList = []
                _value.forEach(( _item, key ) => {
                    if ( _.get(value, 'metaData.filterType', '1') === _.get(_item, 'metaData.filterType', '2') ) {
                        removeList.push(key)
                    }
                })
                removeList = removeList.slice(0, removeList.length + _multi - 1)

                while ( removeList.length ) {
                    const removeIndex = removeList.pop()
                    _value.splice(removeIndex, 1)
                }

                _value.unshift(value)
                safeRun(props.onChange, _value)
            } else {
                _value.unshift(value)
                safeRun(props.onChange, _value)
            }
        }
    }

    return (
        <View style={ [styles.tagListBox, {width: pt(width)}] }>
            <View style={ styles.titleBox }>
                <View>
                    {
                        hasValue(title) ? <JDText style={ styles.title }>{ title }</JDText> : null
                    }
                </View>
                {
                    hasValue(linkWord) ? <TouchableWithoutFeedback onPress={ () => {
                        if ( link ) {
                            jumpTo({to: 'web', params: {url: link}})
                        }
                    } }>
                        <View style={styles.linkBox}>
                            <JDText style={ styles.link }>
                                { linkWord }
                            </JDText>
                            <View style={ styles.linkIconBox }>
                                <Image style={ styles.linkIcon }
                                       source={ {uri: 'https://img14.360buyimg.com/imagetools/jfs/t1/135893/18/49718/411/67164594F901f961b/03a5baab453a73c4.png'} }/>
                            </View>
                        </View>
                    </TouchableWithoutFeedback> : null
                }
                {
                    typeof exposeItemCount === 'number' && data?.length > exposeItemCount
                        ? <JDTouchable onPress={ onToggle }>
                            <View style={ styles.iconWordBox }>
                                <JDText style={ styles.expendedBox }>
                                    { expended ? '收起' : '展开' } <JDText> </JDText>
                                </JDText>
                                <Image
                                    style={ [styles.expendedIcon, expended ? styles.expendedUp : styles.expendedDown] }
                                    source={ {uri: 'https://img14.360buyimg.com/imagetools/jfs/t1/135893/18/49718/411/67164594F901f961b/03a5baab453a73c4.png'} }/>
                            </View>
                        </JDTouchable>
                        : null
                }
            </View>
            <View style={ styles.tagBox }>
                {
                    Array.isArray(data) ? data.slice(0, expended ? undefined : exposeItemCount).map(( item, index ) => {
                        return <View style={ [styles.gridBox, {width: Math.floor(pt(width / itemCount - 0.5))}] }
                                     key={ index }><Tag
                            // width={ typeof itemWidth === 'number' ? itemWidth : Math.floor((width - 21) / 3) }
                            onChange={ onChange }
                            fieldNames={ fieldNames }
                            field={ field }
                            data={ item }
                            value={ item }
                            tagStyle={{fontSize: pt(12)}}
                            isActive={ value.some(subItem => item?.sameKey === subItem?.sameKey) }
                            key={ index }/></View>
                    }) : null
                }
            </View>
            {
                descInfo ? <JDText style={ styles.descWord }>{ descInfo }</JDText> : null
            }
        </View>
    )
}

export default Select

const styles = StyleSheet.create({
    linkBox: {
        flexDirection: "row",
        alignItems: "center"
    },
    tagListBox: {
        marginBottom: pt(22),
    },
    gridBox: {
        // paddingBottom: pt(7),
        paddingRight: pt(6),
        paddingTop: pt(1)
    },
    titleBox: {
        flexDirection: 'row',
        display: 'flex',
        justifyContent: 'space-between',
        marginBottom: pt(16)
    },
    link: {
        color: '#0068ff'
    },
    linkIcon: {
        width: pt(4),
        height: pt(8)
    },
    linkIconBox: {
        height: pt(10),
        paddingTop: pt(1),
        paddingLeft: pt(6),
        marginRight: pt(16)
    },
    title: {
        fontSize: pt(14),
        lineHeight: pt(16),
        height: pt(16),
        color: '#1A1A1A',
        fontWeight: isAndroid ? 'bold' : '600',
        paddingLeft: pt(4)
    },
    tagBox: {
        flexDirection: 'row',
        display: 'flex',
        flexWrap: 'wrap',
    },
    expendedBox: {
        fontSize: pt(12),
        fontWeight: '400',
        color: '#0068ff',
        alignItems: 'center'
    },
    iconWordBox: {
        flexDirection: 'row',
        display: 'flex',
        justifyContent: 'flex-start',
        flexWrap: 'nowrap',
        alignItems: 'center',
        marginRight: pt(16)
    },
    expendedUp: {
        transform: [{rotate: '270deg'}]
    },
    expendedDown: {
        transform: [{rotate: '90deg'}]
    },
    expendedIcon: {
        height: pt(8),
        width: pt(4),
    },
    descWord: {
        color: '#888B94',
        fontSize: pt(12),
        marginTop: pt(4)
    }
})

import React, { useState, useRef, useEffect } from 'react'
import { SectionList, StyleSheet, View } from 'react-native'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import Select from './_FilterSelect'
import TagList from './FilterTagList'
import { scrollTo } from '../utils'
import _ from 'lodash'

const getSelectedValue = ( filterList, value, defaultValue ) => {
    if ( value ) {
        return filterList.filter(item => item?.sameKey === value?.parents?.sameKey)
    } else {
        return defaultValue
    }
}

const getActValue = ( filterList, value, defaultValue ) => {
    if ( value ) {
        return filterList.filter(item => item?.sameKey === value?.sameKey)
    } else {
        return defaultValue
    }
}

const TabScroll = ( props ) => {
    const {filterInfo, value, maxHeight} = props
    const [filterList] = useState(filterInfo.filter(item => item.level === 1))
    const Selection = useRef(null)
    const [selectIndex, setSelectIndex] = useState(getSelectedValue(filterList, _.get(value, [0]), [_.get(filterList, [0])]))
    const [scrollFlag, setScrollFlag] = useState(false)

    const [sectionData] = useState(filterList.map(item => {
        return {
            info: {
                exposeItemCount: _.get(item, ['metaData', 'exposeItemCount']),
                multi: _.get(item, ['metaData', 'multi'])
            },
            title: _.get(item, 'metaData.filterName'),
            data: [filterInfo.filter(x => x.parents === item)],
            ...item
        }
    }))

    const setSelect = ( value ) => {
        setScrollFlag(true)
        setSelectIndex([value])
        scrollTo(Selection, filterList, value)
        setTimeout(() => {
            setScrollFlag(false)
        }, 300)
    }

    useEffect(() => {
        // 当被展开时，该状态只能被渲染时获取
        const selectParent = _.get(value, [0, 'parents'])
        setTimeout(() => {
            scrollTo(Selection, filterList, selectParent)
        }, 300)
    }, [props.showFilterType])

    return (
        <View style={ styles.tabScroll }>
            <Select options={ filterList } showsVerticalScrollIndicator={ false } bounces={ false }
                    overScrollMode='never'
                    selected={ value }
                    value={ selectIndex }
                    style={ {height: pt(350)} }
                    onChange={ setSelect }/>

            <SectionList ref={ Selection }
                         showsVerticalScrollIndicator={ false } bounces={ false }
                         overScrollMode='never'
                         sections={ sectionData }
                         style={ [styles.tagBox, {maxHeight: pt(350)}] }
                         onViewableItemsChanged={ ( {changed, viewableItems} ) => {
                             const showItem = _.get(viewableItems, [0, 'section'])
                             const findParentIndex = getActValue(filterList, showItem)
                             if ( Array.isArray(findParentIndex) && findParentIndex.length > 0 && !scrollFlag ) {
                                 setSelectIndex(getActValue(filterList, showItem))
                             }
                         } }
                         renderItem={ ( {item, section} ) => {
                             const {info} = section
                             return <TagList title={ section.title } { ...info } value={ value }
                                             onChange={ ( value ) => {
                                                 props.onChange(value)
                                             } } data={ item } width={ 270 }/>
                         } }/>
        </View>
    )
}

export default TabScroll

const styles = StyleSheet.create({
    tabScroll: {
        flexDirection: 'row',
        flexGrow: 0,
        display: 'flex',
        backgroundColor: '#fff',
        justifyContent: 'space-between'
    },
    tabBox: {
        width: pt(88),
        backgroundColor: '#f6f7fa',
        height: pt(375),
    },
    tagBox: {
        padding: pt(8)
    }
})

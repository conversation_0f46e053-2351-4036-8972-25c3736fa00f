import React, { useState } from 'react'
import { ScrollView, StyleSheet, View } from 'react-native'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import Select from './FilterSelect'
import _ from 'lodash'

const getSelectedValue = ( filterList, value, defaultValue ) => {
    if ( value ) {
        return filterList.filter(item => item?.sameKey === value?.parents?.sameKey)
    } else {
        return defaultValue
    }
}

const TabCascader = ( props ) => {
    const {filterInfo, value} = props
    const [filterList] = useState(filterInfo.filter(item => item.level === 1))
    const [selectIndex, setSelectIndex] = useState(getSelectedValue(filterList, _.get(value, [0]), [_.get(filterList, [0])]))

    const subItem = filterInfo.filter(item => {
        return item.parents?.uuId === _.get(selectIndex, [0])?.uuId
    })

    const onChange = ( value ) => {
        props.onChange([value])
    }

    return (
        Array.isArray(subItem) ? <View style={ styles.tabScroll }>
            <ScrollView style={ styles.tabBox } showsVerticalScrollIndicator={ false } bounces={ false }
                        overScrollMode='never'>
                <Select options={ filterList }
                        selected={ value }
                        value={ selectIndex }
                        unParent={true}
                        itemStyle={{paddingLeft: pt(16)}}
                        onChange={ ( value ) => setSelectIndex([value]) }/>
            </ScrollView>
            <ScrollView style={ styles.selectBox } showsVerticalScrollIndicator={ false } bounces={ false }
                        overScrollMode='never'>
                <Select options={ subItem }
                        value={ value }
                        itemStyle={{fontWeight: 400}}
                        showSelectImage={ true }
                        onChange={ onChange }/>
            </ScrollView>
        </View> : null
    )
}

export default TabCascader

const styles = StyleSheet.create({
    tabScroll: {
        flexDirection: 'row',
        flexGrow: 0,
        display: 'flex',
        backgroundColor: '#fff',
        justifyContent: 'space-between'
    },
    tabBox: {
        width: pt(88),
        height: pt(375),
        backgroundColor: '#f6f7fa'
    },
    selectBox: {
        width: pt(287),
        maxHeight: pt(375),
        paddingLeft: pt(12),
        paddingRight: pt(12)
    }
})

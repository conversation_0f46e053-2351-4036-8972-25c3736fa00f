import React, { useEffect, useRef } from 'react'
import { Platform, SectionList, StyleSheet } from 'react-native'
import Option from './FilterOption'

import _ from 'lodash'
import { hasValue, safeRun, scrollTo } from '../utils'
import TagList from './FilterTagList'
import { isIOS, pt } from '@ltfe/ltfe-core-lib/lib/utiles'

const checkTipIcon = ( option = {}, value = [] ) => {
    const parentUuId = _.get(option, 'childrenSameKey', '-----')
    // console.log(option, value, 'aabbee')
    return value.some(item => {
        return parentUuId.indexOf(item?.sameKey) > -1
    })
}

const Select = ( props ) => {
    const {
        options,
        mode = 'single',
        showLine,
        showSelectImage,
        value,
        selected, style, itemStyle, scrollFlag
    } = props

    const Selection = useRef(null)
    const sectionData = options.map(item => {
        return {
            title: '',
            data: [item]
        }
    })

    useEffect(() => {
        // 当被展开时，该状态只能被渲染时获取
        const selectParent = _.get(selected, [0, 'parents'])
        setTimeout(() => {
            scrollTo(Selection, options, selectParent)
        }, 300)
    }, [props.showFilterType])

    useEffect(() => {
        if ( !scrollFlag ) {
            const selectParent = _.get(props.value, [0])
            scrollTo(Selection, options, selectParent)
        }
    }, [props.value])

    return (
        <SectionList ref={ Selection } style={ [styles.tabBox, style] }
                     sections={ sectionData }
                     showsVerticalScrollIndicator={ false } bounces={ false }
                     overScrollMode="never"
                     renderItem={ ( {item, section} ) => {
                         return <Option mode={ mode } metaData={ item } showLine={ showLine } key={ item.uuId }
                                        showSelectImage={ showSelectImage }
                                        showTipIcon={ checkTipIcon(item, selected) }
                                        itemStyle={ {paddingLeft: pt(16)} }
                                        isActive={ Array.isArray(value) && value.some(valueItem => valueItem.uuId === item.uuId) }
                                        onChange={ props.onChange }/>
                     } }/>
    )
}

export default Select

const styles = StyleSheet.create({
    tabBox: {
        width: pt(88),
        backgroundColor: '#f6f7fa',
        height: pt(375)
    }
})

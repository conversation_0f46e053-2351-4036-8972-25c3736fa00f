import Custom from './element/FilterCustom'
import React, { useEffect, useState } from 'react'
import { findInListKey, safeRun, arrayDiffByKey } from './utils'
import _ from 'lodash'
import SearchPanel, { flattenGroupsHelper } from './element/SearchPanel'
import SearchTabScroll from './element/SearchTabScorll'
import SearchTabList from './element/SearchTabList'
import SearchList from './element/SearchList'
import Location from './element/Location'
import SearchTabSelect from './element/SearchTabSelect'
import SearchSelect from '@/Components/Filter/element/SearchSelect'
import SearchTabListGroup from './element/SearchTabListGroup'

// 实例集合,暂时只支持组件一次使用，理论上filter也不会使用两次。先这样。

const Filter = ( props = {} ) => {
    const {options, initValue} = props
    const [value, onChange] = useState({})
    const [editKey, setEditKey] = useState('')
    const [showFilterType, setShowFilterType] = useState(props.showFilterType)
    const [isInit, setIsInit] = useState(true)
    const [unDispatchPropsRun] = useState(props.unDispatch)

    useEffect(() => {
        if ( typeof props.showFilterType === 'string' ) {
            setIsInit(true)
        }
        // 同步当前打开面板的value值。
        Filter.Instance.set('formValues', _.cloneDeep(value))
        setShowFilterType(props.showFilterType)
    }, [props.showFilterType])
    //

    // 数据处理
    const onValuesChange = ( field = '', newValue ) => {
        setIsInit(false)

        let _value = {...value}
        _.set(_value, field, newValue)

        // TODO 出现不同类型的sortType 同时设置一下sortType 得下游配合修改。
        if ( field.indexOf('SortType') > -1 ) {
            _.set(_value, 'sortType', newValue)
        }
        setEditKey(field)
        onChange(_value)
    }

    const clearData = ( key, clearFilterType ) => {
        // console.log('aabbcc cleardata', key, clearFilterType)
        setIsInit(false)
        if ( typeof key === 'string' ) {
            removeKey(key, clearFilterType)
        } else {
            // console.log('Filter onChange 清空了')
            onChange(false)
            safeRun(props.onClear, showFilterType)
        }
    }

    // 过滤类型数组下的某个值
    const filterKeyValue = ( keys, clearFilterType ) => {
        setIsInit(false)
        const selfValue = Filter.Instance.get('formValues')
        const _value = {...selfValue}
        Object.keys(_value).forEach(itemKey => {
            const changeValues = _.get(_value, itemKey)

            if ( Array.isArray(changeValues) ) {
                _.set(_value, itemKey, changeValues.filter(item => item.filterKey !== keys))
            }
        })
        onChange(_value)
        safeRun(props.onClear, clearFilterType, _value)
    }

    // 过滤类型数组下的某个值
    const filterClearByKey = ( keys, clearFilterType ) => {
        setIsInit(false)
        const selfValue = Filter.Instance.get('formValues')
        const _value = {...selfValue}
        Object.keys(_value).forEach(itemKey => {
            const changeValues = _.get(_value, itemKey)

            if ( Array.isArray(changeValues) ) {
                _.set(_value, itemKey, changeValues.filter(item => _.get(item, ['metaData', 'filterType']) !== keys))
            }
        })
        onChange(_value)
        safeRun(props.onClear, clearFilterType, _value)
    }

    const removeKey = ( keys, clearFilterType ) => {
        // console.log('aabbcc', clearFilterType)
        setIsInit(false)
        const selfValue = Filter.Instance.get('formValues')
        const _value = {...selfValue}
        _.unset(_value, keys)
        onChange(_value)
        safeRun(props.onClear, typeof clearFilterType === 'string' ? clearFilterType : showFilterType)
    }

    const _onOk = () => {
        const {formatHooks} = props
        let resList = [], _formatHooks = formatHooks
        const selfValue = Filter.Instance.get('formValues')

        if ( !Array.isArray(formatHooks) ) {
            _formatHooks = [formatHooks]
        }

        _formatHooks.forEach(( hook ) => {
            if ( typeof hook === 'function' ) {
                resList.push(hook(_.cloneDeep(selfValue)))
            }
        })
        safeRun(props.onOk, selfValue, ...resList, showFilterType)
    }

    useEffect(() => {
        const propsList = Object.values(props?.value || {}).reduce(( pre, cur ) => pre.concat(cur), [])
        const selfList = Object.values(value).reduce(( pre, cur ) => pre.concat(cur), [])
        if ( arrayDiffByKey(propsList, selfList) || arrayDiffByKey(selfList, propsList) ) {
            setIsInit(false)
            onChange(_.cloneDeep(props.value))
        }
    }, [props.value])

    useEffect(() => {
        const {onChange, formatHooks} = props
        Filter.Instance.set('formValues', value)
        if ( !isInit && value ) {
            let resList = [], _formatHooks = formatHooks
            if ( !Array.isArray(formatHooks) ) {
                _formatHooks = [formatHooks]
            }

            _formatHooks.forEach(( hook ) => {
                if ( typeof hook === 'function' ) {
                    resList.push(hook(_.cloneDeep(value)))
                }
            })
            // console.log('filter onChange 通知父级改变了')
            safeRun(onChange, _.cloneDeep(value), ...resList, showFilterType)
        }
    }, [value])

    useEffect(() => {
        setIsInit(false)
        Filter.Instance.set('onClear', clearData)
        Filter.Instance.set('clearByKey', filterKeyValue)
        if (props.id) {
            Filter.Instance.set(props.id+'-filterClearByKey', filterClearByKey)
        } else {
            Filter.Instance.set('filterClearByKey', filterClearByKey)
        }
    }, [])

    useEffect(() => {
        setShowFilterType(showFilterType)
    }, [showFilterType])

    return showFilterType
        ? <Filter.SearchPannel { ...props } field={ [props.field] } onOk={ _onOk }
                               onChange={ onValuesChange }
                               clearData={ clearData } removeKey={ removeKey }
                               value={ value }
                               option={ findInListKey(options, showFilterType, 'filterPanelCode') }/> : null
}

// 筛选组

// 大搜专用 - schema内置无法通用

// 面板，处理数据用
Filter.SearchPannel = SearchPanel
// tab切换滚动列表
Filter.SearchTabScroll = SearchTabScroll
// tab长列表
Filter.SearchTabList = SearchTabList
// tab长列表
Filter.SearchTabListGroup = SearchTabListGroup
// 长列表
Filter.SearchList = SearchList
// tab单选
Filter.SearchTabSelect = SearchTabSelect
// 下拉选项
Filter.Select = SearchSelect
// 自定义组件
Filter.Custom = Custom
// 地址选择
Filter.Location = Location

Filter.formatData = flattenGroupsHelper

// Filter实例方法
Filter.Instance = new Map()

export const compsMapper = new Map()

// 下拉面板
export default Filter

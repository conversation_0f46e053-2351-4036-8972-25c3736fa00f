import React, { memo, useState } from 'react'
import HotelSearch from './mode/HotelSearch'
import CardList from './mode/CardList'
import Collapse from './mode/Collapse'
import Scenic from './mode/Scenic'
import LineHotel from './mode/LineHotel'
import LineScenic from './mode/LineScenic'
import LineTraffic from './mode/LIneTraffic'
import Element from './element/index'
import { isComps, objSetMapperObj, safeRun } from './element/utils'
import { View } from 'react-native'

const CardModes = {
    '2': Scenic, // 景点
    '1': HotelSearch, // 酒店
    line0: LineTraffic,// 双列交通
    line1: LineHotel,// 双列酒店
    line2: LineScenic// 双列景点
}

// 京东协议价
const Card = ( props ) => {
    const {
        mode = 'hotel',
        type = '',
        data,
        onPress,
        onToggle,
        fieldNames,
        collapse,
        onTagPress,
        showLine,
        disabled,
        style,
        layoutInfo,
        cardWidth,
        isFirst,
        isClick,
        mtaTrack
    } = props
    const Card = CardModes[`${ type }${ mode }`] || {}
    const compFieldNames = Card?.modeMap?.fieldNames || {}
    const [expended, setExpended] = useState(false)
    const [hasCollapse] = useState(isComps(collapse))
    const _actions = {
        onPress: ( event ) => {
            safeRun(onPress, data, event)
        },
        onToggle: ( event ) => {
            safeRun(onToggle, data, event, !expended)
            setExpended(!expended)
        },
        // 机票用
        onTagPress: ( event ) => {
            safeRun(onTagPress, data, event)
        }
    }

    return <>
        <Card { ...objSetMapperObj(fieldNames || compFieldNames, data) } { ..._actions } showLine={ showLine }
              disabled={ disabled }
              style={ style }
              cardWidth={ cardWidth }
              layoutInfo={ layoutInfo }
              isFirst={isFirst}
              isClick={isClick}
              expended={ expended }
              mtaTrack={mtaTrack}
        />
        {
            hasCollapse && expended
                ? <Collapse expended={ expended } collapse={ collapse } showLine={ showLine }/> : <></>
        }
    </>
}

export default memo(Card)

Card.Group = ( props ) => {
    const {children, cardList = [], mode = 'hotel', collapse, cardBoxStyle, showLine, onPress, onToggle} = props
    if ( typeof children === 'object' ) {
        return <View>{ children }</View>
    } else {
        return <CardList
            cardList={ cardList }
            mode={ mode }
            showLine={ showLine }
            cardBoxStyle={ cardBoxStyle }
            collapse={ collapse }
            onPress={ onPress }
            onToggle={ onToggle }
        />
    }
}

export const MapperElement = Element

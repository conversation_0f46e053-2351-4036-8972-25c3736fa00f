import React from 'react'
// 基础组件
import { StyleSheet, TouchableWithoutFeedback, View } from 'react-native'
import { JDText, JDImage } from '@jdreact/jdreact-core-lib'
// 卡片元素组件
import Price from '../element/price'
import TitleRender from '../element/TitleRender'
import Rank from '../element/Rank'
import TagRender from '../element/TagRender'
import { getImageUrl } from '@/assets/imgs'
// 工具类
import { pt, px, isAndroid } from '@ltfe/ltfe-core-lib/lib/utiles'
import { safeRun } from '../element/utils'
import _ from 'lodash'
import { getUuid } from '@/utils';
import { getImg } from '../../Filter/utils'
import { Dimensions } from 'react-native';
const { width } = Dimensions.get('window');
export const hasValue = (text) => {
  return text !== null && text !== '' && text !== undefined;
};
const IMGS = {
  defaultImg: getImageUrl('derfaultImg')
}
const ScenicCard = (props) => {
  const {
    title,
    src,
    price,
    oPrice,
    disabled,
    promotionTagListMap,
    layoutInfo,
    cardWidth,
    style,
  } = props
  const [descWidth, setDescWidth] = React.useState(0)
  const onPress = (target) => (event) => safeRun(props.onPress, {
    event,
    trigger: target
  })
  const descLayout = (e) => {
    let width = e.nativeEvent.layout.width + pt(6);
    setDescWidth(width);
  }
  // 标题节点
  // const titleNode = React.useMemo(() => {
  const mainHotelTitleAfter = promotionTagListMap?.['mainHotelTitleAfter'] // 主要景点标题后缀
  let tagName = '', starCount = 0
  if (mainHotelTitleAfter && Array.isArray(mainHotelTitleAfter) && mainHotelTitleAfter.length) {
    mainHotelTitleAfter.forEach((item) => {
      if (item?.styleCode === "Tag") {
        tagName = item?.listShowName ?? ''
      }
      if (item?.styleCode === "Stars") {
        starCount = (item?.listShowName ?? 0) * 1
      }
    })
  }
  let titleWidth = 0, tagWidth = 0;
  if (layoutInfo && Array.isArray(layoutInfo) && layoutInfo.length) {
    layoutInfo.forEach((item) => {
      if (item.text === title) {
        titleWidth = item?.layout?.width ?? 0
      }
      if (item.text === tagName) {
        tagWidth = item?.layout?.width ?? 0
      }
    })
  }
  // cardLayout: paddingLeft: pt(8) ,paddingRight: pt(8)
  const spacing = isAndroid ? pt(8) : pt(20)
  // layout: paddingLeft: pt(12),  paddingRight: pt(12)
  // cardLayout: spacing: pt(8), paddingLeft: pt(8) ,paddingRight: pt(8)
  const defaultWidth = (width - pt(48)) / 2
  const titleNode = (<View style={styles.titleWr}>
    <TitleRender isComprehensive={true} titleWidth={titleWidth} titleSize={15} cardWidth={!!cardWidth ? (cardWidth - spacing) : defaultWidth} tagWidth={tagWidth} title={title} disabled={disabled} starCount={starCount}
      tagName={!!starCount ? '' : tagName} />
  </View>)
  // }, [layoutInfo, cardWidth, title, disabled, promotionTagListMap?.['mainHotelTitleAfter']])

  // const TagNode = React.useMemo(() => {
  const options = []
  const mainHotelScore = promotionTagListMap?.['mainHotelScore']
  if (mainHotelScore && Array.isArray(mainHotelScore) && mainHotelScore?.length) {
    mainHotelScore.map((item, index) => {
      options.push({
        title: item?.listShowName,
        value: (item?.labelCode ?? index),
        styleId: 'tag_score_warp_comp',
      })
    })
  }
  const mainHotelPromotion = promotionTagListMap?.['mainHotelPromotion'] // 主要景点促销
  if (mainHotelPromotion && Array.isArray(mainHotelPromotion) && mainHotelPromotion.length) {
    mainHotelPromotion?.slice(0, 2)?.forEach((item, index) => {
      if (item?.styleCode === 'TagList') {
        options.push({
          title: item?.listShowName, // 标题
          value: item?.labelCode ?? index
        })
      }
    })
  }
  const TagNode = options.length ? <View style={styles.tagWr}><TagRender style={{ overflow: 'hidden', height: pt(18) }} options={options} /></View> : <></>
  // }, [promotionTagListMap?.['mainHotelPromotion'], promotionTagListMap?.['mainHotelScore']])
  // const descNode = React.useMemo(() => {
  let descNode;
  const comment = promotionTagListMap?.['mainHotelComment']
  if (Array.isArray(comment) && comment.length) {
    descNode =
      <View style={{flexShrink: 1,display: 'flex', flexDirection: 'row'}}>
        <View style={[styles.subTitleWr, {display: 'flex', flexDirection: 'column'}]}>
          {comment.map((item, index) => <View style={styles.summary_warp} key={index}>
            <JDText numberOfLines={1} style={[styles.summary, disabled ? styles.disabled : {}]} onLayout={descLayout}>
              {item.listShowName}
            </JDText>
          </View>)}
          <View style={[styles.summary_line]}></View> 
        </View>
      </View>

  }
  // else {
  //   return <></>
  // }
  // }, [promotionTagListMap?.['mainHotelComment'], descWidth])
  // const locationNode = React.useMemo(() => {
  let locationNode;
  const mainHotelLocationDistance = promotionTagListMap?.['mainHotelLocationDistance']
  if (Array.isArray(mainHotelLocationDistance) && mainHotelLocationDistance.length > 0) {
    locationNode = (<View style={[styles.locationWr, { flexWrap: 'nowrap', paddingRight: pt(8) }]}>
      <JDImage style={styles.locationIcon} source={{ uri: getImg('location') }} resizeMode="contain" resizeMethod='resize' />
      <JDText numberOfLines={1} style={[styles.geoInfo, disabled ? styles.disabled : '']}>
        {
          mainHotelLocationDistance.map((item, index) => <JDText key={index}>
            {item?.listShowName ?? ''}
            {index < mainHotelLocationDistance.length - 1 ? <JDText>·</JDText> : <></>}
          </JDText>)
        }
      </JDText>
    </View>)
  }
  // else {
  //   return <></>
  // }
  // }, [promotionTagListMap?.['mainHotelLocationDistance']])
  return (
    <TouchableWithoutFeedback onPress={onPress('hotel-card')}>
      <View style={[styles.container, style]}>
        <View style={[styles.left, typeof src === 'string' && src !== '' ? {} : { backgroundColor: '#F6FAFF', justifyContent: 'center', alignItems: 'center' }]}>
          {
            typeof src === 'string' && src !== '' ? <JDImage resizeMethod='resize' style={styles.img} source={{ uri: src }} /> : <JDImage resizeMethod='resize' style={styles.default_img} source={{ uri: IMGS.defaultImg }} />
          }
        </View>
        <View style={styles.right}>
          {titleNode}
          {descNode}
          {TagNode}
          <Rank data={promotionTagListMap?.['mainHotelRank']}/>
          {locationNode}
          <View style={styles.lineBottom}>
            {!!price &&
              <Price
                amount={price}
                numberDecimal={'auto'}
                suffix="起"
                suffixWarp={{ marginBottom: pt(-1) }}
                suffixStyle={styles.money_suffix}
                symbolStyle={styles.symbolStyle}
                decimalStyle={styles.decimalStyle}
                integerStyle={styles.money_integer}
              />}
            {!!price && !!oPrice &&
              <Price
                amount={oPrice}
                isUnderlined={true}
                warpStyle={{ marginLeft: pt(6) }}
                numberDecimal={'auto'}
                integerStyle={styles.money_del}
                warpPriceStyle={{ marginBottom: pt(-1) }}
                symbolStyle={styles.money_del}
              />}
            {!price ? <JDText style={styles.details}>查看详情</JDText> : ''}
          </View>          
        </View>
      </View>
    </TouchableWithoutFeedback>
  )
}

export default ScenicCard

ScenicCard.modeMap = {
  fieldNames: {
    stars: 'star',
    score: 'score',
    oPrice: 'originPrice',
    price: 'price',
    src: 'picUrl',
    title: 'name',
    geoInfo: 'geoInfo',
    scoreList: 'promotionTagListMap.mainHotelScore',
    labelList: 'promotionTagListMap.mainHotelPromotion',
    locationList: 'promotionTagListMap.mainHotelLocationDistance',
    titleAfter: 'promotionTagListMap.mainHotelTitleAfter',
  },
  // 酒店类型
  hotelGradeText: {
    1: '',
    2: '经济',
    3: '舒适',
    4: '高档',
    5: '豪华'
  },
  labelMap: () => {
    return []
  }
}

export const styles = StyleSheet.create({
  container: {
    flexGrow: 0,
    borderRadius: pt(8),
    backgroundColor: '#fff',
  },
  left: {
    borderTopStartRadius: pt(8),
    borderTopEndRadius: pt(8),
    overflow: 'hidden',
    minHeight: pt(172),
  },
  img: {
    maxHeight: pt(172),
    minHeight: pt(172),
    width: '100%',
    // flex: 1,
    backgroundColor: '#efefef'
  },
  default_img: {
    marginLeft: pt(8),
    marginRight: pt(8),
    width: pt(74),
    height: pt(26)
  },
  right: {
    flexGrow: 0,
    flexShrink: 0,
    marginHorizontal: pt(8),
    display: 'flex',
    flexDirection: 'column',
    paddingBottom: pt(12),
    paddingTop: pt(6),
  },
  titleWr: {
    flexShrink: 0,
    flexGrow: 1,
    width: '100%',
  },
  locationIcon: {
    width: pt(10),
    height: pt(12),
    marginTop: isAndroid ? pt(1) : pt(0),
    marginRight: pt(4)
  },
  locationWr: {
    flexDirection: 'row',
    display: 'flex',
    flexWrap: 'wrap',
    marginTop: pt(6),
    alignItems: 'center',
  },
  tagWr: {
    flexDirection: 'row',
    display: 'flex',
    flexWrap: 'wrap',
    marginTop: pt(6),
    alignItems: 'center',
  },
  line4_label: { // 返优惠券的样式
    height: pt(16),
    paddingHorizontal: pt(4),
    marginRight: pt(4)
  },
  labelFont: {
    fontSize: pt(11),
    lineHeight: pt(12),
    fontWeight: '300',
    color: '#006EEB'
  },
  disabled_label: {
    fontSize: pt(11),
    lineHeight: pt(12),
    fontWeight: '300',
    color: '#A2ABBF'
  },
  labelBorder: {
    borderRadius: pt(2),
    borderWidth: px(1),
    borderColor: '#006EEB'
  },
  disabled_labelBorder: {
    borderRadius: pt(2),
    borderWidth: px(1),
    borderColor: '#A2ABBF'
  },
  line6: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    marginBottom: pt(-4)
  },
  subTitleWr: {
    display: 'flex',
    marginTop: pt(6),
    zIndex: 1999,
    position: 'relative',
    flexDirection: 'column',
  },
  summary_line: {
    height: pt(4),
    width: '100%',
    flexGrow: 1,
    flex: 1,
    zIndex: -1,
    position: 'absolute',
    bottom: pt(1),
    // borderWidth: pt(1),
    backgroundColor: '#D4D9E3',
    opacity: 0.4
  },
  summary_warp: {
    display: 'flex',
    alignItems: 'flex-start',
    zIndex: 1999,
  },
  summary: {
    color: '#7C859C',
    fontWeight: '500',
    borderColor: 'red',
    fontSize: pt(11),
  },
  lineBottom: {
    marginTop: pt(8),
    flexDirection: 'row',
  },
  price: {
    display: 'flex',
    alignItems: 'flex-end',
  },
  details: {
    color: '#5E6880',
    fontSize: pt(14),
    fontWeight: '500',
  },
  money_del: {
    color: '#A2ABBF',
    fontSize: pt(12),
    fontWeight: '400',
    fontFamily: 'JDZhengHT-Regular',
    textDecorationLine: 'line-through'
  },
  decimalStyle: {
    color: '#006EEB',
    fontSize: pt(20),
    fontWeight: '400',
    fontFamily: 'JDZhengHT-Regular'
  },
  money_integer: {
    color: '#006EEB',
    fontSize: pt(20),
    fontWeight: '400',
    fontFamily: 'JDZhengHT-Regular'
  },
  symbolStyle: {
    color: '#006EEB',
    fontWeight: '400',
    fontSize: pt(12)
  },
  money_suffix: {
    color: '#006EEB',
    fontWeight: '400',
    fontSize: pt(12),
  },
  right_review: {
    color: '#5E6880',
    fontSize: pt(12),
    fontWeight: '300',
    marginRight: pt(8),
    height: pt(18),
    lineHeight: pt(18)
  },
  subTitleLocation: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    height: pt(18),
    alignItems: 'center',
    overflow: 'hidden',
    marginTop: pt(4)
  },
  geoInfo: {
    fontWeight: '400',
    color: '#5E6880',
    fontSize: pt(12),
  },
  disabled: {
    color: '#A2ABBF'
  }
})

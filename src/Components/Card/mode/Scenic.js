import React from 'react'
// 基础组件
import { Image, StyleSheet, TouchableWithoutFeedback, View } from 'react-native'
import { JDText } from '@jdreact/jdreact-core-lib'
import { getImageUrl } from '@/assets/imgs'
// 卡片元素组件
import Price from '../element/price'
import TagRender from '../element/TagRender'
import { isWeb } from '@/common/common'
// 工具类
import { pt, px, isAndroid } from '@ltfe/ltfe-core-lib/lib/utiles'
import { safeRun } from '../element/utils'
import _ from 'lodash'
import { getUuid } from '@/utils';
import { THEME_BASE, THEME_FONT } from '@ltfe/ltfe-core-lib/lib/utiles/theme'
import Rank from '../element/Rank'

const IMGS = {
    defaultImg: getImageUrl('defaultScenic')
}
/**
 * 渲染景点卡片组件
 * @param {Object} props - 组件属性
 * @param {string} title - 景点标题
 * @param {string} src - 图片链接
 * @param {number} price - 价格
 * @param {boolean} free - 是否免费
 * @param {boolean} disabled - 是否禁用
 * @param {Object} style - 样式
 * @param {Object} promotionTagListMap - 促销标签列表
 * @returns {JSX.Element} - 返回一个可点击的景点卡片视图
 */
const ScenicCard = ({
    title,  // 景点标题
    src,  // 图片链接
    price,  // 价格
    free,  // 是否免费
    disabled,  // 是否禁用
    style,  // 样式
    promotionTagListMap,  // 促销标签列表
    layoutInfo,
    cardWidth,
    ...props  // 其他属性
}) => {
    const onPress = (target) => (event) => safeRun(props.onPress, {
        event,
        trigger: target
    })
    // 标题
    const titleNode = React.useMemo(() => {
        let tagName = ''
        let scenicTitleAfter = promotionTagListMap?.['scenicTitleAfter']
        if (scenicTitleAfter && Array.isArray(scenicTitleAfter) && scenicTitleAfter.length) {
            tagName = scenicTitleAfter?.[0]?.listShowName ?? ''
        }
        // let titleWidth = 0, tagWidth = 0;
        // if (layoutInfo && Array.isArray(layoutInfo) && layoutInfo.length) {
        //     layoutInfo.forEach((item) => {
        //         if (item.text === title) {
        //             titleWidth = item?.layout?.width ?? 0
        //         }
        //         if (item.text === tagName) {
        //             tagWidth = item?.layout?.width ?? 0
        //         }
        //     })
        // }
        // // imageWidth: pt(110); paddingLeft: pt(8), paddingRight: pt(8)
        // const spacing = pt(130)
        // // imageWidth: pt(110); layout: paddingLeft: pt(12), paddingRight: pt(12)
        // // cardLayout: paddingLeft: pt(8), paddingRight: pt(8)
        // const defaultWidth = 375 - pt(150)
        let str = title;
        if (str.length >= 25 && !!tagName) {
            str = str.substring(0, 25) + '... '
        }
        return (<View style={[styles.titleWr]}>
            {!!tagName ? (<JDText style={[styles.title_content]} numberOfLines={2}>
                {str}
                <JDText style={[{ width: pt(4), height: pt(16) }, isWeb ? { dispaly: 'inline-block' } : {}]}> </JDText>
                <View style={[{display: 'flex', flexDirection: 'column'}, isWeb ? { dispaly: 'inline-block' } : {}]}>
                    <View style={[styles.tag_warp, { marginBottom: isAndroid ? pt(-2) : pt(0) }]}><JDText style={styles.tag_text} >{tagName}</JDText></View>
                </View>
            </JDText>) :
                (<JDText style={[styles.title_content_line]} numberOfLines={2}>
                    {str}
                </JDText>)}

        </View>)

        // <View style={styles.titleWr}>
        //     <TitleRender cardWidth={!!cardWidth ? (cardWidth - spacing) : defaultWidth} title={title} titleWidth={titleWidth} tagWidth={tagWidth} disabled={disabled} tagName={tagName} />
        // </View>
    }, [title, disabled, promotionTagListMap?.['scenicTitleAfter']])
    // 分数及评论
    const scoreAndCommentNode = React.useMemo(() => {
        const scenicScore = promotionTagListMap?.['scenicScore']
        if (scenicScore && Array.isArray(scenicScore) && scenicScore.length) {
            return <View style={[styles.tagWr, { marginTop: pt(2) }]}>
                {
                    scenicScore.map((item, index) => <JDText index={index} style={[styles.right_review, disabled ? styles.disabled : '']}>
                        {item?.listShowName ?? ''}
                    </JDText>)
                }
            </View>
        } else {
            return <></>
        }
    }, [promotionTagListMap?.['scenicScore']])
    // 一句话评价
    const commentNode = React.useMemo(() => {
        const scenicComment = promotionTagListMap?.['scenicComment']
        if (scenicComment && Array.isArray(scenicComment) && scenicComment.length) {
            return <View style={styles.subTitleWr}>
                <JDText numberOfLines={1}
                    style={[styles.summary, disabled ? styles.disabled : {}]}>
                    {scenicComment.map((item, index) => <JDText key={index}>{item?.listShowName ?? ''}</JDText>)}
                </JDText>
            </View>
        } else {
            return <></>
        }
    }, [promotionTagListMap?.['scenicComment'], disabled])
    // tag
    const tagNode = React.useMemo(() => {
        let options = []
        const scenicPromotion = promotionTagListMap?.['scenicRightPromotion']
        // console.log('[ scenicPromotion ] >', scenicPromotion)
        if (scenicPromotion && Array.isArray(scenicPromotion) && scenicPromotion?.length) {
            scenicPromotion.forEach((item, index) => {
                if ( item?.styleCode === 'Image' ) {
                    options.push({
                        imageUrl: item?.imageUrl ?? '',
                        imageHeight: item?.imageHeight,
                        imageWidth: item?.imageWidth
                    })
                }
                if (item?.styleCode === 'TagList') {
                    options.push({
                        title: item?.listShowName,
                        value: getUuid() + (item?.labelCode ?? index)
                    })
                }
            })
        }
        return !!options.length ? <View style={[styles.tagWr, { marginTop: pt(4) }]}><TagRender options={options} /></View> : null
    }, [promotionTagListMap?.['scenicRightPromotion']])
    // 位置距离信息
    const locationNode = React.useMemo(() => {
        const scenicLocationDistance = promotionTagListMap?.['scenicLocationDistance']
        if (scenicLocationDistance && Array.isArray(scenicLocationDistance) && scenicLocationDistance.length > 0) {
            return (<View style={styles.location_warp}>
                <JDText numberOfLines={1} style={styles.location_text} >
                    {
                        scenicLocationDistance.map((item, index) => <JDText
                            key={index}>
                            {item.listShowName}
                            {index < scenicLocationDistance.length - 1 ? <JDText>·</JDText> : <></>}
                        </JDText>)
                    }
                </JDText>
            </View>)
        } else {
            return <View style={styles.location_warp}></View>
        }
    }, [promotionTagListMap?.['scenicLocationDistance']])
    // 价格
    const priceNode = React.useMemo(() => {
        if (price || !!free) {
            return (<View style={styles.price_warp}>
                {!!price ? (<JDText style={styles.price}>
                    <Price
                        preFix={'门票 '}
                        amount={price}
                        numberDecimal={'auto'}
                        suffix="起"
                        preWarp={isAndroid ? { marginBottom: pt(2) } : {}}
                        suffixWarp={isAndroid ? { marginBottom: pt(2) } : {}}
                        symbolWarp={isAndroid ? { marginBottom: pt(0) } : { marginBottom: pt(-2) }}
                        warpPriceStyle={isAndroid ? { marginBottom: pt(-1) } : { marginBottom: pt(-2) }}
                        // priceWarp={{marginTop: pt(-1)}}
                        // symbolWarp={{marginBottom: pt(-1)}}
                        // preWarp={{marginBottom: pt(-1)}}
                        decimalStyle={styles.decimalStyle}
                        suffixStyle={styles.money_suffix}
                        symbolStyle={styles.symbolStyle}
                        integerStyle={styles.money_integer}
                    />
                </JDText>) : !!free ? <JDText style={styles.free}>免费</JDText> : <></>}
            </View>)
        } else {
            return <></>
        }
    }, [price, free])
    return (
        <TouchableWithoutFeedback onPress={onPress('card')}>
            <View style={[styles.container, style]}>
                <View style={styles.warp}>
                    <View style={[styles.left, typeof src === 'string' && src !== '' ? {} : { backgroundColor: '#F6FAFF', justifyContent: 'center' }]}>
                        {
                            typeof src === 'string' && src !== '' ? <Image resizeMethod='resize' style={styles.img} source={{ uri: src }} /> : <Image resizeMethod='resize' style={styles.img} source={{ uri: IMGS.defaultImg }} />
                        }
                    </View>
                    <View style={styles.right}>
                        {titleNode}
                        {commentNode}
                        {scoreAndCommentNode}
                        {tagNode}
                        <Rank data={promotionTagListMap?.['scenicRank']}/>
                        <View style={styles.lineBottom}>
                            {locationNode}
                            {priceNode}
                        </View>
                    </View>
                </View>

            </View>
        </TouchableWithoutFeedback>
    )
}

export default ScenicCard

ScenicCard.modeMap = {
    fieldNames: {
        src: 'picUrl',
        title: 'name',
        price: 'price',
        geoInfo: 'geoInfo',
        scoreList: 'promotionTagListMap.scenicScore',
        labelList: 'promotionTagListMap.scenicPromotion',
        locationList: 'promotionTagListMap.scenicLocationDistance',
        titleAfter: 'promotionTagListMap.scenicTitleAfter',
    },
    labelMap: () => {

    }
}

export const styles = StyleSheet.create({
    container: {
        backgroundColor: '#fff',
        paddingTop: pt(12),
        borderBottomWidth: pt(1),
        // marginHorizontal: pt(8),
        borderColor: THEME_BASE.middleColorThree
    },
    warp: {
        flexDirection: 'row',
        flexGrow: 0,
        position: 'relative',
    },
    left: {
        borderRadius: pt(8),
        overflow: 'hidden'
    },
    img: {
        minHeight: pt(86),
        width: pt(110),
        flex: 1,
        borderRadius: pt(6),
        backgroundColor: '#efefef'
    },
    default_img: {
        marginLeft: pt(8),
        marginRight: pt(8),
        width: pt(74),
        height: pt(26)
    },
    right: {
        flex: 1,
        flexDirection: 'column',
        paddingLeft: pt(8),
    },
    titleWr: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
    },
    title_content_line: {
        color: THEME_BASE.primaryColor,
        fontSize: pt(16),
        lineHeight: pt(22),
        fontWeight: THEME_FONT.fontWeight.SemiBold
    },
    title_content: {
        color: THEME_BASE.primaryColor,
        fontSize: pt(16),
        display: 'flex',
        flexDirection: 'row',
        lineHeight: pt(22),
        alignItems: 'center',
        fontWeight: THEME_FONT.fontWeight.SemiBold
    },
    tag_warp: {
        backgroundColor: THEME_BASE.middleColorThree,
        paddingLeft: pt(4),
        paddingRight: pt(4),
        height: pt(16),
        flexDirection: 'row',
        alignItems: 'center',
        display: 'flex',
        borderRadius: isAndroid ? pt(2.1) : pt(2)
    },
    tag_text: {
        fontSize: pt(11),
        lineHeight: pt(16),
        alignItems: 'center',
        color: THEME_BASE.secondaryColor
    },
    location_warp: {
        paddingBottom: pt(2),
        display: 'flex',
        flex: 1,
        alignItems: 'flex-start',
        justifyContent: 'flex-start',
    },
    location_text: {
        fontSize: pt(12),
        fontWeight: '400',
        color: '#5E6880'
    },
    tagWr: {
        flexDirection: 'row',
        marginTop: pt(6),
        flexWrap: 'wrap',
        alignItems: 'center',
    },
    line4_label: { // 返优惠券的样式
        height: pt(16),
        // paddingHorizontal: pt(4),
        // marginRight: pt(4)
    },
    labelFont: {
        fontSize: pt(11),
        lineHeight: pt(12),
        fontWeight: '300',
        color: '#006EEB'
    },
    disabled_label: {
        fontSize: pt(11),
        lineHeight: pt(12),
        fontWeight: '300',
        color: '#A2ABBF'
    },
    labelBorder: {
        borderRadius: pt(2),
        borderWidth: px(1),
        borderColor: '#006EEB'
    },
    disabled_labelBorder: {
        borderRadius: pt(2),
        borderWidth: px(1),
        borderColor: '#A2ABBF'
    },
    line6: {
        flex: 1,
        justifyContent: 'flex-end',
        alignItems: 'flex-end',
        marginBottom: pt(-4)
    },
    lineBottom: {
        marginTop: pt(2),
        flex: 1,
        flexDirection: 'row',
        display: 'flex',
        alignItems: 'flex-end',
    },
    price_warp: {
        minWidth: pt(80),
        marginLeft: pt(12),
        alignItems: 'flex-end'
    },
    price: {
    },
    money_del: {
        color: '#A2ABBF',
        fontSize: pt(12),
        textDecorationLine: 'line-through'
    },
    money_integer: {
        color: '#006EEB',
        fontSize: pt(20),
        fontFamily: 'JDZhengHT-Regular',
    },
    decimalStyle: {
        color: '#006EEB',
        fontSize: pt(20),
        fontFamily: 'JDZhengHT-Regular',
    },
    free: {
        fontSize: pt(12),
        color: '#5E6880',
        fontWeight: '400',
    },
    symbolStyle: {
        color: '#006EEB',
        fontSize: pt(12),
        borderBottomWidth: pt(2),
        borderColor: '#fff'
    },
    money_suffix: {
        color: '#006EEB',
        fontSize: pt(12),
    },
    right_review: {
        color: '#5E6880',
        fontSize: pt(12),
        fontWeight: '400',
        marginRight: pt(8),
        height: pt(18),
        lineHeight: pt(18)
    },
    subTitleWr: {
        marginTop: pt(4),
        display: 'flex',
        flexWrap: 'nowrap',
        flexDirection: 'row',
        alignSelf: 'flex-start',
        borderRadius: pt(4),
        paddingLeft: pt(4),
        paddingRight: pt(4),
        height: pt(18),
        backgroundColor: '#F0F6FF',
        justifyContent: 'flex-start',
        alignItems: 'center',
    },
    subTitleLocation: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        height: pt(18),
        alignItems: 'center',
        overflow: 'hidden'
    },
    geoInfo: {
        fontWeight: '300',
        color: '#5E6880',
        fontSize: pt(12),
        height: pt(14),
        lineHeight: pt(13),
        maxWidth: pt(180)
    },
    summary: {
        color: '#006EEB',
        fontWeight: '400',
        fontSize: pt(11),
        lineHeight: pt(18),
    },
    disabled: {
        color: '#A2ABBF'
    }
})

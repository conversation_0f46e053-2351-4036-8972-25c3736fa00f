import React, { useEffect, useState } from 'react'
import { isWeb } from '@/common/common'
// 基础组件
import { StyleSheet, TouchableWithoutFeedback, View } from 'react-native'
import LinearGradient from '@jdreact/jdreact-core-linear-gradient'
import { JDText, JDImage } from '@jdreact/jdreact-core-lib'
import { getImg } from '../../Filter/utils'

// 工具类
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import { safeRun } from '../element/utils'
import { THEME_BASE, THEME_FONT } from '@ltfe/ltfe-core-lib/lib/utiles/theme'

const ScenicCard = ( props ) => {
  const infoList = props.infoList || []

  const onPress = ( target, data ) => ( event = {} ) => safeRun(props.onPress, {
    event,
    trigger: target,
    data
  })

  return (

    <LinearGradient
      colors={isWeb ? ['#12C8E0', '#0CBFF8', '#0CBFF8'] : ['#12C8E0','#0CBFF8']}
      style={ [styles.bgBox, props.style] }
    >
      <View style={[styles.titleWarp]}>
        <JDText style={ [styles.title] }>交通方式推荐</JDText>
      </View>
      <View style={ styles.infoBox }>
        {
          Array.isArray(infoList) ? infoList.map(( item, index ) =>
            <TouchableWithoutFeedback onPress={ onPress('traffic-item', item) }>
              <View style={ [styles.infoItem, index > 0 ? styles.infoItemMargin : {} ]}
                    key={ index }>
                <View style={ styles.wordsBox }>
                  <JDText style={ styles.words }>{ item.fromCityName }</JDText>
                  <View style={ styles.iconBox }>
                    <JDImage style={ styles.arrowIcon } source={ {uri: getImg('arrowLine')} }/>
                  </View>
                  <JDText style={ styles.words }>{ item.toCityName }</JDText>
                </View>
                <View style={ styles.typeIconBox }>
                  <JDImage style={ styles.typeIcon }
                         source={ {uri: getImg(ScenicCard.modeMap.typeConstant[item.type])} }/>
                </View>
              </View></TouchableWithoutFeedback>) : null
        }
      </View>
    </LinearGradient>
  )
}

export default ScenicCard

ScenicCard.modeMap = {
  typeConstant: {
    1: 'train',
    2: 'fight'
  }
}

export const styles = StyleSheet.create({
  bgBox: {
    // flex: 1,
    flexGrow: 0,
    flexShrink: 1,
    // padding: pt(6),
    borderRadius: pt(8),
    display: 'flex',
    flexDirection: 'column',
  },
  titleWarp: {
    paddingLeft: pt(10),
  },
  title: {
    height: pt(48),
    lineHeight: pt(48),
    color: '#fff',
    fontSize: pt(18),
    fontWeight: '500',
    textAlign: 'left',
  },
  infoBox: {
    padding: pt(10),
    margin: pt(6),
    marginTop: pt(0),
    paddingBottom: pt(0),
    backgroundColor: '#fff',
    borderRadius: pt(6)
  },
  iconBox: {
    paddingLeft: pt(8),
    paddingRight: pt(8),
    // height: pt(16),
    // paddingTop: pt(10)
  },
  arrowIcon: {
    width: pt(11),
    height: pt(3)
  },
  words: {
    fontSize: pt(14),
    color: THEME_BASE.primaryColor,
    fontWeight: THEME_FONT.fontWeight.Medium
  },
  wordsBox: {
    height: pt(20),
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: pt(10),
  },
  infoItemMargin: {
    paddingTop: pt(10),
    borderTopWidth: pt(1),
    borderColor: THEME_BASE.middleColorThree
  },
  typeIcon: {
    width: pt(14),
    height: pt(14)
  },
  typeIconBox: {
    height: pt(20),
    paddingTop: pt(4)
  }
})

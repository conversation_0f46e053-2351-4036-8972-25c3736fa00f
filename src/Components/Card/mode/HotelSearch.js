import React, { useRef, useState } from 'react'
// 基础组件
import { Image, StyleSheet, Text, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native'
import LinearGradient from '@jdreact/jdreact-core-linear-gradient'
import { InView } from '@/BaseComponents/IntersectionObserver'
import { JDImage, JDText } from '@jdreact/jdreact-core-lib'
import TitleRender from '../element/HotelTitleRender'
import TagRender from '../element/TagRender'
import Rank from '../element/Rank'
import { getImageUrl } from '@/assets/imgs'
import { THEME_FONT } from '@ltfe/ltfe-core-lib/lib/utiles/theme'
import Price from '../element/price'
import { isWeb } from '@/common/common'
import { getImg } from '../../Filter/utils'
// 工具类
import { isIOS ,isAndroid, pt, px, endMarkCounterAndReport } from '@ltfe/ltfe-core-lib/lib/utiles'
import { safeRun } from '../element/utils'
import { isEmpty } from '@/utils/isType';
import { getUuid } from '@/utils'
import { Dimensions } from 'react-native'
import { errorCodeConstantMapping } from '../../../common/reporter/errorMapping';
import { M_PAGE, ModuleName } from '../../../common/mta'
import { PERFORMANCE, PERFORMANCE_LABEL } from '../../../common/performance'

const {width} = Dimensions.get('window')

export const hasValue = ( text ) => {
    return text !== null && text !== '' && text !== undefined
}
const IMGS = {
    saleDone: getImg('soldOut'),
    defaultImg: getImageUrl('derfaultImg'),
    limitedImg: getImageUrl('limited')
}

const STYLE_CODE = {
    MultiItem: 'MultiItem',
    Stars: 'Stars',
    Tag: 'Tag',
    Image: 'Image',
    TagList: 'TagList',
    Text: 'Text',
    Diamonds: 'Diamonds'
}

/**
 * 酒店卡片组件
 * @param {Object} props - 属性对象
 * @param {string} title - 标题
 * @param {string} src - 图片链接
 * @param {number} price - 价格
 * @param {number} oPrice - 划线价
 * @param {string} promo - 促销信息
 * @param {boolean} disabled - 是否禁用
 * @param {string} promotionImgUrl - 促销图片链接
 * @param {Object} promotionTagListMap - 促销标签列表映射
 * @param {({isExposure: boolean, scene: string, param: any}) => {}} mtaTrack - 卡片内组件的曝光/点击埋点
 * @returns {JSX.Element} - 返回酒店卡片组件
 */
const HotelCard = ({
    title, // 标题
    src, // 图片链接
    price, // 价格
    discountPrice,
    oPrice, // 划线价
    promo, // 促销信息
    disabled, // 是否禁用
    beltInfoVO,
    promotionImgUrl, // 促销图片链接
    promotionTagListMap, // 促销标签列表映射
    hotelRoomVOList, // 房型促销sku
    layoutInfo,
    cardWidth,
    style,
    promotionLayerVO,
    promoList,
    discountItemCount,
    isFirst,
    isClick,
    isNatural,
    mtaTrack,
    priceStyle,

    ...props
}) => {
    const [isReduce, setIsReduce] = useState(false)
    const expoEndData = useRef({}) // 埋点信息
    const onPress = ( target ) => ( event ) => {
        const { jumpUrl, tagInfo } = event || {}
        
        // 调用父组件的 onPress 函数，传递标签信息
        safeRun(props.onPress, {
            event,
            trigger: target,
            jumpUrl,
            tagInfo
        });
    }

    const roomTrackParam = (roomTypeId, roomName, index) => {
        return {
            displayName: roomName,
            beltCode: beltInfoVO?.beltCode || '-100',
            discountPrice: discountPrice || '-100',
            roomTypeId: roomTypeId || '-100',
            skuPos: `${index + 1}`,
            ...buildHotelCardEventData()
        }
    }

    /**
     * 组装[百亿补贴sku]埋点的额外参数 - 逻辑同<useHotelSearch>中的buildHotelCardEventData()
     * 为了新/老垂搜、大搜的埋点都有这些参数，故在此做兼容统一拼接
     * @returns 
     */
    const buildHotelCardEventData = () => {
        const { index, id, originPrice, userAction, wareRankVO } = props
        // 构建标签列表
        const getTagList = () => {
            const keys = ['hotelRightPromotion']
            const tagListMap = promotionTagListMap || {}
            const result = []
            for (const key in tagListMap) {
                if (Object.prototype.hasOwnProperty.call(tagListMap, key)) {
                    if (keys.includes(key)) {
                    const ele = tagListMap[key] || []
                        ele.forEach(item => {
                            result.push({
                            trackId: item?.trackId || '-100',
                            labelName: item?.listShowName || '-100'
                            })
                        })
                    }
                }
            }
            return result.length ? result : '-100'
        }

        const discountList = promotionLayerVO?.promotionDetailList || []
        function toNumber(data, def = -100) {
            return (data == null || data === "" || isNaN(Number(data))) ? def : Number(data)
        }
        return {
            index: index,
            hotelId: id || -100,
            score: toNumber(promotionTagListMap?.hotelScore?.[0]?.listShowName),
            firpricetype: '11',
            firprice: toNumber(price),
            secpricetype: '52',
            secprice: toNumber(originPrice) || -100,
            tagList: getTagList(),
            discountType: Array.isArray(discountList) ? discountList.map(item => item.type) : -100,
            rankId: wareRankVO?.rankId || '-100',
            ...(userAction || {})
        }
    }
    
    const renderRoomItem = (item) => {
        if (!item) return <></>;
        const {styleCode, margin } = item
        switch (styleCode) {
            case 'Image':
                const { imageUrl, imageHeight, imageWidth } = item; 
                const realHeight = pt(16)
                if(imageHeight === 0) return <></>;
                // 根据固定高度和宽高比计算宽度
                const calculatedWidth = realHeight * (imageWidth / imageHeight);   
                if (!calculatedWidth || calculatedWidth <= 0) {
                    return <></>;
                } 
                return (
                    <JDImage 
                        style={{height: realHeight, width: calculatedWidth, marginLeft: pt(margin ?? 0)}} 
                        source={{ uri: imageUrl }}
                        resizeMode="contain"
                    />
                );
            case 'Text':
                const { listShowName, fontColor, textStyle, textSize, fontFamily, bgImage, bgImageHeight, bgImageWidth, startBgColor, endBgColor } = item;
                if (bgImage) {
                    const realHeight = pt(16)
                    let calculatedWidth =  pt(8);
                    if (bgImageHeight !== 0) {
                        // 根据固定高度和宽高比计算宽度
                        calculatedWidth = realHeight * (bgImageWidth / bgImageHeight);  
                        // 如果计算出的宽度为0或无效，使用默认宽度8
                        if (!calculatedWidth || calculatedWidth <= 0) {
                            calculatedWidth = pt(8);
                        }  
                    }
                    return (
                        <View style={{flexDirection: 'row', alignItems: 'center', marginLeft: pt(margin ?? 0)}}>
                            <JDImage 
                                source={{ uri: bgImage }} 
                                style={{height: realHeight, width: calculatedWidth}} 
                            />
                            <LinearGradient
                                colors={[startBgColor ?? '#FFE8E8FF', startBgColor ?? '#FFE8E8FF', endBgColor ?? '#FFE8E800']} // 从FFE8E8到FFE8E8透明
                                start={{ x: 0, y: 0 }}
                                end={{ x: 1, y: 0 }}
                                style={{height: pt(bgImageHeight ?? 16), justifyContent: 'center', alignItems: 'center', paddingRight: pt(6)}}
                            >
                                <JDText style={{
                                    color: fontColor, 
                                    fontSize: pt(textSize), 
                                    fontWeight: textStyle === 'bold' ? THEME_FONT.fontWeight.SemiBold : THEME_FONT.fontWeight.Regular, 
                                    fontFamily: fontFamily
                                }}>
                                    {listShowName}
                                </JDText>
                            </LinearGradient>
                        </View>
                    );
                }
                return (
                    <JDText style={{
                        color: fontColor, 
                        fontSize: pt(textSize), 
                        fontWeight: textStyle === 'bold' ? THEME_FONT.fontWeight.SemiBold : THEME_FONT.fontWeight.Regular, 
                        marginLeft: pt(margin ?? 0),
                        fontFamily: fontFamily
                    }}>
                        {listShowName}
                    </JDText>
                );
            case 'MultiItem':
                const { itemList } = item
                if(isEmpty(itemList)) {
                    return <></>
                }
                return (
                    <View style={{flexDirection: 'row', alignItems: 'baseline', marginLeft: pt(margin ?? 0)}}>
                        {itemList.map((item, index) => renderRoomItem(item, index))}
                    </View>
                )
            default:
                return <></>;
        }
    };

    // 标题节点
    const titleNode = React.useMemo(() => {
        let starCount = 0, tagName = '', iconType = 'Stars'
        const hotelTitleAfter = promotionTagListMap?.['hotelTitleAfter']
        if ( hotelTitleAfter && Array.isArray(hotelTitleAfter) && hotelTitleAfter.length ) {
            hotelTitleAfter.forEach(( item ) => {
                if ( item?.styleCode === STYLE_CODE.Stars || item?.styleCode === STYLE_CODE.Diamonds ) {
                    starCount = (item?.listShowName ?? 0) * 1
                    iconType = item?.styleCode
                }
                if ( item?.styleCode === STYLE_CODE.Tag ) {
                    tagName = (item?.listShowName ?? '')
                }
            })
        }
        let titleWidth = 0, tagWidth = 0
        if ( layoutInfo && Array.isArray(layoutInfo) && layoutInfo.length ) {
            layoutInfo.forEach(( item ) => {
                if ( item.text === title ) {
                    titleWidth = item?.layout?.width ?? 0
                }
                if ( item.text === tagName ) {
                    tagWidth = item?.layout?.width ?? 0
                }
            })
        }
        // imageWidth: pt(92); paddingLeft: pt(8), paddingRight: pt(8)
        const spacing = pt(126)
        // imageWidth: pt(92); layout: paddingLeft: pt(12), paddingRight: pt(12)
        // cardLayout: paddingLeft: pt(8), paddingRight: pt(8)
        const defaultWidth = width - pt(140)
        return (<View style={ styles.titleWr }>
            <TitleRender cardWidth={ !!cardWidth ? (cardWidth - spacing) : defaultWidth } title={ title }
                         iconType={ iconType }
                         titleWidth={ titleWidth } tagWidth={ tagWidth } disabled={ disabled } starCount={ starCount }
                         tagName={ tagName }/>
        </View>)
    }, [promotionTagListMap?.['hotelTitleAfter'], cardWidth, title, disabled, layoutInfo])
    // 评分节点
    const socreNode = React.useMemo(() => {
        const styleConfig = {
            'Score': 'tag_score_warp',
            'ScoreMap': 'tag_good_warp',
            'Text': 'tag_text_warp',
        }
        let options = []
        const hotelScore = promotionTagListMap?.['hotelScore']
        if ( hotelScore && Array.isArray(hotelScore) && hotelScore?.length ) {
            options = hotelScore.map(item => {
                return {
                    title: item?.listShowName,
                    value: getUuid() + (item?.labelCode ?? '_score_'),
                    styleId: styleConfig[item?.styleCode ?? ''] ?? 'tag_score_warp',
                }
            })
        }
        return options.length ? <View style={ styles.tagWrTag }><TagRender options={ options }/></View> : <></>
    }, [promotionTagListMap?.['hotelScore']])
    // 位置距离节点
    const locationNode = React.useMemo(() => {
        const hotelLocationDistance = promotionTagListMap?.['hotelLocationDistance'] || []
        if ( Array.isArray(hotelLocationDistance) && hotelLocationDistance.length > 0 ) {
            return (<View style={ [styles.tagWr, styles.tagWrMarginTop] }>
                {
                    hotelLocationDistance?.map(( item, index ) => {
                        if ( item?.styleCode == STYLE_CODE.MultiItem ) {
                            return (
                                <>
                                    {
                                        item?.itemList?.map(( el ) => {
                                            if ( el?.styleCode == STYLE_CODE.Text ) {
                                                return (
                                                    <>
                                                        <JDText
                                                            ellipsizeMode={el?.useDotOmit ? 'middle': 'tail'}
                                                            style={ [styles.location_text, el?.useDotOmit ? styles.location : styles.location_no] }
                                                            key={ index } numberOfLines={ el?.useDotOmit ? 1 : 0 }>
                                                            { el?.listShowName }
                                                        </JDText>
                                                    </>
                                                )
                                            }
                                        })
                                    }
                                    { index < hotelLocationDistance.length - 1 ?
                                        <JDText style={ styles.location_text }>·</JDText> : <></> }
                                </>
                            )
                        }
                        if ( item?.styleCode == STYLE_CODE.Text ) {
                            return (
                                <>
                                    <JDText
                                        ellipsizeMode={item?.useDotOmit ? 'middle': 'tail'}
                                        style={ [styles.location_text, item?.useDotOmit ? styles.location : styles.location_no] }
                                        key={ index } numberOfLines={ item?.useDotOmit ? 1 : 0 }>
                                        { item?.listShowName }
                                    </JDText>
                                    { index < hotelLocationDistance.length - 1 ?
                                        <JDText style={ styles.location_text }>·</JDText> : <></> }
                                </>
                            )
                        }
                        return <></>
                    })
                }
            </View>)
        } else {
            return <></>
        }
    }, [promotionTagListMap?.['hotelLocationDistance']])
    // 标签
    const tagNode = React.useMemo(() => {
        let options = []
        let hotelPromotion = promotionTagListMap?.['hotelRightPromotion']
        if ( hotelPromotion && Array.isArray(hotelPromotion) && hotelPromotion.length ) {
            hotelPromotion.forEach(( item, index ) => {
                if ( item?.styleCode === STYLE_CODE.Image ) {
                    options.push({
                        imageUrl: item?.imageUrl ?? '',
                        imageHeight: item?.imageHeight,
                        imageWidth: item?.imageWidth
                    })
                }
                if ( item?.styleCode === STYLE_CODE.TagList ) {
                    options.push({
                        title: item?.listShowName,
                        value: getUuid() + (item?.labelCode ?? index)
                    })
                }
            })
        }

        return options.length ? <View style={ styles.tag_list }><TagRender options={ options } style={ {
            overflow: 'hidden',
            maxHeight: pt(18),
            flexDirection: 'row',
        } }/></View> : <></>
    }, [promotionTagListMap?.['hotelRightPromotion']])
    // 一句话评价
    const commentNode = React.useMemo(() => {
        const hotelComment = promotionTagListMap?.['hotelComment']
        if ( hotelComment && Array.isArray(hotelComment) && hotelComment.length ) {
            return <View style={ styles.desc_warp }>
                <JDText numberOfLines={ 1 }
                        style={ [styles.desc_warp_text, disabled ? styles.disabled : {}] }>
                    { hotelComment.map(( item, index ) => <JDText key={ index }>{ item?.listShowName ?? '' }</JDText>) }
                </JDText>
            </View>
        } else {
            return <></>
        }
    }, [promotionTagListMap?.['hotelComment'], disabled])

    const priceDefaultNode = React.useMemo(() => {        
        if (disabled) {
            return <View style={styles.saleDone}>
                <JDImage source={{ uri: IMGS.saleDone }} style={styles.soldOut} />
            </View>
        } else {
            return <View style={styles.lineBottom}>
                {
                    !!price ? <View style={styles.line6}>
                        <TouchableWithoutFeedback onPress={onPress('discount')}>
                            <View style={styles.price}>
                                {!!oPrice && price !== oPrice &&
                                    <Price
                                        amount={oPrice}
                                        numberDecimal={'auto'}
                                        integerStyle={styles.money_del}
                                        symbolStyle={styles.money_del}
                                        isUnderlined={true}
                                        warpPriceStyle={{ marginBottom: pt(-1) }}
                                    />}
                                {!!price &&
                                    <Price
                                        amount={price}
                                        warpStyle={{ marginLeft: pt(2) }}
                                        numberDecimal={'auto'}
                                        suffix="起"
                                        suffixWarp={{ marginBottom: pt(isAndroid ? -1 : 1) }}
                                        decimalStyle={styles.decimalStyle}
                                        suffixStyle={styles.money_suffix}
                                        symbolStyle={styles.symbolStyle}
                                        integerStyle={styles.money_integer}
                                    />}
                            </View>
                        </TouchableWithoutFeedback>
                        <TouchableWithoutFeedback>
                            {!!discountPrice && +discountPrice !== 0 ? (<View>
                                <View
                                    style={styles.limitedWarp}>
                                    {Array.isArray(promoList) && promoList.length > 0 &&
                                        <View style={styles.limitedImgWarp}>
                                            {
                                                isReduce ? <>
                                                    {
                                                        promoList.slice(0, 2).map((item, index) => {
                                                            const { listShowName, trackId } = item;
                                                            return <>
                                                                {index !== 0 ? <View style={styles.limitedImgBorderLine} /> : null}
                                                                <TouchableWithoutFeedback onPress={(e) => {
                                                                    onPress('discount')({
                                                                        ...e,
                                                                        tagInfo: { name: listShowName }
                                                                    });
                                                                }}>
                                                                    <JDText italic style={styles.limitedWord}>{listShowName}</JDText>
                                                                </TouchableWithoutFeedback>
                                                            </>
                                                        })
                                                    }
                                                    <JDImage style={styles.limitedImgBorder} resizeMode="cover" source={{ uri: getImg('border') }} />
                                                </> : <>
                                                    {
                                                        promoList.slice(0, 2).map((item, index) => {
                                                            const { imageUrl, imageWidth, imageHeight, listShowName } = item;
                                                            const tagActualHeight = pt(15)
                                                            // 根据固定高度和宽高比计算宽度
                                                            const tagCalculatedWidth = imageHeight && imageWidth && imageHeight > 0 ? tagActualHeight * (imageWidth / imageHeight) : 0;
                                                            if (!tagCalculatedWidth || tagCalculatedWidth <= 0) {
                                                                return <></>;
                                                            }
                                                            return <>
                                                                {index !== 0 ? <View style={styles.limitedImgBorderLine} /> : null}
                                                                <TouchableWithoutFeedback onPress={(e) => {
                                                                    onPress('discount')({
                                                                        ...e,
                                                                        tagInfo: { name: listShowName }
                                                                    });
                                                                }}>
                                                                    <JDImage style={[styles.limitedImg,
                                                                    {
                                                                        width: tagCalculatedWidth,
                                                                        height: tagActualHeight
                                                                    }
                                                                    ]}
                                                                        resizeMode="cover"
                                                                        onError={() => setIsReduce(true)}
                                                                        source={{ uri: imageUrl }} />
                                                                </TouchableWithoutFeedback>
                                                            </>
                                                        })
                                                    }
                                                    <JDImage style={styles.limitedImgBorder} resizeMode="cover" source={{ uri: getImg('border') }} />
                                                </>
                                            }
                                        </View>}
                                    {!!discountPrice &&
                                        <TouchableWithoutFeedback onPress={(e) => {
                                            const discountText = discountItemCount ? `${discountItemCount}项优惠${discountPrice}` : `优惠${discountPrice}`;
                                            // 调用 onPress 并传递优惠信息
                                            onPress('discount')({
                                                ...e,
                                                tagInfo: { name: discountText }
                                            });
                                        }}>
                                            <View>
                                                <LinearGradient
                                                    colors={['#FFFFFF', '#FFF0F0', '#FFE1E1']}
                                                    start={{ x: 0, y: 0 }}
                                                    end={{ x: 1, y: 0 }}
                                                    style={styles.discountWarp}>
                                                    <JDText
                                                        style={styles.discount_price_text}>{discountItemCount ? `${discountItemCount}项` : ''}优惠</JDText>
                                                    <View style={styles.discountPriceWarp}>
                                                        <JDText
                                                            style={styles.discountPrice}>{discountPrice}</JDText>
                                                    </View>
                                                    {
                                                        !!promotionLayerVO ? <View style={styles.arrowLeft}>
                                                            <JDImage style={[
                                                                {
                                                                    width: pt(4),
                                                                    height: pt(8)
                                                                }]
                                                            }
                                                                resizeMode="cover"
                                                                source={{ uri: 'https://img13.360buyimg.com/imagetools/jfs/t1/306711/39/4418/307/68352bcdFcd616a87/e6649cbc3ffe1729.png' }} />
                                                        </View> : null
                                                    }
                                                </LinearGradient>
                                            </View>
                                        </TouchableWithoutFeedback>
                                    }
                                </View>
                            </View>) : <></>}
                        </TouchableWithoutFeedback>
                    </View> : <View style={styles.showDetail}><JDText
                        style={styles.showDetailWord}>查看详情</JDText></View>
                }
            </View>
        }
    },[])

    const priceIdentityNode = React.useMemo(() => {
        if (!isEmpty(priceStyle)) {  // 卡片是否有特定样式
            const tagActualHeight = pt(16);
            let tagImage = null;
            if (!isEmpty(promoList) && promoList?.length > 0) {
                const promoIdItem = promoList[0];
                const { imageUrl, imageWidth = '0', imageHeight = '0' } = promoIdItem || {};
                if (Number(imageWidth) > 0 && Number(imageHeight) > 0) {
                    const tagCalculatedWidth = imageHeight > 0 ? tagActualHeight * (imageWidth / imageHeight) : 0;
                    tagImage = <View style={[styles.limitedIDImg, { width: tagCalculatedWidth, height: tagActualHeight }]}>
                        <JDImage
                            style={[styles.limitedIDImg, { width: tagCalculatedWidth, height: tagActualHeight }]}
                            source={{ uri: imageUrl }}
                            resizeMode="cover" />
                    </View>
                }
            }
            return <View style={styles.lineBottom}>
                {!!price ? (
                    <View style={[styles.priceCard, { height: pt(Number(priceStyle?.bgLeftImgHeight || 44)) }]} >
                        <View style={[styles.priceBg, { backgroundColor: priceStyle?.bgColor || '#FFFFFF', height: pt(Number(priceStyle?.bgLeftImgHeight || 44)) }]}></View>
                        {priceStyle?.bgLeftImgUrl && priceStyle?.bgLeftImgWidth && priceStyle?.bgLeftImgHeight && <JDImage
                            style={[styles.priceBgLeftImg, { width: pt(Number(priceStyle?.bgLeftImgWidth)), height: pt(Number(priceStyle?.bgLeftImgHeight)) }]}
                            resizeMode="stretch"
                            source={{ uri: priceStyle?.bgLeftImgUrl }}
                        />}
                        <TouchableWithoutFeedback onPress={onPress('discount')}>
                            <View style={styles.price}>
                                {!!oPrice && price !== oPrice &&
                                    <Price
                                        amount={oPrice}
                                        numberDecimal={'auto'}
                                        integerStyle={[styles.money_del, { color: priceStyle.underlinedPriceColor || '#888B94' }]}
                                        symbolStyle={styles.money_del}
                                        isUnderlined={true}
                                        warpPriceStyle={{ marginBottom: pt(-1) }}
                                    />}
                                {!!price &&
                                    <Price
                                        amount={price}
                                        warpStyle={{ marginLeft: pt(2) }}
                                        numberDecimal={'auto'}
                                        suffix="起"
                                        suffixWarp={{ marginBottom: pt(isAndroid ? -1 : 1) }}
                                        decimalStyle={[styles.decimalStyle, {color: priceStyle.priceColor}]}
                                        suffixStyle={[styles.money_suffix, {color: priceStyle.priceColor}]}
                                        symbolStyle={[styles.symbolStyle, {color: priceStyle.priceColor}]}
                                        integerStyle={[styles.money_integer, {color: priceStyle.priceColor, marginBottom: 0}]}
                                    />}
                            </View>
                        </TouchableWithoutFeedback>
                        {!!discountPrice && +discountPrice !== 0 && (
                            <TouchableOpacity
                                activeOpacity={1}
                                style={styles.limitedIDWarp}
                                onPress={(e) => {
                                    const discountText = discountItemCount ? `${discountItemCount}项优惠${discountPrice}` : `优惠${discountPrice}`;
                                    // 调用 onPress 并传递优惠信息
                                    onPress('discount')({ ...e, tagInfo: { name: discountText } });
                                }}>
                                {tagImage}
                                <View style={[styles.discountIDWarp, { backgroundColor: priceStyle.discountBgColor }]}>
                                    <View style={styles.discountIDPriceWarpDefault}>
                                        <JDText style={[styles.discountPriceDefaultTxt, { color: priceStyle.discountTextColor }]}>{discountItemCount ? `${discountItemCount}项` : ''}优惠</JDText>
                                        <JDText style={[styles.discountPriceDefault, { color: priceStyle.discountPriceColor }]}>{discountPrice}</JDText>
                                    </View>
                                    <View style={styles.arrowLeft}>
                                        <JDImage
                                            style={{ width: pt(Number(priceStyle?.arrowIconUrlWidth)) || pt(3), height: pt(Number(priceStyle?.arrowIconUrlHeight)) || pt(7) }}
                                            resizeMode="stretch"
                                            source={{ uri: priceStyle?.arrowIconUrl || 'https://img13.360buyimg.com/imagetools/jfs/t1/306711/39/4418/307/68352bcdFcd616a87/e6649cbc3ffe1729.png' }}
                                        />
                                    </View>
                                </View>
                            </TouchableOpacity>
                        )}
                    </View>
                ) : (
                    <View style={styles.showDetail}>
                        <JDText style={styles.showDetailWord}>查看详情</JDText>
                    </View>
                )}
            </View>
        } else {
            return priceDefaultNode
        }
    }, [])

    const roomNode = React.useMemo(() => {
        if(isEmpty(hotelRoomVOList)) {
            return <></>
        }
        return <View style={styles.hotelRoomContainer}>
            {hotelRoomVOList.map((roomItem, index) => {
                const {roomId, name, jumpUrl, promotionTagListMap} = roomItem || {}
                const roomData = promotionTagListMap?.roomTitleBefore
                if(isEmpty(roomData)) {
                    return <></>
                }

                return (
                    <InView key={roomId} onChange={ (visible) => {
                        if (visible && !expoEndData?.current?.[roomId]) {
                            mtaTrack && mtaTrack({
                                isExposure: true, 
                                scene: 'subsidy',
                                param: roomTrackParam(roomId, name, index)
                            })
                            if (expoEndData?.current) {
                                expoEndData.current[roomId] = true;
                            }
                        }
                        }}>
                        <View key={index} style={styles.hotelRoomWrapper}>
                            {roomData[0] && <View style={styles.roomLogoWrapper}>
                                {renderRoomItem(roomData[0])}
                            </View>}
                            <View style={styles.roomTextWrapper}>
                                {roomData?.map((item, idx) => {
                                    if(idx > 0) return renderRoomItem(item)
                                    return null;
                                })}
                                {!!name && 
                                    <TouchableWithoutFeedback onPress={(e) => {
                                        onPress('room')({ ...e, jumpUrl: jumpUrl });
                                        mtaTrack && mtaTrack({
                                            isExposure: false, 
                                            scene: 'subsidy',
                                            param: roomTrackParam(roomId, name, index)
                                        })
                                    }}>
                                        {
                                            isIOS ? 
                                                <View style={styles.textWrapper}>
                                                    <JDText style={styles.roomText}>{name}</JDText>
                                                </View>
                                                :
                                                <JDText style={styles.textWrapper}>
                                                    <JDText style={styles.roomText}>{name}</JDText>
                                                </JDText>
                                        }
                                    </TouchableWithoutFeedback>
                                }
                            </View>
                        </View>
                    </InView>
                )
            })}
        </View>
    }, [hotelRoomVOList])

    const onLoadEnd = () => {
        const { index } = props
        if (index <= 2) {
                const duration = endMarkCounterAndReport(ModuleName, PERFORMANCE_LABEL.FIRSTSCREEN_TIME_KEY, {
                performanceKey: PERFORMANCE.FIRSTSCREEN_TIME_KEY,
                code: errorCodeConstantMapping?.PERFORMANCE_REPORT_FIRST_SCREEN_TIME,
                pageName:  M_PAGE.HotelSearch
            })
            alert(duration)
        }
    }

    return (
        <TouchableWithoutFeedback onPress={ onPress('card') }>
            <View style={ [styles.card, isClick ? {backgroundColor: '#F7F8FC'} : {}] }>
                <View style={ styles.container }>
                    <TouchableWithoutFeedback onPress={ onPress('img') }>
                        <View style={ [styles.left, typeof src === 'string' && src !== '' ? {} : {
                            backgroundColor: '#F6FAFF',
                            justifyContent: 'center'
                        }] }>
                            {
                                typeof src === 'string' && src !== '' ?
                                    <Image rn resizeMethod="resize" style={ styles.img } resizeMode="cover"
                                           source={ {uri: src} } onLoadEnd={onLoadEnd}/> :
                                    <JDImage resizeMethod="resize" style={ styles.default_img } resizeMode="resize"
                                             source={ {uri: IMGS.defaultImg} }/>
                            }
                            {
                                beltInfoVO && beltInfoVO?.backImage
                                    ? <JDImage style={ styles.promoImg } resizeMode="contain"
                                               source={ {uri: beltInfoVO?.backImage} }/> : null
                            }
                        </View>
                    </TouchableWithoutFeedback>
                    <View style={ [styles.right, isFirst ? {borderTopWidth: 0} : {}] }>
                        { titleNode }
                        { socreNode }
                        { locationNode }
                        { commentNode }
                        { tagNode }
                        <Rank data={ promotionTagListMap?.['hotelRank'] }/>
                        {priceIdentityNode}
                    </View>
                </View>
                { roomNode }
            </View>

        </TouchableWithoutFeedback>
    )
}

export default HotelCard

HotelCard.modeMap = {
    fieldNames: {
        stars: 'star',
        score: 'score',
        oPrice: 'originPrice',
        price: 'price',
        src: 'picUrl',
        title: 'name',
        geoInfo: 'geoInfo',
        afterTitle: 'promotionTagListMap.hotelTitleAfter',
        scoreList: 'promotionTagListMap.hotelScore',
        labelList: 'promotionTagListMap.scenicPromotion',
        locationList: 'promotionTagListMap.hotelLocationDistance',
        tagList: 'promotionTagListMap.hotelPromotion',
        promoList: 'promotionTagListMap.hotelRightDown'
    },
    // 酒店类型
    hotelGradeText: {
        1: '',
        2: '经济',
        3: '舒适',
        4: '高档',
        5: '豪华'
    },
    labelMap: () => {
        return []
    }
}

export const styles = StyleSheet.create({
    showDetail: {
        width: '100%',
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'flex-end',
        justifyContent: 'flex-end',
        marginTop: pt(12)
    },
    showDetailWord: {
        color: '#888B94',
        // fontWeight: isAndroid ? 'bold' : '500',
        fontWeight: '500',
        fontSize: pt(14),
    },
    card: {
        backgroundColor: '#FFFFFF',
        paddingLeft: pt(12),
        paddingRight: pt(12)
    },
    container: {
        flexDirection: 'row',
        flexGrow: 0,
        // marginHorizontal: pt(8),
        // paddingBottom: pt(12),
        // borderBottomWidth: pt(1),
        // borderColor: "#F5F7FA",
    },
    promoImg: {
        width: pt(102),
        height: pt(28),
        position: 'absolute',
        borderBottomLeftRadius: pt(8),
        borderBottomRightRadius: pt(8),
        bottom: pt(-2),
        overflow: 'hidden'
    },
    left: {
        width: pt(102),
        borderRadius: pt(8),
        position: 'relative',
        display: 'flex',
        minHeight: pt(88),
        overflow: 'hidden',
        marginBottom: pt(6),
        paddingTop: pt(6)
    },
    default_img: {
        marginLeft: pt(8),
        marginRight: pt(8),
        width: pt(74),
        height: pt(26)
    },
    img: {
        width: '100%',
        borderRadius: pt(6),
        flex: 1,
        backgroundColor: '#efefef',
    },
    discountWarp: {
        borderRadius: pt(2),
        paddingLeft: pt(4),
        paddingRight: pt(4),
        height: pt(16),
        lineHeight: pt(16),
        display: 'flex',
        alignItems: isWeb ? 'top' : 'center',
        flexDirection: 'row',
        backgroundColor: '#fff6f6'
    },
    discountPriceWarp: {
        display: 'flex',
        alignItems: isWeb ? 'top' : 'center',
        marginLeft: pt(1)
    },
    arrowLeft: {
        backgroundColor: 'transparent',
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: pt(2),
    },
    noBackImage: {
        paddingLeft: pt(4),
        borderBottomLeftRadius: pt(4),
        borderTopLeftRadius: pt(4)
    },
    limitedWarp: {
        display: 'flex',
        flexDirection: 'row',
        backgroundColor: 'white',
        borderRadius: pt(3.1),
        overflow: 'hidden',
        height: pt(16),
        alignItems: 'center',
        justifyContent: 'flex-end'
        // borderWidth: isWeb ? pt(1.1) : pt(0),
        // borderColor: 'red'
    },
    limitedImgBorder: {
        height: pt(16),
        width: pt(0),
        position: 'absolute',
        right: pt(-3),
        top: pt(0)
    },
    limitedImgBorderLine: {
        width: pt(0),
        height: pt(8),
        backgroundColor: '#FFA2A2',
        marginTop: pt(4)
    },
    limitedImgWarp: {
        position: 'relative',
        backgroundColor: '#FFFFFF',
        height: pt(20),
        paddingTop: pt(1),
        flexDirection: 'row',
        paddingLeft: pt(3),
        marginTop: -pt(2),
        marginLeft: -pt(1.1)
    },
    limitedImg: {
        borderRadius: pt(2),
        height: pt(10),
        width: pt(44),
        marginTop: pt(3),
        marginRight: pt(2),
        marginLeft: pt(2),
    },
    limitedWord: {
        fontSize: pt(10),
        lineHeight: pt(16),
        color: '#fff',
        fontWeight: THEME_FONT.fontWeight.Bold,
        marginRight: pt(2),
        marginLeft: pt(3),
        fontStyle: 'italic'
    },
    priceCard:{
        borderRadius: pt(4),
        // height: pt(45),
    },
    priceBg: {
        position: 'absolute',
        width: '100%',
        // height: pt(45), 
        borderRadius: pt(4), 
    },
    priceBgLeftImg: {
        position: 'absolute',
        backgroundColor: '#FFFFFF',
        // height: pt(46),
        // width: pt(32),
    },
    limitedIDWarp: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
    },
    limitedIDImg:{
        height: pt(14),
        borderTopLeftRadius: pt(2),
        borderBottomLeftRadius: pt(2),
        overflow: 'hidden'
    },
    discountIDWarp: {
        backgroundColor: '#FFE3C5',
        borderTopRightRadius: pt(2),
        borderBottomRightRadius: pt(2),
        paddingLeft: pt(4),
        paddingRight: pt(4),
        height: pt(16),
        display: 'flex',
        alignItems: isWeb ? 'top' : 'center',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center'
    },
    discountIDPriceWarpDefault: {
        display: 'flex',
        alignItems: isWeb ? 'top' : 'center',
        flexDirection: 'row',
        alignItems: 'center',
    },
    discountPriceDefaultTxt: {
        color: '#FB5E00',
        fontSize: pt(10),
        textAlign: 'center',
        lineHeight: pt(14),
    },
    discountPriceDefault:{
        color: '#FB5E00',
        fontFamily: 'JDZhengHT-Regular',
        fontWeight: THEME_FONT.fontWeight.Regular,
        fontSize: pt(12),
        lineHeight: pt(14),
        marginLeft: pt(2),
    },

    discountPrice: {
        color: '#FF0400',
        fontFamily: 'JDZhengHT-Regular',
        fontWeight: THEME_FONT.fontWeight.Regular,
        fontSize: pt(12),
        lineHeight: isWeb ? pt(16) : pt(14),
    },
    right: {
        flex: 1,
        marginLeft: pt(8),
        paddingBottom: pt(12),
        paddingTop: pt(6),
        borderTopWidth: pt(0.6),
        borderColor: 'rgba(194,196,204, 0.2)',
    },
    titleWr: {},
    tag_list: {
        // display: 'flex',
        // flex: 1,
        flexDirection: 'row',
        flexWrap: 'nowrap',
        marginTop: pt(6),
        paddingRight: pt(8)
    },
    tagWr: {
        display: 'flex',
        // flex: 1,
        flexDirection: 'row',
        // marginTop: pt(4),
        alignItems: 'center'
    },
    tagWrTag: {
        display: 'flex',
        flexDirection: 'row',
        marginTop: pt(4),
        alignItems: 'center'
    },
    tagWrMarginTop: {
        marginTop: pt(6)
    },
    location_text: {
        lineHeight: pt(14),
        fontSize: pt(12),
        fontWeight: '400',
        color: '#505259',
    },
    location_no: {
        flexShrink: 0,
    },
    location: {
        flexShrink: 1,
        flexGrow: 0
    },
    line4_label: { // 返优惠券的样式
        height: pt(16),
        // paddingHorizontal: pt(4),
        marginRight: pt(4)
    },
    labelFont: {
        fontSize: pt(11),
        lineHeight: pt(12),
        fontWeight: '300',
        color: '#006EEB'
    },
    disabled_label: {
        fontSize: pt(11),
        lineHeight: pt(12),
        fontWeight: '300',
        color: '#A2ABBF'
    },
    labelBorder: {
        borderRadius: pt(2),
        borderWidth: px(1),
        borderColor: '#006EEB'
    },
    disabled_labelBorder: {
        borderRadius: pt(2),
        borderWidth: px(1),
        borderColor: '#A2ABBF'
    },
    scoreTips: {
        fontSize: pt(14),
        lineHeight: pt(18),
        fontWeight: '500',
        color: '#006EEB'
    },
    line6: {
        flex: 1,
        justifyContent: 'flex-end',
        alignItems: 'flex-end',
    },
    price: {
        display: 'flex',
        flex: 1,
        flexDirection: 'row',
        alignItems: 'flex-end',
        justifyContent: 'flex-end',
        marginBottom: pt(5),
        paddingRight: pt(4),
        paddingLeft: pt(10),
    },
    money_del: {
        color: '#888B94',
        fontSize: pt(12),
        fontWeight: '400',
        textDecorationLine: 'line-through',
        marginBottom: pt(-1)
    },
    money_integer: {
        color: '#006EEB',
        fontSize: pt(22),
        fontWeight: '400',
        fontFamily: 'JDZhengHT-Regular',
        marginBottom: pt(-1)
    },
    decimalStyle: {
        color: '#006EEB',
        fontSize: pt(20),
        fontWeight: '400',
        fontFamily: 'JDZhengHT-Regular'
    },
    discount_price: {
        backgroundColor: '#FFF6F6',
        paddingLeft: pt(4),
        paddingRight: pt(4),
        // marginTop: pt(4),
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: pt(2.1),
        display: 'flex',
        flexDirection: 'column',
    },
    discount_price_text: {
        color: '#FF0400',
        fontFamily: 'JDZhengHT-Regular',
        fontSize: pt(11),
        textAlign: 'center',
        display: 'flex',
        alignItems: 'center',
        lineHeight: isWeb ? pt(16) : pt(17),
        fontWeight: '400',
        marginLeft: pt(0)
    },
    symbolStyle: {
        color: '#006EEB',
        fontSize: pt(12),
        fontWeight: '400',
    },
    money_suffix: {
        color: '#006EEB',
        fontSize: pt(11),
        fontWeight: '400',
    },
    right_review: {
        color: '#5E6880',
        fontSize: pt(12),
        fontWeight: '300',
        marginLeft: pt(8),
        height: pt(18),
        // lineHeight: pt(18)
    },
    desc_warp: {
        marginTop: pt(6),
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center',
    },
    desc_warp_text: {
        fontSize: pt(12),
        fontWeight: '400',
        color: '#1B5DC1'
    },
    subTitleWr: {
        flexDirection: 'row',
        // marginTop: pt(8),
        flexWrap: 'wrap',
        height: pt(12),
        alignItems: 'center',
        overflow: 'hidden'
    },
    geoInfo: {
        fontWeight: '300',
        color: '#5E6880',
        fontSize: pt(12),
        height: pt(12),
        lineHeight: pt(12)
    },
    summary: {
        color: '#013B94',
        fontWeight: '300',
        fontSize: pt(12),
        height: pt(12),
        lineHeight: pt(12)
    },
    disabled: {
        color: '#A2ABBF'
    },
    saleDone: {
        flex: 1,
        height: pt(40),
        justifyContent: 'flex-end',
        alignItems: 'flex-end',
        marginBottom: pt(-4),
        marginRight: pt(6),
        // marginTop: pt(8),
    },
    soldOut: {
        width: pt(58),
        height: pt(58)
    },
    lineBottom: {
        flex: 1,
        flexDirection: 'row',
        display: 'flex',
        justifyContent: 'flex-end',
        marginTop: pt(2)
    },
    hotelRoomContainer: {
        flexDirection: 'column',
        marginTop: pt(2),
        marginBottom: pt(2)
    },
    hotelRoomWrapper: {
        flexDirection: 'row',
        marginBottom: pt(8),
        alignItems: 'center'
    },
    roomLogoWrapper: {
        flexDirection: 'row',
        width: pt(102), // 与图片宽度相同
        justifyContent: 'flex-end',
    },
    roomTextWrapper: {
        flexDirection: 'row',
        flex: 1,
        marginLeft: pt(8),
        alignItems: 'center'
    },
    textWrapper: {
        flex: 1,
        display: 'flex',
        flexDirection: 'row',
        height: pt(isAndroid ? 16: 13),
        lineHeight: pt(isAndroid ? 16: 13),
        flexWrap: 'wrap',
        overflow: 'hidden',
        marginLeft: pt(4)
    },
    roomText: {
        fontSize: pt(12),
        fontWeight: '400',
        color: '#000000'
    }
})

import React from 'react'
// 基础组件
import { StyleSheet, TouchableWithoutFeedback, View, Image } from 'react-native'
import { JDText, JDImage } from '@jdreact/jdreact-core-lib'
// 卡片元素组件
import TitleRender from '../element/TitleRender'
import TagRender from '../element/TagRender'
import Rank from '../element/Rank'
import { getImageUrl } from '@/assets/imgs'
import Price from '../element/price'
// 工具类
import { pt, px, isAndroid } from '@ltfe/ltfe-core-lib/lib/utiles'
import { safeRun } from '../element/utils'
import _ from 'lodash'
import { getImg } from '../../Filter/utils'
import { getUuid } from '@/utils';
import { isWeb } from '@/common/common'
import { THEME_FONT, THEME_BASE } from '@ltfe/ltfe-core-lib/lib/utiles/theme';
export const hasValue = (text) => {
  return text !== null && text !== '' && text !== undefined;
};
const IMGS = {
  defaultImg: getImageUrl('defaultScenic')
}
/**
 * ScenicCard组件
 * @param {string} title - 标题
 * @param {string} src - 图片地址
 * @param {object} promotionTagListMap - 促销标签列表映射
 * @param {boolean} disabled - 是否禁用
 * @param {object} style - 样式
 * @returns {JSX.Element} - 返回一个包含景点卡片信息的组件
 */
const ScenicCard = ({
  title, // 标题
  src, // 图片地址
  price,
  oPrice,
  free,
  promotionTagListMap, // 促销标签列表映射
  disabled, // 是否禁用
  style = {}, // 样式
  layoutInfo,
  cardWidth,
  ...props
}) => {
  const onPress = (target) => (event) => safeRun(props.onPress, { // 点击事件处理函数
    event, // 事件
    trigger: target // 触发目标
  })
  // 标题节点
  // const titleNode = React.useMemo(() => {
  let titleNode;
  const mainScenicTitleAfter = promotionTagListMap?.['mainScenicTitleAfter'] // 主要景点标题后缀
  let tagName = ''
  if (mainScenicTitleAfter && Array.isArray(mainScenicTitleAfter) && mainScenicTitleAfter.length) {
    mainScenicTitleAfter.forEach((item) => {
      if (item?.styleCode === "Tag") {
        tagName = item?.listShowName ?? ''
      }
    })
  }
  // let titleWidth = 0, tagWidth = 0;
  // if (layoutInfo && Array.isArray(layoutInfo) && layoutInfo.length) {
  //   layoutInfo.forEach((item) => {
  //     if (item.text === title) {
  //       titleWidth = item?.layout?.width ?? 0
  //     }
  //     if (item.text === tagName) {
  //       tagWidth = item?.layout?.width ?? 0
  //     }
  //   })
  // }
  // cardLayout: paddingLeft: pt(8) ,paddingRight: pt(8)
  // const spacing = pt(16)
  // layout: paddingLeft: pt(12),  paddingRight: pt(12)
  // cardLayout: spacing: pt(8), paddingLeft: pt(8) ,paddingRight: pt(8)
  // const defaultWidth = (375 - pt(48)) / 2
  let str = title
  if (str.length > 18 && !!tagName) {
    str = str.substring(0, 18) + '...'
  }
  titleNode = (<View style={styles.titleWr}>
    {!!tagName ? (<JDText style={styles.title_content} numberOfLines={2}>
      {str}
      {!!tagName && <JDText style={styles.tag_text} > {tagName}</JDText>}
    </JDText>) : (<JDText style={styles.title_content_line} numberOfLines={2}>
      {str}
    </JDText>)}

    {/* <TitleRender titleWidth={titleWidth} cardWidth={!!cardWidth ? (cardWidth - spacing) : defaultWidth} tagWidth={tagWidth} title={title} disabled={disabled} tagName={tagName} /> */}
  </View>)
  // }, [title, disabled, promotionTagListMap?.['mainScenicTitleAfter']])
  // 标签节点
  // const TagNode = React.useMemo(() => {
  let options = []
  const mainScenicPromotion = promotionTagListMap?.['mainScenicPromotion'] // 主要景点促销
  if (mainScenicPromotion && Array.isArray(mainScenicPromotion) && mainScenicPromotion.length) {
    mainScenicPromotion?.slice(0, 2)?.forEach((item, index) => {
      if (item?.styleCode === 'TagList') {
        options.push({
          title: item?.listShowName, // 标题
          value: getUuid() + (item?.labelCode ?? index)
        })
      }
    })
  }
  const TagNode = options.length ? <View style={styles.tagWr}><TagRender options={options} /></View> : <></>
  // }, [promotionTagListMap?.['mainScenicPromotion']])
  // 位置距离节点
  // const locationNode = React.useMemo(() => {
  let locationNode;
  const locationList = promotionTagListMap?.['mainScenicLocationDistance'] // 主要景点位置距离
  if (Array.isArray(locationList) && locationList.length > 0) {
    locationNode = (<View style={styles.tagWr}>
      <JDImage resizeMode="contain" style={styles.locationIcon} source={{ uri: getImg('location') }} resizeMethod='resize' />
      <JDText numberOfLines={1} style={[styles.location_geoInfo, disabled ? styles.disabled : '']}>
        {
          locationList.map((item, index) => <JDText key={index}>
            {item.listShowName}
            {index < locationList.length - 1 ? <JDText>·</JDText> : <></>}
          </JDText>)
        }
      </JDText>
    </View>)
  }
  // else {
  //   return <></>
  // }
  // }, [promotionTagListMap?.['mainScenicLocationDistance']])
  // 评分节点
  // const scoreNode = React.useMemo(() => {
  let scoreNode;
  const mainScenicScore = promotionTagListMap?.['mainScenicScore'] // 主要景点评分
  if (Array.isArray(mainScenicScore) && mainScenicScore.length > 0) {
    scoreNode = (<View style={[styles.priceWr, styles.lineBottom]}>
      {!!price &&
        <View style={styles.priceContent}><Price
          amount={price}
          numberDecimal={'auto'}
          suffix="起"
          suffixWarp={{ marginBottom: pt(-1) }}
          suffixStyle={styles.money_suffix}
          symbolStyle={styles.symbolStyle}
          decimalStyle={styles.decimalStyle}
          integerStyle={styles.money_integer}
        /></View>}
      <View style={[styles.priceScore, { marginLeft: !price ? pt(0) : pt(6), }]}>
        <JDText style={[styles.geoInfo, disabled ? styles.disabled : '']}>
          {mainScenicScore[0]?.listShowName ?? ''}
        </JDText>
      </View>
    </View>)
  }
  // else {
  //   return <></>
  // }
  // }, [promotionTagListMap?.['mainScenicScore']])
  // 一句话评价
  // const commentNode = React.useMemo(() => {
  let commentNode
  const scenicComment = promotionTagListMap?.['mainScenicComment'] // 主要景点评论
  if (Array.isArray(scenicComment) && scenicComment.length) {
    commentNode = <View style={{flexShrink: 1,display: 'flex', flexDirection: 'row'}}>
      <View style={[styles.subTitleWr, {display: 'flex', flexDirection: 'column'}]}>
        {scenicComment.map((item, index) => <View style={styles.summary_warp} key={index}>
          <JDText numberOfLines={1} style={[styles.summary, disabled ? styles.disabled : {}]}>
            {item?.listShowName ?? ''}
          </JDText>
        </View>)}
        <View style={[styles.summary_line]}></View>
      </View>
    </View>
  }
  //  else {
  //   return <></>
  // }
  // }, [promotionTagListMap?.['mainScenicComment'], descWidth])
  return (
    <TouchableWithoutFeedback onPress={onPress('scenic-card')}>
      <View style={[styles.container,  style]}>
        <View style={[styles.left, typeof src === 'string' && src !== '' ? {} : { backgroundColor: '#F6FAFF', justifyContent: 'center', alignItems: 'center' }]}>
          {
            typeof src === 'string' && src !== '' ? <JDImage resizeMethod='resize' style={styles.img} source={{ uri: src }} /> : <Image style={styles.img} source={{ uri: IMGS.defaultImg }} />
          }
        </View>
        <View style={styles.right}>
          {titleNode}
          {commentNode}
          {TagNode}
          <Rank data={promotionTagListMap?.['mainScenicRank']}/>
          {locationNode}
          {scoreNode}
        </View>
      </View>
    </TouchableWithoutFeedback>
  )
}

export default ScenicCard

ScenicCard.modeMap = {
  fieldNames: {
    src: 'picUrl',
    title: 'name',
    price: 'price',
    geoInfo: 'geoInfo',
    scoreList: 'promotionTagListMap.mainScenicScore',
    labelList: 'promotionTagListMap.mainScenicPromotion',
    locationList: 'promotionTagListMap.mainScenicLocationDistance',
    titleAfter: 'promotionTagListMap.mainScenicTitleAfter',
    summary: 'promotionTagListMap.mainScenicComment'
  },
  modeMap: {},
  labelMap: () => {

  }
}

export const styles = StyleSheet.create({
  container: {
    // flex: 1,
    position: 'relative',
    borderRadius: pt(8),
    backgroundColor: '#fff'
  },
  locationIcon: {
    width: pt(10),
    height: pt(12),
    marginTop: isAndroid ? pt(1) : pt(0),
    marginRight: pt(6)
  },
  left: {
    borderTopStartRadius: pt(8),
    borderTopEndRadius: pt(8),
    overflow: 'hidden',
    minHeight: pt(172),
  },
  img: {
    maxHeight: pt(172),
    minHeight: pt(172),
    width: '100%',
    // flex: 1,
    backgroundColor: '#efefef'
  },
  default_img: {
    marginLeft: pt(8),
    marginRight: pt(8),
    width: pt(74),
    height: pt(26)
  },
  right: {
    // flex: 1,
    paddingLeft: pt(8),
    paddingRight: pt(8),
    paddingBottom: pt(12),
    paddingTop: pt(6),
  },
  titleWr: {
    paddingVertical: pt(2),
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  title_content_line: {
    color: THEME_BASE.primaryColor,
    fontSize: pt(15),
    lineHeight: pt(20),
    fontWeight: THEME_FONT.fontWeight.SemiBold
  },
  title_content: {
    color: THEME_BASE.primaryColor,
    fontSize: pt(15),
    display: isWeb ? 'inline-block' : 'flex',
    lineHeight: pt(20),
    fontWeight: THEME_FONT.fontWeight.SemiBold
  },
  tag_warp: {
    backgroundColor: THEME_BASE.middleColorThree,
    paddingLeft: pt(4),
    height: pt(16),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    paddingRight: pt(4),
    marginRight: pt(4),
    marginLeft: pt(4),
    borderRadius: isAndroid ? pt(4) : pt(2)

  },
  tag_text: {
    fontSize: pt(11),
    display: isWeb ? 'inline-block' : 'flex',
    fontWeight: THEME_FONT.fontWeight.Medium,
    color: '#7C859C',
    flexShrink: 1,
  },
  tagWr: {
    flexDirection: 'row',
    display: 'flex',
    marginTop: pt(6),
    flexWrap: 'nowrap',
    alignItems: 'center',
  },
  line4_label: { // 返优惠券的样式
    height: pt(16),
    paddingHorizontal: pt(4),
    marginRight: pt(4)
  },
  labelFont: {
    fontSize: pt(11),
    lineHeight: pt(12),
    fontWeight: '300',
    color: '#006EEB'
  },
  disabled_label: {
    fontSize: pt(11),
    lineHeight: pt(12),
    fontWeight: '300',
    color: '#A2ABBF'
  },
  labelBorder: {
    borderRadius: pt(2),
    borderWidth: px(1),
    borderColor: '#006EEB'
  },
  disabled_labelBorder: {
    borderRadius: pt(2),
    borderWidth: px(1),
    borderColor: '#A2ABBF'
  },
  line6: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    marginBottom: pt(-4)
  },
  subTitleWr: {
    display: 'flex',
    marginTop: pt(6),
    zIndex: 1999,
    position: 'relative',
    flexDirection: 'column',
  },
  subTitleLocation: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    height: pt(18),
    alignItems: 'center',
    overflow: 'hidden'
  },
  location_geoInfo: {
    flex: 1,
    fontWeight: '400',
    color: '#7C859C',
    fontSize: pt(12),
  },
  priceContent: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-end'
  },
  priceScore: {
    display: 'flex',
    alignItems: 'flex-end',
    marginLeft: pt(6),
    marginBottom: isAndroid ? pt(-2) : pt(-3)
  },
  geoInfo: {
    fontWeight: '400',
    color: '#7C859C',
    fontSize: pt(12),
    paddingBottom: pt(2),
  },
  summary_line: {
    height: pt(4),
    width: '100%',
    flexGrow: 1,
    flex: 1,
    zIndex: -1,
    backgroundColor: '#D4D9E3',
    position: 'absolute',
    bottom: pt(1),
    // borderWidth: pt(1),
    opacity: 0.4,
  },
  summary_warp: {
    display: 'flex',
    alignItems: 'flex-start',
    zIndex: 1999,
    marginBottom: pt(2),
  },
  summary: {
    color: '#7C859C',
    fontWeight: '500',
    fontSize: pt(11),
  },
  disabled: {
    color: '#A2ABBF'
  },
  lineBottom: {
    alignItems: 'flex-end',
  },
  priceWr: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginTop: pt(6),
  },
  price: {
    display: 'flex',
    alignItems: 'flex-end',
  },
  money_del: {
    color: '#A2ABBF',
    fontSize: pt(12),
    fontWeight: '400',
    fontFamily: 'JDZhengHT-Regular',
    textDecorationLine: 'line-through'
  },
  money_integer: {
    color: '#006EEB',
    fontSize: pt(20),
    fontWeight: '400',
    fontFamily: 'JDZhengHT-Regular'
  },
  decimalStyle: {
    color: '#006EEB',
    fontSize: pt(20),
    fontWeight: '400',
    fontFamily: 'JDZhengHT-Regular'
  },
  symbolStyle: {
    color: '#006EEB',
    fontWeight: '400',
    fontSize: pt(12)
  },
  money_suffix: {
    color: '#006EEB',
    fontWeight: '400',
    fontSize: pt(12)
  },
  free: {
    fontSize: pt(12),
    color: '#5E6880',
    fontWeight: '400',
  },
})

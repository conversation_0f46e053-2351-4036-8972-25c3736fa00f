import React from 'react';
import { FlatList, StyleSheet, View } from 'react-native';
import Card from '../index';
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';

const CardList = (props) => {
    const { cardList, mode, collapse, cardBoxStyle, onToggle, onPress } = props;
    const renderItem = ({ item, index }) => {
        return <View style={ cardBoxStyle }>
            <Card
                mode={ mode }
                data={ item }
                index={ index }
                collapse={ collapse }
                showLine={ index < cardList.length - 1 }
                onToggle={ onToggle }
                onPress={ onPress }
            />
        </View>;
    };

    return <View style={ styles.listBox }>
        <FlatList
            data={ cardList }
            initialNumToRender={ 3 }
            keyExtractor={ (item, index) => index + '' }
            renderItem={ renderItem }
        />
    </View>;
};

export default CardList;

const styles = StyleSheet.create({
    listBox: {
        backgroundColor: '#fff',
        marginBottom: pt(12),
        borderRadius: pt(12)
    }
});

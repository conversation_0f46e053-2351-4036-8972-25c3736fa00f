import {StyleSheet, View} from 'react-native'
import {isAndroid, pt} from '@ltfe/ltfe-core-lib/lib/utiles'
import {JDText} from '@jdreact/jdreact-core-lib'
import Star from '../element/Stars'
import {isWeb} from '@/common/common'
import {THEME_FONT, THEME_BASE} from '@ltfe/ltfe-core-lib/lib/utiles/theme'

const getTitle = (title) => {
    return title.length > 24 ? `${title.slice(0, 24)}...` : title
}

const Title = (props) => {
    const {title = '', starCount = 0, tagName, disabled, iconType} = props
    return <View style={styles.titleBox}>
        <JDText style={styles.title}>
            {getTitle(title)}
            <Star count={starCount} disabled={disabled} iconType={iconType}/>
            {!!tagName && <View style={styles.title_warp_tag}>
                <JDText style={styles.title_label}>{tagName ?? ''}</JDText>
            </View>}
        </JDText>
    </View>
}

export default Title

const styles = StyleSheet.create({
    titleBox: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    title: {
        color: THEME_BASE.primaryColor,
        fontSize: pt(16),
        fontWeight: isAndroid ? 'bold' : "600",
        lineHeight: pt(22)
    },
    title_label: {
        fontSize: pt(11),
        lineHeight: pt(14),
        fontWeight: THEME_FONT.fontWeight.Regular,
        color: THEME_BASE.secondaryColor
    },
    title_warp_tag: {
        backgroundColor: THEME_BASE.middleColorThree,
        height: pt(16),
        display: isWeb ? 'inline-flex' : 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        paddingRight: pt(4),
        paddingLeft: pt(4),
        marginRight: pt(4),
        marginLeft: pt(4),
        borderRadius: isAndroid ? pt(2.1) : pt(2),
        marginTop: pt(-1)
    }
})

import React from 'react';
import { StyleSheet } from 'react-native';
import { View, Image } from '@/BaseComponents/atoms'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import { JDText } from '@jdreact/jdreact-core-lib';
import { isWeb, isAndroid } from '@/common/common'
import LinearGradient from '@jdreact/jdreact-core-linear-gradient'
import { isEmpty } from '@/utils/isType';
import useJumpTo from '@/common/useJumpTo'

enum STYLE_CODE {
    TEXT = 'Text',
    IMAGE = 'Image',
    JUMP = 'Jump'
}

const Rank = (props) => {
    const jumpTo = useJumpTo()
    const { data = [] } = props

    if (isEmpty(data)) {
        return null
    }

    const handleClick = (jumpUrl) => {
        if (!isEmpty(jumpUrl)) {
            jumpTo({ to: 'web', params: { url: decodeURIComponent(jumpUrl) } })
        }
    }

    const renderArrowImage = (item) => {
        const { rightImage, rightImageWidth, rightImageHeight } = item || {}
        if (isEmpty(rightImage) || isEmpty(rightImageWidth) || isEmpty(rightImageHeight)) {
            return null
        }

        const numberWidth = Number(rightImageWidth)
        const numberHeight = Number(rightImageHeight)
        if (numberWidth <= 0 || numberHeight <= 0) {
            return null
        }

        const showHeight = 7
        const showWidth = showHeight * (numberWidth / numberHeight)
        return (
            <Image
                style={{ width: pt(showWidth), height: pt(showHeight), marginLeft: pt(2) }}
                src={rightImage}    
            />
        )
    }

    return (
        <View style={styles.rankWrapper}>
            {
                data?.map((item, index) => {
                    if (item.styleCode == STYLE_CODE.TEXT || item.styleCode == STYLE_CODE.JUMP) {
                        return (
                            <View
                                key={index}
                                onClick={() => handleClick(item?.jumpUrl)}>
                                <LinearGradient
                                    locations={[0, 0.97]}
                                    colors={isWeb ? ['#FFEDDD', '#FFEDDD', '#FFEDDD'] : ['#FFEDDD', '#FFEDDD']}
                                    style={[styles.rankContainer]}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 0 }}
                                    key={`${item?.listShowName}_${index}`}
                                >
                                    <JDText ellipsizeMode={'middle'} numberOfLines={1} style={styles.rankTitle}>{item?.listShowName}</JDText>
                                    {renderArrowImage(item)}
                                </LinearGradient>
                            </View>
                        )
                    }
                })
            }
        </View>
    );
};

export default React.memo(Rank);

const styles = StyleSheet.create({
    rankWrapper: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
    },
    rankContainer: {
        marginTop: pt(4),
        height: pt(16),
        paddingHorizontal: pt(4),
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: pt(2),
        overflow: 'hidden',
    },
    rankTitle: {
        color: '#7C4302',
        fontSize: pt(12),
        marginTop: (isAndroid && !isWeb) ? pt(-1) : 0,
        fontWeight: isAndroid ? 'bold' : '500'
    }
});

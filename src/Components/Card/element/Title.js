import React, { useState } from 'react'
import { StyleSheet, View } from 'react-native'
import { isAndroid, pt, px } from '@ltfe/ltfe-core-lib/lib/utiles'
import { JDText } from '@jdreact/jdreact-core-lib'
import Star from '../element/Stars'
import { Label } from '@ltfe/ltfe-core-lib'
import { hasValue } from './utils'

const calcLength = ( title, starCount, tagName, maxCount ) => {
  return (title.length + starCount + tagName.length) > maxCount
}

const getTagName = ( titleAfter, tagName ) => {
  let _tag = false
  Array.isArray(titleAfter) && titleAfter.forEach(item => {
    const {styleCode, listShowName} = item
    if ( styleCode === 'Tag' ) {
      _tag = listShowName
    }
  })
  return hasValue(_tag) ? _tag : tagName
}

const Title = ( props ) => {
  const {title, style, starCount = 0, tagName = '', maxCount = 30, disabled, titleAfter} = props

  const [titleTag] = useState(getTagName(titleAfter, tagName))

  return <View>
    {
      calcLength(title, starCount, tagName, maxCount) ? <View>
          <JDText style={ [styles.title, style, disabled ? styles.disabled : ''] } numberOfLines={ 2 }>
            { title }
          </JDText>

          <View style={ styles.contentBox }>
            <Star count={ starCount } disabled={ disabled }/>
            {
              starCount > 0 ? <JDText> </JDText> : null
            }
            {
              hasValue(titleTag) ? <Label name={ titleTag } style={ styles.title_label }
                                          font={ styles.labelTitleFont }
                                          border={ styles.labelTitleBorder }/> : null
            }
          </View>
        </View>
        : <JDText style={ [styles.title, style, disabled ? styles.disabled : ''] } numberOfLines={ 2 }>
          { title }<JDText> </JDText>
          <View style={ styles.contentBox }>
            <Star count={ starCount } disabled={ disabled }/>
            {
              starCount > 0 ? <JDText> </JDText> : null
            }
            {
              hasValue(titleTag) ? <Label name={ titleTag } style={ styles.title_label }
                                          font={ styles.labelTitleFont }
                                          border={ styles.labelTitleBorder }/> : null
            }
          </View>
        </JDText>
    }
  </View>
}

export default Title

const styles = StyleSheet.create({
  title: {
    color: '#1A1A1A',
    fontSize: pt(16),
    fontWeight: isAndroid ? 'bold' : '600',
    lineHeight: pt(20),
    verticalAlign: 'top'
  },
  labelTitleFont: {
    fontSize: pt(11),
    fontWeight: '300',
    color: '#5E6880'
  },
  title_label: {
    height: pt(16),
    paddingHorizontal: pt(4),
    backgroundColor: '#F5F7FA',
    lineHeight: pt(16),
    marginTop: pt(4)
  },
  labelTitleBorder: {
    borderRadius: pt(2),
    borderWidth: px(1),
    borderColor: '#F5F7FA'
  },
  contentBox: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    height: pt(20),
    paddingTop: pt(2)
  },
  disabled: {
    color: '#A2ABBF'
  }
})

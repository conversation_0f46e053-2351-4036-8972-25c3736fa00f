import React from 'react';
import { Platform, StyleSheet, View } from 'react-native';
import { pt, px, isAndroid } from '@ltfe/ltfe-core-lib/lib/utiles';
import { JDText } from '@jdreact/jdreact-core-lib';
import { THEME_FONT, THEME_BASE } from '@ltfe/ltfe-core-lib/lib/utiles/theme';
import {Image} from '@/BaseComponents/atoms'
import pd from '@ltfe/ltfe-core-lib/lib/utiles/pd';
import { isWeb } from '@/common/common'

const TagRender = ({ options, style = {} }) => {
    return <>
        {options && Array.isArray(options) ? <View style={[styles.warp, style, isWeb ? styles.warp_web : {}]}>
            {options?.map((item: any, index) => {
                return item?.imageUrl ?
                    <View style={[styles.marR4, isWeb && styles.marT4]}>
                        <Image mode="scaleToFill" src={{uri: item.imageUrl}} style={{height: pd(item?.imageHeight || 16), width: pd(item?.imageWidth || 67)}} />
                    </View> :
                    <View style={[styles.tag_warp, styles?.[item?.styleId ?? ''] ?? {}]} key={index}>
                        <JDText style={[styles.tag_warp_text, styles?.[item?.styleId ?? ''] ? styles?.[item?.styleId + '_text'] : {}]}>{item?.title ?? ''}</JDText>
                    </View>
            })}
        </View> : null}

    </>;
};

export default React.memo(TagRender);

const styles = StyleSheet.create({
    warp_web: {
        width: '100%'
    },
    warp: {
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'center',
        flex: 1, // 解决 点评掉下来换行展示问题
    },
    tag_score_warp: {
        height: pt(16),
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: THEME_BASE.highLightBlue,
        borderBottomRightRadius: pt(6),
    },
    tag_score_warp_comp: {
        backgroundColor: THEME_BASE.highLightBlue,
        borderBottomRightRadius: pt(6),
        marginRight: pt(4),
        height: pt(16),
    },
    tag_score_warp_comp_text: {
        color: "#FFFFFF",
        fontSize: pt(12),
        lineHeight: pt(14),
        alignItems: 'center',
        fontWeight: THEME_FONT.fontWeight.Regular,
        fontFamily: 'JDZhengHT-Regular',
    },
    tag_score_warp_text: {
        color: "#fff",
        fontSize: pt(12),
        lineHeight: pt(14),
        textAlign: 'center',
        fontWeight: THEME_FONT.fontWeight.Regular,
        fontFamily: 'JDZhengHT-Regular'
    },
    tag_good_warp: {
        borderWidth: pt(0),
        marginRight: pt(0),
        marginLeft: pt(-4),
        height: pt(18),
    },
    tag_good_warp_text: {
        color: THEME_BASE.highLightBlue,
        fontSize: pt(13),
        lineHeight: pt(18),
        fontWeight: THEME_FONT.fontWeight.Medium,
    },
    tag_text_warp: {
        borderWidth: 0,
        marginRight: pt(0),
        marginLeft: pt(-4),
        height: pt(18),
    },
    tag_text_warp_text: {
        color: "#1A1A1A",
        fontSize: pt(12),
        lineHeight: pt(18),
        fontWeight: THEME_FONT.fontWeight.Regular,
    },
    tag_warp: {
        height: pt(16),
        paddingLeft: pt(4),
        paddingRight: pt(4),
        borderColor: 'rgba(0, 110, 235, 0.5)',
        borderWidth: pt(0.6),
        borderRadius: pt(2.1),
        marginRight: pt(4),
        // paddingBottom: isAndroid ? pt(2) : pt(0),
        display: 'flex',
        // justifyContent: 'center',
        // alignItems: 'center',
        marginBottom: pt(2),
    },
    tag_warp_text: {
        color: THEME_BASE.highLightBlue,
        fontSize: pt(10),
        lineHeight: pt(14)
    },
    marR4: {
        marginRight: pt(4),
        borderRadius: pt(2),
        marginBottom: pt(2),
        overflow: 'hidden'
    },
    marT4: {
        marginTop: pt(2),
        marginBottom: pt(4),
        paddingLeft: pt(1)
    }
});

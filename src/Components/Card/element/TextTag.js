import React from 'react';
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import { StyleSheet, Text } from 'react-native';

function TextTag(props) {
    const { data, style } = props;

    return (
        <Text style={ [styles.txt, style, { color: data.color }] }>{data.text}</Text>
    );
}

export default TextTag;

const styles = StyleSheet.create({
    txt: {
        fontSize: pt(12),
        fontWeight: '400'
    }
});

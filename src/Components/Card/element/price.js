/**
 * @module Price
 * <AUTHOR> by mac
 * @date 2020/11/18
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { StyleSheet, View } from 'react-native';
import { JDText } from '@jdreact/jdreact-core-lib';
import { pt, fen2Yuan } from '@ltfe/ltfe-core-lib/lib/utiles';
import { THEME_LIST } from '../../utils/theme';
import { hasValue } from './utils';

export default class Price extends PureComponent {
    static defaultProps = {
        name: 'Price',
        // 发票金额 单位：分
        amount: 0,
        // 是否展示小数 展示的话展示两位
        numberDecimal: 'auto'
    }

    static displayName = 'Price'

    constructor(props) {
        super(props);
        this.state = {};
    }

    render() {
        const {
            amount,
            numberDecimal,
            suffix,
            symbolStyle,
            preFix,
            integerStyle,
            decimalStyle,
            suffixStyle,
            isUnderlined=false,
            warpPriceStyle = {},
            warpStyle={},
            suffixWarp={},
            symbolWarp={},
            preWarp={},
        } = this.props;
        const price = numberDecimal === 'auto' ? amount : fen2Yuan(amount, numberDecimal);
        const isShowDecimal = price % 1 !== 0;
        return (
            <View style={[st.warp, warpStyle]}>
                <View style={[st.preStyle, preWarp]}>
                    {
                        hasValue(preFix) ? <JDText style={st.prefix}>
                            {preFix}
                        </JDText> : null
                    }
                </View>
                <View style={[st.symbolStyle, symbolWarp]}>
                    <JDText type='Regular' style={[st.symbolText,symbolStyle]}>
                        &yen;
                    </JDText>
                </View>
                <View style={[isUnderlined ? st.underlined : st.price, warpPriceStyle]}>
                    <JDText type='Regular' style={[st.price_integer, integerStyle]}>
                        {price | 0}
                    </JDText>
                    {isShowDecimal &&
                        <JDText type='Regular' style={[st.decimal_style, decimalStyle]}>
                            {`.${String(price).replace(/\d+\.(\d*)/, '$1')}`}
                        </JDText>
                    }
                </View>
                <View style={[st.suffixStyle, suffixWarp]}>
                    {!!suffix &&
                        <>
                            <JDText style={suffixStyle}>{suffix}</JDText>
                        </>
                    }
                </View>
            </View>
            // <JDText type='Regular' style={[st.price_common, symbolStyle]}>
            //     {
            //         hasValue(preFix) ? <JDText style={st.prefix}>
            //             {preFix}
            //         </JDText> : null
            //     }
            //     &yen;
            //     <JDText style={[st.price_integer, integerStyle]}>
            //         {price | 0}
            //     </JDText>
            //     {isShowDecimal &&
            //         <JDText style={decimalStyle}>
            //             {`.${String(price).replace(/\d+\.(\d*)/, '$1')}`}
            //         </JDText>
            //     }
            //     {!!suffix &&
            //         <>
            //             <View style={{width: pt(2)}}></View>
            //             <JDText style={suffixStyle}>{suffix}</JDText>
            //         </>
            //     }
            // </JDText>

        );
    }
}

Price.propTypes = {
    name: PropTypes.string,
    // 发票金额 单位：分
    amount: PropTypes.number.isRequired,
    // 是否展示小数 展示的话展示两位
    showDecimal: PropTypes.bool,
    // 后缀内容
    suffix: PropTypes.string,
    // 金额符号样式
    symbolStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
    // 整数部分样式
    integerStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
    // 小数部分样式
    decimalStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
    // 后缀样式
    suffixStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array])
    // 前缀样式
};

const st = StyleSheet.create({
    // container: {
    //     flex: 1,
    //     backgroundColor: '#fff'
    // },
    preStyle: {
        // marginBottom: pt(-1)
    },
    symbolStyle: {
        marginBottom: pt(-1),
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'flex-end',
    },
    symbolText: {
        fontFamily: 'JDZhengHT-Regular',
    },
    warp: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'flex-end',
    },
    underlined: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'flex-end',
    },
    price: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'flex-end',
        marginBottom: pt(-3),
    },
    price_content: {
    },
    priceSuffix: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'flex-end'
    },
    price_common: {
        color: THEME_LIST.highLightBlue,
        fontSize: pt(12),
        includeFontPadding: false,
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'flex-end'
    },
    price_integer: {
        fontSize: pt(20),
        fontFamily: 'JDZhengHT-Regular',
    },
    decimal_style: {
        fontFamily: 'JDZhengHT-Regular',
    },
    prefix: {
        color: THEME_LIST.secondaryColor,
        fontSize: pt(12),
    },
    suffixStyle: {
        marginLeft: pt(2),
    }
});

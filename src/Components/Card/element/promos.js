import React, { PureComponent } from 'react';
import PropTypes, { func } from 'prop-types';
import { View, Image, StyleSheet } from 'react-native';
import { JDImage, JDTouchable, JDText } from '@jdreact/jdreact-core-lib';
import { getImg } from '../../Filter/utils';
import { pt, isAndroid } from '@ltfe/ltfe-core-lib/lib/utiles';

const IMGS = {
    plus: getImg('plus'),
    arrowRightRed: getImg('arrowRightRed'),
    plus_disable: getImg('plus_disable'),
    rectAngle: getImg('rectangle'),
    limitPrice: getImg('limitPrice')
};

class Promos extends PureComponent {
    static displayName = 'Promos'

    constructor(props) {
        super(props);
        this.state = {};
    }

    static propTypes = {
        // 报价接口的所有数据
        promoList: PropTypes.array,
        onPress: func

    }

    render() {
        const { promoList, notShowDetail, isDisable, type } = this.props;
        let savePrice = 0;
        let promos = [];
        if (promoList?.length) {
            promos = [...promoList];
            if (+promos[0].promotionType === 50 || +promos[0].user === 200) { // 如果第一项是plus，就放到最后一位。
                promos.push(promos.shift());
            }

            promos.forEach(item => {
                savePrice += Number(item.value);
            });
        }

        if (!promoList || !promoList?.length) {
            return null;
        }

        const limitprice = promoList.some(val => val.promotionType === 102);
        const hasPlus = promoList.some(val => +val.promotionType === 50 || +val.user === 200);
        const num = hasPlus ? 1 : 2;
        const plus = hasPlus ? promoList.filter(val => +val.promotionType === 50 || +val.user === 200) : [];

        return (
            <JDTouchable onPress={ notShowDetail ? null : this.onPress } contentContainerStyle={ styles.line7 }>
                {
                    promoList.map((item, i) => {
                        if (i > num) {
                            return;
                        }
                        if (type === 'recommend' && i > (num - 1)) {
                            return;
                        }
                        if (+item.promotionType === 50 || +item.user === 200) { // 如果是plus
                            return null;
                        }

                        if (limitprice) {
                            return <View key={ i }
                                style={ [styles.line7_label, i === 0 && styles.line7_label_first, isDisable && styles.disable_border_color] }>
                                <Image style={styles.thImg} source={ { uri: getImg('xsth') } } />
                            </View>;
                        }

                        return item.tag && (
                            // 第一个标签，左侧有borderRadius
                            <View key={ i }
                                style={ [styles.line7_label, i === 0 && styles.line7_label_first, isDisable && styles.disable_border_color] }>
                                <JDText
                                    style={ [styles.line7_label_text, isDisable && styles.disable_color] }>{ item.tag }</JDText>
                            </View>
                        );
                    })
                }
                {
                    plus?.length > 0 && plus.map((item, i) => {
                        if (+item.promotionType === 50 || +item.user === 200) { // 如果是plus
                            return (
                                <JDImage source={ { uri: isDisable ? IMGS.plus_disable : IMGS.plus } } style={ styles.plus }
                                    key={ i } />
                            );
                        }
                    })
                }
                <View style={ [styles.line7_sheng, isDisable && styles.new_border] }>
                    { limitprice && <JDImage style={ styles.line7_rect } source={ { uri: IMGS.rectAngle } } /> }
                    <JDText style={ styles.line7_sheng_text }>
                        { promoList.length > 1 ? `${promoList.length}项` : '' }优惠
                        <JDText style={ styles.price }>{ savePrice }</JDText>
                    </JDText>
                    { !notShowDetail && <Image style={ styles.line7_sheng_img } source={ { uri: IMGS.arrowRightRed } } /> }
                </View>
            </JDTouchable>
        );
    }

    onPress = () => {
        this.props.onPress && this.props.onPress();
    }
}

export default Promos;

export const styles = StyleSheet.create({
    disable_color: {
        color: '#CCC'
    },
    disable_border_color: {
        borderColor: '#CCC'
    },
    line7: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end'
        // marginTop: pt(2)
    },
    line7_label: {
        height: pt(16),
        paddingHorizontal: pt(2),
        backgroundColor: '#FF0400',
        justifyContent: 'center',
        alignItems: 'center',
        paddingLeft: pt(6)
    },
    line7_label_first: {
        borderTopLeftRadius: pt(2),
        borderBottomLeftRadius: pt(2)
    },
    line7_label_text: {
        color: '#fff',
        fontSize: pt(10),
        fontWeight: isAndroid ? 'bold' : '600'
    },
    line7_sheng: {
        backgroundColor: '#FFEFEF',
        height: pt(16),
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: pt(2),
        paddingLeft: pt(0),
        borderBottomRightRadius: pt(2),
        borderTopRightRadius: pt(2),
        overflow: 'hidden',
        color: '#FF0400',
        marginRight: pt(-1)
    },
    price: {
        fontSize: pt(12),
        fontWeight: isAndroid ? 'bold' : '600',
        color: '#FF0400'
    },
    new_border: {
        borderLeftColor: '#FFF',
        borderLeftWidth: pt(1)
    },
    line7_sheng_text: {
        color: '#FF0400',
        fontWeight: 300,
        fontSize: pt(12),
        marginLeft: pt(2)
    },
    line7_rect: {
        width: pt(4),
        height: pt(16)
    },
    line7_sheng_img: {
        width: pt(4),
        height: pt(8),
        marginLeft: pt(2),
        marginRight: pt(2)
    },
    plus: {
        width: pt(49),
        height: pt(15)
    },
    thImg: {
        width: pt(40),
        height: pt(9)
    }
});

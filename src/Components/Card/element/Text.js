import { StyleSheet } from 'react-native'
import { JDText } from '@jdreact/jdreact-core-lib'

import React from 'react'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import { THEME_BASE } from '../../utils/theme'

const Stars = ({ listShowName, disabled, style, ...props }) => {
    return <JDText style={[styles.defaultText, disabled ? styles.disabled : {}, style]} {...props}>{listShowName}</JDText>
}

export default Stars

export const styles = StyleSheet.create({
    defaultText: {
        color: THEME_BASE.secondaryColor,
        fontSize: pt(12),
        fontWeight: '300',
        height: pt(18),
        lineHeight: pt(18)
    },
    disabled: {
        color: THEME_BASE.middleColorOne
    }
})

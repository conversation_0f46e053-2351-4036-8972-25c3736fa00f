import React from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';

const TextTag = ({ cutPrice, timeOrder }) => {
    if (cutPrice && timeOrder) {
        return (
            <View style={ styles.offerBox }>
                <Image style={ styles.offerIcon }
                    source={ { uri: 'https://img10.360buyimg.com/imagetools/jfs/t1/156525/18/48562/2486/6711f9beFbf7740d2/6b8c5939411a598a.png' } } />
                <Text style={ styles.wordBox }>
                    2项优惠150
                </Text>
            </View>
        );
    } else if (cutPrice) {
        return (
            <View style={ styles.offerBox }>
                <Text style={ styles.wordBox }>已省 </Text>
                <Text style={ styles.count }>100</Text>
            </View>
        );
    } else if (timeOrder) {
        return (
            <Image style={ styles.offerIcon }
                source={ { uri: 'https://img14.360buyimg.com/imagetools/jfs/t1/100012/37/53413/1633/67125465F692b5cbf/6bbdda91e9a33c7d.png' } } />
        );
    }
    return null;
};

export default TextTag;

const styles = StyleSheet.create({
    offerBox: {
        flexDirection: 'row',
        flexGrow: 0,
        display: 'flex',
        justifyContent: 'space-between',
        backgroundColor: '#ffefef',
        paddingRight: pt(4),
        borderRadius: pt(2)
    },
    offerIcon: {
        height: pt(16),
        width: pt(48)
    },
    wordBox: {
        fontSize: pt(10),
        color: '#ff0400',
        height: pt(16),
        lineHeight: pt(16),
        paddingLeft: pt(4)
    },
    count: {
        color: '#FF0400',
        fontWeight: '600',
        height: pt(16),
        lineHeight: pt(16),
        fontSize: pt(14)
    }
});

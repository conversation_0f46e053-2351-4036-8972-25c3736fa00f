import { StyleSheet, View } from 'react-native';
import { isAndroid, pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import { JDText } from '@jdreact/jdreact-core-lib';
import Star from '../element/Stars';
import { isWeb } from '@/common/common'
import { THEME_FONT, THEME_BASE } from '@ltfe/ltfe-core-lib/lib/utiles/theme';
const TitleRenderLine = (props) => {
    const { title, starCount = 0, tagName = '', disabled, titleSize = 16, iconType } = props;
    return <View style={[styles.title_warp]}>
        <View style={[styles.title_warp_top, isWeb ? styles.inline_block : {}]}>
            <JDText style={[styles.title_warp_title, { fontSize: pt(titleSize) }]} numberOfLines={2}>{title}</JDText>
        </View>
        {(!!tagName || !!starCount) && <View style={[styles.title_warp_content, isWeb ? styles.inline_block : {}]}>
            <View style={{ display: 'flex', flexDirection: 'row' }}>
                {!!starCount && <View style={[styles.title_warp_star, { marginLeft: 0 }]}>
                    <Star count={starCount} disabled={disabled} iconType={iconType}/>
                </View>}
                {!!tagName && <View style={[tagName ? styles.title_warp_tag : {}, starCount > 0 ? {} : { marginLeft: 0 }]}>
                    <JDText style={styles.title_label}>{tagName ?? ''}</JDText>
                </View>}
            </View>

        </View>}
    </View>;
};
const TitleRenderColumn = (props) => {
    const { title, starCount = 0, tagName = '', disabled, titleSize = 16, iconType } = props;
    return <View style={[styles.title_warp_column, isWeb ? styles.title_warp_top_web : {}]}>
        <View style={[styles.title_warp_top]}>
            <JDText style={[styles.title_warp_title, { fontSize: pt(titleSize) }]} numberOfLines={2}>{title}</JDText>
        </View>
        {(!!tagName || !!starCount) && <View style={[styles.title_warp_content, { marginTop: pt(2), marginLeft: pt(0) }]}>
            {!!starCount && <View style={[styles.title_warp_star]}>
                <Star count={starCount} disabled={disabled} iconType={iconType}/>
            </View>}
            {!!tagName && <View style={[tagName ? styles.title_warp_tag : {}, { alignItems: 'flex-start', marginLeft: !!starCount ? pt(4) : 0 }]}>
                <JDText style={styles.title_label}>{tagName ?? ''}</JDText>
            </View>}
        </View>}
    </View>;
};
const TitleRenderFollow = (props) => {
    const { title, starCount = 0, tagName = '', disabled, titleSize = 16, layoutWidth, iconType } = props;
    return <View style={[styles.title_warp]}>
        <View style={[styles.title_warp_top, isWeb ? styles.title_warp_top_web : {}]}>
            <JDText style={[styles.title_warp_title, { fontSize: pt(titleSize) }, isWeb ? styles.inline_block : {}]}>
                {title}
                <JDText style={{ fontSize: pt(10) }}> </JDText>
                {(!!starCount || !!tagName) && <View style={styles.tagContent}>
                    { !isWeb && <View style={styles.emptyBlock}></View> }
                    <View style={[styles.tag_warp, isWeb ? styles.inline_block : {}]}>
                        {!!starCount && <Star count={starCount} disabled={disabled} iconType={iconType}/>}
                        {!!tagName && <View style={[!!tagName ? styles.title_label_follow : {}, starCount > 0 ? {} : { marginLeft: 0 }]}>
                            <JDText style={styles.title_label_follow_text}>{tagName ?? ''}</JDText>
                        </View>}
                    </View>
                </View>}
            </JDText>
        </View>
    </View>;
};

const Title = (props) => {
    const { title = '', starCount = 0, tagName = '', titleWidth, tagWidth, cardWidth } = props;
    const starWidth = starCount > 0 ? starCount * pt(14) + pt(4) : 0
    let titleLayoutWidth = titleWidth ? titleWidth : title?.length * pt(15) + pt(14)
    let tagLayoutWidth = tagWidth ? tagWidth : tagName?.length * pt(12) + pt(12)
    let totalWidth = starWidth + tagLayoutWidth + titleLayoutWidth;
    // // 使用useRef钩子创建一个textRef，用于获取CalcTextLayout组件的实例
    // const [_titleWidth, setTitleWidth] = React.useState(titleWidth);
    // const [_tagWidth, setTagWidth] = React.useState(tagWidth)
    // const titleRender = React.useMemo(() => {
    //     const starWidth = starCount * pt(14)
    //     let titleLayoutWidth = _titleWidth ? _titleWidth + pt(14) : 0
    //     let tagLayoutWidth = _tagWidth ? _tagWidth + pt(12) : 0
    //     let totalWidth = starWidth + tagLayoutWidth + titleLayoutWidth;
    //     if (totalWidth) {
    //         if (totalWidth < cardWidth) {
    //             return <TitleRenderLine {...props} layoutWidth={cardWidth} />;
    //         } else if ((totalWidth >= cardWidth && titleLayoutWidth < cardWidth) || totalWidth > cardWidth * 2) {
    //             return <TitleRenderColumn {...props} layoutWidth={cardWidth} />;
    //         } else {
    //             return <TitleRenderFollow {...props} layoutWidth={cardWidth} />;
    //         }
    //     } else {
    //         return <></>;
    //     }
    // }, [_tagWidth, _titleWidth, cardWidth, title, tagName, starCount]);
    // const _onLayoutTitle = (e) => {
    //     let width = e.nativeEvent.layout.width;
    //     setTitleWidth(width);
    // }

    return <>
        <View style={{ width: '100%' }}>
            {!!title ?
                (totalWidth < cardWidth) ?
                    <TitleRenderLine {...props} layoutWidth={cardWidth} /> : (totalWidth < cardWidth * 2) ? <TitleRenderFollow {...props} layoutWidth={cardWidth} />
                        : <TitleRenderColumn {...props} layoutWidth={cardWidth} /> : <></>
            }
        </View>
        {/* {!(_titleWidth && _titleWidth > 0) && <View style={styles.container}>
         <JDText style={[styles.txt, { fontSize: pt(16), fontWeight: 600 }]} onLayout={_onLayoutTitle}>{title}</JDText>
         <JDText style={[styles.txt, { fontSize: pt(11), fontWeight: 300 }]} onLayout={_onLayoutTag}>{tagName}</JDText>
         </View>} */}
    </>;
};

export default Title;

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        top: 0,
        zIndex: -1,
        width: 99999,
        alignItems: 'flex-start',
        opacity: 0,
        transform: [{ translateY: -9999 }]
    },
    txt: {
        flexShrink: 0,
        opacity: 0,
        flexGrow: 0,
    },
    titleBox: {
        alignItems: 'center'
    },
    title_warp: {
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'nowrap',
        justifyContent: 'flex-start',
        alignItems: 'center',
        // borderWidth: pt(1),
        // borderColor: 'red',
    },
    tagContent: {
        display: isWeb ? 'inline-flex' : 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: (isAndroid && !isWeb) ? pt(16) : 'auto'
    },
    emptyBlock: {
        width: pt(10),
        height: pt(6)
    },
    title_warp_column: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'flex-start',
    },
    title_warp_top_web: {
        width: '100%',
        display: 'inline-block',
    },
    inline_block: {
        display: 'inline-block',
    },
    title_warp_top: {
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'nowrap',
        alignItems: 'flex-end',
        justifyContent: 'flex-start',
        borderWidth: pt(1),
        borderColor: 'transparent'
    },
    tag_warp: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: isWeb ? pt(2) : pt(-1),
    },
    title_warp_title: {
        color: THEME_BASE.primaryColor,
        fontSize: pt(16),
        lineHeight: pt(20),
        display: isWeb ? '-webkit-box' : 'flex',
        // alignItems: 'flex-end',
        fontWeight: THEME_FONT.fontWeight.SemiBold
    },
    title_warp_content: {
        marginLeft: pt(4),
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start'
    },
    title_warp_star: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: pt(4),
    },
    title_warp_tag: {
        backgroundColor: THEME_BASE.middleColorThree,
        height: pt(16),
        display: isWeb ? 'inline-flex' : 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        paddingRight: pt(4),
        paddingLeft: pt(4),
        marginRight: pt(4),
        marginLeft: pt(4),
        borderRadius: isAndroid ? pt(2.1) : pt(2)
    },
    title: {
        color: THEME_BASE.primaryColor,
        fontSize: pt(16),
        fontWeight: THEME_FONT.fontWeight.SemiBold
    },
    title_label: {
        fontSize: pt(11),
        lineHeight: pt(14),
        fontWeight: THEME_FONT.fontWeight.Regular,
        color: THEME_BASE.secondaryColor
    },
    title_label_follow: {
        display: isWeb ? 'inline-flex' : 'flex',
        flexDirection: 'row',
        backgroundColor: THEME_BASE.middleColorThree,
        justifyContent: 'center',
        alignItems: 'center',
        paddingLeft: pt(4),
        height: pt(16),
        marginLeft: pt(4),
        lineHeight: isWeb ? pt(13) : pt(16),
        paddingRight: pt(4),
        borderRadius: isAndroid ? pt(4) : pt(2),
    },
    title_label_follow_text: {
        fontSize: pt(11),
        lineHeight: pt(14),
        fontWeight: THEME_FONT.fontWeight.Regular,
        color: THEME_BASE.secondaryColor
    },
    contentBox: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'center'
    },
    disabled: {
        color: THEME_BASE.middleColorOne
    }
});

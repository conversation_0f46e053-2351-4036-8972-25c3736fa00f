import React from 'react';
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import { Image, StyleSheet, Text, View, ImageBackground } from 'react-native';
import { hasValue } from './utils';

function Tag(props) {
    const { text } = props;
    return (
        <View style={ styles.tagOuter }>
            <View style={ styles.tagBox }>
                <View style={ styles.iconBox }>
                    <Image
                        style={ styles.iconImg }
                        source={ { uri: 'https://img13.360buyimg.com/imagetools/jfs/t1/208059/28/45673/1289/67124a6cF7c869018/23b9f86b7991e705.png' } }
                        resizeMode={ 'contain' } />
                </View>
                <View style={ styles.textBox } />
                <ImageBackground resizeMode={ 'stretch' } style={ styles.textBox }
                    source={ { uri: 'https://img11.360buyimg.com/imagetools/jfs/t1/147755/38/44297/574/67124a68F0136105b/d2873457d8d18c79.png' } }>
                    <Text style={ styles.text }>{ hasValue(text) ? text : '--' }豆</Text>
                </ImageBackground>
            </View>
        </View>
    );
}

export default Tag;

const styles = StyleSheet.create({
    tagOuter: {
        flexDirection: 'row',
        flexGrow: 0,
        display: 'flex',
        justifyContent: 'space-between',
        marginRight: pt(4),
        backgroundColor: '#FFEFEF',
        borderRadius: pt(2)
    },
    tagBox: {
        height: pt(16),
        borderRadius: pt(2),
        borderColor: '#A2ABBF',
        paddingLeft: pt(4),
        flexDirection: 'row',
        flexGrow: 0,
        display: 'flex',
        justifyContent: 'space-between'
    },
    iconImg: {
        height: pt(10),
        width: pt(33),
        marginTop: pt(3)
    },
    textBox: {
        height: pt(16),
        marginLeft: pt(2)
    },
    text: {
        fontSize: pt(10),
        color: '#ff0400',
        fontWeight: '600',
        lineHeight: pt(16),
        height: pt(16),
        paddingLeft: pt(4),
        paddingRight: pt(4)
    }
});

import { StyleSheet, View } from 'react-native'
import React from 'react'
import { JDImage } from '@jdreact/jdreact-core-lib'
import { pt, isAndroid } from '@ltfe/ltfe-core-lib/lib/utiles'
import { getImg } from '../../Filter/utils'

const Stars = ( props ) => {
    const {inTitle, count, disabled, style, iconType = 'Stars'} = props
    return (
        count > 0 ? <View style={ [styles.starWr, !inTitle ? styles.starMgT : styles.pdB, style] }>
            {
                Array.apply(null, {length: count}).map(( item, index ) => {
                    return (
                        <JDImage key={ index } style={ iconType === 'Stars' ? styles.starImg : styles.zs }
                                 source={ {uri: getImg(disabled ? `${ iconType }_disable` : iconType)} }/>
                    )
                })
            }
        </View> : null
    )
}

export default Stars

export const styles = StyleSheet.create({
    starWr: {
        paddingLeft: pt(4),
        flexDirection: 'row',
        alignItems: 'center',
        paddingTop: isAndroid ? pt(6): 0,
        height: pt(16)
    },
    pdB: {
        paddingBottom: pt(1)
    },
    starImg: {
        width: pt(9),
        height: pt(9),
        marginRight: pt(2)
    },
    zs: {
        width: pt(9),
        height: pt(9),
        marginRight: pt(2)
    }
})

import _ from 'lodash';
import React from 'react';

export const objSetMapperObj = (obj = {}, otherObj) => {
    const res = { ...otherObj };
    Object.keys(obj).forEach(key => {
        res[key] = _.get(otherObj, obj[key], '');
    });
    return res;
};

export const objGetMapperObj = (obj = {}, otherObj) => {
    const res = {};
    Object.keys(obj).forEach(key => {
        res[key] = _.get(otherObj, obj[key], '');
    });
    return res;
};

export const formatDistance = distance => {
    let text = '';

    distance = distance || 0;

    if (distance < 1000) {
        text = distance + '米';
    } else {
        text = parseFloat((distance / 1000).toFixed(2)) + '公里';
    }

    return text;
};

export const filterGeoName = (name, distance) => {
    const textRightLen = distance?.length || 0;
    const maxLen = 17 - textRightLen;

    return name && name.length >= maxLen ? `${name.slice(0, maxLen)}...` : name;
};

export const rangeText = (score, sourceMap) => {
    let text = '';
    Object.keys(sourceMap).forEach(scoreRange => {
        if (_.inRange(score, ...scoreRange.split('|'))) {
            text = sourceMap[scoreRange];
        }
    });
    return text;
};

export const safeRun = (func, ...args) => {
    if (_.isFunction(func)) {
        func(...args);
    } else {
        console.warn('Utils: safeRun func is not a function!');
    }
};

export const isComps = (element) => {
    return React.isValidElement(element) || _.isFunction(element);
};

export const safeParseJSON = (jsonStr) => {
    try {
        return JSON.parse(jsonStr);
    } catch (e) {
        console.log('safeParseJSON: run failed please check json');
        return {};
    }
};

export const textOverflow = (text, count) => {
    if (text.length > count) {
        return `${text.slice(0, count - 2)}...`;
    } else {
        return text;
    }
};

export const formatFightTime = (timeStr) => {
    if (typeof timeStr === 'string') {
        return timeStr.replace(/(\d{2})/, '$1:');
    } else {
        return '--:--';
    }
};

export const formatMinCount = (minCount) => {
    return `${Math.floor(minCount / 60) > 0 ? `${Math.floor(minCount / 60)}时` : ''}${minCount % 60}分`;
};

export const calcListCount = (list, fieldKey) => {
    let res = 0;
    for (let i = 0; i < list.length; i++) {
        if (fieldKey) {
            res += _.get(list, [i, fieldKey]);
        } else {
            res += _.get(list, [i]);
        }
    }
    return res;
};

export const isSameDay = (day, diffday) => {
    return ~~((new Date(day) - new Date(diffday)) / 864e5);
};

export const complement = num => num < 10 ? '0' + num : num;

export const formatDate = (day, formatType) => {
    if (day === 'null') { return ''; }
    // let dayS = typeof day === 'string' ? day.replace(/-/g, '/') : day;
    const dateF = new Date(day);
    let result;

    const y = dateF.getFullYear();

    const m = dateF.getMonth() + 1;

    const d = dateF.getDate();

    const h = dateF.getHours();

    const min = dateF.getMinutes();

    const ms = dateF.getSeconds();

    const getDay = dateF.getDay();

    const week = ['日', '一', '二', '三', '四', '五', '六'];

    switch (formatType) {
        case '-':
            result = complement(m) + '-' + complement(d);
            break;
        case '/':
            result = y + '/' + complement(m) + '/' + complement(d);
            break;
        case 'A':
            result = `${y}-${complement(m)}-${complement(d)} ${h}:${min}:${ms}`;
            break;
        case '.':
            result = `${y}.${complement(m)}.${complement(d)}`;
            break;
        case 'week':
            result = '周' + week[getDay];
            break;
        case 'month':
            result = `${complement(m)}-${complement(d)}`;
            break;
        case 'hour':
            result = `${complement(h)}:${complement(min)}`;
            break;
        default:
            result = complement(m) + '月' + complement(d) + '日';
    }
    return result;
};

export const hasValue = (text) => {
    return text !== null && text !== '' && text !== undefined;
};

import React from 'react';
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import { StyleSheet, Text, View } from 'react-native';
import { THEME_BASE } from '@ltfe/ltfe-core-lib/lib/utiles/theme';

function BookIcon(props) {
    const { payModeText, disabled } = props;
    return (
        <View style={ [styles.bookBox, disabled ? styles.disabledBox : ''] }>
            <Text style={ [styles.bookWord, disabled ? styles.disabled : ''] }>
                { disabled ? '订完' : '订' }
            </Text>
            <Text style={ [styles.bookType, disabled ? styles.disabledText : ''] }>
                { payModeText }
            </Text>
        </View>
    );
}

export default BookIcon;

const styles = StyleSheet.create({
    bookBox: {
        width: pt(44),
        height: pt(44),
        borderColor: THEME_BASE.highLightBlue,
        borderRadius: pt(4),
        borderWidth: pt(1),
        marginLeft: pt(6)
    },
    bookWord: {
        textAlign: 'center',
        fontSize: pt(16),
        color: '#fff',
        backgroundColor: THEME_BASE.highLightBlue,
        height: pt(26),
        lineHeight: pt(26),
        borderTopLeftRadius: pt(0),
        borderTopRightRadius: pt(0),
        borderBottomLeftRadius: pt(0),
        borderBottomRightRadius: pt(8),
        alignItems: 'center',
        justifyContent: 'center'
    },
    bookType: {
        color: THEME_BASE.highLightBlue,
        textAlign: 'center',
        fontSize: pt(12)
    },
    disabled: {
        backgroundColor: THEME_BASE.middleColorOne
    },
    disabledBox: {
        borderColor: THEME_BASE.middleColorOne
    },
    disabledText: {
        color: THEME_BASE.middleColorOne
    }
});

import React, { useState } from 'react';
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import { Image, StyleSheet, Text, View, TouchableWithoutFeedback } from 'react-native';
import { safeRun } from './utils';

function Tag(props) {
    const { text, iconList, expended, price } = props;

    const [_expended, setExpended] = useState(expended);

    const onPress = (event) => {
        if (typeof props.onToggle === 'function') {
            safeRun(props.onToggle, props, !_expended, event);
            setExpended(!_expended);
        } else {
            safeRun(props.onPress, props, event);
        }
    };

    return (
        <TouchableWithoutFeedback onPress={ onPress }>
            <View style={ styles.tagOuter }>
                <View style={ styles.tagBox }>
                    <View style={ styles.iconBox }>
                        {
                            Array.isArray(iconList) && iconList.map((item, index) => <View key={ index }
                                style={ styles.icon }><Image
                                    style={ styles.iconImg }
                                    source={ { uri: item } }
                                    resizeMode={ 'contain' } /></View>)
                        }
                    </View>
                    <Text style={ styles.tagWord }>{ text } </Text>
                    {
                        price ? <Text style={ styles.price }>¥ { price } <Image style={ styles.offerArrow }
                            source={ { uri: 'https://img14.360buyimg.com/imagetools/jfs/t1/179830/17/49853/238/67122128F5edf03dd/473e74fde8d643e6.png' } } /></Text> : null
                    }

                </View>
                {
                    typeof props.onToggle === 'function' && <Image style={ [styles.tagArrow, {
                        transform: _expended ? 'rotate(180deg)' : ''
                    }] }
                    source={ { uri: 'https://img13.360buyimg.com/imagetools/jfs/t1/90663/5/50849/401/6711fe02F228f26f1/5ce0502ec080452e.png' } } />
                }
            </View>
        </TouchableWithoutFeedback>

    );
}

export default Tag;

const styles = StyleSheet.create({
    tagOuter: {
        flexDirection: 'row',
        flexGrow: 0,
        display: 'flex',
        justifyContent: 'space-between',
        marginRight: pt(4)
    },
    tagBox: {
        height: pt(16),
        borderRadius: pt(2),
        borderWidth: pt(1),
        borderColor: '#A2ABBF',
        paddingLeft: pt(4),
        paddingRight: pt(4),
        flexDirection: 'row',
        flexGrow: 0,
        display: 'flex',
        justifyContent: 'space-between'
    },
    tagArrow: {
        height: pt(3),
        width: pt(8),
        marginTop: pt(6),
        marginLeft: pt(6),
        transform: 'rotate(0deg)'
    },
    tagWord: {
        lineHeight: pt(14),
        color: '#7C869C',
        fontSize: pt(12),
        marginLeft: pt(4)
    },
    icon: {
        justifyContent: 'center',
        width: pt(10),
        height: pt(10),
        borderRadius: pt(5),
        borderWidth: pt(1),
        borderColor: '#D4D9E3',
        marginTop: pt(3),
        marginLeft: pt(-4),
        backgroundColor: '#fff',
        overflow: 'hidden'
    },
    iconImg: {
        width: pt(8),
        height: pt(8)
    },
    iconBox: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        marginLeft: pt(6)
    },
    price: {
        fontSize: pt(12),
        color: '#FF0400'
    },
    offerArrow: {
        width: pt(2),
        height: pt(4),
        marginTop: pt(5),
        marginLeft: pt(2)
    }
});

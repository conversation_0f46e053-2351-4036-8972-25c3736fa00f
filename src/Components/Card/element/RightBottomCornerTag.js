import React from 'react';
import { isAndroid, pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import { StyleSheet, Text, View } from 'react-native';
import { THEME_BASE } from '../../utils/theme'

function RightBottomCornerTag(props) {
    const { bgColor, text, secondText, style, color, height, disabled } = props;
    return (
        <View style={ [styles.container, style] }>
            <View style={ [styles.innerWr, disabled ? styles.disabled : {}, { backgroundColor: bgColor, height: height }] }>
                <Text style={ [styles.txt, { color: color, lineHeight: height }] }>{ text }</Text>
            </View>
            <Text style={ [styles.secText, { color: bgColor }] }>{ secondText }</Text>
        </View>
    );
}

export default RightBottomCornerTag;

const borderRadius = pt(2);
const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        flexGrow: 0,
        flexShrink: 0
    },
    innerWr: {
        borderTopLeftRadius: borderRadius,
        borderTopRightRadius: borderRadius,
        borderBottomLeftRadius: borderRadius,
        borderBottomRightRadius: pt(6),
        height: pt(16),
        paddingHorizontal: pt(4),
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: THEME_BASE.highLightBlue
    },
    txt: {
        fontSize: pt(14),
        fontWeight: '400',
        lineHeight: pt(16),
        fontFamily: 'JDZhengHT',
        color: "#fff"
    },
    secText: {
        marginLeft: pt(4),
        fontSize: pt(14),
        fontWeight: isAndroid ? 'bold' : '500'
    },
    disabled: {
        backgroundColor: THEME_BASE.middleColorOne
    }
});

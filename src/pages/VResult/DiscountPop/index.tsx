import {StyleSheet, TouchableWithoutFeedback} from 'react-native'
import {View, Text} from '@/BaseComponents/atoms'
import classNames from 'classnames'
import {Image} from '@tarojs/components'
import {IOScrollView} from '@/BaseComponents/IntersectionObserver'
import {getImg} from '@/Components/Filter/utils'
import {safeRun} from '@/Components/Filter/utils'
import EventCatch from '@/Components/EventCatch'
import {pt, deviceHeight} from '@ltfe/ltfe-core-lib/lib/utiles'
import PriceTag from '../PriceTag'
import {JDPopupWindow} from '@jdreact/jdreact-core-lib'
import _ from 'lodash'
import styles from './index.module.scss'
import {isAndroid, isWeb} from '@/common/common'
import Markdown from '@/BaseComponents/MarkDown'
import {THEME_BASE} from '@ltfe/ltfe-core-lib/lib/utiles/theme'

import './index.css'

// const styles = {}
// official_standard_description
const popRender = {
    discount: (props) => {
        const {onClose, toPage, cardInfo = {}} = props
        const promotionLayerVO = _.get(cardInfo, ['cardProps', 'promotionLayerVO'], {})

        const isOneDayOneRoom = +_.get(promotionLayerVO, ['nightCount'], 1) === 1 && +_.get(promotionLayerVO, ['roomCount'], 1) === 1

        return <View className={styles.popup_box_content} style={isWeb ? {position: 'absolute'} : {}}>
            <Text
                className={classNames('bold', styles.popup_box_title)}>{_.get(promotionLayerVO, ['title'], '费用明细')}</Text>
            <TouchableWithoutFeedback onPress={() => {
                safeRun(onClose, {
                    showPopUp: '',
                    cardInfo: false
                })
            }}>
                <Image className={styles.popup_box_close} src={getImg('close')}/>
            </TouchableWithoutFeedback>
            <Text className={styles.popup_box_subTitle}>
                {`${_.get(promotionLayerVO, ['checkInDate'], '')}-${_.get(promotionLayerVO, ['checkOutDate'], '')}`}&nbsp;
                {+_.get(promotionLayerVO, ['roomCount']) >= 2 ? `${_.get(promotionLayerVO, ['roomCount'])}间` : ''}
                {`${_.get(promotionLayerVO, ['nightCount'], 1)}晚`}
            </Text>
            <IOScrollView showsVerticalScrollIndicator={false} bounces={false}
                          style={{maxHeight: pt(420), overflowY: 'auto'}}
                          overScrollMode="never">
                <View>
                    {
                        +_.get(promotionLayerVO, ['physicsRoomCount']) >= 2 ? <Text numberOfLines={2}
                                                                                    className={styles.popup_box_infoText}>{_.get(promotionLayerVO, ['physicsRoomName'])}｜{`${_.get(promotionLayerVO, ['physicsRoomCount'])}间`}</Text> : null
                    }
                    <View className={styles.popup_box_infoBox}>
                        <View className={styles.popup_box_infoItem}>
                            <Text
                                className={classNames('bold', styles.popup_box_infoTitle)}>{_.get(promotionLayerVO, ['marketingPriceTitle'], '房费')}</Text>
                            {
                                isOneDayOneRoom ?
                                    <PriceTag
                                        tipsStyle={{color: '#5E6880', fontSize: pt(12)}}
                                        symbolStyle={{color: '#1A1A1A', fontSize: pt(14)}}
                                        currencyStyle={{color: '#1A1A1A', fontSize: pt(14)}}
                                        integerPartStyle={{color: '#1A1A1A', fontSize: pt(20)}}
                                        fractionalPartStyle={{color: '#1A1A1A', fontSize: pt(14)}}
                                        tips={_.get(promotionLayerVO, ['totalMarketingPriceTips'])}
                                        symbol={_.get(promotionLayerVO, ['totalMarketingPrice', 'symbol'])}
                                        currency={_.get(promotionLayerVO, ['totalMarketingPrice', 'currency'])}
                                        integerPart={_.get(promotionLayerVO, ['totalMarketingPrice', 'integerPart'])}
                                        fractionalPart={_.get(promotionLayerVO, ['totalMarketingPrice', 'fractionalPart'])}
                                    /> : <PriceTag
                                        tips={_.get(promotionLayerVO, ['avgMarkingPriceTips'])}
                                        tipsStyle={{color: '#5E6880', fontSize: pt(12)}}
                                        symbol={_.get(promotionLayerVO, ['avgMarkingPrice', 'symbol'])}
                                        symbolStyle={{color: '#1A1A1A', fontSize: pt(14)}}
                                        currency={_.get(promotionLayerVO, ['avgMarkingPrice', 'currency'])}
                                        currencyStyle={{color: '#1A1A1A', fontSize: pt(14)}}
                                        integerPart={_.get(promotionLayerVO, ['avgMarkingPrice', 'integerPart'])}
                                        integerPartStyle={{color: '#1A1A1A', fontSize: pt(20)}}
                                        fractionalPart={_.get(promotionLayerVO, ['avgMarkingPrice', 'fractionalPart'])}
                                        fractionalPartStyle={{color: '#1A1A1A', fontSize: pt(14)}}
                                    />
                            }
                        </View>
                        {
                            isOneDayOneRoom ?
                                <View className={styles.popup_box_infoItemRight}/> :
                                <View className={styles.popup_box_infoItemRight}>
                                    <PriceTag
                                        tips={_.get(promotionLayerVO, ['totalMarketingPriceTips'])}
                                        symbol={_.get(promotionLayerVO, ['totalMarketingPrice', 'symbol'])}
                                        currency={_.get(promotionLayerVO, ['totalMarketingPrice', 'currency'])}
                                        integerPart={_.get(promotionLayerVO, ['totalMarketingPrice', 'integerPart'])}
                                        fractionalPart={_.get(promotionLayerVO, ['totalMarketingPrice', 'fractionalPart'])}
                                        tipsStyle={{color: '#5E6880', fontSize: pt(12)}}
                                        symbolStyle={{color: '#5E6880', fontSize: pt(13)}}
                                        currencyStyle={{color: '#5E6880', fontSize: pt(13)}}
                                        integerPartStyle={{color: '#5E6880', fontSize: pt(13)}}
                                        fractionalPartStyle={{color: '#5E6880', fontSize: pt(13)}}
                                    />
                                </View>
                        }
                    </View>

                    <View className={classNames(styles.popup_box_infoBox, styles.popup_box_color)}>
                        <View className={styles.popup_box_infoItem}>
                            <Text
                                className={classNames('bold', styles.popup_box_infoTitle)}>{_.get(promotionLayerVO, ['discountPriceTitle'], '优惠')}</Text>
                            {
                                isOneDayOneRoom ?
                                    <PriceTag
                                        tipsStyle={{color: '#5E6880', fontSize: pt(12)}}
                                        symbolStyle={{color: '#FF0400', fontSize: pt(14)}}
                                        currencyStyle={{color: '#FF0400', fontSize: pt(14)}}
                                        integerPartStyle={{color: '#FF0400', fontSize: pt(20)}}
                                        fractionalPartStyle={{color: '#FF0400', fontSize: pt(14)}}
                                        tips={_.get(promotionLayerVO, ['totalDiscountPriceTips'])}
                                        symbol={_.get(promotionLayerVO, ['totalDiscountPrice', 'symbol'])}
                                        currency={_.get(promotionLayerVO, ['totalDiscountPrice', 'currency'])}
                                        integerPart={_.get(promotionLayerVO, ['totalDiscountPrice', 'integerPart'])}
                                        fractionalPart={_.get(promotionLayerVO, ['totalDiscountPrice', 'fractionalPart'])}
                                    /> : <PriceTag
                                        tips={_.get(promotionLayerVO, ['avgDisCountPriceTips'])}
                                        tipsStyle={{color: '#5E6880', fontSize: pt(12)}}
                                        symbol={_.get(promotionLayerVO, ['avgDiscountPrice', 'symbol'])}
                                        symbolStyle={{color: '#FF0400', fontSize: pt(14)}}
                                        currency={_.get(promotionLayerVO, ['avgDiscountPrice', 'currency'])}
                                        currencyStyle={{color: '#FF0400', fontSize: pt(14)}}
                                        integerPart={_.get(promotionLayerVO, ['avgDiscountPrice', 'integerPart'])}
                                        integerPartStyle={{color: '#FF0400', fontSize: pt(20)}}
                                        fractionalPart={_.get(promotionLayerVO, ['avgDiscountPrice', 'fractionalPart'])}
                                        fractionalPartStyle={{color: '#FF0400', fontSize: pt(14)}}
                                    />
                            }
                        </View>
                        {
                            isOneDayOneRoom ?
                                <View className={styles.popup_box_infoItemRight}/> :
                                <View className={styles.popup_box_infoItemRight}>
                                    <PriceTag
                                        tips={_.get(promotionLayerVO, ['totalDiscountPriceTips'])}
                                        symbol={_.get(promotionLayerVO, ['totalDiscountPrice', 'symbol'])}
                                        currency={_.get(promotionLayerVO, ['totalDiscountPrice', 'currency'])}
                                        integerPart={_.get(promotionLayerVO, ['totalDiscountPrice', 'integerPart'])}
                                        fractionalPart={_.get(promotionLayerVO, ['totalDiscountPrice', 'fractionalPart'])}
                                        tipsStyle={{color: '#5E6880', fontSize: pt(12)}}
                                        symbolStyle={{color: '#5E6880', fontSize: pt(13)}}
                                        currencyStyle={{color: '#5E6880', fontSize: pt(13)}}
                                        integerPartStyle={{color: '#5E6880', fontSize: pt(13)}}
                                        fractionalPartStyle={{color: '#5E6880', fontSize: pt(13)}}
                                    />
                                </View>
                        }
                        <View className={styles.popup_box_line}/>
                        {
                            Array.isArray(_.get(promotionLayerVO, ['promotionDetailList'])) && _.get(promotionLayerVO, ['promotionDetailList']).map((item = {}, index) => {
                                const {labelTagListMap} = item
                                return <View className={styles.popup_box_infoListItem} key={index}>
                                    <View className={styles.popup_box_infoListItemTitle}>
                                        <View className={styles.popup_box_labelBoxOuter}>
                                            {
                                                Array.isArray(_.get(labelTagListMap, ['hotelPromotionLayerLine1'])) && _.get(labelTagListMap, ['hotelPromotionLayerLine1']).map((item = {}, index) => {
                                                    const {listShowName, borderColor, fontColor} = item
                                                    return <View key={index} className={styles.popup_box_labelBox} style={{borderColor: borderColor || '#FFBDBC'}}>
                                                        <Text className={styles.popup_box_labelText} style={{color: fontColor || '#FF0400'}}>{listShowName}</Text>
                                                    </View>
                                                })
                                            }
                                        </View>
                                        <PriceTag
                                            tips={_.get(item, ['priceTips'])}
                                            symbol={_.get(item, ['price', 'symbol'])}
                                            currency={_.get(item, ['price', 'currency'])}
                                            integerPart={_.get(item, ['price', 'integerPart'])}
                                            fractionalPart={_.get(item, ['price', 'fractionalPart'])}
                                            tipsStyle={{color: '#5E6880', fontSize: pt(12)}}
                                            symbolStyle={{color: '#5E6880', fontSize: pt(13)}}
                                            currencyStyle={{color: '#5E6880', fontSize: pt(13)}}
                                            integerPartStyle={{color: '#5E6880', fontSize: pt(13)}}
                                            fractionalPartStyle={{color: '#5E6880', fontSize: pt(13)}}
                                        />
                                    </View>
                                    {
                                        Array.isArray(_.get(labelTagListMap, ['hotelPromotionLayerLine2'])) && _.get(labelTagListMap, ['hotelPromotionLayerLine2']).map((item, index) => {
                                            const {listShowName} = item
                                            return <View key={index} className={styles.popup_box_descBox}>
                                                <Text className={styles.popup_box_descWord}
                                                      numberOfLines={1}>{listShowName}</Text>
                                            </View>
                                        })
                                    }
                                </View>
                            })
                        }
                    </View>

                    <View className={styles.popup_box_totalBox}>
                        {
                            _.get(promotionLayerVO, ['avgPriceFractional', 'integerPart']) || _.get(promotionLayerVO, ['avgPrice', 'integerPart']) ?
                                <View className={styles.popup_box_totalPriceBox}>
                                    <View style={{marginBottom: pt(2)}}>
                                        <PriceTag
                                            tips={_.get(promotionLayerVO, ['avgPriceFractionalTips'])}
                                            symbol={_.get(promotionLayerVO, ['avgPriceFractional', 'symbol'])}
                                            currency={_.get(promotionLayerVO, ['avgPriceFractional', 'currency'])}
                                            integerPart={_.get(promotionLayerVO, ['avgPriceFractional', 'integerPart'])}
                                            fractionalPart={_.get(promotionLayerVO, ['avgPriceFractional', 'fractionalPart'])}
                                            tipsStyle={{color: '#5E6880', fontSize: pt(12)}}
                                            symbolStyle={{color: '#5E6880', fontSize: pt(14)}}
                                            currencyStyle={{color: '#5E6880', fontSize: pt(14)}}
                                            integerPartStyle={{color: '#5E6880', fontSize: pt(14)}}
                                            fractionalPartStyle={{color: '#5E6880', fontSize: pt(14)}}
                                        />
                                    </View>
                                    {
                                        _.get(promotionLayerVO, ['avgPriceFractional', 'integerPart']) ?
                                            <View style={{marginBottom: isAndroid ? pt(2) : pt(4)}}><Text
                                                style={{color: '#5E6880'}}> ≈ </Text></View> : null
                                    }
                                    <PriceTag
                                        symbol={_.get(promotionLayerVO, ['avgPrice', 'symbol'])}
                                        currency={_.get(promotionLayerVO, ['avgPrice', 'currency'])}
                                        integerPart={_.get(promotionLayerVO, ['avgPrice', 'integerPart'])}
                                        fractionalPart={_.get(promotionLayerVO, ['avgPrice', 'fractionalPart'])}
                                        currencyStyle={{color: '#1A1A1A', fontSize: pt(14)}}
                                        integerPartStyle={{color: '#1A1A1A', fontSize: pt(24)}}
                                    />
                                </View> : null
                        }

                        {
                            _.get(promotionLayerVO, ['avgPriceTips']) ?
                                <View className={styles.popup_box_totalPriceBox}>
                                    <Text
                                        className={styles.popup_box_totalPriceTips}>{_.get(promotionLayerVO, ['avgPriceTips'])}</Text>
                                </View> : null
                        }
                        {
                            _.get(promotionLayerVO, ['avgPriceFractional', 'integerPart']) || _.get(promotionLayerVO, ['avgPrice', 'integerPart']) ?
                                <View className={styles.popup_box_totalPriceBox}>
                                    <PriceTag
                                        tips={_.get(promotionLayerVO, ['totalPriceTips'])}
                                        symbol={_.get(promotionLayerVO, ['totalPrice', 'symbol'])}
                                        currency={_.get(promotionLayerVO, ['totalPrice', 'currency'])}
                                        integerPart={_.get(promotionLayerVO, ['totalPrice', 'integerPart'])}
                                        fractionalPart={_.get(promotionLayerVO, ['totalPrice', 'fractionalPart'])}
                                        tipsStyle={{color: '#5E6880', fontSize: pt(12)}}
                                        symbolStyle={{color: '#5E6880', fontSize: pt(14)}}
                                        currencyStyle={{color: '#5E6880', fontSize: pt(14)}}
                                        integerPartStyle={{color: '#5E6880', fontSize: pt(14)}}
                                        fractionalPartStyle={{color: '#5E6880', fontSize: pt(14)}}
                                    />
                                </View> : <View className={styles.popup_box_totalPriceBox}><PriceTag
                                    tips={_.get(promotionLayerVO, ['totalPriceTips'])}
                                    symbol={_.get(promotionLayerVO, ['totalPrice', 'symbol'])}
                                    currency={_.get(promotionLayerVO, ['totalPrice', 'currency'])}
                                    integerPart={_.get(promotionLayerVO, ['totalPrice', 'integerPart'])}
                                    fractionalPart={_.get(promotionLayerVO, ['totalPrice', 'fractionalPart'])}
                                    currencyStyle={{color: '#1A1A1A', fontSize: pt(14)}}
                                    integerPartStyle={{color: '#1A1A1A', fontSize: pt(24)}}
                                /></View>
                        }
                    </View>
                </View>
            </IOScrollView>
            <TouchableWithoutFeedback onPress={() => {
                safeRun(toPage, {
                    ..._.get(cardInfo, 'cardProps', {}),
                    cardProps: _.get(cardInfo, 'cardProps', {}),
                    trigger: 'card',
                    type: 'popup',
                    index: _.get(cardInfo, 'index', -100)
                }, {
                    trigger: 'card',
                    type: 'popup'
                }, _.get(cardInfo, 'index', -100))
            }}>
                <View className={styles.popup_box_button}>
                    <Text className={styles.popup_box_button_text}>酒店详情</Text>
                </View>
            </TouchableWithoutFeedback>
        </View>
    },
    describe: (props) => {
        const {onClose, description} = props

        return <View className={styles.popup_box_content} style={isWeb ? {position: 'absolute'} : {}}>
            <Text
                className={classNames('bold', styles.popup_box_title)}>差标说明</Text>
            <TouchableWithoutFeedback onPress={() => {
                safeRun(onClose, {
                    showPopUp: '',
                    cardInfo: false
                })
            }}>
                <Image className={styles.popup_box_close} src={getImg('close')}/>
            </TouchableWithoutFeedback>
            <IOScrollView showsVerticalScrollIndicator={false} bounces={false}
                          style={{maxHeight: pt(420), overflowY: 'auto'}}
                          overScrollMode="never">
                <View className={styles.popup_box_view}>
                    <Markdown style={markDownStyle}>
                        {description}
                    </Markdown>
                </View>
            </IOScrollView>
        </View>
    }
}

const HotelCard = (props) => {
    const {showPopup, onClose} = props
    const _popRender = _.get(popRender, showPopup)

    return (
        <JDPopupWindow 
        className={styles.popup_box}
                       transparent={true}
                       show={_.isFunction(_popRender)}
                       speed={0.3}
                       height={600}  // 重要这里有坑，安卓用的动画，没有传入高度的话，手势滑动区域有问题，给一个比自己高的高度。可以解决问题。
                       onHide={(e) => {
                           let timer = setTimeout(() => {
                               safeRun(onClose, {
                                   showPopUp: '',
                                   cardInfo: false
                               })
                               clearTimeout(timer)
                           }, 200)
                       }}
        >
            {_.isFunction(_popRender) && _popRender(props)}
        </JDPopupWindow>
    )
}

export default EventCatch(HotelCard)

const markDownStyle = StyleSheet.create({
    body: {
        color: THEME_BASE.thirdaryColor
    },
    img: {
        width: '100%'
    },
    p: {
        marginBottom: 8,
        fontSize: 12,
        color: '#7c869c'
    }
})

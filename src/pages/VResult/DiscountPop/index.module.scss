@import "@/assets/theme.scss";

.popup_box {
    // height: 400px;
    -webkit-transform: translate3d(0,0,0);
}

.popup_box_mask {
    background-color: rgba(0, 0, 0, .7);
    width: 100%;
    height: 100%;
}

.popup_box_content {
    width: 100%;
    min-height: 420px;
    background-color: #fff;
    //position: absolute;
    bottom: 0;
    border-radius: 16px 16px 0 0;
    padding: 16px;
}

.popup_box_title {
    color: #1a1a1a;
    text-align: center;
    font-size: 18px;
    margin-top: 4px;
    font-weight: 600;
}

.popup_box_close {
    width: 20px;
    height: 20px;
    position: absolute;
    top: 20px;
    right: 20px;
}

.popup_box_subTitle {
    font-size: 12px;
    text-align: center;
    color: var(--secColor);
    margin-top: 4px;
    margin-bottom: 12px;
}

.popup_box_infoText {
    font-size: 12px;
    color: var(--secColor);
    margin-top: 8px;
    margin-bottom: 6px;
}

.popup_box_infoBox {
    width: 100%;
    background-color: #F5F7FA;
    margin-top: 12px;
    border-radius: 12px;
}

.popup_box_color {
    background-color: #FFF5F5;
    padding-bottom: 9px;
}

.popup_box_infoItem {
    padding-left: 12px;
    padding-right: 12px;
    padding-top: 12px;
    flex-direction: row;
    justify-content: space-between;
    vertical-align: center;
    align-items: center;
}

.popup_box_infoTitle {
    font-size: 16px;
    font-weight: 500;
}

.popup_box_line {
    height: 0.5px;
    width: calc(100% - 6px);
    background-color: #FFE4E1;
    margin-left: 3px;
    margin-right: 3px;
    margin-bottom: 9px;
}

.popup_box_infoListItem {
    padding: 7px 12px;
}

.popup_box_infoListItemTitle {
    flex-direction: row;
    justify-content: space-between;
    vertical-align: center;
    align-items: center;
}

.popup_box_labelBox {
    border-radius: 2px;
    border-color: #ffbdbc;
    border-width: 0.5px;
    height: 16px;
    padding-left: 4px;
    padding-right: 4px;
    margin-right: 8px;
}

.popup_box_labelText {
    font-size: 11px;
    color: #ff0400;
    line-height: 14px;
}

.popup_box_descBox {
    margin-top: 4px;
    max-width: 280px;
}

.popup_box_descWord {
    font-size: 14px;
    color: #5E6880;
}

.popup_box_totalPriceBox {
    flex-direction: row;
    justify-content: flex-end;
    margin-bottom: 6px;
    align-items: flex-end;
}

.popup_box_totalPriceTips {
    justify-content: flex-end;
    color: #a2abbf;
    font-size: 12px;
}

.popup_box_totalBox {
    margin-top: 16px;
}

.popup_box_button {
    height: 40px;
    background-color: #013b94;
    text-align: center;
    border-radius: 6px;
    margin-top: 16px;
    align-items: center;
    justify-content: space-between;
    vertical-align: center;
}

.popup_box_button_text {
    text-align: center;
    line-height: 40px;
    font-size: 16px;
    font-weight: 600;
    color: #fff;
}

.popup_box_infoItemRight {
    flex: 1;
    flex-direction: column;
    align-items: flex-end;
    padding-right: 12px;
    padding-bottom: 12px;
}

.popup_box_labelBoxOuter {
    flex-direction: row;
}

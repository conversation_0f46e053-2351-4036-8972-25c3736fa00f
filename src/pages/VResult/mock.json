{"resInfo": {"commonUserAction": {"pvid": "39564a7a71f147fe9ecb7067b69f817d", "search_fouraddrid": "1,1,106,0", "fouraddrid": "1,2800,55816,", "channel": "-100", "logid": "bb919bbb5bf840f1beb01f3d0a6d8018", "queryPattern": "location", "o2o_coordinates": "39.95931,116.29812", "keyword": "北京环球", "lbsSearchAction": {"touchstone_exp_ids": ["tsabtest|base64|SG90ZWxUcmlwU2VhcmNoXzYxNzQ3fFQxPTA|tsabtest"]}, "search_o2o_coordinates": "39.855181,116.681240"}, "tabType": "1", "curPage": 1, "hasNextPage": true, "countSummaryVO": {"hotelCount": 938, "scenicCount": 0, "resultCount": 938, "resultCountShowText": "查看结果"}, "filterPanelVOList": [{"filterPanelTextType": 0, "filterPanelName": "位置距离", "filterPanelCode": "location_distance", "showFilter": true}, {"filterPanelTextType": 0, "filterPanelName": "价格/星级", "filterPanelCode": "price_star", "showFilter": true, "filterList": [{"groupCode": "hotel_price", "groupName": "价格", "filterName": "价格", "filterType": "hotel_price_lowest", "multi": 1, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_price_lowest", "itemId": "0,150", "itemName": "¥150以下", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_price_lowest", "itemId": "150,300", "itemName": "¥150-300", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_price_lowest", "itemId": "300,450", "itemName": "¥300-450", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_price_lowest", "itemId": "450,600", "itemName": "¥450-600", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_price_lowest", "itemId": "600,1000", "itemName": "¥600-1000", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_price_lowest", "itemId": "1000,", "itemName": "¥1000以上", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "hotel_grade", "groupName": "星级", "filterName": "星级", "filterType": "hotel_grade", "multi": 0, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_grade", "itemId": "2", "itemName": "经济型", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_grade", "itemId": "3", "itemName": "3星/舒适", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_grade", "itemId": "4", "itemName": "4星/高档", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_grade", "itemId": "5", "itemName": "5星/豪华", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}]}, {"filterPanelTextType": 0, "filterPanelName": "筛选", "filterPanelCode": "hotel_filter", "showFilter": true, "filterList": [{"groupCode": "hotel_hot", "groupName": "热门筛选", "filterName": "热门筛选", "filterType": "hotel_hot", "multi": 0, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_jd_service", "itemId": "2", "itemName": "立即确认", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_promotion", "itemId": "1", "itemName": "限时特惠", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "651-0", "itemName": "免费停车", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_jd_service", "itemId": "1", "itemName": "免费取消", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "266", "itemName": "接机服务", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "36", "itemName": "如家", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "239", "itemName": "健身室", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "361", "itemName": "泳池", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "13843", "itemName": "秋果", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 9}, {"groupCode": "hotel_category", "groupName": "住宿类型", "filterName": "住宿类型", "filterType": "hotel_category", "multi": 0, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_category", "itemId": "1", "itemName": "酒店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_category", "itemId": "2", "itemName": "酒店公寓", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_category", "itemId": "3", "itemName": "客栈", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_category", "itemId": "4", "itemName": "别墅", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_category", "itemId": "5", "itemName": "农家乐", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_category", "itemId": "6", "itemName": "民宿", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_category", "itemId": "7", "itemName": "青旅", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_category", "itemId": "8", "itemName": "特色住宿", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "facility", "groupName": "设施", "filterName": "酒店设施", "filterType": "hotel_facility", "multi": 0, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_facility", "itemId": "651-0", "itemName": "免费停车", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "361", "itemName": "泳池", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "358", "itemName": "送机服务", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "266", "itemName": "接机服务", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "655", "itemName": "停车场", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "658", "itemName": "婴儿推车", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "239", "itemName": "健身室", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "359", "itemName": "洗衣房", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "244", "itemName": "穿梭机场班车", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "652", "itemName": "充电桩", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "224", "itemName": "棋牌室", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "216", "itemName": "洗衣服务", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "249", "itemName": "Spa", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "207", "itemName": "会议厅", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "261", "itemName": "行李寄存", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "287", "itemName": "吸烟区", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "269", "itemName": "电梯", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "292", "itemName": "餐厅", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "206", "itemName": "酒吧", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "285", "itemName": "24小时前台", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "208", "itemName": "商务中心", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "262", "itemName": "叫醒服务", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "294", "itemName": "代客泊车", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_facility", "itemId": "252", "itemName": "儿童乐园", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "facility", "groupName": "设施", "filterName": "开业/装修时间", "filterType": "hotel_decoration", "multi": 1, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_decoration", "itemId": "6", "itemName": "6个月以内", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_decoration", "itemId": "12", "itemName": "12个月以内", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_decoration", "itemId": "24", "itemName": "24个月以内", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "facility", "groupName": "设施", "filterName": "客房设施", "filterType": "room_facility", "multi": 0, "filterBehavior": "normal", "itemList": [{"filterType": "room_facility", "itemId": "462", "itemName": "浴缸", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "498", "itemName": "洗衣机", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "489", "itemName": "厨房", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "551", "itemName": "智能马桶", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "458", "itemName": "冰箱", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "478", "itemName": "电脑", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "432", "itemName": "棋牌桌", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "533", "itemName": "阳台", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "516", "itemName": "私人泳池", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "501", "itemName": "露台", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "586", "itemName": "私人卫生间", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "474", "itemName": "电视机", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "522", "itemName": "按摩浴缸", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "466", "itemName": "空调", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "531", "itemName": "花园景", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "567", "itemName": "庭院景", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "527", "itemName": "海景", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "483", "itemName": "咖啡机", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "453", "itemName": "电热水壶", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "549", "itemName": "客房WIFI", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "room_facility", "itemId": "534", "itemName": "地标景", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "hotel_brand", "groupName": "品牌", "filterName": "高端连锁", "filterType": "hotel_brand_high", "multi": 0, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_brand_high", "itemId": "13925", "itemName": "Joie de Vivre Hotels", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "52", "itemName": "铂尔曼", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "31", "itemName": "君悦", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "612", "itemName": "泛太平洋", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "125", "itemName": "希尔顿花园酒店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "583", "itemName": "星河湾", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "75", "itemName": "万丽", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "25", "itemName": "万豪", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "127", "itemName": "希尔顿惠庭酒店(Home2 Suites by <PERSON>)", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "737", "itemName": "京伦饭店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "585", "itemName": "中油阳光", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "577", "itemName": "维景国际", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "658", "itemName": "盛捷", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "19", "itemName": "智选假日", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13609", "itemName": "宝格丽", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13965", "itemName": "柏纳酒店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "21", "itemName": "华尔道夫", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "733", "itemName": "凯悦臻选(the Unbound Collection by Hyatt)", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "581", "itemName": "半岛", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13967", "itemName": "假日度假酒店(Holiday Inn Resort)", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13815", "itemName": "丽筠(<PERSON><PERSON><PERSON>)", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13705", "itemName": "瑞吉(St Regis)", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "65", "itemName": "希尔顿欢朋酒店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "319", "itemName": "蔚徕", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "604", "itemName": "华邑", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "727", "itemName": "花筑", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13707", "itemName": "喜来登(Sheraton)", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "677", "itemName": "中州国际", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "67", "itemName": "逸林希尔顿", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "88", "itemName": "白天鹅", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "46", "itemName": "安缦", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "648", "itemName": "凯悦嘉轩", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "17", "itemName": "洲际", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "627", "itemName": "开元名都", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "38", "itemName": "康莱德", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13751", "itemName": "丽晶(Regent)", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13709", "itemName": "源宿（Element）", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "69", "itemName": "亚朵", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13680", "itemName": "千宿", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "40", "itemName": "温德姆", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13703", "itemName": "福朋(Four Points)", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "63", "itemName": "万达文华", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "623", "itemName": "诺金", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13", "itemName": "香格里拉", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "107", "itemName": "华美达", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "86", "itemName": "万怡", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "565", "itemName": "柏悦", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "57", "itemName": "桔子", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "7", "itemName": "丽思卡尔顿", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "740", "itemName": "今旅", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13770", "itemName": "东隅", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "465", "itemName": "丽雅", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "80", "itemName": "金陵", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "161", "itemName": "山水S", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "640", "itemName": "瑞士", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "103", "itemName": "假日", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "82", "itemName": "山水时尚", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "582", "itemName": "世纪金源", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "32", "itemName": "凯悦", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "24", "itemName": "JW万豪", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "736", "itemName": "建国饭店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13745", "itemName": "丽呈", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "605", "itemName": "花间堂", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "584", "itemName": "新世界", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "76", "itemName": "乌镇旅业", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "199", "itemName": "品质", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "686", "itemName": "华天", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "55", "itemName": "海航", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "26", "itemName": "四季", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "636", "itemName": "钓鱼台", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "128", "itemName": "旅居", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "628", "itemName": "唐拉雅秀", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "586", "itemName": "格兰云天", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "578", "itemName": "君澜度假酒店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13639", "itemName": "费尔蒙(Fairmont)", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "122", "itemName": "舒适", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13843", "itemName": "秋果", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "72", "itemName": "马哥孛罗", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "653", "itemName": "诺富特", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "755", "itemName": "CitiGO", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "14", "itemName": "皇冠假日", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13706", "itemName": "威斯汀(Westin)", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "655", "itemName": "美爵", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "118", "itemName": "万达嘉华", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "576", "itemName": "雅诗阁", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13708", "itemName": "雅乐轩(Aloft)", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "68", "itemName": "锦江", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "89", "itemName": "瑰丽", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "39", "itemName": "文华东方", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "18", "itemName": "戴斯", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "751", "itemName": "万枫", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13658", "itemName": "时光漫步", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "243", "itemName": "地中海俱乐部", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "10", "itemName": "希尔顿", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "112", "itemName": "丽亭(Park Plaza)", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "91", "itemName": "斯维登", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13854", "itemName": "建发酒店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "570", "itemName": "嘉里", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "33", "itemName": "凯宾斯基", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "622", "itemName": "谭阁美", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13725", "itemName": "璞隐", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "85", "itemName": "维景", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "35", "itemName": "索菲特", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "747", "itemName": "锦华", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "616", "itemName": "康德思", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13871", "itemName": "蓝海御华", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "87", "itemName": "万豪行政公寓", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "587", "itemName": "漫心", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "668", "itemName": "尊茂", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13923", "itemName": "遨途", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "29", "itemName": "和颐", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "13771", "itemName": "居舍系列", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_high", "itemId": "110", "itemName": "凯莱", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "hotel_brand", "groupName": "品牌", "filterName": "中端连锁", "filterType": "hotel_brand_mid", "multi": 0, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_brand_mid", "itemId": "13691", "itemName": "汉庭优佳", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "1", "itemName": "汉庭", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "13722", "itemName": "格菲", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "82", "itemName": "山水时尚", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "53", "itemName": "宜必思尚品", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "13723", "itemName": "格雅", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "4", "itemName": "速8", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "737", "itemName": "京伦饭店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "585", "itemName": "中油阳光", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "13768", "itemName": "花美时", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "128", "itemName": "旅居", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "13942", "itemName": "驿捷", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "78", "itemName": "富驿时尚", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "13943", "itemName": "驿雲", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "19", "itemName": "智选假日", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "20", "itemName": "飘HOME", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "71", "itemName": "如家商旅", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "13741", "itemName": "兰欧", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "13813", "itemName": "隐沫", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "13843", "itemName": "秋果", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "550", "itemName": "驿居", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "93", "itemName": "格林东方", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "174", "itemName": "华驿精选", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "13734", "itemName": "途客中国", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "727", "itemName": "花筑", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "147", "itemName": "智尚", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "16", "itemName": "怡莱", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "698", "itemName": "怡程", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "68", "itemName": "锦江", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "13882", "itemName": "雅悦酒店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "13730", "itemName": "H酒店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "162", "itemName": "宜尚", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "69", "itemName": "亚朵", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "13658", "itemName": "时光漫步", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "120", "itemName": "星程", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "13680", "itemName": "千宿", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "91", "itemName": "斯维登", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "11", "itemName": "全季", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "541", "itemName": "睿柏", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "92", "itemName": "欣燕都连锁", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "12", "itemName": "格林豪泰", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "724", "itemName": "开元名庭", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "542", "itemName": "素柏", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "187", "itemName": "欢墅", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "5", "itemName": "宜必思", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "543", "itemName": "都市花园", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "13748", "itemName": "尚客优品", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "36", "itemName": "如家", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "587", "itemName": "漫心", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "138", "itemName": "北方朗悦", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "181", "itemName": "柏曼", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "29", "itemName": "和颐", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_mid", "itemId": "8", "itemName": "如家精选", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "hotel_brand", "groupName": "品牌", "filterName": "快捷连锁", "filterType": "hotel_brand_low", "multi": 0, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_brand_low", "itemId": "13721", "itemName": "格美", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "102", "itemName": "拜登", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "531", "itemName": "欣燕都", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "489", "itemName": "华利佳合", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "2", "itemName": "7天", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "358", "itemName": "南苑e家连锁", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "13723", "itemName": "格雅", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "460", "itemName": "中安之家", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "554", "itemName": "云上四季", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "533", "itemName": "99优选", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "4", "itemName": "速8", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "504", "itemName": "嘉利华连锁", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "483", "itemName": "便宜居连锁", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "13942", "itemName": "驿捷", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "13667", "itemName": "贝壳", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "13892", "itemName": "161酒店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "13740", "itemName": "骏怡精选", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "354", "itemName": "城市客栈", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "456", "itemName": "驿家365", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "13965", "itemName": "柏纳酒店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "71", "itemName": "如家商旅", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "427", "itemName": "和家", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "550", "itemName": "驿居", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "13886", "itemName": "布丁严选", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "13734", "itemName": "途客中国", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "94", "itemName": "尚客优", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "552", "itemName": "华驿酒店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "319", "itemName": "蔚徕", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "23", "itemName": "布丁", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "727", "itemName": "花筑", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "373", "itemName": "A家", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "13882", "itemName": "雅悦酒店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "721", "itemName": "华驿至尊", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "519", "itemName": "派酒店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "11", "itemName": "全季", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "367", "itemName": "都市118", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "92", "itemName": "欣燕都连锁", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "746", "itemName": "YUNIK", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "5", "itemName": "宜必思", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "361", "itemName": "锐思特", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "57", "itemName": "桔子", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "36", "itemName": "如家", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "138", "itemName": "北方朗悦", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "13691", "itemName": "汉庭优佳", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "538", "itemName": "岭南佳园", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "30", "itemName": "格盟", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "9", "itemName": "海友", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "1", "itemName": "汉庭", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "540", "itemName": "派柏", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "3", "itemName": "莫泰", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "97", "itemName": "骏怡", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "555", "itemName": "云栖", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "78", "itemName": "富驿时尚", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "557", "itemName": "青皮树", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "13943", "itemName": "驿雲", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "20", "itemName": "飘HOME", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "499", "itemName": "小米连锁", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "101", "itemName": "99旅馆", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "13843", "itemName": "秋果", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "174", "itemName": "华驿精选", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "551", "itemName": "华驿快捷", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "13887", "itemName": "布丁精选", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "13858", "itemName": "青季", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "451", "itemName": "禧龙", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "13960", "itemName": "怡莱精品", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "66", "itemName": "维也纳", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "45", "itemName": "城市便捷", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "147", "itemName": "智尚", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "16", "itemName": "怡莱", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "39", "itemName": "文华东方", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "162", "itemName": "宜尚", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "120", "itemName": "星程", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "91", "itemName": "斯维登", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "13956", "itemName": "99新标", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "41", "itemName": "易佰", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "541", "itemName": "睿柏", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "12", "itemName": "格林豪泰", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "85", "itemName": "维景", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "187", "itemName": "欢墅", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "13748", "itemName": "尚客优品", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "79", "itemName": "IU酒店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "435", "itemName": "城市之家", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "181", "itemName": "柏曼", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "29", "itemName": "和颐", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_brand_low", "itemId": "487", "itemName": "鸿炜亿家连锁", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "hotel_bed_meal", "groupName": "床型餐食", "filterName": "床型", "filterType": "hotel_bed_type", "multi": 1, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_bed_type", "itemId": "2,12", "itemName": "大床房", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_bed_type", "itemId": "1,11", "itemName": "双床房", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_bed_type", "itemId": "6", "itemName": "单床房", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_bed_type", "itemId": "13", "itemName": "特大床房", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "hotel_bed_meal", "groupName": "床型餐食", "filterName": "卧室数", "filterType": "hotel_room_count", "multi": 1, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_room_count", "itemId": "1,1", "itemName": "单间/单卧室", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_room_count", "itemId": "2,2", "itemName": "2间卧室", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_room_count", "itemId": "3,", "itemName": "3+间卧室", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "hotel_bed_meal", "groupName": "床型餐食", "filterName": "餐食", "filterType": "hotel_meal", "multi": 1, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_meal", "itemId": "1", "itemName": "含早餐", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_meal", "itemId": "2", "itemName": "单份早餐", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_meal", "itemId": "3", "itemName": "双份早餐", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_meal", "itemId": "11", "itemName": "含晚餐", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_meal", "itemId": "12", "itemName": "单份晚餐", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_meal", "itemId": "13", "itemName": "双份晚餐", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "hotel_theme_tag", "groupName": "主题特色", "filterName": "主题特色", "filterType": "hotel_theme_tag", "multi": 0, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_theme_tag", "itemId": "87", "itemName": "野营房/简易旅馆", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "3", "itemName": "休闲度假", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "1", "itemName": "亲子酒店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "117", "itemName": "火车站周边", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "2", "itemName": "浪漫情侣", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "5", "itemName": "精品酒店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "47", "itemName": "乡村", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "16", "itemName": "温泉酒店", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "4", "itemName": "商务出行", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "143", "itemName": "网红美宿", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "102", "itemName": "含拼车接机", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "9", "itemName": "青旅", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "25", "itemName": "纳西风情", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "127", "itemName": "<PERSON>推荐", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "124", "itemName": "出海捕鱼", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "12", "itemName": "威士(VISA)", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "144", "itemName": "艺术品藏", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "92", "itemName": "Capsule Hotel", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "129", "itemName": "禅意满满", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "113", "itemName": "洱海美景", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "122", "itemName": "可供氧房型", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "121", "itemName": "可供氧房", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "157", "itemName": "窗外好景", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_theme_tag", "itemId": "118", "itemName": "大学周边", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "hotel_area", "groupName": "房间面积", "filterName": "房间面积", "filterType": "hotel_area", "multi": 1, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_area", "itemId": "15,", "itemName": "≥15㎡", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_area", "itemId": "30,", "itemName": "≥30㎡", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_area", "itemId": "60,", "itemName": "≥60㎡", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "hotel_dianping", "groupName": "点评", "filterName": "评分", "filterType": "hotel_score", "multi": 1, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_score", "itemId": "4.8,", "itemName": "4.8分以上", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_score", "itemId": "4.5,", "itemName": "4.5分以上", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_score", "itemId": "4.0,", "itemName": "4.0分以上", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "hotel_dianping", "groupName": "点评", "filterName": "点评数", "filterType": "hotel_comment_count", "multi": 1, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_comment_count", "itemId": "500,", "itemName": "500条以上", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_comment_count", "itemId": "200,", "itemName": "200条以上", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_comment_count", "itemId": "100,", "itemName": "100条以上", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "hotel_promotion", "groupName": "促销/权益", "filterName": "促销/权益", "filterType": "hotel_promotion", "multi": 0, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_promotion", "itemId": "1", "itemName": "限时特惠", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_promotion", "itemId": "2", "itemName": "首单优惠", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "service", "groupName": "政策服务", "filterName": "京东服务", "filterType": "hotel_jd_service", "multi": 0, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_jd_service", "itemId": "1", "itemName": "免费取消", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_jd_service", "itemId": "2", "itemName": "立即确认", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "service", "groupName": "政策服务", "filterName": "适用人群", "filterType": "hotel_customer_type", "multi": 1, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_customer_type", "itemId": "1", "itemName": "中国大陆宾客", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_customer_type", "itemId": "2", "itemName": "港澳宾客", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_customer_type", "itemId": "3", "itemName": "台湾宾客", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_customer_type", "itemId": "4", "itemName": "中国宾客", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_customer_type", "itemId": "5", "itemName": "海外宾客", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}]}], "outsideFilterPanelVOList": [{"filterPanelTextType": 0, "filterPanelName": "筛选", "filterPanelCode": "hotel_filter", "showFilter": true, "filterList": [{"groupCode": "hotel_bed_meal", "groupName": "床型餐食", "filterName": "床型", "filterType": "hotel_bed_type", "multi": 1, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_bed_type", "itemId": "2,12", "itemName": "大床房", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_bed_type", "itemId": "1,11", "itemName": "双床房", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_bed_type", "itemId": "6", "itemName": "单床房", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_bed_type", "itemId": "13", "itemName": "特大床房", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "service", "groupName": "政策服务", "filterName": "京东服务", "filterType": "hotel_jd_service", "multi": 1, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_jd_service", "itemId": "2", "itemName": "立即确认", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "hotel_promotion", "groupName": "促销/权益", "filterName": "权益/促销", "filterType": "hotel_promotion", "multi": 1, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_promotion", "itemId": "1", "itemName": "限时特惠", "itemType": "button", "group": false, "style": {"type": "icon", "listImage": "https://img10.360buyimg.com/imagetools/jfs/t1/130490/17/49085/5942/6720eaacF60b662c8/c621461413642d82.png", "activeBorderColor": "#FF0400"}, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "facility", "groupName": "设施", "filterName": "酒店设施", "filterType": "hotel_facility", "multi": 1, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_facility", "itemId": "651-0", "itemName": "免费停车", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "service", "groupName": "政策服务", "filterName": "京东服务", "filterType": "hotel_jd_service", "multi": 0, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_jd_service", "itemId": "1", "itemName": "免费取消", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "facility", "groupName": "设施", "filterName": "酒店设施", "filterType": "hotel_facility", "multi": 0, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_facility", "itemId": "266", "itemName": "接机服务", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "hotel_brand", "groupName": "品牌", "filterName": "中端连锁", "filterType": "hotel_brand_mid", "multi": 0, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_brand_mid", "itemId": "36", "itemName": "如家", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "facility", "groupName": "设施", "filterName": "酒店设施", "filterType": "hotel_facility", "multi": 0, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_facility", "itemId": "239", "itemName": "健身室", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "facility", "groupName": "设施", "filterName": "酒店设施", "filterType": "hotel_facility", "multi": 0, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_facility", "itemId": "361", "itemName": "泳池", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}, {"groupCode": "hotel_brand", "groupName": "品牌", "filterName": "中端连锁", "filterType": "hotel_brand_mid", "multi": 0, "filterBehavior": "normal", "itemList": [{"filterType": "hotel_brand_mid", "itemId": "13843", "itemName": "秋果", "itemType": "button", "group": false, "itemBehavior": "normal"}], "exposeItemCount": 6}]}], "naturalCardVOList": [{"cardType": "1", "hotelCardVO": {"id": "3042023", "name": "环球影城大酒店", "picUrl": "https://img30.360buyimg.com/hotel/jfs/t1/184981/37/34993/103106/641e6088F42055fb7/a396c94e8cc18eae.jpg", "price": "1655", "originPrice": "1672", "discountPrice": "17", "beltInfoVO": {"beltCode": "1", "backImage": "https://img14.360buyimg.com/imagetools/jfs/t1/106473/18/53877/11018/672b2645Fd81bf3c4/6d6de5c28ea28cbc.png"}, "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=3042023&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "公用区wifi"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "无障碍客房"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "健身室"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "儿童餐"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "熨斗/挂烫机"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区203米"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "4.5分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "非常棒"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "158点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "豪华型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "3042022", "name": "诺金度假酒店–北京环球度假区", "picUrl": "https://img14.360buyimg.com/hotel/jfs/t1/218255/6/29936/39166/64886257F2273424e/ebfde49b86f8e3bf.jpg", "price": "1846", "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=3042022&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "净水机"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "叫车服务"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "茶室"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "儿童餐"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "按摩室"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区470米"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "4.2分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "很棒"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "146点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "豪华型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "3279030", "name": "北京通州环球度假区丽柏酒店", "picUrl": "https://img11.360buyimg.com/hotel/jfs/t1/175041/27/22999/135277/61b32fb5E7c33dd7b/f2d26856104b0221.jpg", "price": "429", "originPrice": "434", "discountPrice": "5", "beltInfoVO": {"beltCode": "1", "backImage": "https://img14.360buyimg.com/imagetools/jfs/t1/106473/18/53877/11018/672b2645Fd81bf3c4/6d6de5c28ea28cbc.png"}, "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=3279030&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "免费停车"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "公用区wifi"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "充电车位"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "无障碍通道"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "健身室"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区1.6公里"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "5.0分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "超预期"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "152点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "高档型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "3278708", "name": "建国铂萃酒店(北京环球度假区店)", "picUrl": "https://img13.360buyimg.com/hotel/jfs/t1/219288/36/5827/82345/61a1b263E69f83cc1/91b1b715be6a2628.jpg", "price": "403", "originPrice": "408", "discountPrice": "5", "beltInfoVO": {"beltCode": "1", "backImage": "https://img14.360buyimg.com/imagetools/jfs/t1/106473/18/53877/11018/672b2645Fd81bf3c4/6d6de5c28ea28cbc.png"}, "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=3278708&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "免费停车"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "公用区wifi"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "停车场"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "健身室"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "儿童餐"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区1.6公里"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "5.0分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "超预期"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "127点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "高档型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "3393328", "name": "全季酒店(北京通州环球度假区店)", "picUrl": "https://img20.360buyimg.com/hotel/jfs/t1/162540/27/25041/94403/62b43e5fE3c9da7fd/c18c53cc9709be4f.jpg", "price": "532", "originPrice": "538", "discountPrice": "6", "beltInfoVO": {"beltCode": "1", "backImage": "https://img14.360buyimg.com/imagetools/jfs/t1/106473/18/53877/11018/672b2645Fd81bf3c4/6d6de5c28ea28cbc.png"}, "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=3393328&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "免费停车"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "公用区wifi"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "停车场"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "儿童拖鞋"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "熨斗/挂烫机"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区1公里"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "5.0分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "超预期"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "35点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "舒适型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "1991797", "name": "喆啡酒店(北京通州环球度假区店)", "picUrl": "https://img12.360buyimg.com/hotel/jfs/t1/139140/31/12051/101450/5f967d4cEaf47834c/19516cdb302a337d.jpg", "price": "266", "originPrice": "269", "discountPrice": "3", "beltInfoVO": {"beltCode": "1", "backImage": "https://img14.360buyimg.com/imagetools/jfs/t1/106473/18/53877/11018/672b2645Fd81bf3c4/6d6de5c28ea28cbc.png"}, "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=1991797&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "免费停车"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "中文指示"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "停车场"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "干衣机"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "咖啡厅"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区2.4公里"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "5.0分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "超预期"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "314点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "舒适型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "3282281", "name": "麗枫酒店(北京通州环球度假区北门店)", "picUrl": "https://img14.360buyimg.com/hotel/jfs/t1/210523/34/11034/115430/61a6f414Eb0d08fbe/7f180e0195a917d8.jpg", "price": "295", "originPrice": "298", "discountPrice": "3", "beltInfoVO": {"beltCode": "1", "backImage": "https://img14.360buyimg.com/imagetools/jfs/t1/106473/18/53877/11018/672b2645Fd81bf3c4/6d6de5c28ea28cbc.png"}, "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=3282281&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "免费停车"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "净水机"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "班车服务"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "儿童拖鞋"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "熨斗/挂烫机"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区1.9公里"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "5.0分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "超预期"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "108点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "舒适型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "24175", "name": "嘉悦文景酒店(北京环球度假区万盛东地铁站店)", "picUrl": "https://img10.360buyimg.com/hotel/jfs/t1/197708/14/30068/93348/63b39a68F5e9bdf74/2050a970a68922b5.jpg", "price": "203", "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=24175&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "免费停车"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "公用区wifi"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "充电车位"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "熨斗/挂烫机"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "大堂吧"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区4.8公里"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "4.9分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "非常棒"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "92点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "经济型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "28079", "name": "海友酒店（北京通州环球度假区南门店）", "picUrl": "https://img13.360buyimg.com/hotel/jfs/t1/178399/37/36284/36599/64d6358aFa92e99b1/e7a3702357e95478.jpg", "price": "231", "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=28079&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "免费停车"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "公用区wifi"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "停车场"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "儿童拖鞋"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "熨斗/挂烫机"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区4.8公里"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "4.5分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "非常棒"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "158点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "经济型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "20819", "name": "白玉兰酒店(北京通州环球度假区地铁站店)", "picUrl": "https://img13.360buyimg.com/hotel/jfs/t1/152431/11/12830/71200/5fed29d1Ef098ffb0/7a42473eb8592278.jpg", "price": "211", "originPrice": "214", "discountPrice": "3", "beltInfoVO": {"beltCode": "1", "backImage": "https://img14.360buyimg.com/imagetools/jfs/t1/106473/18/53877/11018/672b2645Fd81bf3c4/6d6de5c28ea28cbc.png"}, "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=20819&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "免费停车"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "净水机"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "停车场"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "熨斗/挂烫机"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区2.6公里"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "5.0分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "超预期"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "151点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "经济型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "3382568", "name": "漫四季酒店(北京通州度假区北街店)", "picUrl": "https://img12.360buyimg.com/hotel/jfs/t1/98881/12/29185/94563/628c8e7bEc2b6f63a/a83e03fb99925a12.jpg", "price": "501", "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=3382568&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "公用区wifi"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "停车场"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "无障碍通道"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "儿童拖鞋"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "餐厅"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区1.2公里"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "5.0分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "超预期"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "9点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "经济型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "15348", "name": "7天连锁酒店(北京通州环球临河里地铁站店)", "picUrl": "https://img14.360buyimg.com/hotel/jfs/t1/220002/27/34255/64765/64f6ac90F2cc90ddb/442f88bfd8db4680.jpg", "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=15348&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "公用区wifi"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "洗衣房"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "送餐服务"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区2.6公里"}], "hotelComment": [{"styleCode": "Summary", "trackId": "hotelComment", "listShowName": "通风很好，酒店很不错", "template": "%s"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "4.6分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "非常棒"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "246点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "经济型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "12727", "name": "环仪酒店（北京万盛东街地铁站店）", "picUrl": "https://img11.360buyimg.com/hotel/jfs/t1/247661/20/6893/52589/660e26c3Fe81ef793/da13acaf1e95f90f.jpg", "price": "288", "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=12727&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "免费停车"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "净水机"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "停车场"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "无障碍客房"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "按摩室"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区2公里"}], "hotelComment": [{"styleCode": "Summary", "trackId": "hotelComment", "listShowName": "卫生还不错，服务态度很好", "template": "%s"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "4.7分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "非常棒"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "68点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "经济型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "2863610", "name": "北京通州梨园地铁站亚朵酒店", "picUrl": "https://img20.360buyimg.com/hotel/jfs/t1/153905/17/6709/61366/5fb8e75dEde9aaef2/ee7892fa442d5292.jpg", "price": "476", "originPrice": "481", "discountPrice": "5", "beltInfoVO": {"beltCode": "1", "backImage": "https://img14.360buyimg.com/imagetools/jfs/t1/106473/18/53877/11018/672b2645Fd81bf3c4/6d6de5c28ea28cbc.png"}, "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=2863610&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "免费停车"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "公用区wifi"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "叫车服务"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "健身室"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "儿童拖鞋"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区6.8公里"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "4.9分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "非常棒"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "275点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "高档型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "17506", "name": "麗枫酒店(北京通州环球果园地铁站店)", "picUrl": "https://img30.360buyimg.com/hotel/jfs/t1/225303/16/19535/80358/665801ffF6eb6a084/cb181865e36c4189.jpg", "price": "251", "originPrice": "254", "discountPrice": "3", "beltInfoVO": {"beltCode": "1", "backImage": "https://img14.360buyimg.com/imagetools/jfs/t1/106473/18/53877/11018/672b2645Fd81bf3c4/6d6de5c28ea28cbc.png"}, "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=17506&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "免费停车"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "公用区wifi"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "停车场"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "楼梯扶手"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "儿童拖鞋"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区4.1公里"}], "hotelComment": [{"styleCode": "Summary", "trackId": "hotelComment", "listShowName": "环境还挺好，酒店挺新", "template": "%s"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "5.0分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "超预期"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "387点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "舒适型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "2946359", "name": "潮漫龙合酒店（北京环球度假区临河里地铁站店）", "picUrl": "https://img30.360buyimg.com/hotel/jfs/t1/166320/30/870/49419/5ff2c41eEe039c525/eb142250cc186270.jpg", "price": "220", "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=2946359&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "免费停车"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "公用区wifi"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "停车场"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "无障碍客房"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "健身室"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区2.6公里"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "4.7分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "非常棒"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "112点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "舒适型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "1908466", "name": "潮漫酒店(北京通州环球度假区临河里地铁站店)", "picUrl": "https://img10.360buyimg.com/hotel/jfs/t1/168506/10/33252/99057/64772912F1e1147db/7ee0f91707010267.jpg", "price": "277", "originPrice": "280", "discountPrice": "3", "beltInfoVO": {"beltCode": "1", "backImage": "https://img14.360buyimg.com/imagetools/jfs/t1/106473/18/53877/11018/672b2645Fd81bf3c4/6d6de5c28ea28cbc.png"}, "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=1908466&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "免费停车"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "净水机"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "停车场"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "儿童餐"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "健身课程"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区2.7公里"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "4.6分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "非常棒"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "142点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "舒适型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "3282814", "name": "云上假日酒店（北京通州区环球影城店）", "picUrl": "https://img11.360buyimg.com/hotel/jfs/t1/244897/7/25429/50581/673ed2d0Fb4ef63f7/0a0e10fbca0aea4b.jpg", "price": "359", "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=3282814&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "免费停车"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "公用区wifi"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "停车场"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "无障碍客房"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "干衣机"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区1.7公里"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "5.0分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "超预期"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "9点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "高档型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "3541861", "name": "R ROYALSS HOTEL（北京通州环球度假区店）", "picUrl": "https://img30.360buyimg.com/hotel/jfs/t1/190562/1/38221/95181/6524d86eF964d598d/19a1ce98b35b13c3.jpg", "price": "550", "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=3541861&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "净水机"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "停车场"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "儿童餐"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "熨斗/挂烫机"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "咖啡厅"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区1.8公里"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "5.0分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "超预期"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "10点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "舒适型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "14232", "name": "全季酒店(北京通州环球运河大街店)", "picUrl": "https://img11.360buyimg.com/hotel/jfs/t1/91955/15/16159/94754/5e79e299Ed87fb44d/a968588cbaae66f4.jpg", "price": "344", "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=14232&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "免费停车"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "公用区wifi"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "叫车服务"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "儿童书籍/影音"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "熨斗/挂烫机"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区4公里"}], "hotelComment": [{"styleCode": "Summary", "trackId": "hotelComment", "listShowName": "卫生条件很好，位置俱佳", "template": "%s"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "4.7分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "非常棒"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "385点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "舒适型"}]}}}], "recommendCardVOList": [{"cardType": "1", "hotelCardVO": {"id": "2946359", "name": "潮漫龙合酒店（北京环球度假区临河里地铁站店）", "picUrl": "https://img30.360buyimg.com/hotel/jfs/t1/166320/30/870/49419/5ff2c41eEe039c525/eb142250cc186270.jpg", "price": "220", "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=2946359&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "免费停车"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "公用区wifi"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "停车场"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "无障碍客房"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "健身室"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区2.6公里"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "4.7分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "非常棒"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "112点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "舒适型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "1908466", "name": "潮漫酒店(北京通州环球度假区临河里地铁站店)", "picUrl": "https://img10.360buyimg.com/hotel/jfs/t1/168506/10/33252/99057/64772912F1e1147db/7ee0f91707010267.jpg", "price": "277", "originPrice": "280", "discountPrice": "3", "beltInfoVO": {"beltCode": "1", "backImage": "https://img14.360buyimg.com/imagetools/jfs/t1/106473/18/53877/11018/672b2645Fd81bf3c4/6d6de5c28ea28cbc.png"}, "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=1908466&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "免费停车"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "净水机"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "停车场"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "儿童餐"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "健身课程"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区2.7公里"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "4.6分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "非常棒"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "142点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "舒适型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "3282814", "name": "云上假日酒店（北京通州区环球影城店）", "picUrl": "https://img11.360buyimg.com/hotel/jfs/t1/244897/7/25429/50581/673ed2d0Fb4ef63f7/0a0e10fbca0aea4b.jpg", "price": "359", "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=3282814&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "免费停车"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "公用区wifi"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "停车场"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "无障碍客房"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "干衣机"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区1.7公里"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "5.0分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "超预期"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "9点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "高档型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "3541861", "name": "R ROYALSS HOTEL（北京通州环球度假区店）", "picUrl": "https://img30.360buyimg.com/hotel/jfs/t1/190562/1/38221/95181/6524d86eF964d598d/19a1ce98b35b13c3.jpg", "price": "550", "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=3541861&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "净水机"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "停车场"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "儿童餐"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "熨斗/挂烫机"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "咖啡厅"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区1.8公里"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "5.0分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "超预期"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "10点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "舒适型"}]}}}, {"cardType": "1", "hotelCardVO": {"id": "14232", "name": "全季酒店(北京通州环球运河大街店)", "picUrl": "https://img11.360buyimg.com/hotel/jfs/t1/91955/15/16159/94754/5e79e299Ed87fb44d/a968588cbaae66f4.jpg", "price": "344", "jumpUrl": "https://hotel.m.jd.com?routerName=detailNew&hotelId=14232&cityId=36&cityName=%E5%8C%97%E4%BA%AC&checkInDate=2024-12-05&checkOutDate=2024-12-06&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detailNew&roomNum=1&adultNum=1", "promotionTagListMap": {"hotelPromotion": [{"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "免费停车"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "公用区wifi"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "叫车服务"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "儿童书籍/影音"}, {"styleCode": "TagList", "trackId": "hotelFacilities", "listShowName": "熨斗/挂烫机"}], "hotelLocationDistance": [{"styleCode": "Text", "trackId": "hotelLocationDistance", "listShowName": "距北京环球度假区4公里"}], "hotelComment": [{"styleCode": "Summary", "trackId": "hotelComment", "listShowName": "卫生条件很好，位置俱佳", "template": "%s"}], "hotelScore": [{"styleCode": "Score", "trackId": "hotelScore", "listShowName": "4.7分"}, {"styleCode": "ScoreMap", "trackId": "hotelScoreDesc", "listShowName": "非常棒"}, {"styleCode": "Text", "trackId": "hotelCommentCount", "listShowName": "385点评", "template": "%s点评"}], "hotelTitleAfter": [{"styleCode": "Tag", "trackId": "hotelGrade", "listShowName": "舒适型"}]}}}], "mddInfoList": [{"type": "1", "showName": "北京", "searchCenterName": "北京环球度假区", "latitude": "39.855181", "longitude": "116.6812398", "province": 1, "city": 1, "county": 106, "street": 0, "level": 3}], "selectedAreaId": "1,1,106,0"}, "shareInfo": {"fromSource": "hotel_vertical", "keyword": "北京环球", "sortType": "comment_count_desc", "orderType": "desc", "filterList": [{"filterType": "hotel_price_lowest", "itemId": "0,150", "itemName": "¥150以下", "itemType": "button", "group": false, "itemBehavior": "normal"}, {"filterType": "hotel_grade", "itemId": "3", "itemName": "3星/舒适", "itemType": "button", "group": false, "itemBehavior": "normal"}], "hotelBaseSearchParam": {"checkInDate": "2024-12-25", "checkOutDate": "2024-12-26", "roomNum": 2, "grownNum": 31, "childrenNum": 0, "childrenAges": []}, "mddInfo": {"type": "1", "showName": "北京", "searchCenterName": "北京环球度假区", "latitude": "39.855181", "longitude": "116.6812398", "province": 1, "city": 1, "county": 106, "street": 0, "level": 3}, "businessType": "1", "pageSize": 20, "page": 1, "latitude": 39.95931, "longitude": 116.29812, "posAreaId": "1,2800,55816,", "pvId": "3c7d31a8dc7b42f8ba7bb7fc75e63b0b", "logId": "8390403c2dfd45378292ca45c27dab5b"}}
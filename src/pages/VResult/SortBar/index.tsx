import {View, Text, Image} from '@/BaseComponents/atoms'
import {memo, useCallback, useContext, useEffect, useRef, useState} from 'react'
import {statusBarHeight, pt} from '@ltfe/ltfe-core-lib/lib/utiles'
import styles from './index.module.scss'
import SortItem from '@/pages/VResult/SortBar/SortItem'
import Filter from '@/Components/Filter'
import EventCatch from '@/Components/EventCatch'
import _ from 'lodash'
import QuickMatchTab from './QuickMatchTab'
import {objToList, safeRun, listToObjByKey, findInListKey, arrayDiffByKey} from '@/Components/Filter/utils'
import {ORDER_TYPE} from '@/common/mta/index'

function SortBar(props) {
    const {
        children,
        data, // 快筛
        onChange,
        showFilterType,
        onClick,
        onOk,
        value,
        onClear,
        mtaExpo,
        clickMta,
        options // 外漏筛选
    } = props

    // 保留综合筛选
    const preservedItem = children?.filter(item => item.value === "hotelSortType")
    // 移除综合筛选
    const remainingItems = children?.filter(item => item.value !== "hotelSortType")
    // set转化
    const outsFliterSet = new Set(options?.map(item => item.filterPanelCode))
    // 去交集
    const intersection = remainingItems.filter(item => outsFliterSet.has(item.value));
    // 合并
    const finalResult = preservedItem.concat(intersection);

    const [filterValue, setFilterValue] = useState({})
    const [selfValue, setSelfValue] = useState({})
    const [init, setInit] = useState(true)
    const [isExpo, setExpo] = useState(false)
    const filterValueRef = useRef(null)
    const refSortBarWr = useRef(null)
    const [mtaInfo, setMtaInfo] = useState({})

    useEffect(() => {
        setInit(false)
    }, [])

    useEffect(() => {
        if (props?.commonUserAction && !isExpo) {
            Array.isArray(finalResult) ? finalResult.forEach((item, index) => {
                safeRun(mtaExpo, {
                    eventId: 'HotelRN_List_MainFilterExpo',
                    eventData: {
                        filterPanelName: _.get(item, 'label', -100),
                        filterPanelCode: _.get(item, 'value', -100),
                        index: index + 1
                    }
                })
            }) : ''
            setExpo(true)
        }
    }, [props?.commonUserAction])

    const formatRes = (filterValue) => {
        const sortType = _.get(filterValue, 'hotelSortType[0].metaData.sortType', 'default')
        const orderType = _.get(filterValue, 'hotelSortType[0].metaData.orderType', 'desc')
        const _filterValue = objToList(_.cloneDeep(filterValue), ['hotelSortType', 'sortType'])
        const distanceFilter = _.get(filterValue, 'location_distance', []).filter(item => _.get(item, 'metaData.filterType') === 'gis_distance')
        const distanceInfo = _.get(distanceFilter, [0, 'metaData'], {itemName: undefined})

        return {
            searchInfo: {
                sortType,
                orderType,
                filterList: _filterValue.map(item => {
                    return {
                        ...item.metaData,
                        groupCode: _.get(item, 'metaData.groupCode', -100)
                    }
                })
            },
            filterMetaData: _filterValue,
            distanceInfo
        }
    }

    useEffect(() => {
        if (init) return
        const {curFilterType} = filterValue
        const showPopup = {}
        if (curFilterType === 'hotelSortType') _.set(showPopup, 'showPopUp', false)
        filterValueRef.current = filterValue
        safeRun(onChange, Object.assign(showPopup, formatRes(_.omit(filterValue, ['curFilterType']))))
        setSelfValue(filterValue)
        Filter.Instance.set("formValues", filterValue)
    }, [filterValue])

    useEffect(() => {
        const propsValue = value
        const _selfValue = objToList(_.cloneDeep(selfValue), ['hotelSortType', 'sortType'])
        if (arrayDiffByKey(propsValue, _selfValue) && arrayDiffByKey(_selfValue, propsValue)) {
            setSelfValue(Object.assign({}, listToObjByKey(value, 'filterKey')))
        }
    }, [value])

    if (!Array.isArray(options) || options.length < 1) {
        return null
    }

    return <>
        <View className={styles.sortBarBox}>
            {
                Array.isArray(finalResult) && finalResult.map((option, index) => <SortItem key={index}
                                                                                     isActive={showFilterType === option.value}
                                                                                     filterValue={_.get(selfValue, option.value)} {...option}
                                                                                     onClick={() => {
                                                                                         const mtaInfo = {
                                                                                             ...option,
                                                                                             index: index + 1,
                                                                                             businessType: '1',
                                                                                             orderType: ORDER_TYPE[_.get(props, 'orderType', 'desc')],
                                                                                             sortType: _.get(props, 'sortType', -100)
                                                                                         }
                                                                                         safeRun(onClick, mtaInfo)
                                                                                         setMtaInfo(mtaInfo)
                                                                                     }}/>)
            }
        </View>
        {
            showFilterType && findInListKey(finalResult, showFilterType, 'value') ? <>
                <View className={styles.mask} onClick={() => {
                    safeRun(onClick, mtaInfo)
                }}/>
                <View className={styles.filterPanel}
                      style={{top: pt(44 + 36) + statusBarHeight}}>
                    <Filter {...props} onChange={(value, curFilterType) => {
                        const _selfValue = objToList(_.cloneDeep(selfValue), ['sortType'])
                        const setValue = objToList(_.cloneDeep(value), ['sortType'])
                        if (arrayDiffByKey(_selfValue, setValue) && arrayDiffByKey(setValue, _selfValue)) {
                            setFilterValue({...value, curFilterType})
                        }
                    }} value={selfValue}
                            unDispatch={true}
                            showFilterType={showFilterType}
                            onClear={(filterPanelCode, _value) => {
                                if (filterPanelCode === 'clear_location_distance') {
                                    // const _filterValue = {..._.get(filterValueRef, 'current', {})}
                                    // _.unset(_filterValue, 'location_distance')
                                    setFilterValue(_value)
                                } else {
                                    const map = {
                                        hotelSortType: '智能排序',
                                        location_distance: '位置距离',
                                        price_star: '价格星级',
                                        hotel_filter: '筛选'
                                    }
                                    safeRun(onClear, {
                                        filterPanelCode,
                                        filterPanelName: map[filterPanelCode],
                                        groupCode: -100, // 综合的分组code 这里没有
                                        index: _.get(mtaInfo, ['index'], -100)
                                    })
                                }
                            }}
                            onOk={(value, filterPanelKey) => {
                                const map = {
                                    hotelSortType: '智能排序',
                                    location_distance: '位置距离',
                                    price_star: '价格星级',
                                    hotel_filter: '筛选'
                                }
                                const _formatRes = formatRes(value)

                                if (filterPanelKey !== 'hotelSortType') {
                                    safeRun(onOk, {
                                        ..._formatRes,
                                        showPopUp: false,
                                        loading: true,
                                        filterPanelCode: filterPanelKey,
                                        filterPanelName: map[filterPanelKey],
                                        groupCode: -100, // 综合的分组code 这里没有
                                        itemList: Array.isArray(_.get(_formatRes, ['searchInfo', 'filterList'])) && _.get(_formatRes, ['searchInfo', 'filterList']).length === 0 ? -100 : _.get(_formatRes, ['searchInfo', 'filterList'])
                                    })
                                } else {
                                    const sortInfo = _.get(value, 'hotelSortType[0].metaData', {})
                                    safeRun(clickMta, {
                                        eventId: 'HotelRN_List_Order',
                                        eventData: {
                                            ...sortInfo,
                                            orderType: ORDER_TYPE[_.get(sortInfo, 'orderType', 'desc')]
                                        }
                                    })
                                }
                            }}
                            handleInView={(isShow, item, value) => {
                                const {sortType, orderType, index} = _.get(item, ['metaData'], {})

                                if (isShow) {
                                    safeRun(mtaExpo, {
                                        eventId: 'HotelRN_List_OrderExpo',
                                        eventData: {
                                            sortType,
                                            orderType: ORDER_TYPE[orderType],
                                            index: index + 1,
                                            isSelect: value.some(valueItem => valueItem.sameKey === item.sameKey)
                                        }
                                    })
                                }
                            }}
                    />
                </View>
            </> : null
        }
        <View ref={refSortBarWr}>
            {
                Array.isArray(data) && data.length > 0 ?
                    <QuickMatchTab value={objToList(selfValue)} mtaExpo={props.mtaExpo}
                                   onChange={setFilterValue} clickMta={clickMta}
                                   data={data}/> : null
            }
        </View>
    </>
}

export default EventCatch(memo(SortBar))

import {View, Text, Image} from '@/BaseComponents/atoms'
import {memo, useContext, useEffect, useRef, useState} from 'react'
import styles from '@/pages/result/widgets/Hotel/SortBar/index.module.scss'
import {getImg, safeRun} from '@/Components/Filter/utils'
import _ from 'lodash'
import {isAndroid} from '@/common/common'

function SortItem(props) {
    const {label, value, filterValue, showValueType, isActive} = props
    const onClick = (value, option, target) => (event) => safeRun(props.onClick, value, {
        event,
        value,
        option,
        target
    })

    const hasValue = () => {
        return Array.isArray(filterValue) && filterValue.length > 0
    }

    return (
        <View className={styles.filterItemBox} onClick={onClick(value, props, 'sortItem')}>
            <Text numberOfLines={1} className={isActive || hasValue() ? styles.sortTextAct : styles.sortText}>
                {showValueType === 'word' ? _.get(filterValue, [0, 'metaData', 'filterName'], label) : label}
            </Text>
            {
                showValueType === 'count' && hasValue() ? <View className={styles.filterCountBox}><Text
                    className={isAndroid ? styles.filterCountAndroid : styles.filterCount}>{filterValue.length}</Text></View> : null
            }
            {
                (hasValue() || isActive) ? <Image className={isActive ? styles.filterArrow : styles.filterArrowDown}
                                                  src={getImg('filterArrowAct')}/> :
                    <Image className={styles.filterArrow} src={getImg('filterArrow')}/>
            }

        </View>
    )
}

export default memo(SortItem)

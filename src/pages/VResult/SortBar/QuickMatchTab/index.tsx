import {AdaptiveImage, View} from '@/BaseComponents/atoms'
import {pt, px} from '@ltfe/ltfe-core-lib/lib/utiles'
import {Image, StyleSheet} from 'react-native'
import styles from './index.module.scss'
import classNames from 'classnames'
import SelectItem from './SelectItem'
import _ from 'lodash'
import {InView, IOScrollView} from '@/BaseComponents/IntersectionObserver'
import {useEffect, useRef, useState} from 'react'
import Filter from '@/Components/Filter'
import {hasValueInArrayByKey, safeRun, listToObjByKey} from '@/Components/Filter/utils'
import {isAndroid, isWeb} from '@/common/common'
import Taro from '@tarojs/taro'

const getImgHeight = (uri, cb) => Image.getSize(uri, (width, height) => {
    cb(pt(width / height * 30))
})

function QuickMatchTab(props) {
    const {onChange} = props
    const [data, setData] = useState(Filter?.formatData(props?.data)?.filter(item => item?.isLeaf))
    const [filterValue, setFilterValue] = useState(props.value)
    const [imgWidth, setImgWidth] = useState({})
    const expoEndData = useRef<any>({}) // 埋点信息

    useEffect(() => {
        setFilterValue(props.value)
    }, [props.value])

    const selectItem = (item) => {
        let selfValues = [...filterValue]

        if (hasValueInArrayByKey(selfValues, item, 'sameKey')) {
            selfValues = selfValues.filter(valueItem => !hasValueInArrayByKey([valueItem], item, 'sameKey'))
        } else {
            const multiCount = _.get(item, 'info.multi')
            if (multiCount) { // 互斥
                let removeList = []
                selfValues.forEach((_item, key) => {
                    if (_.get(item, 'metaData.filterType', '1') === _.get(_item, 'metaData.filterType', '2')) {
                        removeList.push(key)
                    }
                })

                removeList = removeList.slice(0, removeList.length + multiCount - 1)

                while (removeList.length) {
                    const removeIndex = removeList.pop()
                    selfValues.splice(removeIndex, 1)
                }
                selfValues.push(item)
            } else {
                selfValues.push(item)
            }
        }
        (isWeb||isAndroid) && Taro.showToast({
            title: '努力加载中...',
            icon: 'none',
            duration: 500,
            mask: true,
        })
        safeRun(onChange, listToObjByKey(selfValues, 'filterKey'))
       
    }

    const expo = (visible, item, index) => {
        if (visible && !expoEndData.current?.[item?.sameKey]) {
            safeRun(props.mtaExpo, {
                eventId: 'HotelRN_List_QuickFilterExpo',
                eventData: _.omit({
                    businessType: 1,
                    ..._.get(item, 'metaData'),
                    index: index + 1,
                    groupCode: _.get(item, 'parents.metaData.groupCode', -100),
                    isChoose: hasValueInArrayByKey(filterValue, item, 'sameKey'),
                    isSelect: hasValueInArrayByKey(filterValue, item, 'sameKey'),
                    itemValue: _.get(item, 'metaData.itemId'),
                    itemName: _.get(item, 'metaData.itemName')
                }, ['exposeItemCount', 'group', 'multi'])
            })
            expoEndData.current[item?.sameKey] = true
        }
    }

    return (
        <View className={styles.quickOutBox}>
            <IOScrollView
                horizontal
                contentContainerStyle={_styles.svStyle}
                showsHorizontalScrollIndicator={false}
            >
                {
                    Array.isArray(data) && data.map((item, index) => {
                        const isIcon = item?.metaData?.style?.type === 'icon'
                        const isActive = hasValueInArrayByKey(filterValue, item, 'sameKey')
                        return (
                            isIcon ? (
                                <InView
                                    onChange={(visible) => {
                                        // TODO 埋点
                                        expo(visible, item, index)
                                    }}
                                    key={index}
                                    className={classNames('center', styles.item, styles.noPd)}
                                    // @ts-ignore
                                    style={
                                        isActive && isIcon &&
                                        {
                                            height: pt(28),
                                            borderWidth: isWeb ? 1 : px(1),
                                            borderColor: item?.metaData?.style?.activeBorderColor! || '#FF0400',
                                            width: isWeb ? pt(_.get(imgWidth, item?.metaData?.style?.listImage)) : ''
                                        }
                                    }
                                    onClick={() => {
                                        selectItem(item)
                                        safeRun(props?.clickMta, {
                                            eventId: 'HotelRN_List_QuickFilter',
                                            eventData: {
                                                rank: index + 1,
                                                type: _.get(item, 'metaData.itemType', -100),
                                                id: _.get(item, 'metaData.itemId', -100),
                                                value: _.get(item, 'metaData.itemName', -100),
                                                itemName: _.get(item, 'metaData.itemName')
                                            }
                                        })
                                    }}
                                    collapsable={false}
                                >
                                    <AdaptiveImage src={item?.metaData?.style?.listImage!}
                                                   onLoad={(info) => {
                                                       if (!isWeb) return
                                                       getImgHeight(item?.metaData?.style?.listImage, (width) => {
                                                           const _imgWidth = imgWidth
                                                           _.set(_imgWidth, item?.metaData?.style?.listImage, width)
                                                           setImgWidth({..._imgWidth})
                                                       })
                                                   }} style={{
                                        height: pt(28),
                                        minWidth: pt(75),
                                        borderWidth: isActive && isWeb ? pt(1) : 'none',
                                        borderColor: isActive && isWeb ? (item?.metaData?.style?.activeBorderColor! || '#FF0400') : 'rgba(0, 0, 0, 0)',
                                        borderRadius: isWeb ? pt(4) : 0,
                                        width: isWeb ? pt(_.get(imgWidth, item?.metaData?.style?.listImage)) : ''
                                    }}/>
                                </InView>
                            ) : (
                                <SelectItem
                                    showCheckedIcon={true}
                                    onChange={(visible) => {
                                        expo(visible, item, index)
                                    }}
                                    key={index} onClick={() => {
                                    selectItem(item)
                                    safeRun(props?.clickMta, {
                                        eventId: 'HotelRN_List_QuickFilter',
                                        eventData: {
                                            businessType: 1,
                                            ..._.get(item, 'metaData'),
                                            index: index + 1,
                                            groupCode: _.get(item, 'parents.metaData.groupCode', -100),
                                            isChoose: hasValueInArrayByKey(filterValue, item, 'sameKey'),
                                            isSelect: hasValueInArrayByKey(filterValue, item, 'sameKey'),
                                            itemValue: _.get(item, 'metaData.itemId'),
                                            itemName: _.get(item, 'metaData.itemName')
                                        }
                                    })
                                }} selected={isActive}
                                    text={_.get(item, 'metaData.itemName')}></SelectItem>
                            )
                        )
                    })

                }
            </IOScrollView>
        </View>
    )
}

export default QuickMatchTab

const _styles = StyleSheet.create({
    svStyle: {
        paddingLeft: pt(12),
        paddingRight: pt(4),
        height: pt(31)
    },
    item: {
        height: pt(30),
        paddingHorizontal: pt(10),
        backgroundColor: '#F5F7FA'
    }
})

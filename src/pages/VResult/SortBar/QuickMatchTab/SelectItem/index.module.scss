@import "@/assets/theme.scss";

.selectItemWr {
    background-color: var(--primaryBgColor);
    margin-right: 8px;
    border-radius: 4px;
    height: 28px;
    border: 0.5px solid transparent;
    margin-top: 2px;
}
.active {
    background-color: #F0F6FF;
    border-color: var(--primaryHLTextColor);
    border-width: 0.5px;
}

.activeImgWr {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 10px;
    height: 10px;
    border-bottom-right-radius: 4px;
    overflow: hidden;
}
.activeImg{
    width: 10px;
    height: 10px;
}

.selectItem {
    padding: 0 10px;
    color: var(--primaryTextColor);
    font-size: 12px;
}
.actTxt{
    color: var(--primaryHLTextColor);
    font-size: 12px;
}

import { getTimeZone } from '@ltfe/ltfe-core-lib/lib/utiles'
import { isEmpty } from "@/utils/isType"
import { reportInfo } from '@/common/reporter';
import { errorCodeConstantMapping, ErrortType } from '@/common/reporter/errorMapping';

export enum CLICK_TYPE {
    DELETE = 'delete',
    BACKFILL = 'backfill',
}
export enum POSITION_TYPE {
    POI = '1', //  poi地址
    CITY = '2', // 选择城市
    LOCATE = '3', // 实时定位
}

export function mapForData(initData = {}, params) {
    const {keyword, hotelBaseSearchParam, mddInfo, latitude, longitude, posAreaId, lbs_city} = params || {}
    const { checkInDate, checkOutDate, roomNum, grownNum, childrenNum, childrenAges } = hotelBaseSearchParam || {}
    const {geoName, provinceId, cityId, districtId, townId, level, lat, lon, is_lbs} = lbs_city || {}
    const dateInfo = defaultDate()
    const data = {
        ...initData,
        checkInDate: checkInDate || dateInfo.checkInDate,
        checkOutDate: checkOutDate || dateInfo.checkOutDate,
        keyword: keyword ?? '',
        roomNum: {
            value: roomNum ?? 1,
            minNum: 1,
            maxNum: 10
        },
        adultNum: {
            value: grownNum ?? 1,
            minNum: roomNum ?? 1,
            maxNum: 30 * (roomNum ?? 1)
        },
        childNum: {
            value: childrenNum ?? 0,
            minNum: 0,
            maxNum: 30 * (roomNum ?? 1),
            age: childrenAges ?? []
        },
        mddInfo: mddInfo || {},
        hotelCity: mddInfo?.type == POSITION_TYPE.LOCATE ? {
            type: POSITION_TYPE.LOCATE,
            showName: geoName,
            province: provinceId,
            city: cityId,
            county: districtId,
            street: townId,
            level: level,
            latitude: lat,
            longitude: lon,
            isLbs: is_lbs,
        } : (mddInfo || {}),
        lbsCity: lbs_city ?? {},
        locationInfo: {
            latitude: latitude,
            longitude: longitude,
            posAreaId: posAreaId,
        }

    }
    report(errorCodeConstantMapping?.PAGE_VERTICAL_SEARCHBAR_GET_PARAMS, params)
    return data
}

export function mapForLoactionData (state, data) {
    const {provinceId, geoId, cityId, districtId, townId, level, is_lbs, geoName, lat, lon, destType} = data?.hotelCity || {}
    const value = {
        ...state,
        checkInDate: data?.checkInDate,
        checkOutDate: data?.checkOutDate,
        roomNum: data?.roomNum,
        adultNum: data?.adultNum,
        childNum: data?.childNum,
        // 目的地城市对应页面给的mddinfo，这个如果刚哥组件没点实时定位，没给我经纬度和四级地址等字段，所以我不应该覆盖信息，如果点击完成，刚哥给的字段里有is_lbs就代表是获取实时定位，这时候应该覆盖
        hotelCity: {
            'showName': geoName,
            'latitude': data?.hotelKeyword?.lat ?? lat  ?? '',
            'longitude': data?.hotelKeyword?.lon ?? lon  ?? '',
            'province': isEmpty(provinceId) ? 0 : Number(provinceId),
            'city': isEmpty(cityId) ? 0 : Number(cityId),
            'county': isEmpty(districtId) ? 0 : Number(districtId),
            'street': isEmpty(townId) ? 0 : Number(townId),
            'level': level ?? 2,
            'type': is_lbs ? POSITION_TYPE.LOCATE : destType,
            isLbs: !!is_lbs
        },
        lbsCity: is_lbs ? (data?.hotelCity || {}) : (state.lbsCity || {}),
        // 接口需要的实时定位城市信息, 这个如果刚哥组件没点实时定位，没给我经纬度和四级地址等字段，所以我不应该覆盖信息，如果点击完成，刚哥给的字段里有is_lbs就代表是获取实时定位，这时候应该覆盖
        locationInfo: is_lbs ? {
            'latitude': lat  ?? '',
            'longitude': lon ?? '',
            'posAreaId': `${isEmpty(provinceId) ? '' : provinceId},${isEmpty(cityId) ? '' : geoId || cityId},${isEmpty(districtId) ? '' : districtId},${isEmpty(townId) ? '' : townId}`, // '1,2800,55816,',
        } : (state.locationInfo || {}),
        keyword: data?.hotelKeyword?.name || '',
        extMap: data?.hotelKeyword?.extMap || {}

    }
    report(errorCodeConstantMapping?.PAGE_VERTICAL_SEARCHBAR_GET_POPUP_PARAMS, data)
    return value
}

export function mapForOriginData (data, options) {
    const value = {
        keyword: data?.keyword ?? '',
        hotelBaseSearchParam: {
            checkInDate: data?.checkInDate,
            checkOutDate: data?.checkOutDate,
            roomNum: data?.roomNum?.value ?? 1,
            grownNum: data?.adultNum?.value ?? 1,
            childrenNum: data?.childNum?.value ?? 0,
            childrenAges: data?.childNum?.age ?? []

        },
        mddInfo: {
            ...data?.hotelCity ?? {},
            // 清空关键词如果type类型是1，这时候改成2
            type: (options === 'clear' && data?.hotelCity?.type == POSITION_TYPE.POI && data?.keyword === '') ? POSITION_TYPE.CITY : data?.hotelCity?.type
        },
        lbs_city: data.lbsCity ?? {},
        extMap: data?.extMap || {},
        ...(data?.locationInfo || {})

    }
    report(errorCodeConstantMapping?.PAGE_VERTICAL_SEARCHBAR_GIVE_PAGE_DATA, value)
    return value
}

export function checkCityChange (newData, oldData) {
    return newData?.cityId !== oldData?.city
}

export function mapMtaParams (data) {
    const {lbsCity, mddInfo} = data || {}
    const value = {
        o2o_coordinates: `${lbsCity?.lat || ''},${lbsCity?.lon || ''}`, // 用户经纬度
        fouraddrid: `${lbsCity?.provinceId || 0},${lbsCity?.cityId || 0},${lbsCity?.districtId || 0},${lbsCity?.townId || 0}`, // 用户四级地址
        search_o2o_coordinates: `${mddInfo?.latitude || ''},${mddInfo?.longitude || ''}`, // 目的地经纬度
        search_fouraddrid: `${mddInfo?.province || 0},${mddInfo?.city || 0},${mddInfo?.county || 0},${mddInfo?.street || 0}`, // 目的地四级地址
    }
    return value
}


export function defaultDate () {
    return {
        checkInDate: getTimeZone().format('YYYY-MM-DD'),
        checkOutDate: getTimeZone().add(1, 'days').format('YYYY-MM-DD')
    }
}

export function report (code, data) {
    try {
        reportInfo({
            code: code,
            errorDetail: {
                errorType: ErrortType.Info,
                customMsg: {
                    errorDescription: `${JSON.stringify(data)}`
                },
            }
        })
    } catch (error) {

    }

}


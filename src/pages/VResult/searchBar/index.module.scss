@import "@/assets/theme.scss";

.navBarWrapper {
  padding-bottom: 4px;
  background-color: #FFF;
}

.navBar {
  display: flex;
  flex-direction: row;
  flex: 1;
  padding-left: 42px;
}

.navBarWeb {
  margin-top: 4px;
  padding-left: 12px;
  flex: 0 0 auto;
}

.searchContainer {
  border-radius: 6px;
  background: #F5F7FA;
  height: 36px;
  padding-left: 5px;
  // padding-right: 20px;
  display: flex;
  flex-direction: row;
  flex: 1;
  align-items: center;
  margin-right: 16px;
}

.wordContainer {
  border-radius: 4px;
  background: var(--primarySubTextColor);
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 4px 8px;
}

.word {
  color: #FFF;
  font-size: 12px;
  margin-right: 4px;
}

.wordH5 {
  max-width: calc(100vw - 85px);
}

.closeIcon {
  width: 6px;
  height: 6px;
  flex: 0 0 auto;
}

// 位置
.search_city {
  // max-width: 62px;
  // padding: 0 12px;
  max-width: 53px;
  padding: 0 8px 0 7px;
  align-items: center;
  justify-content: center;
}

.city_min {
  // max-width: 50px;
  max-width: 41px;
}

.city_text {
  color: var(--primaryTextColor);
  font-weight: var(--fontActWeight);
}

.city_text_font {
  font-weight: bold;
}

.disabled {
  color: var(--primarySubTextColor);
}

// 入离时间
.search_date {
  height: 100%;
  padding-right: 8px;
  padding: 4px 0;
  position: relative;
}

.date_text {
  flex-direction: row;
  align-items: center;
}

.date_common {
  font-family: 'JDZhengHT-Regular';
  line-height: 14px;
  font-size: 12px;
  color: var(--primaryTextColor);
}

.date_common_disable {
  font-family: 'JDZhengHT-Regular';
  line-height: 14px;
  font-size: 12px;
  color: var(--primarySubTextColor);
}

.num_text {
  font-weight: var(--fontActWeight);
}

.fontbold {
  font-weight: bold;
}

.font600 {
  font-weight: 500;
}

.date_bottom {
  margin-top: 1px;
}

// 分割线
.line {
  width: 1px;
  height: 36px;
  background-color: #fff;
  margin-left: 12px;
}

.search_keyword {
  flex: 1;
  flex-direction: row;
  align-items: center;
}

.search_icon {
  margin-left: 8px;
  width: 16px;
  height: 16px;
}

.keyword_view {
  flex: 1;
  padding-right: 8px;
}

.keyword_default {
  color: '#5E6880';
  font-size: 12px;
  margin-left: 8px
}

.keyword_text {
  color: var(--primaryTextColor)
}

.keyword_text_disable {
  color: var(--primarySubTextColor);
}

.close {
  width: 32px;
  height: 36px;
  align-items: center;
  justify-content: center;
}

.close_img {
  width: 16px;
  height: 16px;
}

.font12 {
  font-size: 12px;
}

.font14 {
  font-size: 14px;
}

.padR32 {
  padding-right: 32px;
}

.marL8 {
  margin-left: 8px;
}

.marT2 {
  margin-top: 3px;
}

.marT1 {
  margin-top: 0px;
}

.date_tips {
  background-color: rgba(0, 0, 0, 0.8);
  height: 30px;
  width: 160px;
  align-items: center;
  border-radius: 6px;
  position: absolute;
  z-index: 99999;
  top: 92px;
  left: 32px;
}

.date_tips_arrow {
  width: 0;
  height: 0;
  border-left-width: 5px;
  border-right-width: 5px;
  border-bottom-width: 5px;
  border-left-color: transparent;
  border-right-color: transparent;
  border-bottom-color: rgba(0, 0, 0, 0.8);
  position: absolute;
  top: -5px;
}

.date_tips_text {
  font-size: 12px;
  color: #fff;
  line-height: 30px;
}

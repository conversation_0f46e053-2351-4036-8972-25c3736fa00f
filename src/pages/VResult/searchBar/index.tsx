import React, {useEffect, useState, useCallback, useContext} from 'react'
import { formatTime } from '@ltfe/ltfe-core-lib/lib/utiles'
import Taro from '@tarojs/taro'
import {View, Text, Image} from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import {
    mapForData,
    mapForLoactionData,
    mapForOriginData,
    mapMtaParams,
    checkCityChange,
    defaultDate,
    POSITION_TYPE,
    report
} from './index.service'
import {getImageUrl} from '@/assets/imgs'
import {isWeb, isAndroid} from '@/common/common'
import classNames from 'classnames'
import NavBar from '@/BaseComponents/NavBar'
import {HotelSearchPanel, Popup} from '@ltfe/ltfe-core-lib'
import Calendar from '@ltfe/ltfe-core-lib/lib/hotel-search-panel/calendar-pop'
import {toUnicode} from '@/utils'
import EventCatch from '@/Components/EventCatch'
import {safeRun} from '@/Components/Filter/utils'
import {encrypt, pt, statusBarHeight, deviceHeight} from '@ltfe/ltfe-core-lib/lib/utiles'
import _ from 'lodash'
import {BasePageContext} from '@/common/withPage'
import {StatusBar} from 'react-native'
import {M_EVENTID} from '@/common/mta'
import {errorCodeConstantMapping, ErrortType} from '@/common/reporter/errorMapping'
import {setShareData} from '@/utils/bridge'

/**
 * 搜索结果页面搜索条
 * @param props
 * @returns
 */

interface Props {
    showPopUp: string
    data: State
    onChange?: Function
    format: Array<any>
    pick: Array<any>
    mtaExpo: Function
    commonUserAction: Object,
    needUpdateDate?: Boolean
}

interface HotelBaseSearchParam {
    checkInDate: string
    checkOutDate: string
    roomNum: Object
    adultNum: Object
    childNum: Object
    childrenAges: Array<string>
    grownNum: Number
}

interface LocationInfo {
    showName: string
    city: Number
    latitude: string
    longitude: string
    searchCenterName?: string
}

interface State {
    hotelBaseSearchParam: HotelBaseSearchParam
    keyword: string
    mddInfo: LocationInfo
    locationInfo: Object
    queryMode: string
}

const ImgConfig = {
    Vclose: getImageUrl('Vclose'), // 删除
    Vsearch: getImageUrl('Vsearch') // 放大镜
}
const dateInfo = defaultDate()
const initialState = {
    checkInDate: dateInfo.checkInDate || '',
    checkOutDate: dateInfo.checkOutDate || '',
    keyword: '',
    // 房间信息
    roomNum: {
        value: 1,
        minNum: 1,
        maxNum: 10
    },
    // 成人信息
    adultNum: {
        value: 1,
        minNum: 1,
        maxNum: 30
    },
    // 儿童信息
    childNum: {
        value: 0,
        minNum: 0,
        maxNum: 30,
        age: null // 年龄，数组类型
    },
    hotelCity: {
        city: 36,
        showName: '北京',
        isLbs: false,
        _detailName: ''
    }
}

export const complement = num => num < 10 ? '0' + num : num;
export const full_date = (ms, type) => {
    const date = new Date(ms);
    const y = date.getFullYear();
    const m = date.getMonth() + 1;
    const d = date.getDate();

    const settype = type || '-';
    return y + settype + complement(m) + settype + complement(d);
};

const getAllDates = (startDate, endDate) => {
    const dateArray = [];
    const currentDate = new Date(startDate);

    while (currentDate <= new Date(endDate)) {
        dateArray.push(full_date(new Date(currentDate)));
        currentDate.setDate(currentDate.getDate() + 1);
    }

    return dateArray;
}

function SearchBar(props: Props) {
    const {travelInfo={}, isTravel, data } = props
    const [navType, setNavType] = useState(0)
    const {registerDidShow, offDidShow} = useContext(BasePageContext)
    const [dateProps] = useState({
        title: '选择日期',
        mode: 'multiple',
        monthNumber: 6,
        limitHour: 6,
        limitDay: 20,
        isShowPrice: true,
        isScrollToActive: false,
        startActiveText: '入住',
        endActiveText: '离店',
        allCanUsedCalendar: getAllDates(travelInfo?.startDate, travelInfo?.endDate),
        isUseCustomCalendarData: true
    })

    const didShow = useCallback(async () => {
        setTimeout(() => {
            setNavType(0)
            StatusBar.setBarStyle('dark-content')
        }, 600)

    }, [])

    useEffect(() => {
        registerDidShow(didShow)
        return () => {
            setNavType(2)
            offDidShow(didShow)
        }
    }, [])
    const [state, setState] = useState(initialState)
    useEffect(() => {
        const data = mapForData(initialState, props?.data)
        setState(data)
    }, [props?.data])

    useEffect(() => {
        props?.commonUserAction && props?.mtaExpo({
            eventId: M_EVENTID.HotelRNListSearchExpo
        })
    }, [props?.commonUserAction])

    const dispatch = (value, type = 'onChange') => {
        safeRun(_.get(props, type), value)
    }

    const toggle = (isClose = false) => {
        dispatch((isClose || props?.showPopUp === 'showQuickPop') ? '' : 'showQuickPop', 'onPanelChange')
    }

    // 间人数、日期等修改
    const update = (value) => {
        const {roomNum, adultNum, childNum, checkInDate, checkOutDate} = value

        if(!isWeb) {
            // 同步缓存数据
            setShareData({
                hotelRoomNum: {
                    roomNum: _.pick(roomNum, ['value', 'minNum', 'maxNum'], 1),
                    adultNum: _.pick(adultNum, ['value', 'minNum', 'maxNum'], 1),
                    childNum: _.pick(childNum, ['value', 'minNum', 'maxNum', 'age'], 0)
                },
                hotelCheckInDate: checkInDate,
                hotelCheckOutDate: checkOutDate
            })
        }

        const isChange = checkCityChange(value?.hotelCity, state?.hotelCity)
        const searchInfo = mapForOriginData(mapForLoactionData(state, value), '')
        let param = {
            showPopUp: '',
            data: Object.assign({}, searchInfo, isChange ? {filterList: []} : {}),
            shareData: value,
            mtaParams: mapMtaParams(mapForLoactionData(state, value)),
            resInfo: {
                naturalCardVOList: [],
                outsideFilterPanelVOList: []
            }
        }
        dispatch(Object.assign({}, param, isChange ? {filterMetaData: []} : {}))
    }

    // 差旅条件修改
    const updateTravel = (value) => {
        let param = {
            showPopUp: '',
            data: {
                hotelBaseSearchParam: value
            },
            shareData: value,
            mtaParams: mapMtaParams(mapForLoactionData(state, value)),
            resInfo: {
                naturalCardVOList: [],
                outsideFilterPanelVOList: []
            }
        }
        dispatch(param)
    }

    // 清空搜索词
    const clear = () => {
        dispatch({
            data: mapForOriginData(Object.assign({}, state, {keyword: ''}), 'clear'),
            shareData: {keyword: ''}
        })
    }

    // 跳转城市中间页
    const toCity = (enterType) => {
        const hotelSearchData = props?.data ? encrypt.Base64Encode(encrypt.Base64Encode(props?.data)) : ''
        report(errorCodeConstantMapping?.PAGE_VERTICAL_SEARCHBAR_TO_CITY_PARAMS, {data: props?.data, enterType})
        // const {checkInDate, checkOutDate, roomNum = 1, grownNum = 1, childrenAges = []} =
        // props?.data?.hotelBaseSearchParam || {} const hotelChildAge = Array.isArray(childrenAges) ?
        // childrenAges.join(',') :  ''
        const keywordName = enterType === 'city' ? '' : props?.data?.keyword
        let url = `https://hotel.m.jd.com/?routerName=location&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=location&fromPage=list&enterType=${enterType}&keywordName=${toUnicode(keywordName)}&hotelSearchData=${hotelSearchData}`
        if (isWeb) {
            url = `https://hotel.m.jd.com/?routerName=location&channel=${_.get(props, ['data', 'priceChannel'])}&flow=${_.get(props, ['data', 'channelId'])}&fromPage=list&enterType=${enterType}&keywordName=${toUnicode(keywordName)}&hotelSearchData=${hotelSearchData}`
        }
        dispatch({
            url,
            showPopUp: enterType === 'city' ? 'showQuickPop' : ''
        }, enterType === 'city' ? 'onClickQuickCity' : 'onClickCity')
    }

    const cityChange = (value) => {
        dispatch({
            data: mapForOriginData(mapForLoactionData(state, value), ''),
            mtaParams: mapMtaParams(mapForLoactionData(state, value))
        }, 'onClickPosition')
    }

    const goBack = () => {
        Taro.navigateBack({
            delta: 1
        })
    }

    const {checkInDate, checkOutDate, roomNum, adultNum, childNum, hotelCity, keyword, lbsCity, mddInfo} = state
    const cityName = hotelCity?.isLbs ? '我的位置' : (hotelCity?.showName || '选择城市')
    const navbarHeight = (isWeb ? pt(44) : pt(40)) + statusBarHeight

    //  差旅拼房
    // 1、单人拼房；2、多人拼房；3、普通因公；4、拼房转因公
    //  queryMode 1, 4  不可修改日期, 不可清除
    //  queryMode 2 可修改日期, 不可清除
    //  queryMode 3  可修改日期, 可清除
    const { queryMode = '' } = data
    // 日期是否可改
    const dateDisable = isTravel && (queryMode === '1' || queryMode === '2' || queryMode === '4')
    // 条件是否课改 -> 是否可以跳转中间页
    const inputDisable = dateDisable
    // 关闭按钮是否可以展示
    const cancelDisable = dateDisable

    const checkInoutClick = () => {
        if (isTravel) {
            if (!dateDisable) {
                dispatch('selectDate', 'onPanelChange')
            }
        } else {
            toggle()
        }
    }

    const keyWordClick = () => {
        if (isTravel) {
            if (!inputDisable) {
                dispatch(undefined, 'onClickSearch')
            }
        } else {
            toCity('keyword')
        }
    }

    const placeHolderText = () => {
        if (isTravel && (queryMode === '1' || queryMode === '2')) {
            return '位置/酒店'
        }
        return '位置/品牌/酒店'
    }

    return (<>
            <View className={styles.navBarWrapper}>
                <NavBar
                    navType={navType}
                    leftFn={goBack}
                    hideBorder
                    center={
                        <View className={classNames({
                            [styles.navBar]: true,
                            [styles.navBarWeb]: isWeb
                        })}>
                            <View className={styles.searchContainer}>
                                {/* 城市 我的位置 */}
                                <View
                                    className={classNames([styles.search_city, cityName?.length === 4 && styles.city_min])}
                                    onClick={isTravel ? () => {
                                    } : () => toggle()}>
                                    <Text
                                        className={classNames({
                                            [styles.city_text]: true,
                                            [styles.city_text_font]: isAndroid,
                                            [styles.font12]: cityName?.length > 2,
                                            [styles.font14]: !(cityName?.length > 2),
                                            [styles.disabled]: isTravel
                                        })}
                                        numberOfLines={2}>{cityName}</Text>
                                </View>
                                {/* 住离时间 */}
                                <View className={styles.search_date}
                                      onClick={checkInoutClick}>

                                    <View className={styles.date_text}>
                                        <Text
                                            className={ dateDisable ? styles.date_common_disable : styles.date_common}>{formatTime(checkInDate, 'MM-DD')}</Text>
                                    </View>

                                    <View
                                        className={classNames({
                                            [styles.date_text]: true,
                                            [styles.date_bottom]: isAndroid,
                                            [styles.disabled]:dateDisable
                                        })}>
                                        <Text
                                            className={ dateDisable ? styles.date_common_disable : styles.date_common}>{formatTime(checkOutDate, 'MM-DD')}</Text>
                                    </View>
                                </View>
                                {/* 几间几人 */}
                                {
                                    !isTravel ?
                                        <View className={classNames([styles.search_date, styles.marL8, styles.marT2])}
                                              onClick={() => toggle()}>

                                            <View className={styles.date_text}>
                                                <Text
                                                    className={classNames({
                                                        [styles.date_common]: true,
                                                        [styles.fontbold]: isAndroid,
                                                        [styles.font600]: !isAndroid
                                                    })}>{roomNum?.value || 1}间</Text>
                                            </View>

                                            <View
                                                className={classNames([styles.date_text, styles.date_bottom, styles.marT1])}>
                                                <Text
                                                    className={classNames({
                                                        [styles.date_common]: true,
                                                        [styles.fontbold]: isAndroid,
                                                        [styles.font600]: !isAndroid
                                                    })}>{(adultNum?.value + childNum?.value) || 1}人</Text>
                                            </View>
                                        </View> : null
                                }

                                <View className={styles.line}/>

                                {/* 关键字 */}
                                <View className={styles.search_keyword}
                                      onClick={keyWordClick}>
                                    {!keyword &&
                                        <Image src={ImgConfig['Vsearch']} className={styles.search_icon}
                                               mode="scaleToFill"/>}
                                    {
                                        !!keyword
                                            ? <View className={classNames([styles.keyword_view, styles.padR32])}>
                                                <Text numberOfLines={1}
                                                      className={classNames([styles.keyword_default, inputDisable ? styles.keyword_text_disable : styles.keyword_text])}>
                                                    {keyword}
                                                </Text>
                                            </View>
                                            : <View className={styles.keyword_view}>
                                                <Text numberOfLines={1} className={classNames({
                                                    [styles.keyword_default]: true,
                                                    [styles.keyword_text_disable ]: inputDisable
                                                })}>
                                                   {placeHolderText()}
                                                </Text>
                                            </View>
                                    }

                                    {
                                       !cancelDisable && !!keyword &&
                                        <View className={styles.close} onClick={clear}>
                                            <Image src={ImgConfig['Vclose']} className={styles.close_img}
                                                   mode="scaleToFill"/>
                                        </View>
                                    }

                                </View>
                            </View>
                        </View>
                    }/>
            </View>
            {
                props?.showPopUp === 'showQuickPop' && <HotelSearchPanel
                    checkInDate={checkInDate}
                    checkOutDate={checkOutDate}
                    roomNum={roomNum}
                    adultNum={adultNum}
                    childNum={childNum}
                    hotelCity={hotelCity?.type == POSITION_TYPE.LOCATE ? lbsCity : {
                        geoName: hotelCity?.showName ?? '',
                        lat: hotelCity?.latitude,
                        lon: hotelCity?.longitude,
                        provinceId: hotelCity?.province,
                        cityId: hotelCity?.city,
                        districtId: hotelCity?.county,
                        townId: hotelCity?.street,
                        destType: hotelCity?.type,
                        level: hotelCity?.level,
                        is_lbs: hotelCity?.isLbs
                    }}
                    mode={'hotel'}
                    onClose={() => toggle(true)}
                    onUpdate={update}
                    toCity={() => toCity('city')}
                    cityChange={cityChange}
                    style={{top: navbarHeight}}
                    hotelKeyword={{
                        lat: mddInfo?.latitude || '',
                        lon: mddInfo?.longitude || '',
                        name: keyword || ''
                    }}
                />
            }
            {
                props?.showPopUp === 'selectDate' && <Popup
                    title="选择日期"
                    speed={200}
                    show={true}
                    height={deviceHeight * 5 / 6}
                    onHide={() => {
                    }}
                >
                    <Calendar
                        {...dateProps}
                        checkInDate={checkInDate}
                        checkOutDate={checkOutDate}
                        onUpdate={(pickDate) => {
                            const { departDate, arriveDate } = pickDate
                            updateTravel({
                                checkInDate: departDate,
                                checkOutDate: arriveDate
                            })
                        }}
                    />
                </Popup>
            }
            {
                props?.needUpdateDate ? <View className={styles.date_tips}  style={isWeb ? {top: pt(50), left: pt(24)} : {}}>
                    <View className={styles.date_tips_arrow}/>
                    <Text className={styles.date_tips_text}>入住日期已变化，即将刷新</Text>
                </View> : null
            }
        </>
    )
}

SearchBar.Base64Encode = encrypt.Base64Encode
SearchBar.Base64Decode = encrypt.Base64Decode
export default EventCatch(SearchBar)

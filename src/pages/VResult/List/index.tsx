import React, { memo, useCallback, useEffect, useRef, useState } from 'react'
import { InView, IOFlatList } from '@/BaseComponents/IntersectionObserver'
import HotelCard from '@/pages/result/widgets/Hotel/HotelCard'
import styles from './index.module.scss'
import EventCatch from '@/Components/EventCatch'
import { Loading, View, Text, Image } from '@/BaseComponents/atoms'
import { safeRun, getImg } from '@/Components/Filter/utils'
import _ from 'lodash'
import NoData from '@/pages/result/widgets/NoData'
import { EmptyType } from '@/pages/result/widgets/NoData'
import NoMatches from '@/pages/VResult/NoMatches'
import ListTips, { TIP_TYPE } from '@/pages/result/widgets/ListBottomTip'
import ClearArea from '@/pages/result/widgets/Hotel/ClearArea'
import { isAndroid } from '@/common/common'
import { M_EVENTID } from '@/common/mta'
import { TouchableWithoutFeedback } from 'react-native'

const List = (props) => {
    const {
        onChange,
        page,
        hasNextPage,
        data,
        recommendData,
        matchList,
        onClear,
        onClick,
        mtaExpo,
        mtaClick,
        fetchError,
        setPopupInfo,
        isTravel,
        noShareRoomClick
    } = props

    const listRef = useRef(null)
    const expoEndData = useRef<any>({}) // 埋点信息
    const refLoading = useRef(false)

    useEffect(() => {
        let st2
        if (refLoading.current) {
            st2 = setTimeout(() => {
                refLoading.current = false
            }, 20)
        }
        return () => {
            clearTimeout(st2)
        }
    }, [data, recommendData])

    const getTagList = (data) => {
        const keys = ['hotelRightPromotion']
        const promotionTagListMap = data?.promotionTagListMap || {}
        const result = []
        for (const key in promotionTagListMap) {
            if (Object.prototype.hasOwnProperty.call(promotionTagListMap, key)) {
                if (keys.includes(key)) {
                    const ele = promotionTagListMap[key] || []
                    ele.forEach(item => {
                        result.push({
                            trackId: item?.trackId || '-100',
                            labelName: item?.listShowName || '-100'
                        })
                    })
                }
            }
        }
        return result.length ? result : '-100'
    }

    const noShareRoomClickAction = () => {
        safeRun(mtaClick, {
            eventId: 'HotelRN_List_NoRoomShareReservation'
        })
        safeRun(noShareRoomClick)
    }

    const getWidgetEventId = (trackData: any, isNutural) => {
        const {isExposure, scene} = trackData
        if(scene === 'subsidy'){
            if(isNutural) {
            return isExposure ? 'HotelRN_List_SubsidyRoomExpo' : 'HotelRN_List_SubsidyRoom'
            }else {
            return isExposure ? 'HotelRN_List_RecSubsidyRoomExpo' : 'HotelRN_List_RecSubsidyRoom'
            }
        }
    }

    const renderItem = (info, _props) => {
        const {cardWidth, item, index} = info
        const {cardType, title, eventId, hasData, show, shareHotelTip} = item
        const cardUuid = _.get(info, ['id'], _.get(item, ['hotelCardVO', 'id'], index))

        switch (cardType) {
            case 'Empty':
                return <View className={styles.nodataBox}><NoData/></View>
            case 'NoRoomShare':
                return  <InView key={cardUuid} onChange={
                    (visible) => {
                        if (visible) {
                            safeRun(mtaExpo, {
                                eventId: 'HotelRN_List_NoRoomShareServationExpo'
                            })
                        }
                    }
                }>
                 <NoData
                    type={EmptyType.NO_SHARE_ROOM}
                    shareHotelTip={shareHotelTip}
                    onClick={noShareRoomClickAction}
                />
                </InView>
            case 'NoRoomShareLowPrice':
                return <InView key={cardUuid} onChange={
                    (visible) => {
                        if (visible) {
                            safeRun(mtaExpo, {
                                eventId: 'HotelRN_List_NoRoomShareServationExpo'
                            })
                        }
                    }
                }>
                    <NoData
                        type={EmptyType.NO_SHARE_ROOM_LOWPRICE}
                        shareHotelTip={shareHotelTip}
                        onClick={noShareRoomClickAction}
                    />
                </InView>
            case 'InfoTips':
                return isTravel ? <TouchableWithoutFeedback onPress={() => {
                    setPopupInfo('describe')
                }}><View className={styles.infoTipsBox}>
                    <View className={styles.infoTipsBoxContent}>
                        <Image className={styles.infoTipsIcon} src={getImg('info')}/>
                        <Text className={styles.infoTipsText}>
                            【出差必看】酒店住宿标准和发票要求
                        </Text>
                    </View>
                </View></TouchableWithoutFeedback> : null

            case 'NoData':
                return show ? <ListTips title={'未找到符合条件的结果，请更改条件重新搜索'} className={'bold'}
                                        style={{color: '#1A1A1A'}}/> : null
            case 'NoMatch':
                return <InView key={cardUuid} onChange={(visible) => {
                    if (visible && Array.isArray(matchList) && !expoEndData?.current['HotelRN_List_RecFilterExpo']) {
                        matchList.forEach((item, index) => {
                            const itemMetaData = _.get(item, ['metaData'])

                            const map = {
                                hotelSortType: '智能排序',
                                location_distance: '位置距离',
                                price_star: '价格星级',
                                hotel_filter: '筛选'
                            }

                            safeRun(mtaExpo, {
                                eventId: 'HotelRN_List_RecFilterExpo',
                                eventData: {
                                    filterPanelCode: _.get(item, 'filterKey', -100),
                                    filterPanelName: _.get(map, _.get(item, 'filterKey', -100), -100),
                                    groupCode: _.get(itemMetaData, 'groupCode', -100),
                                    filterType: _.get(itemMetaData, 'filterType', -100),
                                    itemId: _.get(itemMetaData, 'itemId', -100),
                                    itemName: _.get(itemMetaData, 'itemName', -100),
                                    index: index + 1
                                }
                            })
                        })
                    }
                    _.set(expoEndData, ['current', 'HotelRN_List_RecFilterExpo'], true)
                }}>
                    {
                        matchList.length > 0 ?
                            <NoMatches key={cardUuid} mtaExpo={mtaExpo} data={matchList} onClear={onClear}
                                       mtaKey={'HotelRN_List_Tip'}
                                       mtaPage={'HotelRN_List'} mtaClick={(info, index) => {
                                const map = {
                                    hotelSortType: '智能排序',
                                    location_distance: '位置距离',
                                    price_star: '价格星级',
                                    hotel_filter: '筛选'
                                }

                                const itemMetaData = _.get(info, ['metaData'])
                                safeRun(mtaClick, {
                                    eventId: 'HotelRN_List_RecFilter',
                                    eventData: {
                                        filterPanelCode: _.get(info, 'filterKey', -100),
                                        filterPanelName: _.get(map, _.get(info, 'filterKey', -100), -100),
                                        groupCode: _.get(itemMetaData, 'groupCode', -100),
                                        filterType: _.get(itemMetaData, 'filterType', -100),
                                        itemId: _.get(itemMetaData, 'itemId', -100),
                                        itemName: _.get(itemMetaData, 'itemName', -100),
                                        index: index + 1
                                    }
                                })
                            }}
                                       mtaPageName={'verticalSearchList'}/> : null
                    }
                </InView>
            case 'ListTitle':
                return <View key={cardUuid} className={styles.listTitle}><Text
                    className={styles.listTitleWord}><Image className={styles.goodIcon} src={getImg('good')}/>以下酒店满足您的部分要求</Text></View>
            case 'AreaClear':
                return _.get(_props, 'distanceInfo.itemName') ?
                    <InView key={cardUuid} onChange={(visible) => {
                        if (visible && !expoEndData?.current['HotelRN_List_TipExpo']) {
                            safeRun(mtaExpo, {
                                eventId: 'HotelRN_List_TipExpo'
                            })
                            _.set(expoEndData, ['current', 'HotelRN_List_TipExpo'], true)
                        }
                    }}><ClearArea key={cardUuid} mtaExpo={mtaExpo}
                                  mtaClick={(filterType, filterKey) => {
                                      safeRun(mtaClick, {
                                          eventId: 'HotelRN_List_Tip'
                                      })
                                  }}
                                  data={{tips: `已显示${_.get(_props, 'distanceInfo.itemName')}内所有酒店`}}
                                  mtaKey={'HotelRN_List_Tip'} mtaPage={'HotelRN_List'}
                                  filterType={'clear_location_distance'}
                                  mtaPageName={'verticalSearchList'}/>
                    </InView> : null
            case 'ListTips':
                return hasData ?
                    <ListTips mtaExpo={mtaExpo} title={title}/> : null
            default :
                return <InView key={cardUuid} onChange={(visible) => {
                    const discountList = _.get(item, ['hotelCardVO', 'promotionLayerVO', 'promotionDetailList'], -100)
                    const eventData = {
                        hotelId: _.get(item, ['hotelCardVO', 'id'], -100),
                        score: _.get(item, 'hotelCardVO.promotionTagListMap.hotelScore[0].listShowName', -100),
                        index,// 位置 number
                        firpricetype: '11', // 第一价格分类 string
                        firprice: _.get(item, ['hotelCardVO', 'price'], -100), // 第一价格金额 number
                        secpricetype: '52', // 第二价格类型 string
                        secprice: _.get(item, ['hotelCardVO', 'originPrice'], -100), //第二价格金额 number
                        tagList: getTagList(item?.hotelCardVO || {}),
                        discountType: Array.isArray(discountList) ? discountList.map(item => item.type) : -100,
                        rankId: _.get(item, 'hotelCardVO.wareRankVO.rankId', '-100'),
                        poiId: _.get(item, ['useAction', 'poiId'],  -100),
                        distance: _.get(item, ['useAction', 'distance'],  -100),
                        distanceType: _.get(item, ['useAction', 'distanceType'],  -100),
                        beltCode: _.get(item, ['hotelCardVO', 'beltInfoVO', 'beltCode'], '-100'),
                        ..._.get(item, ['userAction'], {})
                    }
                    if (visible && !expoEndData?.current?.[item?.hotelCardVO?.id]) {
                        safeRun(mtaExpo, {
                            eventId: eventId ? M_EVENTID.HotelRN_List_RecHotelExpo : M_EVENTID.HotelRN_List_HotelMesExpo,
                            eventData
                        })
                        safeRun(mtaExpo, {
                            eventId: eventId ? M_EVENTID.HotelRN_List_RecHotelPicExpo : M_EVENTID.HotelRN_List_HotelPicExpo,
                            eventData
                        })
                    }
                    _.set(expoEndData, ['current', item?.hotelCardVO?.id], true)
                }} index={index}><HotelCard key={cardUuid} mtaExpo={mtaExpo} cardWidth={cardWidth} data={item}
                                            index={index}
                                            mtaTrack={(trackData) => {
                                                const {isExposure, param} = trackData
                                                if(isExposure) {
                                                    // eventId有值说明是推荐数据
                                                    safeRun(mtaExpo, { 
                                                        eventId: getWidgetEventId(trackData, !!eventId), 
                                                        eventData: param
                                                    })
                                                }else {
                                                    safeRun(mtaClick, { 
                                                        eventId: getWidgetEventId(trackData, !!eventId), 
                                                        eventData: param
                                                    })
                                                }
                                            }}
                                            onPress={_.debounce((cardProps, target) => {
                                                const {trigger, tagInfo} = target
                                                safeRun(onClick, {
                                                    eventId,
                                                    cardProps,
                                                    target,
                                                    index,
                                                    trigger,
                                                    tagInfo
                                                })
                                            }, 300)}/></InView>
        }
    }


    const getDataList = (data, recommendData = []) => {

         // 过滤掉追加的data数据
         const filteredData = data?.filter((item, index) => {
            let show = true;
            if (item.cardType === 'NoRoomShare' || 
                item.cardType === 'NoRoomShareLowPrice' || 
                item.cardType === 'InfoTips') {
                show = false;
            }
            if (index < 2) return show
            return true;
        });
        // 有推荐流量
        const hasRecommendData = Array.isArray(recommendData) && recommendData.length > 0
        // 自然流量大于20
        const hasSufficientData = Array.isArray(filteredData) && filteredData.length > 20
        // 有筛选
        const hasNoFliter = Array.isArray(matchList) && matchList.length === 0

        const hasDataWithoutMatches = Array.isArray(filteredData) && filteredData.length > 0
        && hasNoFliter

        const hasValidData = hasRecommendData || hasSufficientData || hasDataWithoutMatches;
        const showMoreTip = !fetchError && hasValidData;
        // 尾部跟进的元素
        const endList = [
            {
                // 空搜狗图，出现场景 没有自然流量和 没有推荐流量。
                cardType: 'NoData',
                show: (!Array.isArray(data) || Array.isArray(data) && data.length === 0) && Array.isArray(matchList) && matchList.length === 0,
                title: '未找到符合条件的结果，请更改条件重新搜索'
            },
            // 区域清除，组件内部判断是否隐藏，根据字段
            {cardType: 'AreaClear', id: 'area_clear'}
        ].concat(data.length < 20 ? [
            // 未命中组件，根据筛选条件判断
            {cardType: 'NoMatch', id: 'no_match'}
        ] : []).concat(
            // 有推荐流量，因为是商卡，不能在组件内部判断是否展示。成本高，只能在数据组装时判断是否拼接。
            Array.isArray(recommendData) && recommendData.length > 0 &&
            // 且自然流量少于20
            (!Array.isArray(data) || Array.isArray(data) && data.length < 20) ? [
                {cardType: 'ListTitle', id: 'list_title'},
                ...recommendData.map(item => {  // 更新推荐数据标识，埋点用
                    return {...item, eventId: 'HotelRN_List_RecHotel'}
                })
            ] : []
        ).concat(
            // 底部，没有筛选，非网络错误，有自然流量或者推荐流量展示。
            [{
                cardType: 'ListTips',
                title: '没有更多数据了',
                id: 'listTips',
                hasData: showMoreTip
            }]
        )

        // 分页拼接数据
        if (Array.isArray(data) && data.length > 0) {
            return hasNextPage ? data : data.concat(endList)
        } else {
            // 否则跟进尾部数据。
            return (!Array.isArray(data) || Array.isArray(data) && data.length === 0) && (!Array.isArray(recommendData) || Array.isArray(recommendData) && recommendData.length === 0) ? [{cardType: 'Empty'}] : endList
        }
    }

    useEffect(() => {
        // 被请求刷新或者回到第一页
        if (page === 1 || props?.loading === true) {
            safeRun(_.get(listRef, ['current', 'scrollToOffset']), 0)
        }
    }, [page, props?.loading])

    const footerRender = (hasNextPage, fetchError) => {
        if (fetchError) {
            return <ListTips type={TIP_TYPE.RETRY_BY_NETERROR} onRetry={() => {
                safeRun(onChange)
            }}/>
        } else if (!fetchError && hasNextPage) {
            return <View style={{backgroundColor: '#fff'}}><Loading/></View>
        }

        return null
    }

    return <View className={styles.listBox}>
        <IOFlatList
            data={getDataList(data, recommendData).filter(item => item)}
            // 根据showNoData决定是否允许滚动
            scrollEnabled={true}
            renderItem={(info) => renderItem(info, props)}
            keyExtractor={(item, index) => index.toString()}
            decelerationRate={isAndroid ? 0.985 : 0.994}
            windowSize={21}
            initialNumToRender={10}
            scrollEventThrottle={16}
            onEndReached={() => {
                if (!refLoading.current && hasNextPage && !_.get(props, 'loading', false)) {
                    refLoading.current = true
                    safeRun(onChange)
                }
            }}
            ListFooterComponent={() => footerRender(hasNextPage, fetchError)}
            ref={listRef}
        />
    </View>
}

export default EventCatch(memo(List))

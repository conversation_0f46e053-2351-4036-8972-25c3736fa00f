@import "@/assets/theme.scss";

.listBox {
    margin-top: 12px;
    flex: 1;
    background-color:  var(--primaryBgColor);
}

.listTitle {
    padding: 12px 18px;
    background-color: #fff;
}

.listTitleWord {
    font-size: 14px;
    font-weight: 500;
    color: var(--primaryTextColor);
}

.nodataBox {
    padding-top: 80px;
}

.infoTipsBox {
    background-color: #fff;
    padding: 0 12px 4px;
}

.infoTipsBoxContent {
    background-color: #f0f6ff;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
}

.infoTipsText {
    text-align: center;
    height: 24px;
    line-height: 24px;
    color: #006EEB;
    font-size: 12px;
}

.infoTipsIcon {
    width: 12px;
    height: 12px;
}

.goodIcon {
    width: 14px;
    height: 14px;
    margin-right: 5px;
}

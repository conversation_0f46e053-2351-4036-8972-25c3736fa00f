import {View} from '@/BaseComponents/atoms'
import {useEffect} from 'react'
import {safeRun} from '@/Components/Filter/utils'
import {getLBS} from '@ltfe/ltfe-core-lib/lib/utiles'
import _ from 'lodash'

import styles from './index.module.scss'
import {window} from '@tarojs/runtime'

const WebViewBox = (props) => {
    const {show} = props

    const eventListener = (event) => {
        if (event?.data) {
            const {type, data} = _.get(event, ['data'])
            if (type === 'getLocationInfo') {
                const locationIframe = document.getElementById('select_city_iframe')
                getLBS().then(res => {
                    locationIframe?.contentWindow?.postMessage({
                        type: 'getLBS',
                        data: res
                    }, '*')
                }).catch(error => {
                    locationIframe?.contentWindow?.postMessage({
                        type: 'getLBS',
                        data: error
                    }, '*')
                })
            } else {
                _.set(data, ['resInfo', 'naturalCardVOList'], [])
                _.set(data, ['resInfo', 'outsideFilterPanelVOList'], [])
                _.set(data, ['loading', 'true'], [])
                _.set(data, ['searchInfo', 'page'], 1)
                safeRun(_.get(props, ['actions', type]), data)
            }
        }
    }

    useEffect(() => {
        if (show && window) {
            window.removeEventListener('message', eventListener)
            window.addEventListener('message', eventListener)
            const frameBox = document.getElementById('location_select_frame_box')
            frameBox.innerHTML = `<iframe allow="geolocation" id="select_city_iframe" src="${_.get(props, 'uri', 'https://jd.com')}"  frameborder="0" scrolling="yes" style="border: none; width: 100%; height: 100%;"></iframe>`
        }
    }, [show])

    useEffect(() => {
        return window ? window.removeEventListener('message', eventListener) : null
    }, [])

    return show ? <View className={styles.box} id={'location_select_frame_box'}/> : null
}

export default WebViewBox

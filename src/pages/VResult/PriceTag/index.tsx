import {memo} from 'react'
import {View, Text} from '@/BaseComponents/atoms'
import st from './index.module.scss'

function PriceTag(props) {
    const {
        tips,
        symbol,
        currency,
        integerPart,
        fractionalPart,
        tipsStyle,
        symbolStyle,
        currencyStyle,
        integerPartStyle,
        fractionalPartStyle
    } = props

    if (typeof integerPart !== 'number' && typeof integerPart !== 'string') {
        return null
    }

    return (
        <View className={st.container}>
            {
                typeof tips === 'string' ? <View style={st.startContainer}>
                    <Text style={tipsStyle} className={st.startText}>{tips}</Text>
                </View> : null
            }
            {
                typeof symbol === 'string' ? <Text style={symbolStyle} className={st.symobl}>{symbol}</Text> : null
            }
            <Text style={currencyStyle} className={st.symobl}>{currency || '￥'}</Text>
            <View>
                <Text style={integerPartStyle} className={st.integer}>{integerPart}</Text>
            </View>
            {
                (typeof fractionalPart === 'string' || typeof fractionalPart === 'number') ?
                    <Text style={fractionalPartStyle} className={st.decimal}>.{fractionalPart}</Text> : null
            }
        </View>
    )
}


export default memo(PriceTag)

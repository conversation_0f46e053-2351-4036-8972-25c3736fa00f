{"defaultParams": {}, "mta": {"params": {"pageId": "HotelRN_List", "pageName": "verticalSearchList"}}, "schema": [{"type": "searchBar", "props": {"eventTypes": {"onChange": {"keys": ["setShareInfo"], "format": [{"setKey": ["searchInfo"], "formatValue": ["data"]}, {"setKey": ["loading"], "value": true}, {"setKey": ["searchInfo", "page"], "value": 1}, {"setKey": ["resInfo", "naturalCardVOList"], "value": []}, {"setKey": ["resInfo", "outsideFilterPanelVOList"], "value": []}], "pick": ["searchInfo", "showPopUp", "shareData", "loading", "resInfo", "filterMetaData"], "mta": {"type": "mtaClick", "eventId": "HotelRN_List_SelectListLayer", "format": [{"setKey": "displayName", "formatValue": ["data", "keyword"], "value": ""}, {"setKey": "keyword", "formatValue": ["data", "keyword"], "value": ""}, {"setKey": "roomNum", "formatValue": ["data", "hotelBaseSearchParam", "roomNum"], "value": -100}, {"setKey": "adultNum", "formatValue": ["data", "hotelBaseSearchParam", "grownNum"], "value": -100}, {"setKey": "childrenNum", "formatValue": ["data", "hotelBaseSearchParam", "childrenNum"], "value": -100}, {"setKey": "startDate", "formatValue": ["data", "hotelBaseSearchParam", "checkInDate"], "value": -100}, {"setKey": "endDate", "formatValue": ["data", "hotelBaseSearchParam", "checkOutDate"], "value": -100}, {"setKey": "cityCode", "formatValue": ["data", "lbs_city", "cityId"], "value": 0}, {"setKey": "mddType", "formatValue": ["data", "mddInfo", "type"], "value": -100}, {"setKey": "o2o_coordinates", "formatValue": ["mtaParams", "o2o_coordinates"], "value": -100}, {"setKey": "<PERSON><PERSON><PERSON><PERSON>", "formatValue": ["mtaParams", "<PERSON><PERSON><PERSON><PERSON>"], "value": -100}, {"setKey": "search_o2o_coordinates", "formatValue": ["mtaParams", "search_o2o_coordinates"], "value": -100}, {"setKey": "search_fouraddrid", "formatValue": ["mtaParams", "search_fouraddrid"], "value": -100}], "pick": ["o2o_coordinates", "<PERSON><PERSON><PERSON><PERSON>", "displayName", "keyword", "search_o2o_coordinates", "search_fouraddrid", "queryPattern", "adultNum", "childrenNum", "endDate", "startDate", "roomNum", "cityCode", "mddType", "queryPattern", "eventId"]}}, "onPanelChange": {"keys": ["setPopupInfo"], "mta": {"type": "mtaClick", "eventId": "HotelRN_List_SelectList", "pick": ["eventId"]}}, "onClickCity": {"keys": ["openLocation"], "mta": {"type": "mtaClick", "eventId": "HotelRN_List_Search", "pick": ["eventId"]}}, "onClickQuickCity": {"keys": ["openLocation"], "mta": {"type": "mtaClick", "eventId": "HotelRN_List_SwitchCity", "pick": ["eventId"]}}, "onClickPosition": {"mta": {"type": "mtaClick", "eventId": "HotelRN_List_Location", "format": [{"setKey": "cityCode", "formatValue": ["data", "lbs_city", "geoId"], "value": 0}, {"setKey": "o2o_coordinates", "formatValue": ["mtaParams", "o2o_coordinates"], "value": -100}, {"setKey": "<PERSON><PERSON><PERSON><PERSON>", "formatValue": ["mtaParams", "<PERSON><PERSON><PERSON><PERSON>"], "value": -100}], "pick": ["o2o_coordinates", "<PERSON><PERSON><PERSON><PERSON>", "cityCode", "eventId"]}}, "onClickSearch": {"keys": ["openSearch"], "mta": {"type": "mtaClick", "eventId": "HotelRN_List_Search", "pick": ["eventId"]}}}}, "propsPaths": {"isTravel": ["storage", "isTravel"], "travelInfo": ["storage", "searchInfo", "travelInfo"], "showPopUp": ["storage", "showPopUp"], "data": ["storage", "searchInfo"], "commonUserAction": ["storage", "resInfo", "commonUserAction"], "needUpdateDate": ["storage", "needUpdateDate"]}}, {"type": "sortBar", "props": {"children": [{"label": "智能排序", "value": "hotelSortType", "showValueType": "word", "eventId": "HotelRN_List_OrderEntrance"}, {"label": "位置距离", "value": "location_distance", "showValueType": "count", "eventId": "HotelRN_List_MainFilter"}, {"label": "价格/星级", "value": "price_star", "showValueType": "count", "eventId": "HotelRN_List_MainFilter"}, {"label": "筛选", "value": "hotel_filter", "showValueType": "count", "eventId": "HotelRN_List_MainFilter"}], "eventTypes": {"clickMta": {"keys": ["mtaClick"]}, "onClick": {"keys": ["setPopupInfo"], "format": [{"formatValue": "value"}], "mta": {"type": "mtaClick", "eventId": "HotelRN_List_MainFilter", "format": [{"setKey": "filterPanelCode", "formatValue": "value"}, {"setKey": "filterPanelName", "formatValue": "label"}], "pick": ["filterPanelCode", "filterPanelName", "eventId", "index", "orderType", "sortType", "groupCode"]}}, "onChange": {"keys": ["setDeepMergeFilter"], "format": [{"setKey": ["searchInfo", "page"], "value": 1}, {"setKey": ["resInfo", "naturalCardVOList"], "value": []}, {"setKey": "loading", "value": true}, {"setKey": ["searchInfo", "extMap"], "value": false}], "pick": ["filterList", "resInfo", "loading", "searchInfo", "filterMetaData", "distanceInfo", "showPopUp"]}, "onOk": {"keys": ["setDeepMergeFilter"], "format": [{"formatValue": "value", "setKey": "showPopUp"}, {"setKey": ["searchInfo", "page"], "value": 1}, {"setKey": ["resInfo", "naturalCardVOList"], "value": []}, {"setKey": "loading", "value": true}], "pick": ["showPopUp"], "mta": {"type": "mtaClick", "eventId": "HotelRN_List_MainFilterConfirm", "pick": ["searchInfo", "itemList", "filterPanelCode", "filterPanelName", "groupCode"]}}, "onOrderTypeChange": {"keys": ["setPopupInfo"], "format": [{"formatValue": "value"}], "mta": {"type": "mtaClick", "eventId": "HotelRN_List_Order", "format": [{"formatValue": "filterValue", "setKey": "itemList"}], "pick": ["itemList", "orderType", "sortType"]}, "pick": ["showPopUp"]}, "onClear": {"mta": {"type": "mtaClick", "eventId": "HotelRN_List_FilterClear"}}}}, "propsPaths": {"value": ["storage", "filterMetaData"], "options": ["storage", "resInfo", "filterPanelVOList"], "data": ["storage", "resInfo", "outsideFilterPanelVOList"], "showFilterType": ["storage", "showPopUp"], "address": ["storage", "resInfo", "selectedAreaId"], "resInfo": ["storage", "resInfo", "countSummaryVO", "resultCountShowText"], "sortType": ["storage", "searchInfo", "sortType"], "orderType": ["storage", "searchInfo", "orderType"], "commonUserAction": ["storage", "resInfo", "commonUserAction"], "distanceInfo": ["storage", "distanceInfo"]}}, {"type": "skeleton", "propsPaths": {"loading": ["storage", "loading"]}}, {"type": "list", "props": {"keyboardShouldPersistTaps": "handle", "decelerationRate": 0.985, "windowSize": 21, "initialNumToRender": 10, "scrollEventThrottle": 16, "onEndReachedThreshold": 0.2, "eventTypes": {"onChange": {"keys": ["loadNextPage"]}, "noShareRoomClick": {"keys": "noShareRoomClick"}, "mtaClick": {"keys": "mtaClick"}, "onClear": {"keys": ["setShareInfo"], "format": [{"setKey": ["searchInfo", "page"], "value": 1}, {"setKey": ["searchInfo", "filterList"], "formatValue": ["value"]}, {"setKey": ["filterMetaData"], "formatValue": ["metaData"]}, {"setKey": "loading", "value": true}, {"setKey": ["resInfo", "naturalCardVOList"], "value": []}, {"setKey": ["searchInfo", "extMap"], "value": false}], "pick": ["filterList", "searchInfo", "filterMetaData", "loading", "resInfo", "distanceInfo"]}, "onClick": {"keys": ["toPage"]}, "onImgClick": {"keys": ["toPage"]}, "setPopupInfo": {"keys": ["setPopupInfo"]}}, "keyPath": ["hotelCardVO", "id"]}, "propsPaths": {"isTravel": ["storage", "isTravel"], "description": ["storage", "description"], "fetchError": ["storage", "fetchError"], "loading": ["storage", "loading"], "distanceInfo": ["storage", "distanceInfo"], "data": ["storage", "resInfo", "naturalCardVOList"], "hasNextPage": ["storage", "resInfo", "hasNextPage"], "recommendData": ["storage", "resInfo", "recommendCardVOList"], "matchList": ["storage", "filterMetaData"], "id": ["storage", "activeInfo"], "page": ["storage", "searchInfo", "page"]}}, {"type": "webView", "propsPaths": {"show": ["storage", "showLocation"], "uri": ["storage", "url"]}}, {"type": "popup", "props": {"eventTypes": {"onClose": {"keys": ["setWebShareInfo"]}, "toPage": {"keys": ["discountToPage"]}}}, "propsPaths": {"showPopup": ["storage", "showPopUp"], "cardInfo": ["storage", "cardInfo"], "description": ["storage", "description"]}}, {"type": "dialog"}]}
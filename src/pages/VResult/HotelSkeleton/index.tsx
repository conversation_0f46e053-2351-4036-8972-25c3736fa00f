import {E_TAB} from '@/pages/result/store/result.model'
import Skeleton from '@/pages/result/widgets/FullLoading/Skeleton'
import {skeletonImgs, skeletonStyle} from '@/pages/result/widgets/FullLoading/skeletonUtils'
import {Dimensions} from 'react-native'
import styles from './index.module.scss'

export default function HotelSkeleton(props) {
    const {loading} = props

    const {height} = Dimensions.get('window')

    return <Skeleton
        className={styles.wr}
        style={{height}}
        useCustomStyle={true}
        loading={loading}
        skeletonStyle={skeletonStyle[E_TAB.Hotel]}
        skeletonImg={skeletonImgs[E_TAB.Hotel]}
    />
}

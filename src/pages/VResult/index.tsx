import Taro from '@tarojs/taro'
import {View} from '@/BaseComponents/atoms'
import withPage, {BasePageContext} from '@/common/withPage'
import styles from './index.module.scss'
import Scene from '@/Components/Scene.js'
import {extendsMapper} from '@/Components/index'
import SearchBar from './searchBar'
import SortBar from './SortBar/index'
import {useCallback, useContext, useEffect, useRef, useState} from 'react'
import List from './List/index'
import {is, Map} from 'immutable'
import {mergeForStorage, updateForStorageByObj, mergeDeepForStorage} from '@/Components/utils/index'
import NoMatches from './NoMatches'
import CatchAsyncData from '@/Components/CatchAsyncData'
import {getEnvCallBack, shareDataByKey, setShareData} from '@/utils/bridge'
import hasLogin, {doLogin} from '@/common/Login'
import WebView from './WebView'
import useJumpTo from '@/common/useJumpTo'
import useFetch from '@/common/useFetch'
import {showToast} from '@/BaseComponents/atoms/utils/toast'
import LRUCatch from '@/Components/utils/lru'
import localStorage from '@/utils/LocalStorage'
import {FORM_SOURCE} from './constance'
import {findInListKey} from '@/Components/Filter/utils'
import Dialog, {dialog} from '@/pages/VResult/Dialog'

import {
    safeJsonParse,
    deCodeDebBase64ParseSafe,
    enCodeDebBase64ParseSafe,
    isSameWithByKey,
    checkValidDate,
    getDefaultDate,
    isStrValue,
    isObjValue,
    isDateValue
} from '@/Components/utils'
import _ from 'lodash'
import defaultSchema from './defaultSchema.json'
import {getBaseInfo} from '@/common/hotelBaseInfo'
import HotelSkeleton from './HotelSkeleton'

import {newMta, mtaPv, M_EVENTID, mtaEp} from '@/common/mta'
import IdleQueue from '@/utils/IdleQueue'
import {reportInfo} from '@/common/reporter'
import {errorCodeConstantMapping, ErrortType} from '@/common/reporter/errorMapping'
import {isWeb} from '@/common/common'
import DiscountPopup from './DiscountPop'
import {isEmpty} from '@/utils/isType'
import {location, window} from '@tarojs/runtime'

extendsMapper({
    searchBar: SearchBar,
    sortBar: SortBar,
    list: List,
    tagList: NoMatches,
    webView: WebView,
    skeleton: HotelSkeleton,
    popup: DiscountPopup,
    dialog: Dialog
})

// const setHistory = (params) => {
//     localStorage.getItem('processCatch').then(processData => {
//         const {processId} = params
//         const _LRUCatch = new LRUCatch({defaultCatch: Array.isArray(processData) ? processData : []})
//         const {list} = _LRUCatch.put(processId, params, 'processId')
//         localStorage.setItem('processCatch', list)
//     }).catch(error => {
//         const {processId} = params
//         const _LRUCatch = new LRUCatch({defaultCatch: []})
//         const {list} = _LRUCatch.put(processId, params, 'processId')
//         localStorage.setItem('processCatch', list)
//     })
// }

const VResult = (props) => {
    const { hotelSearchData = {}, schema = {}, isTravel, travelStandard, shareTravelStandard } = props
    const { filterMetaData, searchInfo } = hotelSearchData
    const [isShowToast, setIsShowToast] = useState(false)
    const [shareHotelScene, setShareHotelScene] = useState('')

    const jumpTo = useJumpTo()
    const {apiFetch} = useFetch()
    const storageRef = useRef(null)
    const [storage, setStorage] = useState(Map({
        searchInfo: searchInfo,  // 搜索条件
        resInfo: {  // 结果信息
            naturalCardVOList: []
        },
        showPopUp: '',  // 显示弹窗
        loading: true,  // 是否loading
        filterMetaData, // 筛选信息
        showLocation: false, // 是否显示webview
        fetchError: false, // 请求异常
        distanceInfo: {itemName: undefined}, // 是否有位置距离信息
        needUpdateDate: false, // 是否需要日期纠偏
        cardInfo: false, // 优惠弹层信息
        isTravel: _.get(hotelSearchData, ['searchInfo', 'fromSource']) === FORM_SOURCE.TRAVEL_PUBLIC
    }))

    // 注册切后台
    const {registerDidShow, offDidShow} = useContext(BasePageContext)

    // 切后台或者回到页面的操作
    const didShow = useCallback(async () => {

        if (isWeb) {
            return
        }

        // 获取基础信息
        const baseInfo = await getBaseInfo({needFixDate: false})

        // 组装基础信息
        const hotelBaseSearchParam = {
            ...baseInfo,
            'roomNum': _.get(baseInfo, 'roomNum.value', 1),
            'grownNum': _.get(baseInfo, 'adultNum.value', 1),
            'childrenNum': _.get(baseInfo, 'childNum.value', 0),
            'childrenAges': _.get(baseInfo, 'childNum.age', [])
        }

        // 检查日期
        if (!checkValidDate(_.get(baseInfo, ['checkInDate']))) {
            let _storage = _.get(storageRef, 'current')
            setStorage(updateForStorageByObj(_storage, {needUpdateDate: true}))
            // 3秒后纠偏
            const timer = setTimeout(() => {
                const _hotelBaseSearchParam = Object.assign(hotelBaseSearchParam, getDefaultDate())

                setStorage(updateForStorageByObj(storage, {
                    searchInfo: {
                        page: 1,
                        hotelBaseSearchParam: _hotelBaseSearchParam
                    },
                    loading: true,
                    resInfo: {
                        naturalCardVOList: []
                    },
                    needUpdateDate: false,
                    filterMetaData: []
                }))

                if (!isTravel) {
                    setShareData({hotelSearchData: _storage.getIn(['searchInfo'])})
                }

                clearTimeout(timer)
            }, 1000)

            return
        }

        const {mta = {}} = schema
        const {pageId, pageName} = _.get(mta, 'params')

        mtaPv(pageId, pageName, {
            pvid: _.get(props, 'basePageInfo.pvId', -100),
            logid: _.get(props, 'basePageInfo.pvId', -100),
            search_fouraddrid: _.get(props, 'hotelSearchData.searchInfo.posAreaId', -100),
            keyword: _.get(props, 'hotelSearchData.searchInfo.keyword', -100),
            displayName: _.get(props, 'hotelSearchData.searchInfo.keyword', -100),
            checkOutDate: _.get(props, 'hotelSearchData.searchInfo.hotelBaseSearchParam.checkOutDate', -100),
            checkInDate: _.get(props, 'hotelSearchData.searchInfo.hotelBaseSearchParam.checkInDate', -100),
            fromSource: _.get(props, 'hotelSearchData.searchInfo.fromSource', -100),
            search_o2o_coordinates: `${_.get(props, 'hotelSearchData.searchInfo.longitude', -100)},${_.get(props, 'hotelSearchData.searchInfo.latitude', -100)}`
        })

        // 获取其他信息
        shareDataByKey('hotelSearchData').then(searchDataRes => {
            let _storage = _.get(storageRef, 'current')
            const _hotelSearchData = safeJsonParse(searchDataRes)

            const setInfo = {
                ..._hotelSearchData,
                hotelBaseSearchParam
            }

            const paths = [
                'keyword',
                'hotelBaseSearchParam.checkInDate',
                'hotelBaseSearchParam.checkOutDate',
                'hotelBaseSearchParam.roomNum',
                'hotelBaseSearchParam.grownNum',
                'hotelBaseSearchParam.childrenNum',
                'hotelBaseSearchParam.childrenAges',
                'mddInfo.type',
                'mddInfo.showName',
                'mddInfo.latitude',
                'mddInfo.longitude',
                'mddInfo.province',
                'mddInfo.city',
                'mddInfo.county',
                'mddInfo.street',
                'mddInfo.level',
                'extMap.searchItemId',
                'latitude',
                'longitude',
                'posAreaId'
            ]
            // 对比信息是否变更
            const isSame = isSameWithByKey(_storage?.getIn(['searchInfo']), setInfo, paths)
            // 未变更不刷新
            if (isSame) {
                return
            }
            reportInfo({
                code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_REFRESH,
                errorDetail: {
                    errorType: ErrortType.Info,
                    customMsg: {
                        compName: '酒店返回刷新页面接口了',
                        errorInfo: {
                            storage: _storage?.getIn(['searchInfo']),
                            newValue: setInfo
                        }
                    }
                }
            })
            // 回到第一页
            _.set(setInfo, ['page'], 1)

            if (!isTravel) {
                setShareData({hotelSearchData: setInfo})
            }

            // 当距离位置发生变更后，删除筛选信息
            if (_storage.getIn(['searchInfo', 'posAreaId']) !== _.get(_hotelSearchData, 'posAreaId')) {
                _storage = updateForStorageByObj(_storage, {
                    searchInfo: {
                        filterList: []
                    },
                    filterMetaData: []
                })
            }

            // 更新查询信息
            setStorage(updateForStorageByObj(_storage, {
                searchInfo: _.omit(setInfo, 'filterMetaData'),
                filterMetaData: _.get(setInfo, 'filterMetaData', []),
                resInfo: {
                    naturalCardVOList: [],
                    outsideFilterPanelVOList: []
                },
                loading: true,
                page: 1
            }))
        }).catch(error => {
            console.warn(error)
        })
    }, [])

    // 拼房条件下web title修改为 '发起拼房'
    useEffect(() => {
        if (!isWeb) return;
        const queryMode = _.get(searchInfo, 'queryMode', '');
        const title = ['1', '2'].includes(queryMode) ? '发起拼房' : '酒店列表';
        Taro.setNavigationBarTitle({ title });
      }, [isWeb, searchInfo?.queryMode]); 

    useEffect(() => {
        // 检查日期
        const {hotelBaseSearchParam} = storage.getIn(['searchInfo'])
        const {checkInDate} = hotelBaseSearchParam
        if (!checkValidDate(checkInDate)) {
            setStorage(updateForStorageByObj(storage, {needUpdateDate: true}))
            const timer = setTimeout(() => {
                const _hotelBaseSearchParam = Object.assign(hotelBaseSearchParam, getDefaultDate())

                setStorage(updateForStorageByObj(storage, {
                    searchInfo: {
                        page: 1,
                        hotelBaseSearchParam: _hotelBaseSearchParam
                    },
                    loading: true,
                    resInfo: {
                        naturalCardVOList: []
                    },
                    needUpdateDate: false,
                    filterMetaData: []
                }))

                if (!isTravel) {
                    setShareData({hotelSearchData: storage.getIn(['searchInfo'])})
                }
                clearTimeout(timer)
            }, 1000)
        }

        registerDidShow(didShow)

        const {mta = {}} = schema
        const {pageId, pageName} = _.get(mta, 'params')

        mtaPv(pageId, pageName, {
            pvid: _.get(props, 'basePageInfo.pvId', -100),
            logid: _.get(props, 'basePageInfo.pvId', -100),
            search_fouraddrid: _.get(props, 'hotelSearchData.searchInfo.posAreaId', -100),
            keyword: _.get(props, 'hotelSearchData.searchInfo.keyword', -100),
            displayName: _.get(props, 'hotelSearchData.searchInfo.keyword', -100),
            checkOutDate: _.get(props, 'hotelSearchData.searchInfo.hotelBaseSearchParam.checkOutDate', -100),
            checkInDate: _.get(props, 'hotelSearchData.searchInfo.hotelBaseSearchParam.checkInDate', -100),
            fromSource: _.get(props, 'hotelSearchData.searchInfo.fromSource', -100),
            search_o2o_coordinates: `${_.get(props, 'hotelSearchData.searchInfo.longitude', -100)},${_.get(props, 'hotelSearchData.searchInfo.latitude', -100)}`
        })

        return () => {
            offDidShow(didShow)
        }
    }, [])

    useEffect(() => {
        if (storage) {
            storageRef.current = storage
        } else {
               // 添加监控埋点
               reportInfo({
                code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_STORAGE_NOT_FOUND,
                errorDetail: {
                    errorType: ErrortType.Info,
                    customMsg: {
                        compName: 'useEffect storage不存在',
                        errorInfo: 'storage 不存在'
                    }
                }
            })
        }
    }, [storage])

    const count = useRef(0)


    useEffect(() => {
        count.current++
        const _count = count.current
        const searchInfo = storage.getIn(['searchInfo'])
        const extMap = _.get(searchInfo, ['extMap'], {})

        const fetchParams = _.omit({
            ...searchInfo,
            ...extMap,
            ...(!!shareHotelScene && shareHotelScene.length > 0 && { shareHotelScene }) 
        }, ['extMap', 'lbs_city', 'filterMetaData', 'searchInfo'])

        const searchInfoQueryMode = _.get(searchInfo, ['queryMode'], '')
        if (searchInfoQueryMode === '1' || searchInfoQueryMode === '2') {
            fetchParams.travelStandard = shareTravelStandard
        } else {
            fetchParams.travelStandard = travelStandard
        }

        reportInfo({
            code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_FETCH_PARAMS,
            errorDetail: {
                errorType: ErrortType.Info,
                customMsg: {
                    compName: '垂搜最终请求参数',
                    errorInfo: JSON.stringify(fetchParams)
                }
            }
        })

        const promisList = isTravel ? [apiFetch('HOTEL_LIST', fetchParams, true), apiFetch('GET_DUCC', {keys: ['official_standard_description']}, true)] : [apiFetch('HOTEL_LIST', fetchParams, true)]
        Promise.all(promisList).then(([[code, res], res2]) => {
            if (_count !== count.current)  {
                return
            }
                
            const _storage = _.get(storageRef, 'current')

            if (+code === 0) {
                // 处理正常
                const resInfo = _.get(res, 'result')
                // 增加埋点公参
                _.set(resInfo, ['commonUserAction', 'displayName'], _.get(resInfo, ['commonUserAction', 'keyword']))
                _.set(resInfo, ['commonUserAction', 'businessType'], '1')

                if (isShowToast && isEmpty(resInfo?.naturalCardVOList)) {
                    showToast({
                        title: '没有找到匹配的结果，请修改筛选条件试试',
                        icon: 'none',
                        duration: 2000
                    })
                }

                if (isShowToast) {
                    setIsShowToast(false)
                }

                const naturalCardVOList = _.get(resInfo, 'naturalCardVOList', [])
                // 差旅 && 第一页数据
                if (isTravel && resInfo && resInfo.curPage === 1) {
                    //  获取拼房展示数据
                    const shareHotelTip = _.get(resInfo, 'shareHotelTip', {})
                    // 第一页获取shareHotelScene, 将shareHotelScene作为请求入参带入下次请求
                    const shareHotelScene = _.get(resInfo, 'shareHotelScene', '')
                    if (!!shareHotelScene && shareHotelScene.length > 0) {
                        setShareHotelScene(shareHotelScene)
                    }
                    if (!!shareHotelTip && shareHotelTip?.show) {
                        if (shareHotelScene === '2' && naturalCardVOList.length > 0) { 
                            naturalCardVOList.unshift({ cardType: 'NoRoomShareLowPrice', shareHotelTip: shareHotelTip })
                        } else if (shareHotelScene === '3') {
                            naturalCardVOList.unshift({ cardType: 'NoRoomShare', shareHotelTip: shareHotelTip })
                        } 
                    }
                    // 差旅[出差必看], 插在如数据的第一位
                    if (naturalCardVOList.length > 0) {
                        naturalCardVOList.unshift({ cardType: 'InfoTips', id: 'info_tips' })
                    }
                }

                // 搜索结果曝光
                const {mta = {}} = schema
                const {pageId, pageName} = _.get(mta, 'params')
                IdleQueue.add(mtaEp, 'HotelRN_List_ResultExpo', pageId, pageName, _.get(resInfo, ['commonUserAction']))

                setStorage(mergeForStorage(_storage, {
                    resInfo: {
                        ...resInfo,
                        naturalCardVOList: _storage.getIn(['resInfo', 'naturalCardVOList'], []).concat(naturalCardVOList),
                        outsideFilterPanelVOList: resInfo?.outsideFilterPanelVOList
                    },
                    loading: false,
                    fetchError: false,
                    description: _.get(res2, [1, 'result', 'configMap', 'official_standard_description'], ''),
                    page: _.get(resInfo, 'curPage', _.get(searchInfo, 'page'))
                }))
            } else {
                const errorInfo = {}
                const resData = _storage.getIn(['resInfo', 'naturalCardVOList'])
                if (Array.isArray(resData) && resData.length > 0) errorInfo.fetchError = true
                setStorage(updateForStorageByObj(_storage, {
                    ...errorInfo,
                    loading: false,
                    resInfo: {
                        hasNextPage: false
                    }
                }))
            }
        }).catch((error) => {
            const _storage = _.get(storageRef, 'current')
             // 添加监控埋点
             reportInfo({
                code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_STORAGE_PROMISE_ERROR,
                errorDetail: {
                    errorType: ErrortType.Info,
                    customMsg: {
                        compName: '垂搜接口Promise catch',
                        error,
                        storageIsEmpty: !_storage ? '空' :'非空' 
                    }
                }
            })

            const errorInfo = {}
            const resData = _storage.getIn(['resInfo', 'naturalCardVOList'])
            if (Array.isArray(resData) && resData.length > 0) errorInfo.fetchError = true
            setStorage(updateForStorageByObj(_storage, {
                ...errorInfo,
                loading: false,
                resInfo: {
                    hasNextPage: false
                }
            }))
             // 添加监控埋点
             reportInfo({
                code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_STORAGE_PROMISE_ERROR,
                errorDetail: {
                    errorType: ErrortType.Info,
                    customMsg: {
                        compName: '垂搜接口Promise catch',
                        error,
                        storageIsEmpty: !_storage ? '空' :'非空' 
                    }
                }
            })

        })
    }, [storage?.getIn(['searchInfo'])])

    return (
        <View className={styles.wrapper}>
            <Scene schema={schema.schema} storage={storage} actions={{
                loadNextPage: () => {
                    setStorage(updateForStorageByObj(storage, {
                        searchInfo: {
                            page: ~~storage.getIn(['searchInfo', 'page']) + 1
                        },
                        fetchError: false,
                        resInfo: {
                            hasNextPage: true
                        }
                    }, ''))
                },
                noShareRoomClick: () => {
                    const newStorgae = updateForStorageByObj(storage, {
                        searchInfo: {
                            // queryMode 切换
                            queryMode: '4',
                            // 差标切换
                            travelStandard: travelStandard,
                            // 清空筛选入参
                            filterList: []
                        },
                        resInfo: {
                            // 列表数组清空
                            naturalCardVOList: []
                        },
                        // 清空筛选项目
                        filterMetaData: [],
                        // 展示占位
                        loading: true
                    })

                    _.set(newStorgae, ['searchInfo', 'page'], 1)
                    // 刷接口
                    setStorage(newStorgae)
                },
                setPopupInfo: (showPopUp) => {
                    const curShowPopUp = storage.getIn(['showPopUp'])

                    if (showPopUp === 'describe') {
                        const {mta = {}} = schema
                        const {pageId, pageName} = _.get(mta, 'params')
                        IdleQueue.add(newMta, 'HotelRN_List_TravelInfo', pageId, pageName, storage.getIn(['resInfo', 'commonUserAction']))
                    }

                    setStorage(mergeForStorage(storage, {showPopUp: showPopUp === curShowPopUp ? false : showPopUp}))
                },
                setDeepMerge: (value) => {
                    const newData = updateForStorageByObj(storage, value)

                    // 条件发生变化，强制回到第一页
                    _.set(newData, ['searchInfo', 'page'], 1)

                    reportInfo({
                        code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_DEEP_MERGE_DATA,
                        errorDetail: {
                            errorType: ErrortType.Info,
                            customMsg: {
                                compName: '酒店垂搜DEEPMARGE参数',
                                errorInfo: newData
                            }
                        }
                    })
                    setStorage(newData)

                    // 更新缓存
                    if (isTravel) {
                        // setHistory({
                        //     processId,
                        //     searchInfo: {
                        //         ...newData.getIn(['searchInfo']),
                        //         filterMetaData: newData.getIn(['filterMetaData'])
                        //     }
                        // })
                    } else {
                        setShareData({
                            hotelSearchData: {
                                ...newData.getIn(['searchInfo']),
                                filterMetaData: newData.getIn(['filterMetaData'])
                            }
                        }).catch(console.warn)
                    }
                },
                // 需要单独判断filter，从deepmarge拆出来。做特殊处理
                setDeepMergeFilter: (value) => {
                    const newData = updateForStorageByObj(storage, value)
                    // 条件发生变化，强制回到第一页
                    _.set(newData, ['searchInfo', 'page'], 1)

                    reportInfo({
                        code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_DEEP_MERGE_DATA,
                        errorDetail: {
                            errorType: ErrortType.Info,
                            customMsg: {
                                compName: '酒店垂搜DEEPMARGE参数',
                                errorInfo: newData
                            }
                        }
                    })
                    setIsShowToast(true)
                    setStorage(newData)

                    // 更新缓存
                    if (isTravel) {
                        // setHistory({
                        //     processId,
                        //     searchInfo: {
                        //         ...newData.getIn(['searchInfo']),
                        //         filterMetaData: newData.getIn(['filterMetaData'])
                        //     }
                        // })
                    } else {
                        setShareData({
                            hotelSearchData: {
                                ...newData.getIn(['searchInfo']),
                                filterMetaData: newData.getIn(['filterMetaData'])
                            }
                        }).catch(console.warn)
                    }
                },
                setShareInfo: ({shareData, ...other}) => {
                    const newData = updateForStorageByObj(storage, other)

                    // 条件发生变化，强制回到第一页
                    _.set(newData, ['searchInfo', 'page'], 1)

                    setStorage(newData)
                    reportInfo({
                        code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_SET_SHARE_INFO_DATA,
                        errorDetail: {
                            errorType: ErrortType.Info,
                            customMsg: {
                                compName: '酒店垂搜SETSHAREINFO参数',
                                errorInfo: newData
                            }
                        }
                    })

                    // 更新缓存
                    if (isTravel) {
                        // setHistory({
                        //     processId,
                        //     searchInfo: {
                        //         ...newData.getIn(['searchInfo']),
                        //         filterMetaData: newData.getIn(['filterMetaData'])
                        //     }
                        // })
                    } else {
                        setShareData({
                            hotelSearchData: {
                                ...newData.getIn(['searchInfo']),
                                filterMetaData: newData.getIn(['filterMetaData'])
                            }
                        }).catch(console.warn)
                    }
                },
                setWebShareInfo: (changeData) => {
                    const newData = updateForStorageByObj(storage, changeData)
                    setStorage(newData)
                },
                openLocation: getEnvCallBack({
                    rn: ({url, showPopUp}) => {
                        jumpTo({to: 'web', params: {url}})
                        setStorage(mergeForStorage(storage, {
                            showPopUp
                        }))
                        // 这个是测试
                        // JDJumping.jumpToOpenapp('openapp.jdmobile://virtual?params={"category":"jump","des":"jdreactdebug","url":"http://127.0.0.1:8088/jsbundles/JDReactHotel.bundle?platform=ios&dev=true","mode":"0","params":{}}');
                    },
                    web: ({url}) => setStorage(mergeForStorage(storage, {
                        showLocation: true,
                        url
                    }))
                }),
                openSearch: () => {
                    // 去中间页处理参数，因为还得带searchInfo回跳。
                    const searchInfo = storage.getIn(['searchInfo'])
                    searchInfo.isFromVResult = true

                    window.location.replace(`/search/search?searchInfo=${enCodeDebBase64ParseSafe(_.pick(searchInfo, [
                        'fromSource',
                        'keyword',
                        'priceChannel',
                        'channelId',
                        'hotelBaseSearchParam',
                        'mddInfo',
                        'latitude',
                        'longitude',
                        'posAreaId',
                        'businessType',
                        'travelStandard',
                        'shareTravelStandard',
                        'travelInfo',
                        // 将queryMode传递给中间页
                        'queryMode',
                        'distance',
                        'isFromVResult'
                    ]))}`)
                },
                discountToPage: async ({cardProps, index, trigger, eventId, tagInfo}) => {
                    const {mta = {}} = schema
                    const {pageId, pageName} = _.get(mta, 'params')

                    const getTagList = (data) => {
                        const keys = ['hotelRightPromotion']
                        const promotionTagListMap = data?.promotionTagListMap || {}
                        const result = []
                        for (const key in promotionTagListMap) {
                            if (Object.prototype.hasOwnProperty.call(promotionTagListMap, key)) {
                                if (keys.includes(key)) {
                                    const ele = promotionTagListMap[key] || []
                                    ele.forEach(item => {
                                        result.push({
                                            trackId: item?.trackId || '-100',
                                            labelName: item?.listShowName || '-100'
                                        })
                                    })
                                }
                            }
                        }
                        return result.length ? result : '-100'
                    }
                    
                    // 获取优惠文本
                    const discountItemCount = _.get(cardProps, 'discountItemCount', 0)
                    const discountPrice = _.get(cardProps, 'discountPrice', '')
                    const discountText = discountItemCount ? `${discountItemCount}项优惠${discountPrice}` : `优惠${discountPrice}`
                    const discountList = _.get(cardProps, ['promotionLayerVO', 'promotionDetailList'], -100)
                    
                    const mtaInfo = {
                        ...storage.getIn(['resInfo', 'commonUserAction']),
                        hotelId: _.get(cardProps, 'id', -100),
                        score: _.get(cardProps, 'promotionTagListMap.hotelScore[0].listShowName', -100),
                        displayName: _.get(cardProps, 'name', -100),
                        index,// 位置 number
                        firpricetype: '11', // 第一价格分类 string
                        firprice: _.get(cardProps, 'price', -100), // 第一价格金额 number
                        secpricetype: '52', // 第二价格类型 string
                        secprice: _.get(cardProps, 'originPrice', -100), //第二价格金额 number
                        tagList: getTagList(cardProps),
                        discountType: Array.isArray(discountList) ? discountList.map(item => item.type) : -100,
                        rankId: _.get(cardProps, 'wareRankVO.rankId', '-100'),
                        promotionName: _.get(tagInfo, 'name', discountText), // 优先使用tagInfo中的name，否则使用构建的discountText
                        ..._.get(cardProps, ['userAction'], {})
                    }

                    IdleQueue.add(newMta, 'HotelRN_List_DiscountLayer_Order', pageId, pageName, mtaInfo)
                    const isLogin = await hasLogin()
                    if (!isLogin) {
                        const logged = await doLogin()
                        if (!logged) {
                            setStorage(mergeForStorage(storage, {page: 1}))
                            return
                        }
                    }

                    const jumpUrl = _.get(cardProps, ['jumpUrl'])

                    setStorage(mergeForStorage(storage, {
                        showPopUp: false,
                        cardInfo: {}
                    }))

                    if (jumpUrl) {
                        jumpTo({to: 'web', params: {url: decodeURIComponent(jumpUrl)}})
                    }
                },
                toPage: ({cardProps, index, trigger, eventId, tagInfo}) => {
                    const {mta = {}} = schema
                    const {pageId, pageName} = _.get(mta, 'params')
                    const {unAgreementHotelToastVO} = cardProps
                    const _toPage = async () => {
                        const getTagList = (data) => {
                            const keys = ['hotelRightPromotion']
                            const promotionTagListMap = data?.promotionTagListMap || {}
                            const result = []
                            for (const key in promotionTagListMap) {
                                if (Object.prototype.hasOwnProperty.call(promotionTagListMap, key)) {
                                    if (keys.includes(key)) {
                                        const ele = promotionTagListMap[key] || []
                                        ele.forEach(item => {
                                            result.push({
                                                trackId: item?.trackId || '-100',
                                                labelName: item?.listShowName || '-100'
                                            })
                                        })
                                    }
                                }
                            }
                            return result.length ? result : '-100'
                        }
                        const discountList = _.get(cardProps, ['promotionLayerVO', 'promotionDetailList'], -100)

                        const discountItemCount = _.get(cardProps, 'discountItemCount', 0)
                        const discountPrice = _.get(cardProps, 'discountPrice', '')
                        const discountText = discountItemCount ? `${discountItemCount}项优惠${discountPrice}` : `优惠${discountPrice}`
                        
                        const mtaInfo = {
                            ...storage.getIn(['resInfo', 'commonUserAction']),
                            hotelId: _.get(cardProps, 'id', -100),
                            score: _.get(cardProps, 'promotionTagListMap.hotelScore[0].listShowName', -100),
                            displayName: _.get(cardProps, 'name', -100),
                            index,// 位置 number
                            firpricetype: '11', // 第一价格分类 string
                            firprice: _.get(cardProps, 'price', -100), // 第一价格金额 number
                            secpricetype: '52', // 第二价格类型 string
                            secprice: _.get(cardProps, 'originPrice', -100), //第二价格金额 number
                            tagList: getTagList(cardProps),
                            discountType: Array.isArray(discountList) ? discountList.map(item => item.type) : -100,
                            rankId: _.get(cardProps, 'wareRankVO.rankId', '-100'),
                            ..._.get(cardProps, ['userAction'], {}),
                            promotionName: _.get(tagInfo, 'name', discountText), // 优先使用tagInfo中的name，否则使用构建的discountText
                        }

                        if (trigger === 'discount' && _.get(cardProps, ['promotionLayerVO'])) {
                            // 优惠层点击
                            IdleQueue.add(newMta, 'HotelRN_List_Discount', pageId, pageName, mtaInfo)
                            setStorage(mergeForStorage(storage, {
                                showPopUp: trigger,
                                cardInfo: {
                                    cardProps,
                                    index,
                                    trigger,
                                    eventId
                                }
                            }))
                            return
                        }

                        // 获取图片点击的腰带文案
                        const tagBeltCode = _.get(cardProps, ['beltInfoVO', 'beltCode'], '-100')
                        if (eventId === M_EVENTID.HotelRNListRecHotel) {
                            // 推荐图片点击
                            IdleQueue.add(newMta, trigger === 'img' ? M_EVENTID.HotelRNListRecHotelPic : M_EVENTID.HotelRNListRecHotel, pageId, pageName, {...mtaInfo, 'beltCode': tagBeltCode })
                        } else {
                            // 普通图片点击
                            IdleQueue.add(newMta, trigger === 'img' ? M_EVENTID.HotelRNListHotelPic : M_EVENTID.HotelRNListHotel, pageId, pageName, {...mtaInfo, 'beltCode': tagBeltCode})
                        }

                        const isLogin = await hasLogin()
                        if (!isLogin) {
                            const logged = await doLogin()
                            if (!logged) {
                                setStorage(mergeForStorage(storage, {page: 1}))
                                return
                            }
                        }
                        if (cardProps && cardProps?.jumpUrl) {
                            jumpTo({to: 'web', params: {url: decodeURIComponent(cardProps?.jumpUrl)}})
                        }
                    }

                    if (unAgreementHotelToastVO) {
                        dialog({
                            onOk: _toPage,
                            title: _.get(unAgreementHotelToastVO, ['title']),
                            content: _.get(unAgreementHotelToastVO, ['content']),
                            closeBtn: _.get(unAgreementHotelToastVO, ['cancelButton', 'text']),
                            okBtn: _.get(unAgreementHotelToastVO, ['confirmButton', 'text'])
                        })
                    } else {
                        _toPage()
                    }
                },
                mtaClick: (event) => {
                    const {mta = {}} = schema
                    const {eventId, eventData = {}} = event || {}
                    const {pageId, pageName} = _.get(mta, 'params')
                    IdleQueue.add(newMta, eventId, pageId, pageName, {
                        ...storage.getIn(['resInfo', 'commonUserAction']),
                        ...eventData
                    })
                },
                mtaExpo: (event) => {
                    const {mta = {}} = schema
                    const {eventId, eventData = {}} = event || {}
                    const {pageId, pageName} = _.get(mta, 'params')
                    IdleQueue.add(mtaEp, eventId, pageId, pageName, {
                        ...storage.getIn(['resInfo', 'commonUserAction']),
                        ...eventData
                    })
                }
            }}/>
        </View>
    )
}

const defaultShareInfo = {
    'fromSource': 'hotel_vertical',
    'keyword': '',  // 兜底空搜
    'priceChannel': '1000', // 兜底酒店1000
    'channelId': '-100',  // 兜底 -100
    'hotelBaseSearchParam': {
        ...getDefaultDate(),
        'roomNum': 1,
        'grownNum': 1,
        'childrenNum': 0,
        'childrenAges': []
    },
    'latitude': '39.855181',  // 位置兜底朝阳公园
    'longitude': '116.6812398',
    'posAreaId': '1,72,55674,0',
    'businessType': '1',
    'pageSize': 20,
    'page': 1,
    'virtualLocation': 1
}

// TODO mock差旅
// const defaultShareInfo = {
//     'fromSource': 'hotel_booking_public',
//     'queryMode': '',
//     'keyword': '',  // 兜底空搜
//     'priceChannel': '3020', // 兜底酒店1000
//     'channelId': '-100',  // 兜底 -100
//     'hotelBaseSearchParam': {
//         ...getDefaultDate(),
//         'roomNum': 1,
//         'grownNum': 1,
//         'childrenNum': 0,
//         'childrenAges': []
//     },
//     'latitude': '39.855181',  
//     'longitude': '116.563967',
//     'posAreaId': '1,1',
//     'businessType': '1',
//     'pageSize': 20,
//     'distance': '10000' ,
//     'page': 1,
//     'travelStandard': '0,300',
//     'shareTravelStandard': '0,400',
//     'travelInfo': {
//         'startDate': '2025-04-07',
//         'endDate': '2025-04-08',
//         'travelId': '112333',
//         'processInstanceId': 'abcdddddd'
//     },
//     'mddInfo': {
//         'type': '2',
//         'showName': '北京',
//         'latitude': '39.855181',
//         'longitude': '116.6812398',
//         'province': 1,
//         'city': 1,
//         'county': 55674,
//         'street': 0,
//         'level': 2
//     },
//     'virtualLocation': 1
// }


// 有效搜索信息验证
const checkSearchDataValid = (searchData = {}) => {
    const {mddInfo, latitude, longitude} = searchData
    const checkInDate = _.get(searchData, ['searchData', 'hotelBaseSearchParam', 'checkInDate'])
    const checkOutDate = _.get(searchData, ['searchData', 'hotelBaseSearchParam', 'checkOutDate'])
    return isStrValue(latitude) &&
        isStrValue(longitude) && // 有经纬度
        isDateValue(checkInDate) && // 有有效入住日期
        isDateValue(checkOutDate) && // 有有效离店日期
        isObjValue(mddInfo) // 有有效mddInfo
}

const formatShareInfo = (getData, isTravel: any) => {
    const defaultData = Map(formatForFilter(defaultShareInfo))
    const resData = mergeDeepForStorage(defaultData, getData).toJS()

    if (!isTravel && checkSearchDataValid(resData)) {
        // 格式化之后顺手记录
        setShareData({hotelSearchData: _.get(resData, ['searchInfo'])})
    }
    return resData
}

const formatForFilter = (data, filterMetaData) => {
    const singleList = {}
    _.get(data, ['filterList'], []).filter(item => {
        return item.filterKey !== 'gis_distance' && item.filterKey !== 'location_distance'
    }).forEach(item => {
        singleList[`${item.filterType}-${item.itemId}`] = item
    })
    const filterList = Object.values(singleList)
    _.set(data, ['filterList'], filterList)

    return {
        searchInfo: data,
        filterMetaData: Array.isArray(filterList) ? filterList.map(item => {
            return {
                sameKey: `${item.filterType}-${item.itemId}`,
                metaData: item,
                filterKey: item.filterKey || item.filterType
            }
        }) : []
    }
}

const formatForCLFilter = (data, filterMetaData) => {
    return {
        searchInfo: data,
        filterMetaData: Array.isArray(filterMetaData) ? filterMetaData : []
    }
}

VResult.preLoad = ({apiFetch, afterActions, ...other}) => new Promise((res, rej) => {

    // 从访问链接中获取hotelSearchData, 并decode
    const urlParams = deCodeDebBase64ParseSafe(_.get(other, 'urlParams.hotelSearchData'))

    // 获取差旅标识
    const isTravel = typeof _.get(urlParams, ['travelInfo', 'processInstanceId']) === 'string' && typeof _.get(urlParams, ['travelInfo', 'travelId']) === 'string'
    // 拼接 processInstanceId 和 travelId 得到 processId
    const processId = `${_.get(urlParams, ['travelInfo', 'processInstanceId'])}_${_.get(urlParams, ['travelInfo', 'travelId'])}`
    // 拼房标记
    const queryMode = _.get(urlParams, 'queryMode', '')
    // 正常差标
    const travelStandard = _.get(urlParams, 'travelStandard', '')
    // 拼房差标
    const shareTravelStandard = _.get(urlParams, 'shareTravelStandard', '')

    const isFromMidSearch = _.get(urlParams, 'isFromMidSearch', false)


    // 因公差旅操作
    if (isTravel) {
        if (checkSearchDataValid(urlParams)) {
            if (!isFromMidSearch) { // 非中间页来源 
                // 其他来源直接走url
                 res({
                    hotelSearchData: formatShareInfo(formatForCLFilter(_.omit(urlParams, ['filterMetaData']), undefined), isTravel),
                    schema: defaultSchema,
                    processId,
                    isTravel,
                    queryMode,
                    travelStandard,
                    shareTravelStandard
                })
                return
            }

            // 获取差旅缓存
            localStorage.getItem('processCatch').then(processData => {
                const {searchInfo} = findInListKey(processData, processId, 'processId')
                const storageData = formatForCLFilter(_.omit(searchInfo, ['filterMetaData']), searchInfo.filterMetaData)
                const endInfo = Map(storageData)
                const resData = mergeDeepForStorage(endInfo, urlParams).toJS()

                res({
                    hotelSearchData: formatShareInfo(resData, isTravel),
                    schema: defaultSchema,
                    processId,
                    isTravel,
                    queryMode,
                    travelStandard,
                    shareTravelStandard
                })
            }).catch(() => {
                // 获取缓存失败，走url参数
                res({
                    hotelSearchData: formatShareInfo(formatForCLFilter(_.omit(urlParams, ['filterMetaData']), undefined), isTravel),
                    schema: defaultSchema,
                    processId,
                    isTravel,
                    queryMode,
                    travelStandard,
                    shareTravelStandard
                })
            })
        } else {
            // alert('url错误初始化缓存')
            rej({
                hotelSearchData: formatShareInfo({}, isTravel),
                schema: defaultSchema,
                processId,
                isTravel,
                queryMode,
                travelStandard,
                shareTravelStandard
            })
        }
        return
    }

    // 酒旅垂搜 & 因私差旅操作
    reportInfo({
        code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_INIT_URL_PARAMS,
        errorDetail: {
            errorType: ErrortType.Info,
            customMsg: {
                compName: '酒店垂搜初始化获取URL参数',
                errorInfo: JSON.stringify(urlParams)
            }
        }
    })

    // url参数是否有效信息
    if (checkSearchDataValid(urlParams)) {
        res({
            hotelSearchData: formatShareInfo(formatForFilter(urlParams)),
            schema: defaultSchema
        })
    } else {
        reportInfo({
            code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_INIT_URL_PARAMS_ERROR,
            errorDetail: {
                errorType: ErrortType.Info,
                customMsg: {
                    compName: '酒店垂搜初始化获取URL参数失败',
                    errorInfo: JSON.stringify(urlParams)
                }
            }
        })
        // url参数无效，获取jdShare
        shareDataByKey('hotelSearchData').then(searchDataRes => {
            reportInfo({
                code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_INIT_FETCH_DATA_GET,
                errorDetail: {
                    errorType: ErrortType.Info,
                    customMsg: {
                        compName: '酒店垂搜初始化JDShareData参数',
                        errorInfo: JSON.stringify(searchDataRes)
                    }
                }
            })
            const _hotelSearchData = safeJsonParse(searchDataRes)
            // 校验jdShare数据是否有效
            if (checkSearchDataValid(_hotelSearchData)) {
                res({
                    hotelSearchData: formatShareInfo(formatForFilter(_hotelSearchData)),
                    schema: defaultSchema
                })
            } else {
                reportInfo({
                    code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_INIT_LOAD_JD_SHARE_DATA,
                    errorDetail: {
                        errorType: ErrortType.Info,
                        customMsg: {
                            compName: '酒店垂搜初始化获取SHARE异常',
                            errorInfo: 'JDShare参数没有通过校验'
                        }
                    }
                })
                // 都无效
                rej({
                    hotelSearchData: formatShareInfo({}),
                    schema: defaultSchema
                })
            }
        }).catch(error => {
            reportInfo({
                code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_INIT_LOAD_JD_SHARE_DATA,
                errorDetail: {
                    errorType: ErrortType.Info,
                    customMsg: {
                        compName: '酒店垂搜初始化获取SHARE异常',
                        errorInfo: '获取初始数据异常，走h5兜底方案'
                    }
                }
            })

            // 都无效
            rej({
                hotelSearchData: formatShareInfo({}),
                schema: defaultSchema
            })
        })
    }
})

VResult.displayName = 'VResult'
export default withPage({pageName: 'VResult'})(CatchAsyncData(VResult))

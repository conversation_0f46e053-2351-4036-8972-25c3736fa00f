@import "@/assets/theme.scss";

.dialogTitle {
    color: #000;
    font-size: 14px;
    text-align: center;
    font-weight: 500;
}

.dialogContent {
    color: var(----primaryTextColor);
    margin-top: 8px;
    font-size: 12px;
    text-align: left;
    font-weight: 500;
}

.dialogBtnContent {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 20px;
}

.commonBtn {
    border-radius: 6px;
    height: 35px;
    width: 125px;
}

.commonBtnText {
    text-align: center;
    line-height: 35px;
}

.cancelBtn {
    @extend .commonBtn;
    background-color: #fff;
    border-width: 1px;
    border-color: #013B94;

}

.cancelText {
    @extend .commonBtnText;
    color: #013B94;
}

.conformBtn {
    @extend .commonBtn;
    background-color: #013B94;
}

.conformText {
    @extend .commonBtnText;
    color: #fff;
}

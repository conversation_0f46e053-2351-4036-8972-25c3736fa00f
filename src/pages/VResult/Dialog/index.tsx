import {View, Text} from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import {pt} from '@ltfe/ltfe-core-lib/lib/utiles'
import Dialog from '@/BaseComponents/Dialog'
import React, {useEffect, useState} from 'react'
import {safeRun} from '@/Components/Filter/utils'
import _ from 'lodash'

const refs = {current: {}}
export const dialog = (props)=> {
    const popup = _.get(refs, ['current', 'popup'])
    if(_.isFunction(popup)) {
        popup(props)
    }
}

const _Dialog = () => {

    const [show, setShow] = useState(false)
    const [info, setInfo] = useState({
        onClose: ()=> {},
        onOk: ()=> {},
        title: ()=> {},
        cancelButton: "确认",
        confirmButton: "取消"
    })
    const {onClose, onOk, title, content, okBtn = "确认", closeBtn = "取消"} = info

    const {} = info

    useEffect(() => {
        refs.current.popup = (props)=> {
            setInfo(_.omit(props, ["show"]))
            setShow(_.get(props, ['show'], true))
        }
    }, [])

    return (
        <Dialog
            show={show}
            onClose={() => setShow(false)}
            dialogStyle={{padding: pt(20)}}
        >
            <View>
                <Text className={styles.dialogTitle}>{title}</Text>
                <Text className={styles.dialogContent}>{content}</Text>
                <View className={styles.dialogBtnContent}>
                    <View
                        className={styles.cancelBtn}
                        onClick={() => {
                            setShow(false)
                            safeRun(onClose)
                        }}
                    >
                        <Text className={styles.cancelText}>{closeBtn}</Text>
                    </View>
                    <View
                        className={styles.conformBtn}
                        onClick={() => {
                            setShow(false)
                            safeRun(onOk)
                        }}
                    >
                        <Text className={styles.conformText}>{okBtn}</Text>
                    </View>
                </View>
            </View>
        </Dialog>
    )
}

export default _Dialog

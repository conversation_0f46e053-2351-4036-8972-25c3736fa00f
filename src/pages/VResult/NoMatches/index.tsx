import {View, Image, Text} from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import classNames from 'classnames'
import {getImageUrl} from '@/assets/imgs'
import {isEmpty, isFunction} from '@/utils/isType'
import {memo, useContext, useEffect, useRef, useState} from 'react'
import {InView} from '@/BaseComponents/IntersectionObserver'
import {BasePageContext} from '@/common/withPage'
import EventCatch from '@/Components/EventCatch'
import {safeRun, arrayDiffByKey} from '@/Components/Filter/utils'
import _ from 'lodash'
import {getImg} from '@/Components/Filter/utils'

const NoMathces = (props) => {
    const {data, onClear, index, businessType, commonUserAction, mtaKey, mtaPage, mtaPageName, mtaClick} = props

    const [renderData, setRenderData] = useState([] as any)
    const [init, setInit] = useState(true)

    useEffect(() => {
        if (refIsClear.current) {
            refIsClear.current = false
            return
        }
        const _data = ([].concat(data)) as any
        // if (!isEmpty(_data)) {
        //     _data.unshift({
        //         metaData: {
        //             itemName: '清除全部',
        //             clearType: 'clear'
        //         },
        //         clear: true
        //     })
        // }
        setRenderData(_data)
    }, [data])

    const refIsClear = useRef(false)
    const onClearFn = (item, index) => {
        refIsClear.current = true

        const {clear} = item
        if (clear) {
            setRenderData([])
        } else {
            setRenderData(pre => {
                pre.splice(index, 1)
                return [...pre]
            })
        }
    }

    useEffect(() => {
        setInit(false)
    }, [])

    useEffect(() => {
        if (init) return
        let hasDistance = {distanceInfo: {itemName: undefined}}
        const isFilter = renderData.filter(item => {
            if (_.get(item, ['metaData', 'filterType']) === 'gis_distance') {
                hasDistance = {}
            }
            return item?.sameKey
        })

        if (arrayDiffByKey(isFilter, data) && arrayDiffByKey(data, isFilter)) {
            safeRun(onClear, {
                value: isFilter.map(item => item.metaData),
                metaData: isFilter,
                ...hasDistance
            })
        }
    }, [renderData])

    const basePageContext = useContext(BasePageContext)
    const getMtaParams = () => {
        const commonMtaParams = basePageContext.getCommonMtaParams()
        const params = {
            ...commonMtaParams,
            businessType,
            ...(commonUserAction || {})
        }
        return params
    }

    if (!data) {
        return null
    }
    return (
        <InView index={index} className={styles.wr} onChange={(visible) => {
            if (visible) {
                safeRun(props.mtaExpo, {
                    eventId: 'HotelRN_List_RecFilterExpo',
                    eventData: getMtaParams()
                })
            }
        }}>
            <View className={styles.delBox} onClick={() => {
                onClearFn({
                    clear: true
                }, 0)
            }}>
                <Image src={getImg('delIcon')} className={styles.delIcon}/>
                <Text className={styles.delWord}>清空</Text>
            </View>

            <View className={styles.txt}>
                <Image src={getImg('search')} className={styles.searchIcon}/>
                <Text className={styles.tipText}>
                    {/* 以上是符合条件的酒店，修改可重新查询 */}
                    没有更多符合条件的结果，请尝试修改条件重新查询
                </Text>
            </View>

            <View className={classNames('row wrap', styles.itemWr)}>

                {
                    renderData.map((item, index) => {
                        return (
                            <View
                                key={index}
                                className={classNames('row center', styles.item, item.clear && styles.clearItem)}
                                onClick={() => onClearFn(item, index)}
                            >
                                <Text className={styles.labelTxt}>{item?.metaData.itemName}</Text>
                                {
                                    !item.clear && (
                                        <View className={styles.clearIconWr}>
                                            <Image src={getImg('blueClose')} className={styles.clearIcon}/>
                                        </View>
                                    )
                                }
                            </View>
                        )
                    })
                }
            </View>

        </InView>
    )
}

export default EventCatch(memo(NoMathces))

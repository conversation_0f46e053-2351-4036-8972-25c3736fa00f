import { setShareData, shareDataByKey } from '@/utils/bridge';
import { stringifyValue, getJsonObjectOrRawValue } from '@/utils/Json';
import { isWeb } from '@/common/common';

// 历史记录的存储键
const HOTEL_FILTER_HISTORY_KEY = 'filterHistory_hotel_filter';


/**
 * 获取不重复的历史记录列表
 * @param list 历史记录列表
 * @param count 最大数量限制，默认为6
 * @returns 去重后的历史记录列表
 */
export const getUniqueHistoryList = (list: any[], count = 6): any[] => {
  const mapCatch = new Map();
  // 倒序遍历，保证更近一次选择优先进入结果，且保持其在结果中的前序
  for (let i = list.length - 1; i >= 0; i -= 1) {
    const item = list[i];
    if (!item) continue;
    const metaData = item.metaData || item;
    const sameKey = `${metaData.filterType}-${metaData.itemId}`;
    if (!mapCatch.has(sameKey)) {
      mapCatch.set(sameKey, item);
    }
  }
  return Array.from(mapCatch.values()).slice(0, count);
};

/**
 * 获取酒店筛选历史记录
 * @returns Promise<any[]> 历史记录列表
 */
export const getHotelFilterHistory = async (): Promise<any[]> => {
  
  try {
    if (isWeb) {
      const historyData = localStorage.getItem(HOTEL_FILTER_HISTORY_KEY) || '';
      return historyData ? getJsonObjectOrRawValue(historyData) : []
    }

    const historyData = await shareDataByKey(HOTEL_FILTER_HISTORY_KEY)
    return historyData ? getJsonObjectOrRawValue(historyData) : []
  } catch (error) {
    console.error('获取酒店筛选历史记录失败:', error);
    return [];
  }
};

/**
 * 保存酒店筛选历史记录
 * @param selectedItems 选中的筛选项列表
 * @returns Promise<void>
 */
export const saveHotelFilterHistory = async (selectedItems: any[]): Promise<void> => {
  if (!selectedItems || selectedItems?.length === 0) return;

  try {
    // 获取现有历史记录
    // const existingHistory = await getHotelFilterHistory();
    // // 删除itemName为空的项
    // const historyItems = selectedItems.filter(item => Boolean(item?.itemName?.trim())).map(item => ({
    //   metaData: item,
    //   timestamp: new Date().getTime()
    // }));
    // 合并并去重，限制最多6条
    const newList = getUniqueHistoryList(selectedItems, 6);
    // 保存更新后的历史记录
    if (isWeb) {
      localStorage.setItem(HOTEL_FILTER_HISTORY_KEY, stringifyValue(newList))
      return;
    }
    setShareData({ [HOTEL_FILTER_HISTORY_KEY]: stringifyValue(newList) })
  } catch (error) {
    console.error('保存酒店筛选历史记录失败:', error);
  }
};

/**
 * 清空酒店筛选历史记录
 * @returns Promise<void>
 */
export const clearHotelFilterHistory = async (): Promise<void> => {
  try {
    // await Storage.removeItem(HOTEL_FILTER_HISTORY_KEY);
    if (isWeb) {
      localStorage.removeItem(HOTEL_FILTER_HISTORY_KEY)
      return;
    }
    setShareData({ [HOTEL_FILTER_HISTORY_KEY]: '' })
  } catch (error) {
    console.error('清空酒店筛选历史记录失败:', error);
  }
};

/**
 * 将历史记录转换为筛选组格式
 * @param historyData 历史记录数据
 * @returns 筛选组对象
 */
export const convertHistoryToFilterGroup = (historyData: any[]): any => {
  if (!historyData || historyData.length === 0) {
    return null;
  }

  return {
    'multi': 0,
    'groupCode': 'hotel_history',
    'filterName': '历史筛选',
    'exposeItemCount': 9,
    'groupName': '历史筛选',
    'itemList': historyData.map(item => item.metaData || item)
  };
};

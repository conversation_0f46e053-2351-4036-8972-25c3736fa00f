import { isEmpty } from '@/utils/isType'
import { getDefaultDate } from './utils'
import { getLocalState } from './utils'
import { POSITION_TYPE } from '../constants/config'
import { getLatLngPos } from '@/common/LBS/getLBS';
import { useLbsAuthorized } from '@/common/LBS/lbsAuthorized'
import { getLbsCity } from '@/common/LBS/getLbsCity'
import { reportInfo } from '@/common/reporter'
import { errorCodeConstantMapping, ErrortType } from '@/common/reporter/errorMapping'

// 酒店首页-城市信息
const hotelCityInfoBKey = 'hotelCityInfoB'
const hotelCheckInDateKey = 'hotelCheckInDate'
const hotelCheckOutDateKey = 'hotelCheckOutDate'
const hotelRoomNumKey = 'hotelTabRoomNum'

// 兜底城市
const defaultCityInfo = {
  showName: '北京',
  latitude: '39.944093',
  longitude: '116.482276',
  province: 1,
  city: 1,
  county: 0,
  street: 0,
  level: 2,
  type: POSITION_TYPE.CITY
}

// 兜底用户位置
const defaultUserAddress = {
  latitude: '39.944093',
  longitude: '116.482276',
  posAreaId: '1,72,55674,0',
  virtualLocation: 1
}

const reportDefaultAddress = () => {
  reportInfo({
    code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_USE_DEFAULT_ADDRESS,
    errorDetail: {
      errorType: ErrortType.Info,
      customMsg: {
        errorDescription: '获取用户定位失败，使用默认定位'
      },
    }
  })
}

const fetchAddress = async () => {
  try {
    // 获取用户是否已授权
    const lbsAuth: any = await useLbsAuthorized()
    const authorized = lbsAuth?.authorized
    const userLoactionInfo: any = await getLatLngPos(!authorized)
    if (isEmpty(userLoactionInfo?.address?.latitude) ||
      isEmpty(userLoactionInfo?.address?.longitude) ||
      isEmpty(userLoactionInfo?.address?.posAreaId)) {
      reportDefaultAddress()
      return defaultUserAddress
    }
    return {
      ...userLoactionInfo?.address,
      virtualLocation: 0
    }
  } catch (error) {
    reportDefaultAddress()
    return defaultUserAddress
  }
}


/**
 * 兜底处理酒店搜索 requestParams 的工具方法
 */
export async function ensureHotelRequestParams(requestParams: any, apiFetch) {
  const {
    priceChannel,
    hotelBaseSearchParam,
    mddInfo,
    latitude,
    longitude,
    posAreaId,
    keyword,
    virtualLocation
  } = requestParams

  requestParams.priceChannel = priceChannel || '1000'
  requestParams.keyword = keyword || ''

  // 兜底地址
  let userAddress
  if (isEmpty(latitude) ||
    isEmpty(longitude) ||
    isEmpty(posAreaId) ||
    isEmpty(virtualLocation)
  ) {
    userAddress = await fetchAddress()
  } else {
    userAddress = {
      latitude,
      longitude,
      posAreaId,
      virtualLocation
    }
  }

  requestParams.latitude = userAddress.latitude
  requestParams.longitude = userAddress.longitude
  requestParams.posAreaId = userAddress.posAreaId
  requestParams.virtualLocation = userAddress.virtualLocation

  // 兜底入住离店时间
  if (isEmpty(hotelBaseSearchParam?.checkInDate) || isEmpty(hotelBaseSearchParam?.checkOutDate)) {
    try {
      const [checkInDate, checkOutDate, hotelRoomNum] = await Promise.all([
        getLocalState(hotelCheckInDateKey),
        getLocalState(hotelCheckOutDateKey),
        getLocalState(hotelRoomNumKey)
      ]) as [any, any, any]
      // 构建基础参数
      const baseParams = {
        roomNum: hotelRoomNum?.roomNum?.value || 1,
        grownNum: hotelRoomNum?.adultNum?.value || 1,
        childrenNum: hotelRoomNum?.childNum?.value || 0,
        childrenAges: hotelRoomNum?.childNum?.age || []
      }

      // 判断是否使用缓存日期
      const useCacheDate = !isEmpty(checkInDate) &&
        !isEmpty(checkOutDate) &&
        (checkInDate as string) >= getDefaultDate().checkInDate

      requestParams.hotelBaseSearchParam = useCacheDate
        ? { ...baseParams, checkInDate: checkInDate as string, checkOutDate: checkOutDate as string }
        : { ...baseParams, ...getDefaultDate() }
    } catch (error) {
      // 发生错误时使用默认参数
      requestParams.hotelBaseSearchParam = {
        ...getDefaultDate(),
        roomNum: 1,
        grownNum: 1,
        childrenNum: 0,
        childrenAges: []
      }
    }
  }

  if (isEmpty(mddInfo)) {
    const hotelCityInfoB: any = await getLocalState(hotelCityInfoBKey)
    if (isEmpty(hotelCityInfoB)) {
      const lbs_city = await getLbsCity(apiFetch, userAddress, 'hotel')
      if (isEmpty(lbs_city?.businessGeoInfo?.hotelSubGeo)) {
        requestParams.mddInfo = defaultCityInfo
      } else {
        const {
          provinceId,
          cityId,
          districtId,
          townId,
          level,
          geoName,
          showGeoName,
        } = lbs_city?.businessGeoInfo?.hotelSubGeo || {}

        const cityInfo = {
          showName: showGeoName ?? geoName ?? '',
          latitude: userAddress.latitude,
          longitude: userAddress.longitude,
          province: isEmpty(provinceId) ? 0 : Number(provinceId),
          city: isEmpty(cityId) ? 0 : Number(cityId),
          county: isEmpty(districtId) ? 0 : Number(districtId),
          street: isEmpty(townId) ? 0 : Number(townId),
          level: isEmpty(level) ? 2 : Number(level),
          type: POSITION_TYPE.CITY
        }
        requestParams.mddInfo = cityInfo
      }
    } else {
      const {
        provinceId,
        cityId,
        districtId,
        townId,
        level,
        geoName,
        showGeoName,
        lat,
        lon
      } = hotelCityInfoB || {}

      const cityInfo = {
        showName: showGeoName ?? geoName ?? '',
        latitude: lat ?? '',
        longitude: lon ?? '',
        province: isEmpty(provinceId) ? 0 : Number(provinceId),
        city: isEmpty(cityId) ? 0 : Number(cityId),
        county: isEmpty(districtId) ? 0 : Number(districtId),
        street: isEmpty(townId) ? 0 : Number(townId),
        level: isEmpty(level) ? 2 : Number(level),
        type: POSITION_TYPE.CITY
      }
      requestParams.mddInfo = cityInfo
    }
  }
} 
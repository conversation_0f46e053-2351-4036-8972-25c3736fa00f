import { setShareData as bridgeSetShareData, shareDataByKey as bridgeShareDataByKey } from '@/utils/bridge';
import { safeJsonParse } from '@/Components/utils';
import { isWeb } from '@/common/common';
import { isEmpty } from '@/utils/isType';

/**
 * 酒店搜索数据键名
 */
export const SHARE_DATA_KEYS = {
  HOTEL_SEARCH_DATA: 'hotelSearchData',
  HOTEL_ROOM_NUM: 'hotelRoomNum',
  HOTEL_CHECK_IN_DATE: 'hotelCheckInDate',
  HOTEL_CHECK_OUT_DATE: 'hotelCheckOutDate',
  PROMO_SEARCH_DATA: 'promoSearchData'
};

/**
 * 保存酒店搜索数据
 * @param searchData 搜索数据对象
 * @returns Promise
 */
export const saveHotelSearchData = (searchData: any) => {
  if (isWeb) {
    return
  }
  return bridgeSetShareData({
    [SHARE_DATA_KEYS.HOTEL_SEARCH_DATA]: searchData
  });
};

/**
 * 获取酒店搜索数据
 * @returns Promise<any> 解析后的搜索数据
 */
export const getHotelSearchData = async () => {
  if (isWeb) {
    return
  }
  try {
    const data = await bridgeShareDataByKey(SHARE_DATA_KEYS.HOTEL_SEARCH_DATA);
    return safeJsonParse(data);
  } catch (error) {
    console.error('获取酒店搜索数据失败:', error);
    return null;
  }
};

/**
 * 频道页获取城市中间页数据
 */
export const getChannelSearchData = async () => {
  if (isWeb) {
    return
  }
  try {
    const data = await bridgeShareDataByKey(SHARE_DATA_KEYS.PROMO_SEARCH_DATA);
    return safeJsonParse(data);
  } catch (error) {
    console.error('获取酒店搜索数据失败:', error);
    return null;
  }
}

export const saveChannelSearchData = (mddInfo: any) => {
  if (isWeb) {
    return
  }

  if (isEmpty(mddInfo)) {
    return
  }

  return bridgeSetShareData({
    [SHARE_DATA_KEYS.PROMO_SEARCH_DATA]: {
      mddInfo
    }
  });
};


/**
 * 保存完整的酒店预订信息（房间信息和日期）
 * @param params 包含房间信息和日期的对象
 * @returns Promise
 */
export const saveHotelBookingInfo = ({
  roomNum,
  adultNum,
  childNum,
  checkInDate,
  checkOutDate
}: {
  roomNum: any;
  adultNum: any;
  childNum: any;
  checkInDate: string;
  checkOutDate: string;
}) => {

  if (isWeb) {
    return
  }

  return bridgeSetShareData({
    [SHARE_DATA_KEYS.HOTEL_ROOM_NUM]: {
      roomNum: {
        value: roomNum?.value
      },
      adultNum: {
        value: adultNum?.value
      },
      childNum: {
        value: childNum?.value,
        age: childNum?.age
      },
    },
    [SHARE_DATA_KEYS.HOTEL_CHECK_IN_DATE]: checkInDate,
    [SHARE_DATA_KEYS.HOTEL_CHECK_OUT_DATE]: checkOutDate
  });
};

/**
 * 获取酒店预订信息（房间信息和日期）
 * @returns Promise<any> 解析后的预订信息
 */
export const getHotelBookingInfo = async () => {
  if (isWeb) {
    return;
  }
  try {
    // 获取房间数量信息
    const roomNumData = await bridgeShareDataByKey(SHARE_DATA_KEYS.HOTEL_ROOM_NUM);
    const roomNumInfo = safeJsonParse(roomNumData);
    
    // 获取入住日期
    const checkInDate = await bridgeShareDataByKey(SHARE_DATA_KEYS.HOTEL_CHECK_IN_DATE);
    
    // 获取离店日期
    const checkOutDate = await bridgeShareDataByKey(SHARE_DATA_KEYS.HOTEL_CHECK_OUT_DATE);

    return {
      roomNum: roomNumInfo?.roomNum || {},
      adultNum: roomNumInfo?.adultNum || {},
      childNum: roomNumInfo?.childNum || {},
      checkInDate,
      checkOutDate
    };
  } catch (error) {
    console.error('获取酒店预订信息失败:', error);
    return null;
  }
};

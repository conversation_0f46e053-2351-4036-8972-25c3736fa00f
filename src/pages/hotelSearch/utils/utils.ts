import _ from 'lodash';
import { getTimeZone, store } from '@ltfe/ltfe-core-lib/lib/utiles'

/**
 * 比较两个值是否完全相同，使用lodash的isEqual方法进行深度比较
 * @param value 第一个值
 * @param other 第二个值
 * @returns 如果两个值完全相同返回true，否则返回false
 */
export const isObjectEqual = (value: any, other: any): boolean => {
    // 过滤掉不需要比较的字段
    const filteredValue = _.omit(value, ['filterList']);
    const filteredOther = _.omit(other, ['filterList']);
    // 使用lodash的isEqual进行深度比较
    return _.isEqual(filteredValue, filteredOther);
};


export function defaultDate() {
    const now = new Date()
    const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)
    const dayAfterTomorrow = new Date(tomorrow.getTime() + 24 * 60 * 60 * 1000)

    return {
        checkInDate: formatDate(tomorrow),
        checkOutDate: formatDate(dayAfterTomorrow)
    }
}

export function formatDate(date: Date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
}

export const getDefaultDate = () => {
    // 今早12点
    const today = getTimeZone()
    // 昨天
    const yesterday = getTimeZone().subtract(1, 'days')
    // 明天
    const tomorrow = getTimeZone().add(1, 'days')
    if (today.hour() < 6) {
        return {
            checkInDate: yesterday.format('YYYY-MM-DD'),
            checkOutDate: today.format('YYYY-MM-DD')
        }
    } else {
        return {
            checkInDate: today.format('YYYY-MM-DD'),
            checkOutDate: tomorrow.format('YYYY-MM-DD')
        }
    }
}

export const getLocalState = (key) => {
    return new Promise((resolve) => {
        store.asyncLoad(key).then(res => {
            res && resolve(res);
        }).catch(() => {
            resolve('');
        });
    });
}
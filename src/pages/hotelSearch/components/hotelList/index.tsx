import React, { useRef, useEffect, memo } from 'react'
import { View } from '@/BaseComponents/atoms'
import { IOFlatList } from '@/BaseComponents/IntersectionObserver'
import { InView } from '@/BaseComponents/IntersectionObserver'
import styles from './index.module.scss'
import EmptyCard from './components/empty'
import NoDataCard from './components/noData'
import NoMatchCard from './components/noMatch'
import HotelCard from '@/pages/result/widgets/Hotel/HotelCard'
import ListTitleCard from './components/recommandTitle'
import AreaClearCard from './components/areaClear'
import TravelTips from './components/travelTips'
import { Loading } from '@/BaseComponents/atoms'
import { safeRun } from '@/Components/Filter/utils'
import ListTips, { TIP_TYPE } from '@/pages/result/widgets/ListBottomTip'
import { isAndroid } from '@/common/common'
import { LoadMoreStatus } from '../../constants/config'
import NoData, { EmptyType } from '@/pages/result/widgets/NoData'
import { HOTEL_SEARCH_EVENT_ID } from '../../constants/mtaParamEvents'
import { hotelCardType } from '../../constants/cardType'
import { MTAPlaceHolderString } from '../../constants/config'

interface HotelListProps {
  fliterValue?: any[]
  locationValue?: any[]
  priceStarValue?: any[]
  data: any[]
  matchList?: any[]
  loading: boolean
  hasMore: boolean
  onLoadMore: () => void
  cardOnClick?: (info: any, other: any) => void
  mtaExpo?: (isExposure, eventId, eventData) => void
  mtaClick?: (params: any) => void
  showTravelPopupInfo?: (type: string) => void
  page?: number,
  loadMoreStatus?: LoadMoreStatus
  clearDistanceFilter: () => void
  clearAllFilters: () => void
  clearFilter: (item) => void
  searchWithOutshareRoom?: () => void

}

const HotelList: React.FC<HotelListProps> = ({
  fliterValue,
  locationValue,
  priceStarValue,
  data,
  loading,
  hasMore,
  onLoadMore,
  cardOnClick,
  mtaExpo,
  mtaClick,
  showTravelPopupInfo,
  loadMoreStatus,
  clearDistanceFilter,
  clearAllFilters,
  clearFilter,
  searchWithOutshareRoom
}) => {
  const listRef = useRef(null)
  const expoEndData = useRef<any>({}) // 埋点信息
  const refLoading = useRef(false)

  useEffect(() => {
    let st2
    if (refLoading.current) {
      st2 = setTimeout(() => {
        refLoading.current = false
      }, 20)
    }
    return () => {
      clearTimeout(st2)
    }
  }, [data])

  const noShareRoomClickAction = () => {
    safeRun(mtaClick, {
      eventId: HOTEL_SEARCH_EVENT_ID.HotelRNListNoRoomShareReservationNew
    })
    safeRun(searchWithOutshareRoom)
  }

  const getWidgetEventId = (trackData: any, isNutural) => {
    const { isExposure, scene } = trackData
    if (scene === 'subsidy') {
      if (isNutural) {
        return isExposure ? HOTEL_SEARCH_EVENT_ID.HotelRNListSubsidyRoomExpoNew : HOTEL_SEARCH_EVENT_ID.HotelRNListSubsidyRoomNew
      } else {
        return isExposure ? HOTEL_SEARCH_EVENT_ID.HotelRNListRecSubsidyRoomExpoNew : HOTEL_SEARCH_EVENT_ID.HotelRNListRecSubsidyRoomNew
      }
    }
  }

  const renderItem = (info, props) => {
    const { cardWidth, item, index } = info
    const { cardType, eventId, shareHotelTip, isFirst } = item
    const cardUuid = cardType || item?.hotelCardVO?.id || index

    switch (cardType) {
      case hotelCardType.Empty:
        return <EmptyCard />
      case hotelCardType.NoData:
        return <NoDataCard
          show={item.show}
          title={item.title}
        />
      case hotelCardType.NoMatch:
        return <NoMatchCard
          cardUuid={cardUuid}
          fliterValue={fliterValue}
          locationValue={locationValue}
          priceStarValue={priceStarValue}
          clearAllFilters={clearAllFilters}
          clearFilter={clearFilter}
          mtaTrack={mtaExpo}
        />
      case hotelCardType.ListTitle:
        return <ListTitleCard
          cardUuid={cardUuid}
        />
      case hotelCardType.AreaClear:
        return <AreaClearCard
          cardUuid={cardUuid}
          locationValue={locationValue}
          clearDistanceFilter={clearDistanceFilter}
          mtaTrack={mtaExpo}
        />
      case hotelCardType.NoRoomShare:
        return <InView key={cardUuid} onChange={
          (visible) => {
            if (visible) {
              safeRun(mtaExpo, {
                isExposure: true,
                eventId: HOTEL_SEARCH_EVENT_ID.HotelRNListNoRoomShareServationExpoNew
              })
            }
          }
        }>
          <NoData
            type={EmptyType.NO_SHARE_ROOM}
            shareHotelTip={shareHotelTip}
            onClick={noShareRoomClickAction}
          />
        </InView>
      case hotelCardType.NoRoomShareLowPrice:
        return <InView key={cardUuid} onChange={
          (visible) => {
            if (visible) {
              safeRun(mtaExpo, {
                isExposure: true,
                eventId: HOTEL_SEARCH_EVENT_ID.HotelRNListNoRoomShareServationExpoNew
              })
            }
          }
        }>
          <NoData
            type={EmptyType.NO_SHARE_ROOM_LOWPRICE}
            shareHotelTip={shareHotelTip}
            onClick={noShareRoomClickAction}
          />
        </InView>
      case hotelCardType.InfoTips:
        return <TravelTips
          showPopUpAction={showTravelPopupInfo}
        />
      default:
        return <InView key={cardUuid} onChange={(visible) => {
          const eventData = item?.eventData
          const isNatural = item?.isNatural
          const beltCodeStr = item?.beltInfoVO?.beltCode || MTAPlaceHolderString
          if (visible && !expoEndData?.current?.[item?.hotelCardVO?.id]) {
            mtaExpo && mtaExpo(true,
              isNatural ? HOTEL_SEARCH_EVENT_ID.HotelRNListHotelMesExpoNew : HOTEL_SEARCH_EVENT_ID.HotelRNListRecHotelExpoNew,
              { ...eventData, 'beltCode': beltCodeStr }
            )
            mtaExpo && mtaExpo(true,
              isNatural ? HOTEL_SEARCH_EVENT_ID.HotelRNListHotelPicExpoNew : HOTEL_SEARCH_EVENT_ID.HotelRNListRecHotelPicExpoNew,
              { ...eventData, 'beltCode': beltCodeStr }
            )
            if (expoEndData?.current) {
              expoEndData.current[item?.hotelCardVO?.id] = true;
            }
          }
        }} index={index}>
          <HotelCard
            cardUuid={cardUuid}
            data={item}
            index={index}
            isFirst={isFirst}
            eventId={eventId}
            cardWidth={cardWidth}
            onPress={cardOnClick}

            mtaTrack={(trackData: any) => {
              const { isExposure, param } = trackData
              mtaExpo && mtaExpo(isExposure,
                getWidgetEventId(trackData, item?.isNatural),
                param
              )
            }}
          />
        </InView>
    }
  }

  const footerRender = (loadMoreStatus) => {
    switch (loadMoreStatus) {
      case LoadMoreStatus.IDLE:
        return null
      case LoadMoreStatus.LOADING:
        return <View style={{ backgroundColor: '#fff' }}><Loading /></View>
      case LoadMoreStatus.ERROR:
        return <ListTips type={TIP_TYPE.RETRY_BY_NETERROR} onRetry={() => {
          safeRun(onLoadMore)
        }} />
      case LoadMoreStatus.NO_MORE:
        return <ListTips title={'没有更多数据了'} />
      default:
        return null
    }
  }

  if (!loading && data.length === 0) {
    return <View />
  }

  return (
    <View className={styles.listBox}>
      <IOFlatList
        data={data}
        scrollEnabled={true}
        renderItem={(info) => renderItem(info, {
          loading
        })}
        keyExtractor={(item, index) => index.toString()}
        decelerationRate={isAndroid ? 0.985 : 0.994}
        windowSize={21}
        initialNumToRender={10}
        scrollEventThrottle={16}
        onEndReached={() => {
          if (!refLoading.current && hasMore && loadMoreStatus !== LoadMoreStatus.LOADING) {
            refLoading.current = true
            safeRun(onLoadMore)
          }
        }}
        ListFooterComponent={() => footerRender(loadMoreStatus)}
        ref={listRef}
      />
    </View>
  )
}

export default memo(HotelList)

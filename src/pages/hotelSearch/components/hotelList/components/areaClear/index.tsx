import { useRef, memo } from 'react'
import { InView } from '@/BaseComponents/IntersectionObserver'
import { View, Text } from '@/BaseComponents/atoms'
import { TouchableWithoutFeedback } from 'react-native'
import styles from './index.module.scss'
import { hairlineWidth } from '@/utils/cross'
import { HOTEL_SEARCH_EVENT_ID } from '../../../../constants/mtaParamEvents'
import { locationSubFilterType } from '../../../../constants/filterType'
import { isEmpty } from '@/utils/isType'

interface AreaClearCardProps {
    cardUuid: string | number
    locationValue?: any[]
    clearDistanceFilter: () => void
    mtaTrack?: (isExposure, eventId, eventData) => void
}

const AreaClearCard = ({
    cardUuid,
    locationValue,
    clearDistanceFilter,
    mtaTrack
}: AreaClearCardProps) => {
    const expoEndData = useRef<any>({}) // 埋点信息
    const distanceFilterItem = locationValue?.find(item => item.filterType === locationSubFilterType.gis_distance)
    const content = !isEmpty(distanceFilterItem?.title) ? `已显示${distanceFilterItem.title}所有酒店` : '当前搜索范围内暂无酒店，已为您推荐附近酒店'

    return (
        <InView key={cardUuid} onChange={(visible) => {
            if (visible && !expoEndData.current[HOTEL_SEARCH_EVENT_ID.HotelRNListTipExpoNew]) {
                mtaTrack && mtaTrack(true, HOTEL_SEARCH_EVENT_ID.HotelRNListTipExpoNew, {
                    displayName: content,
                })
                expoEndData.current[HOTEL_SEARCH_EVENT_ID.HotelRNListTipExpoNew] = true
            }
        }}>
            <InView className={styles.wr}>
                <Text className={styles.txtLeft} >
                    {content}
                </Text>
                <TouchableWithoutFeedback onPress={() => {
                    mtaTrack && mtaTrack(false, HOTEL_SEARCH_EVENT_ID.HotelRNListTipNew, {
                        displayName: '扩大搜索范围',
                    })
                    clearDistanceFilter()
                }}>
                    <View
                        style={{ borderWidth: hairlineWidth }}
                        className={styles.btn} >
                        <Text className={styles.txt}>扩大搜索范围</Text>
                    </View>
                </TouchableWithoutFeedback>
            </InView>
        </InView>
    )
}

export default memo(AreaClearCard)

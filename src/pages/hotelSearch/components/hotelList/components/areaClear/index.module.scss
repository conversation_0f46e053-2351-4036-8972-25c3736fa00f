@import "@/assets/theme.scss";

.wr {
    flex-direction: row;
    justify-content: space-between;
    height: 54px;
    background-color: #FFFFFF;
    align-items: center;
    padding: 0 16px;
}

.btn {
    align-items: center;
    justify-content: center;
    flex-grow: 0;
    flex-shrink: 0;
    border-color: var(--primaryHLTextColor);
    background-color: var(--primaryLightTextColor);
    padding: 0 10px;
    height: 31px;
    border-radius: 4px;
    border-width: 0.5px;
}

.txtLeft {
    align-items: center;
    flex: 1;
    font-size: 12px;
    color: var(--primaryTextColor);
    padding-left: 4px;
    padding-right: 4px;
    font-weight: var(--fontActWeight);
}

.txt {
    color: var(--primaryHLTextColor);
    font-size: 12px;
}
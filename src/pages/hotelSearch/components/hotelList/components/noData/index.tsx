import { memo } from 'react'
import ListTips from '@/pages/result/widgets/ListBottomTip'

interface NoDataCardProps {
  show: boolean
  title?: string
}

const NoDataCard = ({ show, title }: NoDataCardProps) => {
  if (!show) return null

  return (
    <ListTips
      title={title || '未找到符合条件的结果，请更改条件重新搜索'}
      className={'bold'}
      style={{ color: '#1A1A1A' }}
    />
  )
}

export default memo(NoDataCard)
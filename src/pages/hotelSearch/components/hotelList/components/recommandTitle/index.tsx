import { memo } from 'react'
import { View, Text, Image } from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import { getImg } from '@/Components/Filter/utils'

interface ListTitleCardProps {
  cardUuid: string | number
}

const ListTitleCard = ({ cardUuid }: ListTitleCardProps) => {
  return (
    <View
      key={cardUuid}
      className={styles.listTitle}>
      <Image className={styles.goodIcon} src={getImg('good')} />
      <Text className={styles.listTitleWord}>
        以下酒店满足您的部分要求
      </Text>
    </View>
  )
}

export default memo(ListTitleCard)
import { memo } from 'react'
import { View, Text, Image } from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import { getImg } from '@/Components/Filter/utils'

const TravelTips = ({ showPopUpAction }) => {
    return (
        <View onClick={() => {
            showPopUpAction && showPopUpAction('describe')
        }}><View className={styles.infoTipsBox}>
                <View className={styles.infoTipsBoxContent}>
                    <Image className={styles.infoTipsIcon} src={getImg('infoNew')} />
                    <Text className={styles.infoTipsText}>
                        【出差必看】酒店住宿标准和发票要求
                    </Text>
                </View>
            </View>
        </View>
    )
}

export default memo(TravelTips)

import { memo } from 'react'
import { InView } from '@/BaseComponents/IntersectionObserver'
import { View, Image, Text } from '@/BaseComponents/atoms'
import { getImg } from '@/Components/Filter/utils'
import { isEmpty } from '@/utils/isType'
import classNames from 'classnames'
import styles from './index.module.scss'
import { useRef } from 'react'
import { HOTEL_SEARCH_EVENT_ID } from '../../../../constants/mtaParamEvents'
import { glabelFilterType } from '@/pages/hotelSearch/constants/filterType'
import { isIOS, pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import { MTAPlaceHolderString } from '../../../../constants/config'

interface NoMatchCardProps {
    cardUuid: string | number
    fliterValue?: any[]
    locationValue?: any[]
    priceStarValue?: any[]
    clearAllFilters: () => void
    clearFilter: (item) => void
    mtaTrack?: (isExposure, eventId, eventData) => void
}

const NoMatchCard = ({
    cardUuid,
    fliterValue = [],
    locationValue = [],
    priceStarValue = [],
    clearAllFilters,
    clearFilter,
    mtaTrack
}: NoMatchCardProps) => {
    const expoEndData = useRef<any>({}) // 埋点信息
    const content = "没有更多符合条件的结果，请尝试修改条件重新查询"
    const filterItems = fliterValue.map(item => ({
        metaData: {
            itemName: item.itemName || item.label,
            filterType: item.filterType,
            itemId: item.itemId || item.value,
            groupCode: item.groupCode,
            filterPanelName: '筛选',
            filterPanelCode: 'hotel_filter'
        }
    }))

    // 处理位置距离数据
    const locationItems = locationValue.map(item => ({
        metaData: {
            itemName: item.title,
            filterType: item.type,
            itemId: item.value,
            groupCode: item.groupCode,
            filterPanelName: '位置距离',
            filterPanelCode: glabelFilterType.location_distance 
        }
    }))

    // 处理价格星级数据
    const priceStarItems = priceStarValue.map(item => ({
        metaData: {
            itemName: item.label || item.itemName,
            filterType: item.filterType,
            itemId: item.itemId,
            groupCode: item.groupCode,
            filterPanelName: '价格/星级',
            filterPanelCode: glabelFilterType.price_star
        }
    }))

    const createTrackParam = (data, index) => {
        return {
            displayName: content,
            groupCode: `${data?.groupCode || MTAPlaceHolderString}`,
            filterType: data?.filterType || MTAPlaceHolderString,
            itemId: `${data?.itemId || MTAPlaceHolderString}`,
            itemName: `${data?.itemName || MTAPlaceHolderString}`,
            index: index + 1,
            filterPanelName: `${data?.filterPanelName || MTAPlaceHolderString}`,
            filterPanelCode: `${data?.filterPanelCode || MTAPlaceHolderString}`
        }
    }

    // 合并所有数据
    const allItems = [...locationItems, ...priceStarItems, ...filterItems]

    return (
        isEmpty(allItems) ? null : <InView key={cardUuid} onChange={(visible) => {
            if (visible && Array.isArray(allItems) && allItems.length > 0 && !expoEndData.current[HOTEL_SEARCH_EVENT_ID.HotelRNListRecFilterExpoNew]) {
                allItems.forEach((item, index) => {
                    const data = item?.metaData || {}
                    mtaTrack && mtaTrack(true, HOTEL_SEARCH_EVENT_ID.HotelRNListRecFilterExpoNew, createTrackParam(data, index))
                })
                expoEndData.current[HOTEL_SEARCH_EVENT_ID.HotelRNListRecFilterExpoNew] = true
            }
        }}>
            <View className={styles.wr}>
                <View className={styles.delBox} 
                    style={isIOS ? { marginTop: pt(2) } : {}}
                    onClick={() => { clearAllFilters()}}
                >
                    <Image src={getImg('delIcon')} className={styles.delIcon} />
                    <Text className={styles.delWord}>清空</Text>
                </View>

                <View className={styles.txt}>
                    <Image src={getImg('search')} className={styles.searchIcon} />
                    <Text className={styles.tipText}>
                        {content}
                    </Text>
                </View>

                <View className={classNames('row wrap', styles.itemWr)}>
                    {
                        allItems.map((item, index) => {
                            return (
                                <View
                                    key={index}
                                    className={classNames('row center', styles.item, styles.clearItem)}
                                    onClick={() => {
                                        const data = item?.metaData || {}
                                        mtaTrack && mtaTrack(false, HOTEL_SEARCH_EVENT_ID.HotelRNListRecFilterNew, createTrackParam(data, index))
                                        clearFilter(item)
                                    }}
                                >
                                    <Text className={styles.labelTxt}>{item.metaData.itemName}</Text>
                                    <View className={styles.clearIconWr}>
                                        <Image src={getImg('blueClose')} className={styles.clearIcon} />
                                    </View>
                                </View>
                            )
                        })
                    }
                </View>
            </View>
        </InView>
    )
}

export default memo(NoMatchCard)

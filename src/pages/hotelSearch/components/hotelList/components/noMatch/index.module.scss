
@import "@/assets/theme.scss";

.wr {
  width: 100%;
  background-color: #F2F3F5;
  padding: 20px 12px 12px;
  position: relative;
}

.txt {
  color: var(--primaryTextColor);
  text-align: left;
  font-size: 14px;
  vertical-align: top;
  flex-direction: row;
}

.labelTxt {
  font-size: 12px;
}

.itemWr {
  padding-right: 8px;
  margin-top: 14px;
}

.item {
  height: 26px;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #FFFFFF;
  color: var(--primaryTextColor);
  font-size: 12px;
  margin-right: 8px;
  margin-bottom: 8px;
  border-radius: 4px;
}

.clearItem {
  padding-right: 10px;
}

.clearIconWr {
  width: 8px;
  height: 8px;
  margin-left: 3px;
}

.clearIcon {
  width: 9px;
  height: 9px;
  margin-right: 8px;
}

.searchIcon {
  margin-top: 3px;
  width: 13px;
  height: 13px;
  margin-right: 8px;
}

.delBox {
  position: absolute;
  top: 20px;
  right: 12px;
  flex-direction: row;
  align-items: center;
  z-index: 1;
}

.delIcon {
  width: 15px;
  height: 15px;
  margin-right: 4px;
}

.delWord {
  color: #505259;
}

.tipText {
  margin-right: 80px;
  font-size: 14px;
  line-height: 18px;
  font-weight: 500;
}


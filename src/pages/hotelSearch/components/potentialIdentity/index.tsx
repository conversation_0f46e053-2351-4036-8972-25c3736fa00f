import React, { memo, useState} from 'react'
import { 
    View,
    AdaptiveImage,  
} from '@/BaseComponents/atoms';
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import { Pressable, StyleSheet } from 'react-native';
// import { JDImage } from '@jdreact/jdreact-core-lib'
import { isWeb } from '@/common/common'
import { Image as NativeImage } from 'react-native'

type Data = {
    'imageHttps': string;
    'url': string;
}
interface PotentialIdentityProps {
    onPress: (url: string) => void;
    data: Data;
}

const PotentialIdentity: React.FC<PotentialIdentityProps> = ({ onPress, data }) => {

    const [imgHeight, setImgHeight] = useState({})

    const getImgHeight = (uri, cb) => {
        try {
            NativeImage.getSize(uri, (width, height) => {
                cb(pt(height / width * 359))
            })
        } catch (error) {
            console.error('Failed to get image size:', error);
            cb(pt(66));
        }
    }

    if (data?.imageHttps && data?.url) {
        return (
            <View style={styles.main}>
                <Pressable onPress={() => onPress(data.url)}>
                    <AdaptiveImage
                        mode={'heightFix'}
                        onLoad={() => {
                            if (!isWeb) return
                            getImgHeight(data.imageHttps, (heigth) => {
                                setImgHeight(prev => ({
                                    ...prev,
                                    [data.imageHttps]: heigth
                                }))
                            })
                        }}
                        src={data?.imageHttps}
                        style={{width: pt(359), minHeight: pt(66), height: isWeb && imgHeight[data.imageHttps] ? pt(imgHeight[data.imageHttps]) * 0.33 : undefined }} />
                </Pressable>
            </View>
        );
    } else {
        return <></>
    }
};

const styles = StyleSheet.create({
    main: {
        position: 'absolute',
        bottom: pt(40),
        left: pt(8),
        width: pt(359),
        justifyContent:'center',
        alignItems: 'center'
    },
})

export default memo(PotentialIdentity);

@import "@/assets/theme.scss";

.container {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: row;
  background-color: #F5F7FA;
  border-radius: 4px;
  margin-left: 12px;
  margin-right: 12px;
  margin-bottom: 2px;
  height: 36px;
}

.img {
  width: 175px;
  height: 30px;
}

.leftContainer {
  position: absolute;
  left: 3px;
  top: 3px;
  width: 175px;
  height: 30px;
  z-index: 1;
}

.rightContainer {
  position: absolute;
  right: 3px;
  top: 3px;
  width: 175px;
  height: 30px;
}

.badgeContainer {
  position: absolute;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: -6px;
  right: -3px;
  z-index: 10;
}

.badgeContainer_web {
  position: absolute;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: -6px;
  right: 8px;
  z-index: 10;
}

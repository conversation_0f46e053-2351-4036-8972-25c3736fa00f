import React, { memo, useState, useEffect, useRef } from 'react';
import { View, Image } from '@tarojs/components';
import styles from './index.module.scss';
import { isEmpty } from '@/utils/isType';
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import AdaptiveImage from '@/BaseComponents/atoms/AdaptiveImage'
import { Image as NativeImage } from 'react-native'
import { isWeb } from '@/common/common'
import { HOTEL_SEARCH_EVENT_ID } from '../../constants/mtaParamEvents';
interface IdentitySectionProps {
    data?: any;
    onSelect?: (any, number) => void;
    mtaTrack: (boolean, string, any) => void;
    userIdentityList?: any;
}

const IdentitySection: React.FC<IdentitySectionProps> = ({
    data,
    onSelect,
    mtaTrack,
    userIdentityList
}) => {
    const isExposure = useRef(false)
    const { identityTab, list } = data || {}
    const getSafeIndex = (list, identityTab) => {
        if (!list) return 0; // 防御空数据
        const index = list.findIndex(item => item.identityTab === identityTab);
        return index >= 0 ? index : 0; // 主动校验负数
    };
    // 默认选中
    const defaultSelectIndex = getSafeIndex(list, identityTab)
    const [selectIndex, setSelectIndex] = useState(defaultSelectIndex)
    const [imgWidth, setImgWidth] = useState({})

    useEffect(() => {
        setSelectIndex(defaultSelectIndex);
    }, [defaultSelectIndex]);

    // 添加身份tab曝光埋点，HotelRN_List_TabExpo
    useEffect(() => {
        if (!isExposure.current && !isEmpty(identityTab) && !isEmpty(userIdentityList)) {
            mtaTrack(true, HOTEL_SEARCH_EVENT_ID.HotelRNListTabExpo, {
                userIdentity: userIdentityList,
                index: defaultSelectIndex,
                isSelect: true,
                identityTab: identityTab
            });
            isExposure.current = true
        }
    }, [userIdentityList, identityTab]);
    
    if (isEmpty(list) || list?.length < 2) {
        return null
    }

    const selectAction = (index) => {
        if (index === selectIndex) {
            return
        }
        setSelectIndex(index)

        if (index < list?.length) {
            onSelect?.(list[index].identityTab, index)
        }
    }

    const showList = list.slice(0, 2)

    return (
        <View
            className={styles.container}>
            {
                showList?.map((item, index) => (
                    <View
                        key={index}
                        className={index === 0 ? styles.leftContainer : styles.rightContainer}
                        onClick={() => selectAction(index)}
                    >
                        <Image
                            className={styles.img}
                            src={index === selectIndex ? item?.selectUrl : item?.url}
                        />
                        {
                            index !== selectIndex && !isEmpty(item?.badge?.imageHttps) && (
                                <View
                                    style={{ width: isWeb ? pt(imgWidth[item?.badge?.imageHttps]) * 0.33 : undefined, height: pt(14) }}
                                    className={isWeb ? styles.badgeContainer_web : styles.badgeContainer}
                                >
                                    <AdaptiveImage
                                        mode={'heightFix'}
                                        onLoad={() => {
                                            if (!isWeb) return
                                            getImgHeight(item?.badge?.imageHttps, (width) => {
                                                setImgWidth(prev => ({
                                                    ...prev,
                                                    [item?.badge?.imageHttps]: width
                                                }))
                                            })
                                        }}
                                        src={item?.badge?.imageHttps}
                                        style={{
                                            height: pt(14),
                                            minWidth: pt(35),
                                            width: isWeb ? pt(imgWidth[item?.badge?.imageHttps]) * 0.33 : undefined
                                        }} />
                                </View>
                            )
                        }
                    </View>
                ))
            }
        </View>
    );
};

export default memo(IdentitySection);

const getImgHeight = (uri, cb) => NativeImage.getSize(uri, (width, height) => {
    cb(pt(width / height * 30))
})
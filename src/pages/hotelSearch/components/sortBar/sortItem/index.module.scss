
@import "@/assets/theme.scss";

.sortItem {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 46px;
  // flex: 1 1 auto; /* 根据内容宽度分配空间 */
}

.text {
  font-size: 14px;
  color: #666;
  text-align: center;
}

.activeText {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  text-align: center;
}

.filterArrowDown {
  width: 6px;
  height: 6px;
  margin-left: 4px;
}

.filterArrowUp {
  width: 6px;
  height: 6px;
  margin-left: 4px;
  transform: rotate(180deg);
}

.sortText {
  font-size: 14px;
  text-align: center;
  color: #191919;
  font-weight: bold;
}

.sortTextAct {
  font-size: 14px;
  text-align: center;
  font-weight: var(--fontActWeight);
  color: var(--primaryHLTextColor);
  font-weight: bold;
}

.filterCountBox {
  width: 14px;
  height: 14px;
  border-radius: 7px;
  margin-left: 4px;
  background-color: var(--primaryHLTextColor);
  display: flex;
  align-items: center;
  justify-content: center;
}

.filterCount {
  color: #fff;
  text-align: center;
  font-size: 10px;
  line-height: 14px;
}

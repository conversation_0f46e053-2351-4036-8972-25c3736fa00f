import React, { memo } from 'react'
import { View, Text, Image } from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import { ORDER_TYPES } from '../../../constants/config'
import { getImg } from '@/Components/Filter/utils'

interface SortItemProps {
  type: string
  label: string
  active: boolean
  orderType: number
  onClick: () => void
  selectedCount?: number
}

const SortItem: React.FC<SortItemProps> = ({
  label,
  active,
  orderType,
  onClick,
  selectedCount = 0,
}) => {

  // 处理点击事件
  const handleClick = () => {
    onClick()
  }

  return (
    <>
      <View
        className={styles.sortItem}
        onClick={handleClick}>
        <Text
          numberOfLines={1}
          className={active ? styles.sortTextAct : styles.sortText}>
          {label}
        </Text>
        {
          selectedCount > 0 && (
            <View className={styles.filterCountBox}>
              <Text className={styles.filterCount}>{selectedCount}</Text>
            </View>
          )
        }
        {
          active ?
            <Image
              className={orderType === ORDER_TYPES.ASC ? styles.filterArrowDown : styles.filterArrowUp}
              src={getImg('filterArrowAct')}
            /> :
            <Image
              className={styles.filterArrowDown}
              src={getImg('filterArrow')}
            />
        }
      </View>
    </>
  );
}

export default memo(SortItem)

import React, { useMemo, memo, useEffect, useRef } from 'react'
import { View } from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import { SortType, ORDER_TYPES } from '../../constants/config'
import SortItem from './sortItem/index'
import QuickMatchTab from './quickMatchTab/index'
import { SORT_OPTIONS } from '../../constants/config'
import { ORDER_TYPE } from '@/common/mta'
import { HOTEL_SEARCH_EVENT_ID } from '../../constants/mtaParamEvents'
import { isEmpty } from '@/utils/isType'
import { pt} from '@ltfe/ltfe-core-lib/lib/utiles'

interface SortBarProps {
  allFilterList?: any[];
  filterMap?: Set<any>;
  onFilterClick: (filterType: SortType) => void;
  quickMatchClick: (quickFilters: any) => void;
  sortType?: string;
  sortValue?: any;
  selectedFilters?: {
    [key: string]: any[];
  };
  activeFilterType: any;
  filterPanelVOList?: any[];
  outsideFilterPanelVOList?: any[];
  mtaTrack: (isExpo: boolean, eventId: string, params: any) => void; // 添加mtaTrack属性
}

const SortBar: React.FC<SortBarProps> = ({
  allFilterList = [],
  filterMap,
  onFilterClick,
  quickMatchClick,
  activeFilterType,
  sortType,
  selectedFilters = {},
  filterPanelVOList = [],
  outsideFilterPanelVOList = [],
  mtaTrack // 接收mtaTrack函数
}) => {
  // 用于跟踪曝光状态
  const isExposed = useRef(false);

  // 构建排序项列表
  const sortItems = useMemo(() => {
    // 网关未下发, 则默认'智能排序'也不展示
    if (isEmpty(filterPanelVOList)) {
      return [];
    }
    // 创建排序选项
    const sortItem = {
      key: 'sort',
      label: '智能排序',
      type: 'sort',
      options: SORT_OPTIONS
    };
    // 从 filterPanelVOList 中提取其他筛选项
    const otherFilterItems = filterPanelVOList.map(panel => ({
      key: panel.filterPanelCode,
      label: panel.filterPanelName,
      type: panel.filterPanelCode,
      options: panel.filterList || []
    }));
    
    // 合并排序项和其他筛选项
    return [sortItem, ...otherFilterItems];
  }, [filterPanelVOList]);

  // 添加筛选项曝光埋点 HotelRN_List_MainFilterExpo
  useEffect(() => {
    if (!isEmpty(sortItems) && !isExposed.current) {
      // 对每个筛选项进行曝光埋点
      sortItems.forEach((item, index) => {
        const eventId = HOTEL_SEARCH_EVENT_ID.HotelRNListMainFilterExpoNew;
        mtaTrack(true, eventId, {
          filterPanelCode: item.key,
          filterPanelName: item.label,
          index: index + 1
        });
      });
      isExposed.current = true;
    }
  }, [sortItems]);

  // 处理筛选项点击事件，添加埋点HotelRN_List_MainFilter
  const handleFilterClick = (filterType: SortType, item: any, index: number) => {
    // 根据filterType决定上报的事件ID
    const eventId = filterType === 'sort' ? HOTEL_SEARCH_EVENT_ID.HotelRNListOrderEntranceNew : HOTEL_SEARCH_EVENT_ID.HotelRNListMainFilterNew;
    // 构建基础参数
    const selectItemParams = {
      filterPanelCode: item.key || '',
      filterPanelName: item.label || '',
    };
    
    // 根据事件类型添加不同的参数
    if (filterType === 'sort') {
      // 获取排序类型信息
      const selectSubItem = getSortTypeInfo('sort');
      mtaTrack(false, eventId, {
        ...selectItemParams,
        sortType: selectSubItem.sortType, 
        orderType: ORDER_TYPE[selectSubItem.orderType] || 1,
        businessType: '1'
      });
    } else {
      mtaTrack(false, eventId, {
        ...selectItemParams,
        index: index + 1
      });
    }
    
    // 调用原有的点击处理函数
    onFilterClick(filterType);
  };

  // 检查筛选项是否有选中的值
  const hasSelectedFilters = (key: string): boolean => {
    return selectedFilters &&
      selectedFilters[key] &&
      (Array.isArray(selectedFilters[key]) ?
        selectedFilters[key].length > 0 :
        !!selectedFilters[key]);
  };

  // 获取筛选项选中的数量
  const getSelectedCount = (key: string): number => {
    if (!selectedFilters || !selectedFilters[key] || key ==='sort') {
      return 0;
    }
    return selectedFilters[key].length;
  };

  // 获取筛选项的显示标签
  const getFilterLabel = (item: { key: string, label: string, type: string }): string => {
    // 如果是排序项且有选中的排序类型，则显示对应的排序名称
    if (item.key === 'sort' && sortType) {
      const selectedSort = SORT_OPTIONS.find(option => option.sortType === sortType);
      return selectedSort ? selectedSort.filterName : item.label;
    }
    // 否则返回原始标签
    return item.label;
  };

  // 回显二级目录筛选的内容，首次进入智能排序默认是降序，default
   const getSortTypeInfo = (key: string): { sortType: string, orderType: string } => {
    // 默认值
    const defaultInfo = { sortType: 'default', orderType: 'desc' };
    
    // 如果不是排序项，返回默认值
    if (key !== 'sort') {
      return defaultInfo;
    }
    
    // 如果有选中的排序类型，从SORT_OPTIONS中获取对应的sortType和orderType
    if (sortType) {
      const selectedSort = SORT_OPTIONS.find(option => option.sortType === sortType);
      if (selectedSort) {
        return {
          sortType: selectedSort.sortType,
          orderType: selectedSort.orderType
        };
      }
    }
    
    // 如果没有找到选中的排序类型，返回默认值
    return defaultInfo;
  };

  return (
    !isEmpty(sortItems) ?
      <View className={styles.container}>
        <View className={styles.sortBar} style={{height: pt(40)}}>
          {sortItems.map((item, index) => {
            const type = item.key;
            const selectedCount = getSelectedCount(item.key);
            return (
              <SortItem
                key={item.key}
                type={item.key}
                label={getFilterLabel(item)}
                active={activeFilterType === type || hasSelectedFilters(item.key) }
                orderType={activeFilterType === type ? ORDER_TYPES.ASC : ORDER_TYPES.DESC}
                onClick={() => handleFilterClick(item.key, item, index)}
                selectedCount={selectedCount}
              />
            );
          })}
        </View>
        {
          !isEmpty(outsideFilterPanelVOList) && <QuickMatchTab
            allFilterList={allFilterList}
            filterMap={filterMap}
            onChange={quickMatchClick}
            outsideFilterPanelVOList={outsideFilterPanelVOList}
            mtaTrack={mtaTrack} // 传递mtaTrack给QuickMatchTab
          />
        }
      </View> : null
  );
};

export default memo(SortBar);

import { memo } from 'react'
import { View, Image } from '@/BaseComponents/atoms'
import { pt, px } from '@ltfe/ltfe-core-lib/lib/utiles'
import styles from './index.module.scss'
import classNames from 'classnames'
import SelectItem from './selectItem'
import { InView, IOScrollView } from '@/BaseComponents/IntersectionObserver'
import { useState, useRef } from 'react'
import { isWeb } from '@/common/common'
import _ from 'lodash'
import { HOTEL_SEARCH_EVENT_ID } from '../../../constants/mtaParamEvents'
import { MTAPlaceHolderString } from '../../../constants/config'
import { priceStarSubFilterType } from '../../../constants/filterType'

interface QuickMatchTabProps {
  allFilterList?: any[]
  filterMap?: Set<any>
  outsideFilterPanelVOList: any[]
  onChange: (value: any) => void
  mtaTrack: (isExpo: boolean, eventId: string, params: any) => void // 添加mtaTrack属性
}

function QuickMatchTab(props: QuickMatchTabProps) {
  const { onChange, outsideFilterPanelVOList = [], filterMap = new Set(), allFilterList = [], mtaTrack } = props || {}

  // 添加埋点记录对象
  const expoEndData = useRef<Record<string, boolean>>({})

  // 将所有 outsideFilterPanelVOList 中的 filterList 下的 itemList 合并成一个数组
  const allItemList = outsideFilterPanelVOList.reduce((acc, panel) => {
    const { filterList = [] } = panel || {}
    filterList.forEach(item => {
      // 将item.groupCode添加到每个itemList元素中
      if (item.itemList && Array.isArray(item.itemList)) {
        const itemsWithGroupCode = item.itemList.map(listItem => ({
          ...listItem,
          groupCode: item.groupCode // 添加groupCode属性
        }));
        acc.push(...itemsWithGroupCode);
      }
    })
    return acc
  }, [])

  const [imgWidth, setImgWidth] = useState<Record<string, number>>({})

  const selectItem = (item: any, index: number) => {
    let result = allFilterList;
    // 补丁: 如果有hotel_price_lowest类型, 先将hotel_price_lowest过滤掉
    if (item?.filterType === priceStarSubFilterType.hotel_price_lowest) {
      result = result.filter(item => item.filterType !== priceStarSubFilterType.hotel_price_lowest)
    }
    const key = `${item.filterType}-${item.itemId}`
    const isCurrentlySelected = filterMap.has(key);

    // 更新选中状态
    if (isCurrentlySelected) {
      // 如果已经选中，则从结果中移除
      result = result.filter(
        selectedItem => !(key === `${selectedItem.filterType}-${selectedItem.itemId}`)
      )
    } else {
      // 如果未选中，则添加到结果中
      result.push(item)
    }
    // 快排item点击埋点: HotelRN_List_QuickFilter
    mtaTrack(false, HOTEL_SEARCH_EVENT_ID.HotelRNListQuickFilterNew, {
      groupCode: item.groupCode || MTAPlaceHolderString,
      filterType: item.filterType || MTAPlaceHolderString,
      itemId: item.itemId || MTAPlaceHolderString,
      itemName: item.itemName || MTAPlaceHolderString,
      itemType: item.itemType || MTAPlaceHolderString,
      itemValue: item.itemValue || MTAPlaceHolderString,
      index: index + 1 || MTAPlaceHolderString,
      isChoose: !isCurrentlySelected,
      businessType: '1'
    });

    onChange(result)
  }

  const isItemSelected = (item: any) => {
    const key = `${item.filterType}-${item.itemId}`
    return filterMap.has(key)
  }

  // 快速筛选条件曝光埋点：HotelRN_List_QuickFilterExpo
  const expo = (visible: boolean, item: any, index: number) => {
    // 添加曝光埋点
    const key = `${item.filterType}-${item.itemId}`
    if (visible && mtaTrack && !expoEndData.current?.[key]) {
      mtaTrack(true, HOTEL_SEARCH_EVENT_ID.HotelRNListQuickFilterExpoNew, {
        filterType: item.filterType || MTAPlaceHolderString,
        itemId: item.itemId || MTAPlaceHolderString,
        itemName: item.itemName || MTAPlaceHolderString,
        itemType: item.itemType || MTAPlaceHolderString,
        index: index + 1,
        groupCode: item.groupCode || MTAPlaceHolderString,
        isSelect: isItemSelected(item),
        itemValue: item.itemId || MTAPlaceHolderString,
        businessType: '1'
      });
      // 记录已曝光
      expoEndData.current[key] = true
    }
  }

  const renderIcon = (item, isActive) => {
    const { imageWidth = 75, imageHeight = 30, activeImage, listImage } = item?.style || {}
    const showHeight = 30;
    let showWidth = 75;
    if (imageHeight > 0 && imageWidth > 0) {
      showWidth = showHeight * imageWidth / imageHeight;
    }
    return (
      <View style={{ 
            position: 'relative',
            height: pt(showHeight),
            width: pt(showWidth), 
            display: 'flex'
             }}>
        <Image
          src={activeImage}
          mode="aspectFit"
          style={{
            height: pt(showHeight),
            width: pt(showWidth),
            position: 'absolute',
            top: 0,
            left: 0,
            opacity: isActive ? 1 : 0
          }}
        />
        <Image
          src={listImage}
          mode="aspectFit"
          style={{
            height: pt(showHeight),
            width: pt(showWidth),
            position: 'absolute',
            top: 0,
            left: 0,
            opacity: isActive ? 0 : 1
          }}
        />
      </View>
    )
}

  return (
    <View className={styles.quickOutBox}>
      <IOScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingLeft: pt(12) }}
      >
        {
          allItemList.map((item, index) => {
            const isIcon = item?.style?.type === 'icon'
            const isActive = isItemSelected(item);

            return isIcon ? (
              <InView
                key={index}
                onChange={(visible) => expo(visible, item, index)}
                className={styles.item_image}
                onClick={() => {
                  selectItem(item, index)
                }}
              >
                {renderIcon(item, isActive)}
              </InView>
            ) : (
              <SelectItem
                key={index}
                showCheckedIcon={true}
                onChange={(visible) => expo(visible, item, index)}
                onClick={() => {
                  selectItem(item, index)
                }}
                selected={isActive}
                text={item.itemName}
              />
            )
          })}
      </IOScrollView>
    </View>
  )
}

export default memo(QuickMatchTab)

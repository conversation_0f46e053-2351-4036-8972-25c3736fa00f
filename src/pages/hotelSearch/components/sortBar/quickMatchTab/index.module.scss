@import "@/assets/theme.scss";

.quickOutBox {
  height: 30px;
  line-height: 30px;
  padding-left: 0px;
  padding-right: 0px;
  background-color: #fff;
}

.item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  margin-right: 8px;
  height: 30px;
  box-sizing: border-box;
  border-radius: 4px;
  background-color: var(--filterBgColor, #F5F7FA);
}

.item_image {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  height: 30px;
}

.item_selected {
  display: flex;
  align-items: center;
  justify-content: center;
  // flex: 1; 在h5上选中多个，文字会换行
  padding: 0 10px;
  margin-right: 8px;
  height: 30px;
  box-sizing: border-box;
  border-radius: 4px;
  background-color: var(--filterActiveBgColor, #EDF6FF);
  border: 0.5px solid #006eeb;
}

.item_selectedWeb {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  margin-right: 8px;
  height: 30px;
  box-sizing: border-box;
  border-radius: 4px;
  background-color: var(--filterActiveBgColor, #EDF6FF);
  border: 1px solid #006eeb;
}

.noPd {
  padding: 0;
}

.text {
  font-size: 12px;
  color: var(--primaryTextColor);
}

.text_selected {
  color: red;
}

.selectedText{
  font-size: 12px;
  color: #006eeb;
}

// .checkedIcon {
//   width: 12px;
//   height: 12px;
//   margin-left: 4px;
//   background-image: url('@/assets/images/checked.png');
//   background-size: contain;
//   background-repeat: no-repeat;
//   background-color: black;
// }

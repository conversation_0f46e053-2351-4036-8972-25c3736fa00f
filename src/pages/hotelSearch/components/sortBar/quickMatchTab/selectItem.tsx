import { View, Text } from '@/BaseComponents/atoms'
import { InView } from '@/BaseComponents/IntersectionObserver'
import styles from './index.module.scss'
import classNames from 'classnames'
import { isWeb } from '@/common/common'

interface SelectItemProps {
  text: string
  selected?: boolean
  showCheckedIcon?: boolean
  onClick?: () => void
  onChange?: (visible: boolean) => void
}

function SelectItem({ text, selected, showCheckedIcon, onClick, onChange }: SelectItemProps) {
  return (
    <InView
      onChange={onChange}
      className={classNames(selected ? isWeb ? styles.item_selectedWeb :styles.item_selected : styles.item)}
      onClick={onClick}
    >
      <Text className={classNames(selected ? styles.selectedText : styles.text)}>
        {text}
      </Text>
      {/* {showCheckedIcon && selected && (
        <View className={styles.checkedIcon} />
      )} */}
    </InView>
  )
}

export default SelectItem


import { memo } from 'react'
import { formatTime, getTimeZone } from '@ltfe/ltfe-core-lib/lib/utiles'
import { useMorningCheckInTip, TIP_TYPE } from '@ltfe/ltfe-core-lib/lib/MorningCheckInTip';
import { View, Text } from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import classNames from 'classnames'
import { isAndroid } from '@/common/common'
import { queryModeType } from '../../../../constants/travelType'
import { isEmpty } from '@/utils/isType'

interface MddInfoViewProps {
  hotelBaseSearchParam: any
  cityName: string
  isTravel?: boolean
  queryMode?: string
  onClick: () => void
}

export const MddInfoView = memo(({
  cityName,
  hotelBaseSearchParam,
  isTravel = false,
  queryMode = '',
  onClick
}: MddInfoViewProps) => {
  const {
    checkInDate,
    checkOutDate,
    roomNum,
    grownNum,
    childrenNum
  } = hotelBaseSearchParam || {}

  const { tipType } = useMorningCheckInTip(checkInDate, checkOutDate);
  const isEarly = tipType === TIP_TYPE.EARLY_YESTERDAY;
  const fromDate = isEarly ? getTimeZone().format('MM-DD') : formatTime(checkInDate, 'MM-DD');


  const dateDisable = isTravel && [queryModeType.SINGLE_SHARE, queryModeType.MULTI_SHARE, queryModeType.SHARE_TO_BUSINESS].map(String).includes(queryMode)

  const onClickCallBack = () => {
    if (dateDisable) {
      return
    }
    onClick()
  }

  return (
    <View
      className={styles.container}
      onClick={onClickCallBack}>
      {/* 城市选择部分 */}
      <View
        className={classNames([styles.cityContainer,
                   {
                    [styles.city_min] : cityName?.length === 4,
                    [styles.city_special_max] : cityName?.length === 3,
                    [styles.city_max_width] : cityName?.length > 4
                   }
                  ])}
      >
        <Text
          className={classNames(styles.city_text, {
            [styles.city_text_font12]: cityName?.length > 2,
            [styles.city_text_font14]: !(cityName?.length > 2),
            [styles.disabled]: isTravel
          })}
          numberOfLines={2}
        >
          {cityName}
        </Text>
      </View>

      {/* 日期选择部分 */}
      {
        !isEmpty(checkInDate) && !isEmpty(checkOutDate) && <View className={styles.search_date}>
          <View className={styles.date_text}>
            <Text className={dateDisable ? styles.date_common_disable : styles.date_common}>
              {fromDate}
            </Text>
          </View>
          <View
            className={classNames({
              [styles.date_text]: true,
              [styles.date_bottom]: isAndroid
            })}
          >
            <Text className={dateDisable ? styles.date_common_disable : styles.date_common}>
              {formatTime(checkOutDate, 'MM-DD')}
            </Text>
          </View>
        </View>
      }

      {
        !isTravel && <View className={ !isEmpty(checkInDate) && !isEmpty(checkOutDate) ? styles.search_room : styles.search_room_fix}>
          <Text className={styles.date_common}>{roomNum || 1}间</Text>
          <Text className={styles.date_common}>{(grownNum + childrenNum) || 1}人</Text>
        </View>
      }

    </View>
  )
})


.container {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 100%;
    border-right: 2px solid #fff;
}

.cityContainer {
    display: flex;
    flex-direction: row;
    align-items: center;
    max-width: 35px;
    margin-left: 6px;
}

.city_min {
  // max-width: 50px;
  max-width: 30px;
}

.city_special_max {
    max-width: 40px;
}

.city_max_width {
  max-width: 45px;
}

.city_text {
    font-size: 18px;
    color: #000;
    text-align: center;
    font-weight: bold;
    font-size: 14px;
    line-height: 16px;
}

.city_text_font12 {
    font-size: 12px;
    line-height: 14px;
}

.city_text_font14 {
    font-size: 14px;
}

.search_date {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-left: 8px;
    margin-right: 8px;
}

.search_room {
    display: flex;
    margin-top: 2px;
    flex-direction: column;
    justify-content: center;
    margin-right: 12px;
}

.search_room_fix {
    display: flex;
    margin-left: 8px;
    margin-top: 2px;
    flex-direction: column;
    justify-content: center;
    margin-right: 12px;
}

.date_text {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.date_bottom {
    margin-top: -2px;
}

.date_common {
    font-family: 'JDZhengHT-Regular';
    line-height: 14px;
    font-size: 12px;
    color: var(--primaryTextColor);
}

.date_common_disable {
    font-family: 'JDZhengHT-Regular';
    line-height: 14px;
    font-size: 12px;
    color: #888B94;
}

.disabled {
    color: #888B94;
}

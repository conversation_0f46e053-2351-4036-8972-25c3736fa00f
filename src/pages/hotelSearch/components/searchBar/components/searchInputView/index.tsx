import { memo } from 'react'
import { View, Text, Image } from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import { getImageUrl } from '@/assets/imgs'
import { queryModeType } from '../../../../constants/travelType'

const ImgConfig = {
  Vclose: getImageUrl('Vclose'),
  Vsearch: getImageUrl('Vsearch')
}

interface SearchInputProps {
  keyword: string
  isTravel?: boolean
  queryMode?: string
  onSearch: () => void
  onClear: () => void
}

const SearchInputComponent = ({
  keyword,
  isTravel = false,
  queryMode = '',
  onSearch,
  onClear
}: SearchInputProps) => {
  const closeImgHidden = isTravel && [queryModeType.SINGLE_SHARE, queryModeType.MULTI_SHARE, queryModeType.SHARE_TO_BUSINESS].map(String).includes(queryMode)

  const placeHolderText = () => {
    if (isTravel && [queryModeType.SINGLE_SHARE, queryModeType.MULTI_SHARE].map(String).includes(queryMode)) {
      return '位置/酒店'
    }
    return '位置/品牌/酒店'
  }

  return (
    <View className={styles.search_input_box}>
      <Image src={ImgConfig.Vsearch} className={styles.search_icon} />
      <View
        className={styles.search_input}
        onClick={onSearch}
      >
        <Text numberOfLines={1} className={keyword?.length > 0 ? styles.keyword : styles.placeholder}>{keyword || placeHolderText()}</Text>
      </View>
      {
        !closeImgHidden && keyword && (
          <View className={styles.close} onClick={onClear}>
            <Image src={ImgConfig.Vclose} className={styles.close_img} mode={'scaleToFill'} />
          </View>
        )
      }
    </View>
  )
}

export const SearchInput = memo(SearchInputComponent)

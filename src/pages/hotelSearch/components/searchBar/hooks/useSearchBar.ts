import { useState, useCallback } from 'react'
import Taro from '@tarojs/taro'

export function useSearchBar() {
  const [showPanel, setShowPanel] = useState(false)

  const goBack = useCallback(() => {
    Taro.navigateBack({ delta: 1 })
  }, [])

  const togglePanel = useCallback(() => {
    const newShowPanel = !showPanel
    setShowPanel(newShowPanel)
  }, [showPanel])

  const closePanel = useCallback(() => {
    setShowPanel(false)
  }, [])

  return {
    goBack,
    showPanel,
    togglePanel,
    closePanel
  }
}

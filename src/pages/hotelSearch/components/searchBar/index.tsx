
import { memo, useRef } from 'react'
import { View, Text } from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import { isWeb } from '@/common/common'
import NavBar from '@/BaseComponents/NavBar'
import { HotelSearchPanel, Popup } from '@ltfe/ltfe-core-lib'
import Calendar from '@ltfe/ltfe-core-lib/lib/hotel-search-panel/calendar-pop'
import { MddInfoView } from './components/mddInfoView'
import { SearchInput } from './components/searchInputView'
import { useSearchBar } from './hooks/useSearchBar'
import { pt, statusBarHeight, deviceHeight, formatTime, getTimeZone } from '@ltfe/ltfe-core-lib/lib/utiles'
import type { Props } from './types'
import { isEmpty } from '@/utils/isType'
import { encrypt } from '@ltfe/ltfe-core-lib/lib/utiles'
import { toUnicode } from '@/utils'
import useJumpTo from "@/common/useJumpTo"
import { useEffect } from 'react'
import { POSITION_TYPE } from '@/pages/hotelSearch/constants/config'
import { HOTEL_SEARCH_EVENT_ID } from '../../constants/mtaParamEvents'
import { MTAPlaceHolderString } from '../../constants/config'

function SearchBar(props: Props) {
  const {
    goBack,
    showPanel,
    togglePanel,
    closePanel
  } = useSearchBar()

  const jumpTo = useJumpTo()

  const {
    searchParams,
    travelParams,
    extraParams,
    isTravel = false, // 是否是差旅
    clear,
    update,
    updateTravelDate,
    openWebView,
    mtaTrack,
    showTogglePanel,
    ignoreCompareKeywordAction,
    updatedCheckInDate = false,
    commonUserAction
  } = props || {}

  const isExposure = useRef(false)

  const { keyword, hotelBaseSearchParam, mddInfo, lbs_city } = searchParams || {}
  const { is_lbs = false, geoName } = lbs_city || {}
  const { showName, type } = mddInfo || {}

  let cityName
  if (type === POSITION_TYPE.LOCATE) {
    // 使用
    if (is_lbs) {
      cityName = '我的位置'
    } else {
      cityName = geoName || '选择城市'
    }
  } else {
    // 使用mddInfo
    cityName = showName || '选择城市'
  }

  // 添加搜索框关键词曝光埋点，HotelRN_List_SearchExpo
  useEffect(() => {
    if (!isExposure.current && !isEmpty(commonUserAction)) {
      mtaTrack(true, HOTEL_SEARCH_EVENT_ID.HotelRNListSearchExpoNew, { keyword: keyword || MTAPlaceHolderString });
      isExposure.current = true
    }
  }, [keyword, commonUserAction]); // 只依赖keyword和mtaTrack

  const tempIs_lbs = type === POSITION_TYPE.LOCATE && is_lbs

  let hotelCity
  if (tempIs_lbs) {
    hotelCity = lbs_city
  } else {
    hotelCity = {
      geoName: cityName,
      lat: mddInfo?.latitude,
      lon: mddInfo?.longitude,
      provinceId: mddInfo?.province,
      cityId: mddInfo?.city,
      districtId: mddInfo?.county,
      townId: mddInfo?.street,
      destType: mddInfo?.type,
      level: mddInfo?.level,
      is_lbs: false
    }
  }

  const hotelBaseSearchParamForComponent = {
    checkInDate: hotelBaseSearchParam?.checkInDate || '',
    checkOutDate: hotelBaseSearchParam?.checkOutDate || '',
    roomNum: {
      value: hotelBaseSearchParam?.roomNum || 1
    },
    adultNum: {
      value: hotelBaseSearchParam?.grownNum || 1
    },
    childNum: {
      value: hotelBaseSearchParam?.childrenNum || 0,
      age: hotelBaseSearchParam?.childrenAges || []
    }
  }

  const navbarHeight = (isWeb ? pt(44) : pt(40)) + statusBarHeight
  const updateMdd = (value) => {
    // 检查是否发生变化
    // 添加点击埋点(列表弹层完成点击) HotelRN_List_SelectListLayer
    mtaTrack(false, HOTEL_SEARCH_EVENT_ID.HotelRNListSelectListLayerNew, {
      adultNum: String(value?.adultNum?.value || MTAPlaceHolderString),
      childrenNum: String(value?.childNum?.value || MTAPlaceHolderString),
      roomNum: String(value?.roomNum?.value || MTAPlaceHolderString)
    });
    update(value)
    closePanel()
  }

  // 添加点击埋点（城市、日历、房间是同一块点击区域） HotelRN_List_SelectList
  const handleTogglePanel = () => {
    mtaTrack(false, HOTEL_SEARCH_EVENT_ID.HotelRNListSelectListNew, {})
    togglePanel()
    showTogglePanel()
  }

  // 添加搜索框点击埋点，HotelRN_List_Search
  const handleSearchClick = (searchKeyword) => {
    mtaTrack(false, HOTEL_SEARCH_EVENT_ID.HotelRNListSearchNew, {})
    toCity('keyword');
  }

  // 处理城市变化并上报埋点,HotelRN_List_Location
  const cityChange = (value) => {
    mtaTrack(false, HOTEL_SEARCH_EVENT_ID.HotelRNListLocationNew, {});
  }

  // const complement = num => num < 10 ? '0' + num : num;
  // const full_date = (ms, type) => {
  //   const date = new Date(ms);
  //   const y = date.getFullYear();
  //   const m = date.getMonth() + 1;
  //   const d = date.getDate();

  //   const settype = type || '-';
  //   return y + settype + complement(m) + settype + complement(d);
  // };

  const getAllDates = (startDate, endDate) => {
    const dateArray = [];
    let currentDate = getTimeZone(startDate);

    while (currentDate.isSameOrBefore(endDate, 'day')) {
      dateArray.push(currentDate.format('YYYY-MM-DD'));
      currentDate = currentDate.add(1, 'days');
    }

    return dateArray;
  }


  const toCity = (enterType) => {
    // 添加切换城市点击埋点，HotelRN_List_SwitchCity
    mtaTrack(false, HOTEL_SEARCH_EVENT_ID.HotelRNListSwitchCityNew, {});

    if (enterType === 'keyword') {
      closePanel()
    }

    if (isTravel) {
      // 将searchParams 和 extraParams合并
      const newSearchParams = {
        ...searchParams,
        ...extraParams
      }

      const {
        fromSource,
        distance,
        queryMode,
        virtualLocation,
        latitude,
        longitude,
        posAreaId,
        keyword,
        travelInfo,
        hotelBaseSearchParam,
        mddInfo,
        channelId,
        priceChannel
      } = newSearchParams

      const businessSearchParams = {
        fromSource,
        distance,
        queryMode,
        virtualLocation,
        latitude,
        longitude,
        posAreaId,
        keyword,
        travelInfo,
        hotelBaseSearchParam,
        mddInfo,
        channelId,
        priceChannel,
        isFromVResult: true
      }


      const businessSearchData = !isEmpty(businessSearchParams) ? encrypt.Base64Encode(encrypt.Base64Encode(businessSearchParams)) : ''
      jumpTo({ to: 'businessSearch', params: { searchInfo: businessSearchData } })
      return
    }

    const hotelSearchData = !isEmpty(searchParams) ? encrypt.Base64Encode(encrypt.Base64Encode(searchParams)) : ''

    let showKeyword = keyword
    if (enterType === 'city') {
      showKeyword = ''
    }

    const ignoreCompareKeyword = enterType === 'city'
    if(ignoreCompareKeywordAction) {
      ignoreCompareKeywordAction(ignoreCompareKeyword)
    }

    if (isWeb) {
      const { priceChannel = '', channelId = '' } = extraParams || {}
      const url = `https://hotel.m.jd.com/?routerName=location&channel=${priceChannel}&flow=${channelId}&fromPage=list&enterType=${enterType}&keywordName=${toUnicode(showKeyword)}&hotelSearchData=${hotelSearchData}`
      openWebView(url)
    } else {
      const url = `https://hotel.m.jd.com/?routerName=location&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=location&fromPage=list&enterType=${enterType}&keywordName=${toUnicode(showKeyword)}&hotelSearchData=${hotelSearchData}`
      jumpTo({ to: 'web', params: { url: url } })
    }
  }

  return (
    <>
      <View className={styles.navBarWrapper}>
        <NavBar
          leftFn={goBack}
          hideBorder
          center={
            <View className={isWeb ? styles.navBar_web : styles.navBar}>
              <View className={styles.searchContainer} style={{ height: pt(36) }}>
                <MddInfoView
                  isTravel={isTravel}
                  queryMode={travelParams?.queryMode}
                  cityName={cityName}
                  hotelBaseSearchParam={hotelBaseSearchParam}
                  onClick={handleTogglePanel}
                />
                <SearchInput
                  isTravel={isTravel}
                  queryMode={travelParams?.queryMode}
                  keyword={keyword}
                  onSearch={() => handleSearchClick(keyword)}
                  onClear={clear}
                />
              </View>
            </View>
          }
        />
      </View>
      {/* 酒店搜索面板 */}
      {
        showPanel && !isTravel && (
          <HotelSearchPanel
            style={{ top: navbarHeight, zIndex: 1000 }}
            {
            ...hotelBaseSearchParamForComponent
            }
            mode={'hotel'}
            hotelCity={hotelCity}
            onClose={closePanel}
            onUpdate={updateMdd} // 完成
            toCity={() => toCity('city')} // 去城市
            cityChange={cityChange} // 添加cityChange处理函数
            hotelKeyword={{
              lat: mddInfo?.latitude || '',
              lon: mddInfo?.longitude || '',
              name: keyword || ''
            }}
          />
        )
      }
      {
        showPanel && isTravel && <Popup
          title="选择日期"
          speed={200}
          show={true}
          height={deviceHeight * 5 / 6}
          onHide={() => {
          }}
        >
          <Calendar
            checkInDate={hotelBaseSearchParamForComponent?.checkInDate}
            checkOutDate={hotelBaseSearchParamForComponent.checkOutDate}
            allCanUsedCalendar={getAllDates(travelParams?.travelInfo?.startDate, travelParams?.travelInfo?.endDate)}
            isUseCustomCalendarData={true}
            onUpdate={(pickDate) => {
              const { departDate, arriveDate } = pickDate
              updateTravelDate({
                checkInDate: departDate,
                checkOutDate: arriveDate
              })
              closePanel()
            }}
          />
        </Popup>
      }
      {
        updatedCheckInDate ? <View
         className={styles.date_tips}
         style={isWeb ? { top: pt(40), left: pt(4) } : { top: statusBarHeight + pt(40), left: pt(22) }}>
          <View className={styles.date_tips_arrow} />
          <Text className={styles.date_tips_text}>入住日期已变化，即将刷新</Text>
        </View> : null
      }
    </>
  )
}

export default memo(SearchBar)

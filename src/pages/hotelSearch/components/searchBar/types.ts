export interface HotelBaseSearchParam {
  checkInDate: string
  checkOutDate: string
  roomNum: Object
  adultNum: Object
  childNum: Object
  childrenAges: Array<string>
  grownNum: Number
}

export interface LocationInfo {
  showName: string
  city: Number
  latitude: string
  longitude: string
  searchCenterName?: string
}

export interface State {
  hotelBaseSearchParam: HotelBaseSearchParam
  keyword: string
  mddInfo: LocationInfo
  locationInfo: Object
  queryMode: string
}

export interface Props {
  searchParams: any
  travelParams: any
  extraParams: any
  clear: () => void
  update: (value: any) => void
  updateTravelDate: (value: any) => void
  openWebView: (url) => void
  isTravel?: boolean
  mtaTrack: (isExposure: boolean, eventId: string, eventData: any) => void
  ignoreCompareKeywordAction?: (ignore: boolean) => void
  showTogglePanel:  () => void,
  updatedCheckInDate?: boolean
  commonUserAction: any
}

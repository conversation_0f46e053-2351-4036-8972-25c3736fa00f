import { View } from '@/BaseComponents/atoms'
import { useEffect } from 'react'
import { getLBS } from '@ltfe/ltfe-core-lib/lib/utiles'
import { window } from '@tarojs/runtime'
import styles from './index.module.scss'
import { isEmpty, isUndefined } from '@/utils/isType'

const WebViewBox = (props) => {
    const {
        show,
        uri = 'https://jd.com',
        iFrameCallBack
    } = props || {}

    const eventListener = (event) => {
        if (event?.data) {
            const {
                type,
                data
            } = event.data || {}
            if (!isUndefined(type) && !isEmpty(type)) {
                if (type === 'getLocationInfo') {
                    const locationIframe = document.getElementById('select_city_iframe') as HTMLIFrameElement
                    getLBS().then(res => {
                        locationIframe?.contentWindow?.postMessage({
                            type: 'getLBS',
                            data: res
                        }, '*')
                    }).catch(error => {
                        locationIframe?.contentWindow?.postMessage({
                            type: 'getLBS',
                            data: error
                        }, '*')
                    })
                } else {
                    const { searchInfo } = data || {}
                    iFrameCallBack(searchInfo)
                }
            }
        }
    }

    useEffect(() => {
        if (show && window) {
            const frameBox = document.getElementById('location_select_frame_box')
            if (frameBox) {
                frameBox.innerHTML = `<iframe allow="geolocation" id="select_city_iframe" src="${uri}"  frameborder="0" scrolling="yes" style="border: none; width: 100%; height: 100%;"></iframe>`
            }
        }
    }, [show])

    useEffect(() => {
        window?.removeEventListener('message', eventListener);
        window?.addEventListener('message', eventListener)
        return () => {
            window?.removeEventListener('message', eventListener);
        }
    }, [])

    return show ? <View className={styles.box} id={'location_select_frame_box'} /> : null
}

export default WebViewBox
import React, { useEffect, memo } from 'react';
import { View, Text, ScrollView, Image } from '@/BaseComponents/atoms';
import styles from './index.module.scss';
import { SORT_OPTIONS } from '../../../constants/config';
import { ORDER_TYPE } from '@/common/mta';
import { HOTEL_SEARCH_EVENT_ID } from '../../../constants/mtaParamEvents';
import { THEME_FONT } from '@ltfe/ltfe-core-lib/lib/utiles/theme'
import { MTAPlaceHolderString } from '../../../constants/config';

interface SortOption {
  filterName: string;
  filterType: string;
  sortType: string;
  orderType: string;
  index: number;
}

interface SortFilterProps {
  value?: any;
  onChange: (option: SortOption) => void;
  onClose: () => void;
  visible: boolean;
  top?: number;
  style?: any;
  mtaTrack: (isExposure: boolean, eventId: string, eventData: any) => void
}

const SortFilter: React.FC<SortFilterProps> = ({
  value,
  onChange,
  onClose,
  visible,
  style,
  mtaTrack
}) => {
  if (!visible) return null;

  useEffect(() => {
    if (visible) {
      SORT_OPTIONS.forEach(item => {
        track(true, item)
      })
    }
  }, [visible]);

  const track = (isExposure, item: SortOption) => {
    const eventId = isExposure ? HOTEL_SEARCH_EVENT_ID.HotelRNListOrderExpoNew : HOTEL_SEARCH_EVENT_ID.HotelRNListOrderNew;
    mtaTrack(isExposure, eventId, {
      displayName: item.filterName || MTAPlaceHolderString,
      sortType: item.sortType || MTAPlaceHolderString,
      orderType: ORDER_TYPE[item.orderType] || MTAPlaceHolderString,
      filterName: item.filterName || MTAPlaceHolderString,
      filterType: item.filterType || MTAPlaceHolderString,
      index: item.index + 1,
      isSelect: `${value == item.sortType}`
    });
  }

  const renderItem = (item) => {
    const selected = (value === item.sortType)
    return (
      <View
        key={item.sortType}
        className={selected ? styles.option_selected : styles.option}
        onClick={() => {
          track(false, item)
          onChange(item);
          onClose();
        }}>
        <Text
          className={selected ? styles.option_text_selected : styles.option_text}
          style={selected ? { fontWeight: THEME_FONT.fontWeight.Medium } : {}}
        >
          {item.filterName}
        </Text>
        {
          selected && <Image
            className={styles.mark}
            src={'https://img12.360buyimg.com/imagetools/jfs/t1/244299/16/22256/513/671cb1d8F087578c0/fedafe097dacb85f.png'}
          />
        }
      </View>
    )
  };
  return (
    <View
      style={style}
      className={styles.container}>
      <View
        className={styles.mask}
        onClick={onClose} />
      <ScrollView
        className={styles.panel}>
        {SORT_OPTIONS.map((item) => (
          renderItem(item)
        ))}
      </ScrollView>
    </View>
  );

}

export default memo(SortFilter);

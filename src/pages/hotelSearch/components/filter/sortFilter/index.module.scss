@import "@/assets/theme.scss";

.container {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  height: 100%;
}

.mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: black;
  opacity: 0.7;
}

.panel {
  position: absolute;
  left: 0;
  right: 0;
  background: #fff;
  max-height: 80vh;
}

.section {
  margin-bottom: 20px;
}

.section_last {
  margin-bottom: 0;
}

.sectionTitle {
  font-size: 14px;
  color: #333;
  margin-bottom: 12px;
}

.options {
  display: flex;
  flex-wrap: wrap;
  margin: -6px;
}

.option {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  margin: 0 16px;
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #F5F7FA;
}

.option_text {
  color: var(--primaryTextColor);
}

.option_selected {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  margin: 0 16px;
  font-size: 14px;
  color: #333;
  color: #fff;
  border-bottom: 1px solid #F5F7FA;
}

.option_text_selected {
  color: var(--primaryHLTextColor);
}

.mark {
  width: 20px;
  height: 20px;
}
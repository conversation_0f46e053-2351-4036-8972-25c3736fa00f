import React, { useState, useMemo, useEffect, CSSProperties, memo } from 'react';
import { View, Text, Image, ScrollView } from '@/BaseComponents/atoms';
import styles from './index.module.scss';
import { isEmpty } from '@/utils/isType';
import { Loading } from '@/BaseComponents/atoms';
import { HOTEL_SEARCH_EVENT_ID } from '../../../constants/mtaParamEvents';
import { glabelFilterType, locationSubFilterType } from '../../../constants/filterType';
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import { THEME_FONT } from '@ltfe/ltfe-core-lib/lib/utiles/theme'

interface LocationFilterProps {
  styleType?: string;
  value?: LocationOption[];
  onChange: (options: LocationOption[]) => void;
  onClose: (trackConfirm?: boolean, extraData?: any) => void;
  visible: boolean;
  locationData?: any; // 接收位置数据
  loading?: boolean; // 加载状态
  onRetry?: () => void; // 重试回调
  style: CSSProperties;
  mtaTrack: (isExposure, eventId, eventData) => void
}

interface LocationOption {
  title: string;
  type: string;
  value: string;
  id?: string;
  geohash?: string;
  userSelect?: string;
  order?: number;
  items?: LocationOption[]; // 子选项
  filterType?: string;
  itemId?: string;
  itemName?: string;
}

interface LocationGroup {
  title: string;
  type: string;
  items: LocationOption[];
  maxDepth?: number;
}

// 格式化位置数据，添加层级关系
const formatLocationData = (locationGroups) => {
  if (isEmpty(locationGroups)) {
    return [];
  }

  return locationGroups.map(group => {
    const formattedGroup = {
      title: group?.title || '',
      type: group?.type || '',
      maxDepth: group?.maxDepth || 1,
      items: []
    };

    if (!isEmpty(group?.items)) {
      formattedGroup.items = group.items
        .filter(item => group?.type === locationSubFilterType.linearDistance || item?.title) // 过滤掉 geohash 或 title 为空的选项
        .map(item => ({
          title: item?.title || '',
          type: item?.type || '',
          value: item?.value || '',
          id: item?.id,
          geohash: item?.geohash,
          userSelect: item?.userSelect,
          order: item?.order,
          items: !isEmpty(item?.items) ? item.items
            .filter(subItem => subItem?.geohash && subItem?.title) // 过滤掉 geohash 或 title 为空的子选项
            .map(subItem => ({
              title: subItem?.title || '',
              type: subItem?.type || '',
              value: subItem?.value || '',
              id: subItem?.id,
              geohash: subItem?.geohash,
              userSelect: subItem?.userSelect,
              order: subItem?.order,
              items: group?.maxDepth === 2 ?
                (!isEmpty(subItem?.items) ?
                  subItem.items.filter(thirdItem => thirdItem?.geohash && thirdItem?.title) : // 过滤掉 geohash 或 title 为空的三级选项
                  []
                ) :
                []
            })) : []
        }));
    }
    return formattedGroup;
  });
};

// const confirmIconUrl = 'https://img12.360buyimg.com/imagetools/jfs/t1/244299/16/22256/513/671cb1d8F087578c0/fedafe097dacb85f.png'

const LocationFilter: React.FC<LocationFilterProps> = ({
  styleType,
  value = [],
  onChange,
  onClose,
  visible,
  locationData,
  loading = false,
  onRetry,
  style,
  mtaTrack
}) => {

  const confirmIconUrl = styleType === 'channel' ? 'https://img13.360buyimg.com/imagetools/jfs/t1/295609/25/7183/349/685a6a9cFb00ce1a0/b39558a2170c02dd.png' : 'https://img12.360buyimg.com/imagetools/jfs/t1/244299/16/22256/513/671cb1d8F087578c0/fedafe097dacb85f.png'
  const activeItemStyle = styleType === 'channel' ? styles.activeItem_red : styles.activeItem;
  const selectedItemStyle = styleType === 'channel' ? styles.selectedItem_red : styles.selectedItem;
  const confirmBtnContainerStyle = styleType === 'channel' ? styles.confirmBtn_container_red : styles.confirmBtn_container;
  const activeMiddleItemStyle = styleType === 'channel' ? styles.activeMiddleItem_red : styles.activeMiddleItem;
  const panelStyle = styleType === 'channel' ? styles.panel_channel : styles.panel;

  const [activeGroup, setActiveGroup] = useState<any>(null);
  const [activeSubGroup, setActiveSubGroup] = useState<LocationOption | null>(null);

  const rightScrollRef = React.useRef<any>(null);

  // 转换位置数据格式
  const locationGroups = useMemo(() => {
    if (isEmpty(locationData?.data?.filters)) {
      return [];
    }
    return formatLocationData(locationData?.data?.filters || []);
  }, [locationData]);

  // 当locationData变化时，重置选中状态，默认选中第一个分类
  useEffect(() => {
    if (locationGroups.length > 0) {
      // locationData变化时，默认选中第一个分类
      let firstGroup = locationGroups[0];
      // 查找locationGroups中第一个type === 'hot'的元素
      const hotGroup = locationGroups.find(group => group?.type === 'hot');

      if (hasSelectedOptionInGroup(hotGroup)) {
        firstGroup = hotGroup;
      } else if (activeGroup) {
        firstGroup = activeGroup
      } else if (hotGroup) {
        firstGroup = hotGroup;
      }
      setActiveGroup(firstGroup?.type);

      // 如果是三级分类（maxDepth === 2），默认选中第一个子分组
      if (firstGroup?.maxDepth === 2 && Array.isArray(firstGroup?.items) && firstGroup.items.length > 0) {
        setActiveSubGroup(firstGroup.items[0]);
      } else {
        setActiveSubGroup(null);
      }
    }
  }, [locationData, visible]);

  // 设置初始选中分组和子分组，但仅在activeGroup为空时执行
  useEffect(() => {
    if (locationGroups.length > 0 && !activeGroup) {
      // 只在初始化时设置默认选中，而不是每次value变化都重置
      let selectedGroup = locationGroups[0];
      let selectedSubGroup = null;
      const hotGroup = locationGroups.find(group => group?.type === 'hot');
      if (hotGroup) {
        selectedGroup = hotGroup;
      }

      // 查找已选中的选项所在的分组
      if (!isEmpty(value)) {
        // 遍历所有分组查找已选中的选项
        for (const group of locationGroups) {
          // 检查是否有选中的二级选项
          const hasSelectedItem = group.items?.some(item =>
            value.some(val => val?.value === item?.value && val?.type === item?.type)
          );

          if (hasSelectedItem) {
            selectedGroup = group;
            break;
          }

          // 检查是否有选中的三级选项
          if (group.maxDepth === 2) {
            let foundSubGroup = false;

            for (const item of group.items || []) {
              const hasSelectedSubItem = item.items?.some(subItem =>
                value.some(val => val?.value === subItem?.value && val?.type === subItem?.type)
              );

              if (hasSelectedSubItem) {
                selectedGroup = group;
                selectedSubGroup = item;
                foundSubGroup = true;
                break;
              }
            }

            if (foundSubGroup) {
              break;
            }
          }
        }
      }

      // 设置选中的分组
      setActiveGroup(selectedGroup?.type);

      // 如果是三级分类（maxDepth === 2）
      if (selectedGroup?.maxDepth === 2) {
        if (selectedSubGroup) {
          // 如果找到了已选中的子分组，则选中它
          setActiveSubGroup(selectedSubGroup);
        } else if (Array.isArray(selectedGroup?.items) && selectedGroup.items.length > 0) {
          // 否则默认选中第一个子分组
          setActiveSubGroup(selectedGroup.items[0]);
        }
      } else {
        setActiveSubGroup(null);
      }
    }
  }, [locationGroups, activeGroup, visible]);

  // 当activeGroup变化时，如果是三级分类，查找已选中的子分组或默认选中第一个
  useEffect(() => {
    const currentGroup = locationGroups.find(group => group?.type === activeGroup);

    if (currentGroup?.maxDepth === 2 && Array.isArray(currentGroup?.items) && currentGroup.items.length > 0) {
      // 查找已选中的子分组
      let selectedSubGroup = null;

      if (!isEmpty(value)) {
        // 先查找直接选中的二级选项
        selectedSubGroup = currentGroup.items.find(item =>
          value.some(val => val?.value === item?.value && val?.type === item?.type)
        );

        // 如果没有直接选中的二级选项，查找包含选中三级选项的二级选项
        if (!selectedSubGroup) {
          for (const item of currentGroup.items) {
            const hasSelectedSubItem = item.items?.some(subItem =>
              value.some(val => val?.value === subItem?.value && val?.type === subItem?.type)
            );

            if (hasSelectedSubItem) {
              selectedSubGroup = item;
              break;
            }
          }
        }
      }

      // 如果找到了已选中的子分组，则选中它，否则默认选中第一个
      setActiveSubGroup(selectedSubGroup || currentGroup.items[0]);
    } else {
      setActiveSubGroup(null);
    }
  }, [activeGroup, locationGroups, value, visible]);

  // 使用 useMemo 缓存当前选中的分组
  const currentGroup = useMemo(() =>
    locationGroups.find(group => group?.type === activeGroup),
    [locationGroups, activeGroup]
  );

  const handleGroupChange = (group: LocationGroup) => {
    setActiveGroup(group?.type);
    setActiveSubGroup(null);
    resetRightContentOffY();
  };

  const handleSubGroupChange = (option: LocationOption) => {
    setActiveSubGroup(option); 
    resetRightContentOffY();
  };

  const resetRightContentOffY = () => {
    // 设置右侧滚动到顶部
    if (rightScrollRef?.current?.scrollTo) {
      rightScrollRef.current.scrollTo({
        y: 0,
        animated: false
      });
    }
  }

  // 检查选项是否被选中
  const isOptionSelected = (option: LocationOption) => {
    return !isEmpty(value) && value.some(item =>
      item?.value === option?.value && item?.type === option?.type
    );
  };

  // 检查分组中是否有选中的选项
  const hasSelectedOptionInGroup = (group: LocationGroup): boolean => {
    if (!group || !Array.isArray(group.items) || group.items.length === 0) {
      return false;
    }

    // 三级分类选中
    if (group?.maxDepth === 2 && !isEmpty(group?.items)) {
      return group.items.some(option =>
        option?.items?.some(subOption => isOptionSelected(subOption))
      );
    }
    return group.items.some(option => isOptionSelected(option));
  };

  // 处理选项点击
  const handleOptionClick = (option: LocationOption) => {
    if (!option) return;
    const isSelected = isOptionSelected(option);
    if (isSelected) {
      return;
    }

    if (option?.type === locationSubFilterType.linearDistance) {
      option.filterType = locationSubFilterType.gis_distance;
      option.itemId = option?.value;
      option.itemName = option?.title;
    } else {
      let newOption = {
        geoHash: option?.geohash ?? '',
        category: option?.type ?? '',
        itemName: option?.title ?? '',
        id: option?.id ?? '',
        userSelect: option?.userSelect ?? ''
      }
      // 将 option jsonstringify
      option.itemId = JSON.stringify(newOption);
      option.filterType = locationSubFilterType.gis_location;
      option.itemName = option?.title;
    }
    // const isSelected = isOptionSelected(option);
    let newValue;
    // 先移除同一section中的其他选项（保证section内单选）
    const sameTypeClearedValue = (value || []).filter(item =>
      item?.type !== option?.type
    );

    if (option?.type === locationSubFilterType.linearDistance) {
      // linearDistance类型可以与其他section共存
      newValue = [...sameTypeClearedValue, option];
    } else {
      // 其他类型section之间互斥，需要移除所有非linearDistance的选项
      const linearDistanceItems = (value || []).filter(item =>
        item?.type === locationSubFilterType.linearDistance
      );
      newValue = [...linearDistanceItems, option];
    }
    // 不改变当前选中的分组和子分组
    onChange(newValue);
  };

  // 清空所有选择
  const handleClear = () => {
    mtaTrack(false, HOTEL_SEARCH_EVENT_ID.HotelRNListFilterClearNew, {
      displayName: '清空',
      filterPanelName: '位置距离',
      filterPanelCode: glabelFilterType.location_distance,
      index: 2
    });

    if (isEmpty(value)) {
      return;
    }
    onChange([]);
  };

  // 获取当前选中分组的子选项
  const currentItems = currentGroup?.items || [];
  // 获取当前选中子分组的子选项
  const currentSubItems = activeSubGroup?.items || [];
  // 判断当前分组是否需要显示中间列（maxDepth === 2）
  const showMiddleColumn = !isEmpty(activeSubGroup)

  if (!visible) return null;

  // 显示加载中
  if (loading) {
    return (
      <View style={style} className={styles.container}>
        <View className={styles.mask} onClick={() => onClose()} />
        <View className={styles.loadingContainer}>
          <Loading />
        </View>
      </View>
    );
  }

  // 显示错误或数据为空
  if (isEmpty(locationGroups)) {
    return (
      <View
        style={style}
        className={styles.container}>
        <View
          className={styles.mask}
          onClick={() => onClose()} />
        <View
          className={styles.errorContainer} >
          <Text>数据加载失败</Text>
          {onRetry && (
            <View onClick={onRetry}>
              <Text
                className={styles.reloadText}
                onClick={onRetry}>
                重新加载
              </Text>
            </View>
          )}
        </View>
      </View>
    );
  }

  const renderHotItem = (option, index) => {
    const {
      title,
      desc = '用户选择',
      userSelect
    } = option || {};
    if (isEmpty(title)) {
      return null
    }
    let showSubTitle = false
    if (!isEmpty(desc) && !isEmpty(userSelect)) {
      showSubTitle = true
    }

    if (!showSubTitle) {
      return (
        <Text
          numberOfLines={2}
          className={`${styles.middleItemTitle} ${activeSubGroup?.value === option?.value ? activeMiddleItemStyle : ''} ${isOptionSelected(option) ? selectedItemStyle : ''}`}
          style={activeSubGroup?.value === option?.value ? { fontWeight: THEME_FONT.fontWeight.Medium } : {}}
        >
          {option?.title || ''}
        </Text>
      )
    }

    return (
      <View className={styles.hotContainer}>
        <Text
          numberOfLines={1}
          className={`${styles.hotTitle} ${isOptionSelected(option) ? selectedItemStyle : ''}`}
          style={isOptionSelected(option) ? { fontWeight: THEME_FONT.fontWeight.Medium } : {}}
        >
          {option?.title || ''}
        </Text>
        <View className={styles.hotSubContainer}>
          <Text className={index <= 2 ? styles.hotSubTitle : styles.hotSubTitleNormal}>{userSelect}</Text>
          <Text className={styles.hotSubDesc}>{desc}</Text>
        </View>

      </View>
    )
  }

  return (
    <View style={style}
      className={styles.container}>
      <View
        className={styles.mask}
        onClick={() => onClose()}
      />
      <View
        className={panelStyle}>
        <View
          className={styles.content}>
          {/* 左侧分类列表 */}
          <View style={{ width: pt(90) }}>
            <ScrollView
              className={styles.leftColumn}>
              {locationGroups.map((group, idx) => (
                <View
                  className={`${styles.leftContainer} ${activeGroup === group?.type ? styles.activeContainer : ''}`}
                  key={group?.type || idx}
                  onClick={() => {
                    if (activeGroup === group?.type) return
                    handleGroupChange(group)
                  }} >
                  {hasSelectedOptionInGroup(group) ? <View className={styles.leftTip} /> : null}
                  <Text
                    numberOfLines={1}
                    className={`${styles.leftItem} ${activeGroup === group?.type ? activeItemStyle : ''}`}
                  >
                    {group?.title || ''}
                  </Text>
                </View>
              ))}
            </ScrollView>
          </View>

          {/* 中间选项列表 */}
          {
            showMiddleColumn && (
              <View style={{ width: pt(104) }} >
                <ScrollView className={styles.middleColumn}>
                  {
                    currentItems.map((option, index) => (
                      <View
                        key={`${option?.type || ''}-${option?.value || ''}-${index}`}
                        className={styles.middleItem}
                        onClick={() => handleSubGroupChange(option)} >
                        {renderHotItem(option, index)}
                        {activeSubGroup?.value === option?.value && (
                          <View className={styles.selectIcon} />
                        )}
                      </View>
                    ))
                  }
                </ScrollView>
              </View>
            )
          }
          {/* 右侧选项列表 */}
          <ScrollView className={styles.rightColumn} ref={rightScrollRef}>
            {
              showMiddleColumn && activeSubGroup ? (
                // 显示三级选项 
                currentSubItems.map((option, index) => (
                  <View
                    key={`${option?.type || ''}-${option?.value || ''}-${index}`}
                    className={styles.rightItem}
                    onClick={() => handleOptionClick(option)} >
                    <Text
                      numberOfLines={2}
                      className={`${styles.rightItemTitle} ${isOptionSelected(option) ? selectedItemStyle : ''}`}
                      style={isOptionSelected(option) ? { fontWeight: THEME_FONT.fontWeight.Medium } : {}}
                    >
                      {option?.title || ''}
                    </Text>
                    <View className={styles.rightImgContainer}>
                      {isOptionSelected(option) ? <Image className={styles.rightItemImg} src={confirmIconUrl} /> : null}
                    </View>
                  </View>
                ))
              ) : (
                // 显示二级选项 (当maxDepth===1或未选中二级分类时)
                currentItems.map((option, index) => (
                  <View
                    key={`${option?.type || ''}-${option?.value || ''}-${index}`}
                    className={styles.rightItem}
                    onClick={() => handleOptionClick(option)} >
                    {renderHotItem(option, index)}
                    <View className={styles.rightImgContainer}>
                      {isOptionSelected(option) ? <Image className={styles.rightItemImg} src={confirmIconUrl} /> : null}
                    </View>
                  </View>
                ))
              )
            }
          </ScrollView>
        </View>
        {/* 底部按钮 */}
        <View className={styles.footer}>
          <View
            style={{ marginRight: 16 }}
            className={styles.clearBtn}
            onClick={handleClear}
          >
            <Text
              className={styles.clearBtn}
              style={{ borderWidth: 0 }}
            >清空</Text>
          </View>
          <View
            className={confirmBtnContainerStyle}
            onClick={() => {
              onClose(true, {
                displayName: '查看结果',
                filterPanelName: '位置距离',
                filterPanelCode: glabelFilterType.location_distance,
              })
            }}
          >
            <Text
              className={styles.confirmBtn}>
              查看结果
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default memo(LocationFilter);

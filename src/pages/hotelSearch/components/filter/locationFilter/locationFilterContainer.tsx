import React, { useState, useEffect, CSSProperties, memo } from 'react';
import LocationFilter from './index';
import useFetch from '@/common/useFetch';
import { reportInfo } from '@/common/reporter';
import { errorCodeConstantMapping, ErrortType } from '@/common/reporter/errorMapping';
import { isEmpty } from '@/utils/isType';
interface LocationFilterContainerProps {
  styleType?: string;
  value?: any[];
  onChange: (values: any[]) => void;
  onClose: (trackConfirm?: boolean, extraData?: any) => void;
  visible: boolean;
  address?: string;
  style: CSSProperties;
  mtaTrack: (isExposure, eventId, eventData) => void
}

const LocationFilterContainer: React.FC<LocationFilterContainerProps> = ({
  styleType,
  value = [],
  onChange,
  onClose,
  visible,
  address = '1,2814,,',
  style,
  mtaTrack
}) => {
  const { apiFetch } = useFetch();
  const [loading, setLoading] = useState(false);
  const [locationData, setLocationData] = useState(null);

  const fetchLocationData = async () => {
    setLoading(true);
    try {
      const [
        provinceCode = '1',
        cityCode = '2814',
        districtCode,
        townCode
      ] = address.split(',');

      const param = {
        bizScene: 'wine-hotel',
        callerApp: 'wine-hotel-front',
        districtInfo: { provinceCode, cityCode, districtCode, townCode }
      }
      const [error, res] = await apiFetch('getLocation', param, true);
      if (!error && res) {
        setLocationData(res);
      } else {
        setLocationData(null);
        reportInfo({
            code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_LOCATION_ERROR,
            errorDetail: {
                errorType: ErrortType.Error,
                functionId: 'getLocation',
                customMsg: {
                    errorDescription: '获取lbs定位失败',
                    errorInfo: error || {},
                    respData: res || {},
                    requestParam: param || {}
                },
            }
        })
      }
    } catch (err) {
      console.error('Failed to fetch location data:', err);
      setLocationData(null);
      reportInfo({
            code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_LOCATION_ERROR_TRY_CATCH,
            errorDetail: {
                errorType: ErrortType.Error,
                customMsg: {
                    errorDescription: '获取lbs定位失败 try/catch',
                    errorInfo: err?.message,
                    errorStack: err?.stack,
                },
            }
        })
    } finally {
      setLoading(false);
    }
  };

    // 当组件可见时获取数据
    useEffect(() => {
      // 如果地址为空，不请求数据
      if (isAddressEmpty(address)) return;
      fetchLocationData();
    }, [address]);
  
    const isAddressEmpty = (address) => {
      if (isEmpty(address)) return true;
      if (address === ',,,') return true;
      return false;
    }

  return (
      <LocationFilter
        styleType={styleType}
        style={style}
        value={value}
        onChange={onChange}
        onClose={onClose}
        visible={visible}
        locationData={locationData}
        loading={loading}
        onRetry={fetchLocationData}
        mtaTrack={mtaTrack}
      />
  );
};

export default memo(LocationFilterContainer);
import React, { useState, useRef, useMemo, useEffect, CSSProperties, memo } from 'react';
import { View, Text, Image } from '@/BaseComponents/atoms';
import { IOScrollView } from '@/BaseComponents/IntersectionObserver';
import styles from './index.module.scss';
import classNames from 'classnames';
import { isEmpty } from '@/utils/isType';
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import {
  getHotelFilterHistory,
  convertHistoryToFilterGroup
} from '../../../utils/hotelFilterHistoryUtils';
import { HOTEL_SEARCH_EVENT_ID } from '../../../constants/mtaParamEvents';
import { THEME_FONT } from '@ltfe/ltfe-core-lib/lib/utiles/theme';
import Section from './section';

interface HotelFilterProps {
  allFilterList?: any[];
  filterMap?: Set<any>; // 所有筛选项列表
  filterPanelVOList: any[];
  onChange: (values: any[]) => void;
  onClose: (trackConfirm?: boolean, extraData?: any) => void;
  visible: boolean;
  style: CSSProperties;
  mtaTrack: (isExposure, eventId, eventData) => void
}

const HotelFilter: React.FC<HotelFilterProps> = ({
  allFilterList = [],
  filterMap = new Set(),
  filterPanelVOList = [],
  onChange,
  onClose,
  visible,
  style,
  mtaTrack
}) => {

  // 合并相同groupCode的filterList
  const mergeFilterGroups = (filterList: any[] = []) => {
    // <groupCode, item>
    // item.itemGroup整合了相同groupCode, 但filterType不同的数据
    const groupMap = new Map();

    filterList.forEach(group => {
      const key = group?.groupCode || '';
      if (groupMap.has(key)) {
        // 如果已存在相同groupCode的组，合并itemList
        const existingGroup = groupMap.get(key);

        // 为每个item添加groupCode属性
        const itemsWithGroupCode = (group?.itemList || []).map(item => ({
          ...item,
          groupCode: key // 添加groupCode属性
        }));

        // 合并两个数组
        // 1. 将itemList合并，用于数据处理
        existingGroup.itemList = [...existingGroup.itemList, ...itemsWithGroupCode];
        // 2. 将itemList添加到对应的item中
        if (existingGroup?.itemGroup) {
          existingGroup.itemGroup.push({
            ...(group || {}),
            itemList: itemsWithGroupCode
          })
        }
      } else {
        // 如果是新的groupCode，为itemList中的每个item添加groupCode后再添加到Map
        const itemsWithGroupCode = (group?.itemList || []).map(item => ({
          ...item,
          groupCode: key // 添加groupCode属性
        }));

        // groupMap.set(key, mergeGroupItem(group, itemsWithGroupCode));
        groupMap.set(key, {
          ...(group || {}),
          itemGroup: [{
            ...(group || {}),
            itemList: itemsWithGroupCode,
          }]
        });
      }
    });

    return Array.from(groupMap.values());
  };

  // 数据转换格式
  const useFilterGroups = () => {
    const hotelFilter = filterPanelVOList?.find(
      panel => panel?.filterPanelCode === 'hotel_filter'
    );
    if (!hotelFilter?.filterList) {
      return [];
    }
    // 先合并相同groupCode的组
    const mergedFilterList = mergeFilterGroups(hotelFilter.filterList);
    return mergedFilterList;
  };

  const [filterGroups, setFilterGroups] = useState<any[]>([]);
  const leftScrollRef = useRef<any>(null);
  const rightScrollRef = useRef<any>(null);
  const [selectedGroupCode, setSelectedGroupCode] = useState('');
  const scrollFlagRef = useRef(false)
  // 记录每个 filterType 的 multi（单选/多选）配置，保证历史组与普通组一致
  const filterTypeMultiRef = useRef<Record<string, number>>({});

  // 用于跟踪选中的项，以便在组件卸载时保存
  const selectedItemsRef = useRef<any[]>([]);

  // 记录每个分组的展开状态
  const [expandedGroups, setExpandedGroups] = useState<{ [key: string]: boolean }>({});

  // 加载历史筛选数据
  useEffect(() => {
    let timer1: any = null;
    let timer2: any = null;
    if (!visible) return;

    const loadFilterGroups = async () => {
      try {
        const initialGroups = useFilterGroups();

        // 获取历史筛选记录
        const historyData = await getHotelFilterHistory();

        // 如果有历史数据，添加历史筛选分组
        const historyGroup = convertHistoryToFilterGroup(historyData);
        if (historyGroup) {
          initialGroups.unshift({
            ...historyGroup,
            itemGroup: [{ ...historyGroup }]
          }
          );
        }

        // 计算每个 filterType 的 multi 配置（排除历史/热门组，以服务端真实面板为准）
        const typeMultiMap: Record<string, number> = {};
        (initialGroups || []).forEach(group => {
          if (group?.groupCode === 'hotel_history' || group?.groupCode === 'hotel_hot') return;
          const groupMulti = Number(group?.multi || 0);
          (group?.itemGroup || []).forEach(sub => {
            const subType = sub?.filterType;
            if (!subType) return;
            const subMulti = Number(sub?.multi ?? groupMulti);
            typeMultiMap[subType] = subMulti;
          })
        });
        filterTypeMultiRef.current = typeMultiMap;

        setFilterGroups(initialGroups || []);
        // 默认选中第一个分组
        let initialGroupCode = (initialGroups || [])?.[0]?.groupCode || 'aaa';
        // 如果filterMap有值，查找最后一个选中项所在的分组
        if (filterMap?.size > 0 && initialGroups?.length > 0) {
          // 获取所有选中项的key
          const selectedKeys = Array.from(filterMap) || [];
          if (selectedKeys?.length > 0) {
            // 获取最后一个选中项的key
            const lastSelectedKey = selectedKeys[selectedKeys.length - 1];
            // 查找这个key对应的分组
            for (const group of initialGroups) {
              if (group.groupCode === 'hotel_history' || group.groupCode === 'hotel_hot') {
                continue;
              }
              const foundItem = group?.itemList.find(item =>
                `${item.filterType}-${item.itemId}` === lastSelectedKey
              );
              if (foundItem) {
                initialGroupCode = group.groupCode;
                break;
              }
            }
          }
        }

        setSelectedGroupCode(initialGroupCode || '');

        // 延迟执行滚动操作，确保DOM已渲染
        timer1 = setTimeout(() => {
          scrollFlagRef.current = true
          // 滚动右侧内容到选中分组
          if (rightScrollRef.current && sectionPositions[initialGroupCode] !== undefined) {
            rightScrollRef.current.scrollTo({
              y: sectionPositions[initialGroupCode],
              animated: false
            });
          }
          // 滚动左侧导航到选中分组
          const index = initialGroups.findIndex(group => group.groupCode === initialGroupCode);
          if (index > -1 && leftScrollRef.current) {
            leftScrollRef.current.scrollTo({
              y: index * pt(50),
              animated: false
            });
          }
          timer2 = setTimeout(() => {
            scrollFlagRef.current = false
          }, 200);
        }, 100);
      } catch (error) {
        console.error('加载筛选组失败:', error);
        // 出错时至少显示基本筛选组
        const initialGroups = useFilterGroups();
        setFilterGroups(initialGroups || []);
        setSelectedGroupCode(initialGroups?.[0]?.groupCode || '');
        scrollFlagRef.current = false
      }
    };

    loadFilterGroups();

    return () => {
      // 组件卸载时清除所有定时器
      if (timer1) clearTimeout(timer1);
      if (timer2) clearTimeout(timer2);
    };
  }, [visible]); // 只依赖 visible，确保每次打开筛选面板时都重新加载

  // 获取不同分组切换状态的key
  const getExpendKey = useMemo(() => (group) => {
    const { groupCode = "", filterType = "" } = group
    return `${groupCode}_${filterType}`
  }, []);

  // 切换展开状态
  const toggleExpand = (key: string) => {
    setExpandedGroups(prev => ({
      ...(prev || {}),
      [key]: !(prev || {})[key]
    }));
  };

  // 计算每个section的高度和位置
  const sectionPositions = useMemo(() => {
    const positions: { [key: string]: number } = {};
    let currentPosition = 0;

    // 取出itemGroup中的每一项计算展示数量
    const calculateItemCount = (group) => {
      let count = 0;
      const exposeItemCount = group?.exposeItemCount || 6;
      (group?.itemGroup || []).forEach((item) => {
        const isExpanded = expandedGroups?.[getExpendKey(item)]
        count += isExpanded ? (item?.itemList || []).length : Math.min(exposeItemCount, (item?.itemList || []).length);
      })
      return count
    }

    (filterGroups || []).forEach((group) => {
      const groupCode = group?.groupCode || '';
      positions[groupCode] = currentPosition;

      const itemGroupCount = (group?.itemGroup || []).length
      const itemCount = calculateItemCount(group);
      const itemRows = Math.ceil(itemCount / 3); // 每行3个选项
      // 计算高度：
      // - 组数 * 标题行高度(42px)
      // - 选项行数 * (item高度 + marginBottom)(34px + 8px)
      currentPosition += (pt(42) * itemGroupCount) + (itemRows * pt(42));
    });

    return positions;
  }, [filterGroups, expandedGroups]); // 依赖项添加expandedGroups

  // 处理左侧导航项点击
  const handleNavItemClick = (groupCode: string) => {
    scrollFlagRef.current = true
    setSelectedGroupCode(groupCode);

    // 使用scrollTo方法滚动到对应位置
    if (rightScrollRef?.current?.scrollTo) {
      rightScrollRef.current.scrollTo({
        y: sectionPositions?.[groupCode] || 0,
        animated: true
      });
    }
    
    // 滚动动画结束后重置标记
    setTimeout(() => {
      scrollFlagRef.current = false
    }, 1000);
  };

  // 处理右侧滚动
  const handleRightScroll = (e) => {
    if (scrollFlagRef.current) return; // 如果是点击导航触发的滚动，不处理
    if (isEmpty(filterGroups)) return;

    const scrollTop = e?.nativeEvent?.contentOffset?.y || 0;

    // 找到当前滚动位置对应的section
    let currentGroupCode = filterGroups?.[0]?.groupCode || '';
    for (const [groupCode, position] of Object.entries(sectionPositions || {})) {
      if (scrollTop >= position) {
        currentGroupCode = groupCode;
      } else {
        break;
      }
    }

    // 更新选中状态
    if (currentGroupCode !== selectedGroupCode) {
      setSelectedGroupCode(currentGroupCode);
      // 同步左侧滚动位置
      const index = (filterGroups || []).findIndex(group => group?.groupCode === currentGroupCode);
      leftScrollRef.current?.scrollTo({
        y: index * pt(50), // 假设每个导航项高度为44px
        animated: true
      });
    }
  };

  const handleSelect = (item: any, group: any) => {
    if (!item) return;

    // 优先按 filterType 的全局 multi 判定；若无则回退到分组的 multi
    const typeKey = item?.filterType || '';
    const globalMulti = filterTypeMultiRef.current?.[typeKey];
    const multi = (globalMulti === undefined || globalMulti === null) ? (group?.multi || 0) : globalMulti;
    const key = `${item?.filterType || ''}-${item?.itemId || ''}`;
    const isSelected = filterMap?.has(key);
    const result = allFilterList || [];
    
    if (Number(multi) === 0) {
      // 原有逻辑
      let newSelectedItems;
      if (isSelected) {
        newSelectedItems = result.filter(
          selectedItem => !(`${selectedItem?.filterType || ''}-${selectedItem?.itemId || ''}` === key)
        );
      } else {
        newSelectedItems = [...result, item];
      }
      selectedItemsRef.current = newSelectedItems;
      onChange(newSelectedItems);
    } else {
      // 单选：移除当前类型下所有已选项，只保留当前点击的
      const groupFilterType = item?.filterType;
      // 过滤掉当前 group 下的所有已选项，只保留其它 group 的
      const newSelectedItems = result.filter(
        selectedItem => selectedItem?.filterType !== groupFilterType
      );
      // 添加当前点击项
      if (!isSelected) {
        newSelectedItems.push(item);
      }
      selectedItemsRef.current = newSelectedItems;
      onChange(newSelectedItems);
    }
  };

  const handleClear = () => {
    mtaTrack(false, HOTEL_SEARCH_EVENT_ID.HotelRNListFilterClearNew, {
      displayName: '清空',
      filterPanelName: '筛选',
      filterPanelCode: "hotel_filter",
      index: 4
    });

    if (isEmpty(allFilterList)) {
      return;
    }

    // 清空选中项引用
    selectedItemsRef.current = [];
    onChange([]);
  };

  // 自定义关闭处理函数，包装原始的onClose
  const handleClose = (trackConfirm?: boolean, extraData?: any) => {
    // 调用原始的onClose
    onClose(trackConfirm, extraData);
  };

  const showDot = useMemo(() => (groupCode: string) => {
    return (filterGroups || []).find(g => g?.groupCode === groupCode)?.itemList?.some(item =>
      filterMap?.has(`${item?.filterType || ''}-${item?.itemId || ''}`)) || false;
  }, [filterGroups, filterMap]);

  if (!visible) return null;

  return (
    <View
      style={style}
      className={styles.container}>
      <View
        className={styles.mask}
        onClick={() => handleClose()}
      />
      <View
        className={styles.panel}>
        <View
          className={styles.content}>
          <View
            style={{ width: pt(88) }}
            className={styles.leftNavWrapper} >
            <IOScrollView
              ref={leftScrollRef}
              // @ts-ignore
              className={styles.leftScroll}
              showsVerticalScrollIndicator={false}
            >
              <View
                className={styles.leftNav}
              >
                {(filterGroups || []).map((group, index) => (
                  <View
                    key={`${group?.groupCode || ''}-${index}`}
                    style={{ paddingLeft: pt(15) }}
                    className={classNames(styles.navItemView, { [styles.navItemViewActive]: selectedGroupCode === group?.groupCode })}
                    onClick={() => handleNavItemClick(group?.groupCode || '')}
                  >
                    {
                      showDot(group?.groupCode || '') && <View className={styles.redPoint} />
                    }
                    <Text className={styles.navItem}
                      style={{
                        height: pt(50),
                        lineHeight: pt(50),
                        color: selectedGroupCode === group?.groupCode ? '#006EEB' : '#1a1a1a',
                        fontWeight: selectedGroupCode === group?.groupCode ? THEME_FONT.fontWeight.SemiBold : THEME_FONT.fontWeight.Regular
                      }}>
                      {group?.groupName || ''}
                    </Text>
                  </View>
                ))}
              </View>
            </IOScrollView>
          </View>

          <View className={styles.rightWrapper}>
            <IOScrollView
              ref={rightScrollRef}
              // @ts-ignore
              className={styles.rightScroll}
              showsVerticalScrollIndicator={false}
              onScroll={handleRightScroll}
              scrollEventThrottle={5} // 控制滚动事件触发频率
            >
              <View
                style={{ paddingLeft: pt(12) }}
                className={styles.rightContent} >
                {(filterGroups || []).map((group, index) => (
                  <View key={`group-${index}`}>
                    {(group?.itemGroup || []).map((item, itemIndex) => (
                      <Section
                        key={`${item?.groupCode || ''}-${itemIndex}`}
                        group={item}
                        onSelect={handleSelect}
                        filterMap={filterMap}
                        expandedGroups={expandedGroups}
                        toggleExpand={toggleExpand}
                        getExpendKey={getExpendKey}
                      />
                    ))}
                  </View>
                ))}
              </View>
            </IOScrollView>
          </View>
        </View>

        <View
          className={styles.footer}>
          <View
            style={{ marginRight: 16 }}
            className={styles.clearBtn}
            onClick={handleClear}>
            <Text
              style={{ borderWidth: 0 }}
              className={styles.clearBtn}
            >
              清空
            </Text>
          </View>
          <View
            className={styles.confirmBtn_container}
            onClick={() => handleClose(true, {
              displayName: `查看结果`,
              filterPanelName: '筛选',
              filterPanelCode: "hotel_filter",
            })}>
            <Text
              className={styles.confirmBtn}>
              查看结果
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default memo(HotelFilter);

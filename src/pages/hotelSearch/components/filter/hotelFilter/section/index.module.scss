.sectionHeader {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-right: 12px;
  height: 42px;
}

.sectionTitle {
  color: #1a1a1a;
  font-size: 14px;
  line-height: 18px;
}

.expandButton {
  align-items: center;
  flex-direction: row;
}

.iconArrow {
  margin-left: 4px;
  width: 4px;
  height: 8px;
  transform: rotate(90deg);
}

.iconArrowOpen {
  margin-left: 4px;
  width: 4px;
  height: 8px;
  transform: rotate(270deg);
}

.options {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.option {
  align-items: center;
  justify-content: center;
  padding: 4px 4px;
  min-height: 34px;
  background: #F5F7FA;
  font-size: 14px;
  color: #191919;
  border-radius: 4px;
}

.optionSelected {
  align-items: center;
  justify-content: center;
  width: 28%;
  padding: 0 4px;
  font-size: 14px;
  color: #333;
  background: #EDF6FF;
  color: #0068FF;
  border: 0.5px solid #006EEB;
}

.optionText {
  line-height: 16px;
  font-size: 12px;
  color: #1a1a1a;
  text-align: center;
}

.optionSelectedText {
  line-height: 16px;
  font-size: 12px;
  color: #0068FF;
  text-align: center;
}
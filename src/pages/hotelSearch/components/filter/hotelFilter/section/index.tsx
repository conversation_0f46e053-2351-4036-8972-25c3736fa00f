
import React from 'react';
import { View, Text, Image } from '@/BaseComponents/atoms';
import styles from './index.module.scss';
import classNames from 'classnames';
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import { THEME_FONT } from '@ltfe/ltfe-core-lib/lib/utiles/theme';

interface SectionProps {
  group: any;
  onSelect: (item: any, group: any) => void;
  filterMap: Set<any>;
  expandedGroups: { [key: string]: boolean };
  toggleExpand: (key: string) => void;
  getExpendKey: (group: any) => string;
}

const Section: React.FC<SectionProps> = ({ 
  group, 
  onSelect, 
  filterMap, 
  expandedGroups, 
  toggleExpand,
  getExpendKey
}) => {
  if (!group) return null;
  const itemList = group?.itemList || [];
  const filterName = group?.filterName || '';
  const exposeItemCount = group?.exposeItemCount || 6;

  const showExpandButton = itemList.length > exposeItemCount;
  const isExpanded = expandedGroups?.[getExpendKey(group)];
  const displayItems = isExpanded ? itemList : itemList.slice(0, exposeItemCount);

  const isItemSelected = (item: any) => {
    if (!item) return false;
    const key = `${item?.filterType || ''}-${item?.itemId || ''}`;
    return filterMap?.has(key);
  };

  return (
    <View className={styles.section}>
      <View className={styles.sectionHeader}>
        <Text
          className={styles.sectionTitle}
          style={{
            fontWeight: THEME_FONT.fontWeight.Medium
          }}>
          {filterName}
        </Text>
        {showExpandButton && (
          <View
            className={styles.expandButton}
            onClick={() => {
              toggleExpand(getExpendKey(group));
            }}
          >
            <Text style={{ fontSize: pt(12), color: '#006EEB' }}>
              {isExpanded ? '收起' : '展开'}
            </Text>
            <Image
              className={isExpanded ? styles.iconArrowOpen : styles.iconArrow}
              src={'https://img14.360buyimg.com/imagetools/jfs/t1/135893/18/49718/411/67164594F901f961b/03a5baab453a73c4.png'} />
          </View>
        )}
      </View>
      <View className={styles.options}>
        {
          displayItems.map((item, index) => (
            <View
              key={`${item?.filterType || ''}-${item?.itemId || ''}-${index}`}
              className={classNames(styles.option, {
                [styles.optionSelected]: isItemSelected(item)
              })}
              style={{ width: pt(82), borderRadius: pt(4), marginRight: pt(8), marginBottom: pt(8) }}
              onClick={() => onSelect(item, group)}
            >
              <Text
                numberOfLines={2}
                className={isItemSelected(item) ? styles.optionSelectedText : styles.optionText}>
                {item?.itemName || ''}
              </Text>
            </View>
          ))
        }
      </View>
    </View>
  );
};

export default Section;


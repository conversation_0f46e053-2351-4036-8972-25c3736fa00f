.container {
  position: absolute;
  top: -30px;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  z-index: 100;
}

.mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
}

.panel {
  display: flex;
  left: 0;
  right: 0;
  background: #fff;
  flex-direction: column;
  height: 60%;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: row;
  overflow: hidden;
}

.leftNavWrapper {
  display: flex;
  flex: 1;
  flex: none;
  background: #F5F5F5;
}

.leftScroll {
  flex: 1;
}

.leftNav {
  flex-direction: column;
}

.rightWrapper {
  flex: 1;
  overflow: hidden;
}

.rightScroll {
  flex: 1;
}

.rightContent {
  background: #fff;
}

.navItemView {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.navItemViewActive {
  background-color: #fff;
  color: #0068FF;
}

.navItem {
  padding: 0;
  font-size: 14px;
  color: #1a1a1a;
  display: flex;
}

.navItemActive {
  background: #fff;
  color: #0068FF;
}

.navItemActive::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 14px;
  background: #0068FF;
  border-radius: 0 2px 2px 0;
}

.footer {
  flex-direction: row;
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-top: 1px solid #EEEEEE;
}

.clearBtn {
  flex: 1;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 22px;
  font-size: 16px;
  border: 0.8px solid #5E6880;
  border-radius: 6px;
  color: #5E6880;
}

.confirmBtn_container {
  flex: 1;
  height: 40px;
  align-items: center;
  justify-content: center;
  line-height: 40px;
  background: #013B94;
  border-radius: 6px;
}

.confirmBtn {
  text-align: center;
  font-size: 16px;
  color: #fff;
}

.redPoint {
  color: red;
  background-color: #FF0400;
  width: 4px;
  height: 4px;
  border-radius: 2px;
  position: absolute;
  left: 8px;
}
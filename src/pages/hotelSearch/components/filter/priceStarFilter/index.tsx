import React, { useState, useEffect, CSSProperties, memo } from "react";
import { View, Text, Image } from '@/BaseComponents/atoms'
import Slider from "@/Components/Filter/element/price/slider";
import styles from "./index.module.scss";
import { isEmpty } from "@/utils/isType";
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import useJumpTo from '@/common/useJumpTo'
import { HOTEL_SEARCH_EVENT_ID } from '../../../constants/mtaParamEvents';
import { glabelFilterType, hotelPriceStarType } from '@/pages/hotelSearch/constants/filterType'
import { getImageUrl } from "@/assets/imgs";

const IMG = {
    slider_icon: getImageUrl('slider_icon')
}
interface PriceRange {
    min: number;
    max: number;
    label: string;
    type: "price";
    filterType?: string;
    itemId?: string;
    itemName?: string;
}

interface StarLevel {
    level: number;
    label: string;
    type: "star";
    filterType?: string;
    itemId?: string;
    itemName?: string;
}

type FilterItem = PriceRange | StarLevel;

interface PriceStarFilterProps {
    styleType?: string;
    value?: FilterItem[];
    filterMap?: Set<any>;
    onChange: (values: FilterItem[]) => void;
    onClose: (trackConfirm?: boolean, extraData?: any) => void;
    visible: boolean;
    filterPanelVOList?: any[];
    style: CSSProperties;
    mtaTrack: (isExposure, eventId, eventData) => void
}

const PriceStarFilter: React.FC<PriceStarFilterProps> = ({
    styleType,
    value = [],
    filterMap = new Set(),
    onChange,
    onClose,
    visible,
    filterPanelVOList = [],
    style,
    mtaTrack
}) => {
    const jumpTo = useJumpTo()

    const getPriceRanges = () => {
        const priceStarPanel = filterPanelVOList?.find(
            (panel) => panel?.filterPanelCode === glabelFilterType.price_star
        );
        const priceGroup = priceStarPanel?.filterList?.find(
            (group) => group?.groupCode === hotelPriceStarType.hotel_price
        );

        if (!priceGroup?.itemList) return [];

        return (priceGroup.itemList || []).map((item) => {
            const [min, max] = (item?.itemId || "0,0").split(",").map((v) => (v === "" ? -1 : Number(v)));
            return {
                min: min || 0,
                max: max || 0,
                label: item?.itemName || "",
                filterType: item?.filterType || "",
                itemId: item?.itemId || "",
                itemName: item?.itemName || "",
                groupCode: priceGroup?.groupCode || ''
            };
        });
    };

    const getStarLevels = () => {
        const priceStarPanel = filterPanelVOList?.find(
            (panel) => panel?.filterPanelCode === glabelFilterType.price_star
        );
        const starGroup = priceStarPanel?.filterList?.find(
            (group) => group?.groupCode === hotelPriceStarType.hotel_grade
        );

        if (!starGroup?.itemList) return [];

        return (starGroup.itemList || []).map((item) => {
            return {
                level: Number(item?.itemId || 0),
                label: item?.itemName || "",
                type: "star",
                filterType: item?.filterType || "",
                itemId: item?.itemId || "",
                itemName: item?.itemName || "",
                itemDesc: item?.itemDesc || "",
                groupCode: starGroup?.groupCode || ''
            };
        });
    };

    // 获取星级说明信息
    const getStarExplain = () => {
        const priceStarPanel = filterPanelVOList?.find(
            (panel) => panel?.filterPanelCode === glabelFilterType.price_star
        );
        const starGroup = priceStarPanel?.filterList?.find(
            (group) => group?.groupCode === hotelPriceStarType.hotel_grade
        );

        if (!starGroup) return null;

        return {
            explain: starGroup.explain || null,
            desc: starGroup.desc?.text || '',
            groupName: starGroup.groupName || "价格/星级"
        };
    };

    const getPriceExplainName = () => {
        const priceStarPanel = filterPanelVOList?.find(
            (panel) => panel?.filterPanelCode === glabelFilterType.price_star
        );
        const priceGroup = priceStarPanel?.filterList?.find(
            (group) => group?.groupCode === hotelPriceStarType.hotel_price
        );
        return priceGroup?.groupName || "价格"
    };

    const PRICE_RANGES = getPriceRanges();
    const STAR_LEVELS = getStarLevels();
    const starInfo = getStarExplain();
    const MAX_PRICE = 1050;
    const MIN_PRICE = 0;
    const SHOW_MAX_PRICE = 1000;

    // 查找当前选中的价格和星级
    const selectedPrice = (value || []).find((item) => "min" in item) as
        | PriceRange
        | undefined;

    // 初始化滑块值
    const [sliderValues, setSliderValues] = useState({
        min: selectedPrice?.min || MIN_PRICE,
        max: selectedPrice?.max || MAX_PRICE,
        step: 50,
    });

    // 当选中的价格变化时，更新滑块值
    useEffect(() => {
        if (selectedPrice) {
            setSliderValues({
                min: selectedPrice.min || MIN_PRICE,
                max: selectedPrice.max === -1 ? MAX_PRICE : (selectedPrice.max || MAX_PRICE),
                step: 50,
            });
        } else {
            setSliderValues({
                min: MIN_PRICE,
                max: MAX_PRICE,
                step: 50,
            });
        }
    }, [selectedPrice]);

    const formatText = ({ min, max }) => {
        // 异常数据
        if (min > max || min < MIN_PRICE || min > MAX_PRICE || max > MAX_PRICE) {
            return ''
        }
        // 如果选择了整个价格范围，返回空字符串
        if (min === MIN_PRICE && max === MAX_PRICE) {
            return '';
        }
        // "xx以下"
        if (min === MIN_PRICE && max <= MAX_PRICE) {
            return `¥${max}以下`
        }

        if (min >= SHOW_MAX_PRICE) {
            return `¥${SHOW_MAX_PRICE}以上`
        }

        if (max === MAX_PRICE) {
            return `¥${min}以上`
        }
        // "¥min-¥max"
        if (min > MIN_PRICE && min < MAX_PRICE && max <= MAX_PRICE) {
            return `¥${min}-¥${max}`
        }
        // "xx以上"
        if (min === MAX_PRICE && max === MAX_PRICE) {
            return `¥${max}以上`
        }
        return ''
    }

    const isFilterSelected = (item) => {
        return filterMap?.has(`${item?.filterType || ""}-${item?.itemId || ""}`);
    }

    // 当滑块值变化时更新
    const handleSliderChange = (values: number[]) => {
        if (!Array.isArray(values) || values.length < 2) return;
        const [min, max] = values;
        setSliderValues((prev) => ({
            ...(prev || {}),
            min: min || MIN_PRICE,
            max: max >= MAX_PRICE ? MAX_PRICE : (max || MAX_PRICE),
        }));
    };

    // 滑块完成时应用价格筛选
    const handleSliderComplete = () => {
        // 找到最接近的价格范围
        const priceItem = PRICE_RANGES.find(
            (range) =>
                (sliderValues?.min || 0) >= (range?.min || 0) &&
                ((sliderValues?.max || 0) <= (range?.max || 0) || (range?.max || 0) === -1)
        );

        // if (!priceItem) return;
        const newPrice: PriceRange = {
            min: sliderValues?.min || MIN_PRICE,
            max: (sliderValues?.max || 0) >= MAX_PRICE ? -1 : (sliderValues?.max || 0),
            label: `¥${sliderValues?.min || 0}-${(sliderValues?.max || 0) >= MAX_PRICE ? "不限" : (sliderValues?.max || 0)}`,
            type: "price",
            filterType: priceItem?.filterType || "hotel_price_lowest",
            itemId: `${sliderValues?.min || 0},${(sliderValues?.max || 0) >= MAX_PRICE ? "" : (sliderValues?.max || 0)}`,
            itemName: priceItem?.itemName || "",
        };

        // 移除旧的价格项，添加新的价格项
        const newValue = (value || []).filter((item) => !("min" in item));
        if (!(newPrice?.min === MIN_PRICE && newPrice?.max === -1)) {
            newValue.push(newPrice);
        }
        onChange(newValue);
    };

    // 处理价格选项点击
    const handlePriceClick = (priceRange: any) => {
        if (!priceRange) return;

        const isSelected = isFilterSelected(priceRange);
        let newValue = [...(value || [])];
        if (isSelected) {
            newValue = newValue.filter((item) => !("min" in item));
        } else {
            // 移除旧的价格项，添加新的价格项
            newValue = newValue.filter((item) => !("min" in item));
            newValue.push(priceRange);
        }
        onChange(newValue);
    };

    // 处理星级选项点击
    const handleStarClick = (starLevel: any) => {
        if (!starLevel) return;

        const isSelected = isFilterSelected(starLevel);

        let newValue = [...(value || [])];

        if (isSelected) {
            // 如果已选中，则取消选择
            newValue = newValue.filter(
                (item) => item?.itemId !== starLevel?.itemId
            );
        } else {
            // 否则添加该星级（多选模式）
            const newStar: StarLevel = {
                level: Number(starLevel?.itemId || 0),
                label: starLevel?.itemName || "",
                type: "star",
                filterType: starLevel?.filterType || "",
                itemId: starLevel?.itemId || "",
                itemName: starLevel?.itemName || "",
            };

            // 添加新的星级项，保留其他星级项
            newValue.push(newStar);
        }

        onChange(newValue);
    };

    const handleClear = () => {
        mtaTrack(false, HOTEL_SEARCH_EVENT_ID.HotelRNListFilterClearNew, {
            displayName: '清空',
            filterPanelName: '价格/星级',
            filterPanelCode: glabelFilterType.price_star,
            index: 3
        })
        if (isEmpty(value)) {
            return;
        }
        onChange([]);
    };

    if (!visible) return null;

    const showChannelType = styleType === 'channel';
    const panelStyle = showChannelType ? styles.panel_channel : styles.panel;

    const renderChannelStyle = () => {
        return (
            <View style={style} className={styles.container}>
                <View className={styles.mask} onClick={() => onClose()} />
                <View className={panelStyle}>
                    <View className={styles.section}>
                        <View className={styles.options}>
                            {STAR_LEVELS.map((star, index) => {
                                // 检查当前星级是否被选中
                                const isSelected = isFilterSelected(star);

                                return (
                                    <View
                                        key={`${star?.level || 0}-${index}`}
                                        className={`${styles.option} ${isSelected ? styles.selected_red : ""}`}
                                        style={{ width: pt(109), minHeight: pt(44), marginRight: pt(8), marginBottom: pt(8) }}
                                        onClick={() => handleStarClick(star)}
                                    >
                                        <Text
                                            className={
                                                isSelected
                                                    ? styles.selectedText_red
                                                    : styles.optionText
                                            }
                                        >
                                            {star?.label || ""}
                                        </Text>
                                        {star.itemDesc ? <Text
                                            className={
                                                isSelected
                                                    ? styles.selectDesc_red
                                                    : styles.optionDesc
                                            }
                                        >
                                            {star.itemDesc}
                                        </Text> : null}
                                    </View>
                                );
                            })}
                        </View>
                    </View>
             

                    <View className={styles.footer}>
                        <View className={styles.clearBtn} style={{ marginRight: 16 }} onClick={handleClear}>
                            <Text className={styles.clearBtn} style={{ borderWidth: 0, paddingLeft: 0 }} >
                                清空
                            </Text>
                        </View>
                        <View className={styles.confirmBtn_container_red} onClick={() => {
                            onClose(true, {
                                displayName: `查看结果`,
                                filterPanelName: '价格/星级',
                                filterPanelCode: glabelFilterType.price_star,
                            })
                        }}>
                            <Text className={styles.confirmBtn}>查看结果</Text>
                        </View>
                    </View>
                </View>
            </View>
        )
    }


    const renderNormalStyle = () => {
        return (
            <View style={style} className={styles.container}>
                <View className={styles.mask} onClick={() => onClose()} />
                <View className={styles.panel}>
                    <View className={styles.section} style={{ paddingLeft: pt(16), paddingRight: 0 }}>
                        <View className={styles.priceSectionView}>
                            <Text className={styles.sectionTitle}>{getPriceExplainName()}</Text>
                            <Text className={styles.sectionTitleNum}>
                                {formatText(sliderValues)}
                            </Text>
                        </View>
                        <View className={styles.silerPriceContainer} style={{ marginRight: pt(16) }}>
                            <Text className={styles.silerPriceText}>
                                ¥{MIN_PRICE || 0}
                            </Text>
                            <Text className={styles.silerPriceText}>¥{SHOW_MAX_PRICE}以上</Text>
                        </View>

                        <Slider
                            style={{ marginRight: pt(16) }}
                            value={[sliderValues?.min || MIN_PRICE, sliderValues?.max || MAX_PRICE]}
                            onValueChange={handleSliderChange}
                            minimumValue={MIN_PRICE}
                            maximumValue={MAX_PRICE}
                            step={sliderValues?.step || 50}
                            minimumTrackTintColor="#0068FF"
                            maximumTrackTintColor="#DADADA"
                            thumbImage={{ uri: IMG.slider_icon }}
                            onSlidingComplete={handleSliderComplete}
                        />

                        <View className={styles.options}>
                            {PRICE_RANGES.map((range, index) => {
                                const isSelected = isFilterSelected(range);
                                return (
                                    <View
                                        key={`${range?.min || 0}-${range?.max || 0}-${index}`}
                                        className={`${styles.option} ${isSelected ? styles.selected : ""}`}
                                        style={{ width: pt(109), height: pt(30), marginRight: pt(8), marginBottom: pt(8) }}
                                        onClick={() => handlePriceClick(range)}
                                    >
                                        <Text className={isSelected ? styles.selectedPriceText : styles.priceText} >
                                            {range?.label || ""}
                                        </Text>
                                    </View>
                                );
                            })}
                        </View>
                    </View>

                    <View className={styles.section}>
                        <View
                            className={styles.sectionTitleView}
                            onClick={() => {
                                if (starInfo?.explain?.jumpUrl) {
                                    jumpTo({ to: 'web', params: { url: starInfo?.explain?.jumpUrl } })
                                }
                            }}
                        >
                            <Text className={styles.sectionTitle}>{starInfo?.groupName || "星级"}</Text>
                            {
                                !isEmpty(starInfo?.explain?.text) && !isEmpty(starInfo?.explain?.jumpUrl) && <View className={styles.sectionTitlDesc}>
                                    <Text className={styles.sectionTitlDescTxt}>
                                        {starInfo?.explain?.text || ""}
                                    </Text>
                                    <Image
                                        className={styles.linkIcon}
                                        src='https://img14.360buyimg.com/imagetools/jfs/t1/135893/18/49718/411/67164594F901f961b/03a5baab453a73c4.png' />
                                </View>
                            }

                        </View>

                        <View className={styles.options}>
                            {STAR_LEVELS.map((star, index) => {
                                // 检查当前星级是否被选中
                                const isSelected = isFilterSelected(star);

                                return (
                                    <View
                                        key={`${star?.level || 0}-${index}`}
                                        className={`${styles.option} ${isSelected ? styles.selected : ""}`}
                                        style={{ width: pt(109), minHeight: pt(44), marginRight: pt(8), marginBottom: pt(8) }}
                                        onClick={() => handleStarClick(star)}
                                    >
                                        <Text
                                            className={
                                                isSelected
                                                    ? styles.selectedText
                                                    : styles.optionText
                                            }
                                        >
                                            {star?.label || ""}
                                        </Text>
                                        {star.itemDesc ? <Text
                                            className={
                                                isSelected
                                                    ? styles.selectDesc
                                                    : styles.optionDesc
                                            }
                                        >
                                            {star.itemDesc}
                                        </Text> : null}
                                    </View>
                                );
                            })}
                        </View>
                    </View>
                    {
                        !isEmpty(starInfo?.desc) &&
                        <View className={styles.tip}>
                            <Text className={styles.tipTxt}>{starInfo?.desc || ""}</Text>
                        </View>
                    }

                    <View className={styles.footer}>
                        <View className={styles.clearBtn} style={{ marginRight: 16 }} onClick={handleClear}>
                            <Text className={styles.clearBtn} style={{ borderWidth: 0, paddingLeft: 0 }} >
                                清空
                            </Text>
                        </View>
                        <View className={styles.confirmBtn_container} onClick={() => {
                            onClose(true, {
                                displayName: `查看结果`,
                                filterPanelName: '价格/星级',
                                filterPanelCode: glabelFilterType.price_star,
                            })
                        }}>
                            <Text className={styles.confirmBtn}>查看结果</Text>
                        </View>
                    </View>
                </View>
            </View>
        )
    }


    return (
        showChannelType ? renderChannelStyle() : renderNormalStyle()
    );
};

export default memo(PriceStarFilter);

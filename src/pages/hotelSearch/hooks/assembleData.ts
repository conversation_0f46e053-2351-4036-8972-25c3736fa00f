import { MTAPlaceHolderString, MTAPlaceHolderNumber } from '../constants/config'

/**
 * 构建酒店卡片埋点数据
 * @param item 酒店卡片数据
 * @param index 卡片索引
 * @returns 格式化后的埋点数据
 */
export const buildHotelCardEventData = (item: any, index: number) => {
  if (!item.hotelCardVO) return null;

  // 构建标签列表
  const getTagList = (data: any) => {
    const keys = ['hotelRightPromotion'];
    const promotionTagListMap = data?.promotionTagListMap || {};
    const result: Array<{ trackId: string, labelName: string }> = [];
    
    for (const key in promotionTagListMap) {
      if (Object.prototype.hasOwnProperty.call(promotionTagListMap, key)) {
        if (keys.includes(key)) {
          const ele = promotionTagListMap[key] || [];
          ele.forEach((item: any) => {
            result.push({
              trackId: item?.trackId || MTAPlaceHolderString,
              labelName: item?.listShowName || MTAPlaceHolderString
            });
          });
        }
      }
    }
    
    return result.length ? result : MTAPlaceHolderString;
  };

  const discountList = item?.hotelCardVO?.promotionLayerVO?.promotionDetailList || [];
  
  function toNumber(data: any, def = MTAPlaceHolderNumber) {
    return (data == null || data === "" || isNaN(Number(data))) ? def : Number(data);
  }
  
  return {
    hotelId: item?.hotelCardVO?.id || MTAPlaceHolderString,
    score: toNumber(item?.hotelCardVO?.promotionTagListMap?.hotelScore?.[0]?.listShowName),
    index,
    firpricetype: '11',
    firprice: toNumber(item?.hotelCardVO?.price),
    secpricetype: '52',
    secprice: toNumber(item?.hotelCardVO?.originPrice) || MTAPlaceHolderNumber,
    tagList: getTagList(item?.hotelCardVO || {}),
    discountType: Array.isArray(discountList) ? discountList.map((item: any) => item.type) : MTAPlaceHolderNumber,
    rankId: item?.hotelCardVO?.wareRankVO?.rankId || MTAPlaceHolderString,
    ...item?.userAction || {}
  };
};
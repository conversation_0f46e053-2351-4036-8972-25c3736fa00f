import {
  useState,
  useCallback,
  useEffect,
  useRef
} from 'react'

import {
  fetchHotelListWithDuccConfig,
  fetchUserIdentityList
} from '../services/api'

import {
  PAGE_SIZE,
  SortType
} from '../constants/config'

import useFetch from '@/common/useFetch'

import globalInfoModel from '@/store/model/globalInfo.model'

import {
  isEmpty
} from '@/utils/isType'

import {
  deCodeDebBase64ParseSafe,
  enCodeDebBase64ParseSafe
} from '@/Components/utils'

import {
  LoadMoreStatus,
  POSITION_TYPE
} from '../constants/config'

import {
  saveHotelSearchData,
  saveHotelBookingInfo,
  getHotelSearchData,
  getHotelBookingInfo
} from '../utils/shareDataUtils'

import {
  isObjectEqual
} from '../utils/utils'
import { checkValidDate, getDefaultDate } from '@/Components/utils'
import defaultRequestParams from '../services/defaultRequestParams'

import useJumpTo from "@/common/useJumpTo";
import { sendMtaEvent } from '../utils/mtaUtils'
import { saveHotelFilterHistory } from '../utils/hotelFilterHistoryUtils';
import { mtaPv } from '@/common/mta'
import { M_PAGEID, M_PAGE } from '@/common/mta'
import { reportInfo } from '@/common/reporter'
import { errorCodeConstantMapping, ErrortType } from '@/common/reporter/errorMapping'
import { HOTEL_SEARCH_EVENT_ID } from '../constants/mtaParamEvents'
import { buildHotelCardEventData } from './assembleData'
import { queryModeType } from '../constants/travelType'
import { glabelFilterType, priceStarSubFilterType, locationSubFilterType } from '../constants/filterType'
import { hotelCardType } from '../constants/cardType'
import { showToast } from '@/BaseComponents/atoms/utils/toast'
import { MTAPlaceHolderString } from '../constants/config'
import { isWeb } from '@/common/common'
import { ensureHotelRequestParams } from '../utils/ensureHotelRequestParams';
import { getUuid } from '@/utils'
import { initMarkCounter, endMarkCounterAndReport } from '@ltfe/ltfe-core-lib/lib/utiles'
import { PERFORMANCE, PERFORMANCE_LABEL } from '@/common/performance'
import { ModuleName } from '@/common/mta'

const searchPath = '/search/hotelSearch?jdreactkey=JDReactLifeTravelSearch&jdreactapp=JDReactLifeTravelSearch&transparentenable=true&initPath=pages/hotelSearch/index&hotelSearchData='

export const useHotelSearch = () => {
  // API 请求工具
  const { apiFetch } = useFetch()

  const customPvIdRef = useRef('')

  // 跳转工具
  const jumpTo = useJumpTo()

  // Refs
  // 当前请求页码
  const currentPageRef = useRef(1)
  // 第一次初始化发起的请求
  const firstLoad = useRef(true)

  const timer = useRef(null as any)
  // 加载状态
  const [loading, setLoading] = useState(true)
  const [loadMoreStatus, setLoadMoreStatus] = useState<LoadMoreStatus>(LoadMoreStatus.IDLE)
  const [hasMore, setHasMore] = useState(true)

  // url获取得到的额外参数
  const extraParamsRef = useRef<any>({})
  // 排序状态
  // Default values
  const defaultSortValue = {
    sortType: 'default',
    orderType: 'desc'
  }
  const [sortValue, setSortValue] = useState<any>(defaultSortValue)
  const sortValueRef = useRef(sortValue);
  const [searchParams, setSearchParams] = useState<any>({})
  // 创建一个ref来跟踪最新的searchParams
  const searchParamsRef = useRef(searchParams);
  // 拆分filter所有筛选条件类型
  const [combiedFliter, setCombiedFliter] = useState({
    location_distance: <any>[],
    price_star: <any>[],
    hotel_filter: <any>[]
  })
  const combiedFliterRef = useRef(combiedFliter);
  // 给选择使用
  const [filterMap, setFilterMap] = useState(new Set<string>())
  // 差旅参数
  const [travelParmas, setTravelParams] = useState<any>({})
  const travelParmasRef = useRef(travelParmas)

  // 数据列表状态
  // 记录自然流量数据
  const [_, setRawList] = useState<any[]>([])
  // 整体列表数据
  const [list, setList] = useState<any[]>([])

  const [identityAuthLayerVO, setIdentityAuthLayerVO] = useState<any>({})

  // 筛选面板数据
  const [filterPanelVOList, setFilterPanelVOList] = useState([])
  // 快筛数据
  const [outsideFilterPanelVOList, setOutsideFilterPanelVOList] = useState([])
  // 身份筛选数据
  const [identitySectionData, setIdentitySectionData] = useState<any>({})
  const identityTabRef = useRef('')

  // 添加commonUserAction状态
  const [commonUserAction, setCommonUserAction] = useState<any>({})
  const commonUserActionRef = useRef(commonUserAction);

  // 筛选状态 - 拆分成三个state
  const [activeFilter, setActiveFilter] = useState<SortType | null>(null)
  // iFrame
  const [webViewUrl, setWebViewUrl] = useState('')
  const [webViewVisible, setWebViewVisible] = useState(false)
  // 差旅请求之后的入参
  const [shareHotelScene, setShareHotelScene] = useState('')
  // 差旅标准MarkDown
  const [official_standard_description, setOfficialStandardDescription] = useState('')
  // 弹窗状态管理
  const [popupInfo, setPopupInfo] = useState<any>({})
  // 纠正时间buble展示
  const [updatedCheckInDate, setUpdatedCheckInDate] = useState(false)

  const [userIdentityList, setUserIdentityList] = useState<Number[]>([])
  const userIdentityListRef = useRef(userIdentityList)

  const ignoreCompareKeywordActionRef = useRef(false)

  // 获取四级地址
  const getAddress = () => {
    const currentestSearchParams = searchParamsRef.current || {}
    // 拿到mddInfo
    const { mddInfo = {} } = currentestSearchParams || {}
    const { type = '' } = mddInfo || {}
    if (type === POSITION_TYPE.LOCATE) {
      // 实时定位
      return currentestSearchParams.posAreaId
    }
    // 从mddInfo中拿字段
    const { province = '', city = '', county = '', street = '' } = mddInfo || {}
    return `${province},${city},${county},${street}`
  }

  // 处理弹窗显示
  const showPopupInfo = useCallback((showPopup) => {
    setPopupInfo({
      showPopup: showPopup,
      description: official_standard_description
    })
  }, [official_standard_description])

  // 关闭弹窗
  const closePopup = useCallback(() => {
    setPopupInfo({
      showPopup: ''
    })
  }, [])

  // 获取差旅标识
  const isTravel = () => {
    const { travelInfo = {} } = travelParmasRef.current || {}
    const { processInstanceId = '', travelId = '' } = travelInfo || {}
    return !isEmpty(processInstanceId) && !isEmpty(travelId)
  }

  // 获取拼房
  const isShareRoom = () => {
    const { queryMode = '' } = travelParmasRef.current || {}
    return [queryModeType.SINGLE_SHARE, queryModeType.MULTI_SHARE].includes(queryMode)
  }

  // 上报PV埋点
  const reportPV = (pvId: string) => {
    const requestParams = searchParamsRef.current;
    // 获取埋点参数
    const pvParams = {
      pvid: pvId || MTAPlaceHolderString,
      logid: pvId || MTAPlaceHolderString,
      search_fouraddrid: requestParams?.posAreaId || MTAPlaceHolderString,
      keyword: requestParams?.keyword || MTAPlaceHolderString,
      displayName: requestParams?.keyword || MTAPlaceHolderString,
      checkOutDate: requestParams?.hotelBaseSearchParam?.checkOutDate || MTAPlaceHolderString,
      checkInDate: requestParams?.hotelBaseSearchParam?.checkInDate || MTAPlaceHolderString,
      fromSource: requestParams?.fromSource || MTAPlaceHolderString,
      search_o2o_coordinates: `${requestParams?.longitude || MTAPlaceHolderString},${requestParams?.latitude || MTAPlaceHolderString}`
    };
    // 发送PV埋点
    mtaPv(M_PAGEID.HotelSearch, M_PAGE.HotelSearch, pvParams);
  }

  // 删除不需要的参数
  const removeUnnecessaryParams = (params: any) => {
    if (!params) return params;

    const fieldsToRemove = [
      'lbs_city',
      'extMap',
      'filterType',
      'orderType',
      'filterName'
    ];


    // 删除其他不需要的字段
    fieldsToRemove.forEach(field => {
      if (field in params) {
        delete params[field];
      }
    });
    return params;
  };

  const refFetchNo = useRef(0)

  const debounceFetchList = (isLoadMore = false, initParams?: any) => {
    if (!isLoadMore) {
      setLoading(true);
    } else {
      setLoadMoreStatus(LoadMoreStatus.LOADING);
    }
    if (timer.current) {
      clearTimeout(timer.current) // 如果定时器已经存在，清除它
    }
    timer.current = setTimeout(() => { // 设置一个新的定时器
      fetchList(isLoadMore, initParams)
    }, 100)
  }

  // 发起网络请求
  const fetchList = async (isLoadMore = false, initParams?: any) => {
    const currentPage = isLoadMore ? currentPageRef.current : 1;
    // 防止短时间内多次调用
    try {
      // 构建请求参数
      let requestParams
      if (initParams) {
        requestParams = initParams
      } else {
        requestParams = {
          ...extraParamsRef.current,
          ...searchParamsRef.current,
          ...sortValueRef.current,
          ...travelParmasRef.current,
          identityTab: identityTabRef.current,
          filterList: getFiltersByType(glabelFilterType.all),
          page: currentPage,
          pageSize: PAGE_SIZE,
        };
      }

      if (!isLoadMore) {
        const uuid = getUuid()
        requestParams.customPvId = uuid
        customPvIdRef.current = uuid
      } else {
        requestParams.customPvId = customPvIdRef.current
      }

      // 将 extMap 中的 searchItemId 提升到一级参数
      if (!isEmpty(requestParams?.extMap?.searchItemId)) {
        requestParams.searchItemId = requestParams?.extMap?.searchItemId
      } else {
        // 非首次, 且requestParams?.extMap?.searchItemId 为空, 则删除searchItemId
        if (!initParams && 'searchItemId' in requestParams) {
          delete requestParams.searchItemId;
        }
      }

      // 差旅请求参数
      if (isShareRoom()) {
        requestParams.travelStandard = requestParams.shareTravelStandard
      }

      // 添加shareHotelScene
      if (!isEmpty(shareHotelScene)) {
        requestParams.shareHotelScene = shareHotelScene
      }

      // 身份list
      if (!isEmpty(userIdentityListRef.current)) {
        requestParams.userIdentityList = userIdentityListRef.current
      }

      const shareData = {
        keyword: requestParams.keyword || '',
        mddInfo: requestParams.mddInfo || {},
        hotelBaseSearchParam: requestParams.hotelBaseSearchParam || {},
        extMap: requestParams.extMap || {},
        latitude: requestParams.latitude || '',
        longitude: requestParams.longitude || '',
        posAreaId: requestParams?.posAreaId || '',
        lbs_city: requestParams.lbs_city || {},
        filterList: requestParams.filterList || []
      }
      // 存储shareData
      saveHotelSearchData(shareData)

      // 将酒店预订信息存在JDShare中
      const { checkInDate, checkOutDate } = requestParams?.hotelBaseSearchParam || {}
      const roomNum = {
        value: requestParams?.hotelBaseSearchParam?.roomNum || 1
      }
      const adultNum = {
        value: requestParams?.hotelBaseSearchParam?.grownNum || 1
      }
      const childNum = {
        value: requestParams?.hotelBaseSearchParam?.childrenNum || 0,
        age: requestParams?.hotelBaseSearchParam?.childrenAges || []
      }

      saveHotelBookingInfo({
        roomNum: roomNum,
        adultNum: adultNum,
        childNum: childNum,
        checkInDate,
        checkOutDate
      })

      let isTravel = false
      if (!isEmpty(requestParams?.travelInfo)) {
        const { travelInfo = {} } = requestParams || {}
        const { processInstanceId = '', travelId = '' } = travelInfo || {}
        isTravel = !isEmpty(processInstanceId) && !isEmpty(travelId)
      }

      // 删除不需要的参数
      removeUnnecessaryParams(requestParams);
      if (isWeb && currentPage === 1) {
        const replaceUrl = `${searchPath}${enCodeDebBase64ParseSafe(requestParams)}`;
        history.replaceState(null, '', replaceUrl);
      }
      const needDuccConfig = isTravel

      refFetchNo.current++;
      const fetchNo = refFetchNo.current
      
      initMarkCounter(ModuleName, PERFORMANCE_LABEL.FUNCTIONID_HOTEL_LIST_KEY)

      const response = await fetchHotelListWithDuccConfig(
        apiFetch,
        requestParams,
        needDuccConfig
      )

      endMarkCounterAndReport(ModuleName, PERFORMANCE_LABEL.FUNCTIONID_HOTEL_LIST_KEY, {
        performanceKey: PERFORMANCE.INTERFACE_TIME_KEY,
        code: errorCodeConstantMapping?.PERFORMANCE_REPORT_INTERFACE_REQUEST_TIME,
        pageName: M_PAGE.HotelSearch
    })

      if (fetchNo < refFetchNo.current) {
        return;
      }

      if (response?.code !== 0) {
        resportRequestError({
          errorDescription: '获取垂搜数据异常',
          errorInfo: response || {},
          requestParam: requestParams || {},
          initParams: initParams,
          needDuccConfig: needDuccConfig,
          currentPage: currentPage
        })
      }
      // 调用抽离出的方法处理响应数据
      processResponseData(
        response,
        currentPage,
        isLoadMore,
        shareData,
        requestParams
      );
    } catch (error) {
      processResponseData({}, currentPage, isLoadMore, {}, {});
      if (!isLoadMore) {
        setLoadMoreStatus(LoadMoreStatus.IDLE);
      } else {
        setLoadMoreStatus(LoadMoreStatus.ERROR);
      }
      resportRequestError({
        errorDescription: '获取垂搜数据异常 try/catch',
        errorInfo: error,
        initParams: initParams
      })
    } finally {
      setLoading(false);
      firstLoad.current = false;
    }
  }

  const changeHotelFilters = (hotelFliters) => {
    const currentCombiedFliter = combiedFliterRef.current
    const newCombiedFliter = {
      ...currentCombiedFliter,
      hotel_filter: hotelFliters || []
    }
    combiedFliterRef.current = newCombiedFliter

    const allFilters = [
      ...newCombiedFliter.location_distance,
      ...newCombiedFliter.price_star,
      ...newCombiedFliter.hotel_filter
    ]
    recordFilterMap(allFilters)
  }

  // 处理响应数据并更新状态
  const processResponseData = (
    response,
    currentPage,
    isLoadMore,
    shareData,
    requestParams
  ) => {
    initMarkCounter(ModuleName, PERFORMANCE_LABEL.FUNCTIONID_HOTEL_LIST_RESPONSE_KEY)
    const {
      naturalCardVOList = [], // 自然流量
      hasNextPage = false,
      filterPanelVOList = [],
      outsideFilterPanelVOList = [],
      recommendCardVOList = [], // 推荐流量
      shareHotelTip = {},
      shareHotelScene = '',
      commonUserAction = {}, // 从响应中获取commonUserAction
      userIdentityList = [], // 从响应中获取userIdentityList
      identitySelection = {},
      priceStyleVOMap = {}, // 价格样式表
      identityAuthLayerVO = {},
      algoFilterItemList = []
    } = response?.hotelList?.result || {};

    // 更新commonUserAction状态
    if (!isEmpty(commonUserAction)) {
      // 组装默认参数
      // commonUserAction.businessType = '1'
      commonUserAction.displayName = commonUserAction.keyword
      // fix: 召回有问题的场景
      commonUserActionRef.current = commonUserAction;
      setCommonUserAction(commonUserAction);
    }

    // 更新userIdentityList状态
    if (currentPage === 1) {
      userIdentityListRef.current = userIdentityList
    }

    // 处理每个酒店卡片的埋点数据
    if (!isEmpty(naturalCardVOList)) {
      naturalCardVOList.forEach((item, index) => {
        // 使用抽取的方法构建埋点数据
        const eventData = buildHotelCardEventData(item, index);
        if (eventData) {
          item.eventData = eventData;
          item.isNatural = true;
        }
        // 加个卡片样式塞到每个item中
        if (!isEmpty(priceStyleVOMap) && item.hotelCardVO?.priceStyleCode) {
          const code = item.hotelCardVO.priceStyleCode;
          if (!isEmpty(priceStyleVOMap[code])) {
            item.priceStyle = priceStyleVOMap[code];
          }
        }
      });
    }

    // 处理推荐流量的埋点数据
    if (!isEmpty(recommendCardVOList)) {
      recommendCardVOList.forEach((item, index) => {
        // 使用抽取的方法构建埋点数据
        const eventData = buildHotelCardEventData(item, index + naturalCardVOList?.length);
        if (eventData) {
          item.eventData = eventData;
          item.isNatural = false;
        }
        // 加个卡片样式塞到每个item中
        if (!isEmpty(priceStyleVOMap) && item.hotelCardVO?.priceStyleCode) {
          const code = item.hotelCardVO.priceStyleCode;
          if (!isEmpty(priceStyleVOMap[code])) {
            item.priceStyle = priceStyleVOMap[code];
          }
        }
      });
    }

    if (currentPage === 1 && isEmpty(naturalCardVOList)) {
      showToast({
        title: '没有找到匹配的结果，请修改筛选条件试试',
        icon: 'none',
        duration: 2000,
      });
    }

    if (currentPage === 1 && (!isEmpty(naturalCardVOList) || !isEmpty(recommendCardVOList))) {
      mtaTrack(true, HOTEL_SEARCH_EVENT_ID.HotelRNListResultExpoNew, {});
    }

    if (currentPage === 1) {
      // 抽离处理价格星级项目的函数
      const processPriceStarItem = (item) => {
        if (item.type === priceStarSubFilterType.hotel_price_lowest) {
          const [min, max] = (item?.itemId || "0,0").split(",").map((v) => (v === "" ? -1 : Number(v)));
          return { ...item, min, max };
        }
        return item;
      };

      // 抽离处理价格星级面板的函数
      const transformPriceStarPanel = (panel) => {
        if (panel?.filterPanelCode === glabelFilterType.price_star) {
          return {
            ...panel,
            filterList: panel?.filterList?.map(processPriceStarItem)
          };
        }
        return panel;
      };
      // 数据格式转化
      const newFilterPanelVOList = filterPanelVOList.map(transformPriceStarPanel);
      const newOutsideFilterPanelVOList = outsideFilterPanelVOList.map(transformPriceStarPanel);

      // 更新query词锚中筛选项
      // 这个调用时机依赖algoFilterItemList, shareData, requestParams, newFilterPanelVOList更新到最新
      if (currentPage === 1) {
        algoFilterHit(algoFilterItemList, shareData, requestParams, newFilterPanelVOList)
      }

      setFilterPanelVOList(newFilterPanelVOList);
      setOutsideFilterPanelVOList(newOutsideFilterPanelVOList);
      setIdentitySectionData(identitySelection);

      // 身份认证浮层
      setIdentityAuthLayerVO(identityAuthLayerVO);
      if (!isEmpty(identityAuthLayerVO)) {
        mtaTrack(true, HOTEL_SEARCH_EVENT_ID.HotelRNListIdentificationExpo, {
          userIdentity: userIdentityListRef.current,
          identityTab: identitySelection?.identityTab
        });
      }
    }

    if (currentPage === 1 && isEmpty(naturalCardVOList) && isEmpty(recommendCardVOList)) {
      setList([{ cardType: hotelCardType.Empty }]);
      setHasMore(false);
      setLoadMoreStatus(LoadMoreStatus.IDLE);
      return;
    }

    setRawList(prevRawList => {
      const updatedRawList = isLoadMore ? [...prevRawList, ...naturalCardVOList] : naturalCardVOList;
      // 使用更新后的列表构建显示列表
      const showList = <any>[];

      const currentCombiedFliter = combiedFliterRef.current;

      const fliterEmpty = isEmpty(currentCombiedFliter.location_distance) && isEmpty(currentCombiedFliter.hotel_filter) && isEmpty(currentCombiedFliter.price_star);
      const filteredLocation = currentCombiedFliter.location_distance.filter(item =>
        item.filterType === locationSubFilterType.gis_distance
      );

      // 差旅场景下处理拼房提示
      if (currentPage === 1) {
        // 保存shareHotelScene，用于后续请求
        if (!isEmpty(shareHotelScene)) {
          setShareHotelScene(shareHotelScene);
        }
        const official_standard_description = response?.duccConfig?.result?.configMap?.official_standard_description
        // 获取差旅标识
        if (isTravel() && !isEmpty(official_standard_description)) {
          setOfficialStandardDescription(official_standard_description)
          // 差旅[出差必看]提示
          showList.push({ cardType: hotelCardType.InfoTips })
          // 添加拼房提示卡片
          if (shareHotelTip && shareHotelTip?.show && [queryModeType.SINGLE_SHARE, queryModeType.MULTI_SHARE].includes(shareHotelScene)) {
            // 低价拼房提示 NoRoomShareLowPrice
            // 无拼房提示 NoRoomShare
            const cardType = shareHotelScene === '2' ? hotelCardType.NoRoomShareLowPrice : hotelCardType.NoRoomShare
            showList.push({ cardType: cardType, shareHotelTip })
          }
        }

        if (isEmpty(updatedRawList) && fliterEmpty) {
          showList.push({
            cardType: hotelCardType.NoData,
            show: true,
            title: '未找到符合条件的结果，请更改条件重新搜索'
          });
        }
      }

      if (!isEmpty(updatedRawList)) {
        // 是否第一项标识，第一项不展示分割线
        if (updatedRawList[0]) {
          updatedRawList[0].isFirst = true;
        }
        showList.push(...updatedRawList);
      }

      if (currentPage === 1 && updatedRawList?.length < 20 && hasNextPage === false) {
        if (!isEmpty(filteredLocation)) {
          // 扩大搜索
          showList.push({
            cardType: hotelCardType.AreaClear
          });
        }

        if (!fliterEmpty) {
          showList.push({
            cardType: hotelCardType.NoMatch
          });
        }
      }

      if (!isEmpty(recommendCardVOList)) {
        if (recommendCardVOList[0]) {
          recommendCardVOList[0].isFirst = true;
        }
        showList.push(
          { cardType: hotelCardType.ListTitle } as any,
          ...recommendCardVOList
        );
      }

      setList(showList);

      let hasMore = hasNextPage;
      if (!isEmpty(updatedRawList) || !isEmpty(recommendCardVOList)) {
        if (!isLoadMore) {
          if (hasMore) {
            setLoadMoreStatus(LoadMoreStatus.IDLE);
          } else {
            setLoadMoreStatus(LoadMoreStatus.NO_MORE);
          }
        } else {
          if (response?.code !== 0) {
            // 失败场景可以重试一次, 所以hasMore设置为true
            hasMore = true;
            setLoadMoreStatus(LoadMoreStatus.ERROR);
          } else {
            if (hasMore) {
              setLoadMoreStatus(LoadMoreStatus.IDLE);
            } else {
              setLoadMoreStatus(LoadMoreStatus.NO_MORE);
            }
          }
        }
      }

      setHasMore(hasMore);
      // 失败场景下hasMore设置为true, 但是currentPageRef不能累加
      if (hasNextPage) {
        currentPageRef.current = currentPage + 1;
      }
      return updatedRawList;
    });

    endMarkCounterAndReport(ModuleName, PERFORMANCE_LABEL.FUNCTIONID_HOTEL_LIST_RESPONSE_KEY, {
      performanceKey: PERFORMANCE.INTERFACE_TIME_KEY,
      code: errorCodeConstantMapping?.PERFORMANCE_REPORT_INTERFACE_RESPONSE_TIME,
      pageName:  M_PAGE.HotelSearch
    })
  }

  // 搜索词命中筛选项之后
  const algoFilterHit = (
    algoFilterItemList,
    shareData,
    requestParams,
    newFilterPanelVOList
  ) => {

    if (isEmpty(algoFilterItemList)) {
      return
    }

    // 清空搜索词相关数据
    const clearKeywordData = () => {
      searchParamsRef.current.keyword = ''
      shareData.keyword = ''
      saveHotelSearchData(shareData)
    }

    // 生成匹配的筛选项列表（去重）
    const generateMatchedFilterList = () => {
      const tempHotelFilterList = newFilterPanelVOList
        .flatMap(panel => panel?.filterPanelCode === glabelFilterType.hotel_filter ? panel.filterList : [])

      const seenKeys = new Set<string>()

      return tempHotelFilterList
        .flatMap(item => item?.itemList || [])
        .filter(listItem => {
          // 检查是否与算法筛选项匹配
          const isMatched = algoFilterItemList?.some(algoItem =>
            algoItem?.filterType === listItem?.filterType &&
            algoItem?.itemId === listItem?.itemId
          )

          if (!isMatched) return false

          // 去重：按 filterType_itemId 组合去重
          const uniqueKey = `${listItem?.filterType || ''}_${listItem?.itemId || ''}`
          if (seenKeys.has(uniqueKey)) return false

          seenKeys.add(uniqueKey)
          return true
        })
    }

    // 更新 H5 页面 URL
    const updateH5Url = (filterList) => {
      if (isWeb) {
        requestParams.keyword = ''
        requestParams.filterList = filterList
        const replaceUrl = `${searchPath}${enCodeDebBase64ParseSafe(requestParams)}`
        history.replaceState(null, '', replaceUrl)
      }
    }

    // 执行主要逻辑
    clearKeywordData()
    const matchedFilterList = generateMatchedFilterList()
    updateH5Url(matchedFilterList)
    changeHotelFilters(matchedFilterList)
  }

  const resportRequestError = (customMsg) => {
    reportInfo({
      code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_FETCH_LIST_ERROR,
      errorDetail: {
        errorType: ErrortType.Error,
        functionId: 'fetchHotelListWithDuccConfig',
        customMsg: customMsg,
      }
    })
  }

  // const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));


  // 初始化加载数据
  const initializeData = async () => {
    // await delay(2000);
    try {
      const initialParams = await getInitialRequestParams();
      initializeParams(initialParams);
      // 直接使用初始参数调用 fetchList
      fetchList(false, initialParams);
    } catch (error) {
      fetchList(false, defaultRequestParams);
    }
  }

  // 获取初始请求参数
  const getInitialRequestParams = async () => {
    const staticParams = globalInfoModel.staticParams ?? {}
    const hotelSearchData = staticParams?.hotelSearchData || '';
    let requestParams
    if (isEmpty(hotelSearchData)) {
      reportInfo({
        code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_PARAM_EMPTY_ERROR,
        errorDetail: {
          errorType: ErrortType.Error,
          functionId: 'getInitialRequestParams',
          customMsg: {
            errorDescription: '页面参数解析为空，使用默认参数'
          },
        }
      })
      // 使用默认参数
      requestParams = defaultRequestParams
    } else {
      // 优先从url上获取
      requestParams = deCodeDebBase64ParseSafe(hotelSearchData)
      await ensureHotelRequestParams(requestParams, apiFetch)
    }
    return requestParams
  }

  // 初始化参数处理核心参数
  const initializeParams = useCallback((searchParams?: any) => {
    if (searchParams) {
      // 提取核心参数
      const {
        keyword: kw,
        // 城市中心经纬度
        // mddinfo里是否有四级地址
        mddInfo: md,
        hotelBaseSearchParam: hbsp,
        filterList: fl,
        extMap: em,
        // 用户经纬度需要用不用
        latitude: lt,
        longitude: lg,
        posAreaId: pa,
        lbs_city: lbs_city,
        ...otherParams
      } = searchParams;

      // 纠偏
      const updatedHbsp = checkAndFixDate(hbsp)
      extraParamsRef.current = otherParams;

      const initSearchParams = {
        keyword: kw || '',
        mddInfo: md || {},
        hotelBaseSearchParam: updatedHbsp || {},
        extMap: em || {},
        latitude: lt || '',
        longitude: lg || '',
        posAreaId: pa || '',
        lbs_city: lbs_city || {}
      }

      // 设置核心参数到状态
      searchParamsRef.current = initSearchParams;
      setSearchParams(initSearchParams);

      // 获取差旅参数
      const {
        queryMode = '',
        travelStandard,
        shareTravelStandard,
        travelInfo
      } = otherParams || {}

      const travelParmas = {
        travelInfo,
        queryMode,
        travelStandard,
        shareTravelStandard
      }
      travelParmasRef.current = travelParmas
      setTravelParams(travelParmas)

      // 初始化时, 设置排序参数
      const { sortType, orderType } = otherParams || {}
      const initialSortValue = {
        sortType: sortType || defaultSortValue.sortType,
        orderType: orderType || defaultSortValue.orderType
      }
      setSortValue(initialSortValue)
      sortValueRef.current = initialSortValue
      // 初始化时, 设置fliterList
      updateInitFliter(fl || [])
    }
  }, []);

  const reloadTravelSearch = useCallback((sugParms?: any) => {
    const { travelSearchType } = sugParms || {}
    const currentSearchParams = searchParamsRef.current;
    if (travelSearchType === 'filter') {
      if (isShareRoom()) {
        // 如果是拼房, 则将其当做关键词搜索
        const { title = '', latitude, longitude } = sugParms || {}
        if (isEmpty(title)) return
        // 设置需要上报PV
        setSearchParams(prev => {
          const updatedParams = {
            ...prev,
            keyword: title,
            mddInfo: {
              ...prev.mddInfo,
              type: POSITION_TYPE.POI,
              latitude,
              longitude
            }
          };
          return updatedParams;
        });
      } else {
        const currentCombiedFliter = combiedFliterRef.current;
        // 将这个筛选项作为fliter合并
        const location_distance = currentCombiedFliter.location_distance;
        // 将sugParms 作为一个位置距离的筛选项目和combiedFliter.location_distance 合并
        const sugFilter = [sugParms]
        // 过滤掉非gis_distance的筛选项
        const filteredLocation = location_distance?.filter(item =>
          item.filterType === locationSubFilterType.gis_distance
        ) ?? [];
        // 合并新的筛选项
        const new_location_distance = [...filteredLocation, ...sugFilter]
        updateFilterByType(glabelFilterType.location_distance, new_location_distance)
      }
    } else if (travelSearchType === 'keyword') {
      if (currentSearchParams?.keyword === sugParms?.keyword) return
      const { keyword = '' } = sugParms || {}
      setSearchParams(prev => {
        const updatedParams = { ...prev, keyword: keyword };
        if (isEmpty(keyword)) {
          if (updatedParams.mddInfo?.type === POSITION_TYPE.POI) {
            updatedParams.mddInfo = {
              ...updatedParams.mddInfo,
              type: POSITION_TYPE.CITY
            };
          }
        }
        return updatedParams;
      });
    } else if (travelSearchType === 'sugList') {
      // 从 sugList 跳转过来的，需要更新 keyword, mddInfo, extMap
      const keyword = sugParms?.mainStr || currentSearchParams.keyword
      // 得把这个梳理一下  ...sugParms?.sugMddInfo, sugMddInfo有type, 就覆盖
      let mddInfo = currentSearchParams.mddInfo;
      if (!isEmpty(sugParms?.sugMddInfo?.type)) {
        mddInfo = {
          ...currentSearchParams.mddInfo,
          ...sugParms?.sugMddInfo
        }
      }
      const extMap = sugParms?.extMap || currentSearchParams.extMap
      // 设置需要上报PV
      setSearchParams(prev => {
        const updatedParams = { ...prev, keyword, mddInfo, extMap };
        return updatedParams;
      });
    }
  }, [])

  // 重新加载数据
  const reloadDataIfNeed = useCallback(async () => {
    // 获取身份字段
    const currentUserIdentityList = userIdentityListRef.current
    // 获取身份接口
    const params = {
      bizType: 1,
      type: -1,
      extraParam: { resource: 1 }
    }
    const response = await fetchUserIdentityList(apiFetch, params)
    if (response?.code === '0') {
      const userIdentityList = response?.result || []
      if (!isEmpty(userIdentityList)) {
        // 对身份列表进行排序后比较，确保顺序不影响比较结果
        const sortedUserIdentityList = (Array.isArray(userIdentityList) ? userIdentityList : []).sort((a, b) => a - b)
        const sortedCurrentUserIdentityList = (currentUserIdentityList as number[]).sort((a, b) => a - b)
        // 如果排序后的身份列表不相等，说明用户身份发生了变化，需要清空当前身份列表
        if (!isObjectEqual(sortedUserIdentityList, sortedCurrentUserIdentityList)) {
          setUserIdentityList([])
          return
        }
      }
    } else {
      reportInfo({
        code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_FETCH_USER_IDENTITY_ERROR,
        errorDetail: {
          errorType: ErrortType.Error,
          functionId: 'fetchUserIdentityList',
          customMsg: {
            errorDescription: '获取用户身份接口异常',
            response: response
          },
        }
      })
    }

    // 优先获取bookingInfo
    getHotelBookingInfo().then(bookingInfo => {
      const currentSearchParams = searchParamsRef.current;
      if (isEmpty(bookingInfo)) {
        reloadDataIfHotelShareChange()
        return;
      }

      const { checkInDate, checkOutDate, adultNum, childNum, roomNum } = bookingInfo || {}
      const { hotelBaseSearchParam } = currentSearchParams || {}

      let needUpdate = false;
      let tempCheckInDate = hotelBaseSearchParam?.checkInDate;
      if (!isEmpty(checkInDate) && checkInDate !== tempCheckInDate) {
        tempCheckInDate = checkInDate;
        needUpdate = true;
      }

      let tempCheckOutDate = hotelBaseSearchParam?.checkOutDate;
      if (!isEmpty(checkOutDate) && checkOutDate !== tempCheckOutDate) {
        tempCheckOutDate = checkOutDate;
        needUpdate = true;
      }

      let tempAdultNum = hotelBaseSearchParam?.grownNum ?? 0;
      if ((adultNum?.value ?? 0) !== tempAdultNum) {
        tempAdultNum = adultNum?.value ?? 1;
        needUpdate = true;
      }

      let tempChildNum = hotelBaseSearchParam?.childrenNum ?? 0;
      if ((childNum?.value ?? 0) !== tempChildNum) {
        tempChildNum = childNum?.value ?? 0;
        needUpdate = true;
      }

      let tempRoomNum = hotelBaseSearchParam?.roomNum ?? 0;
      if ((roomNum?.value ?? 0) !== tempRoomNum) {
        tempRoomNum = roomNum?.value ?? 1;
        needUpdate = true;
      }

      let tempAge = hotelBaseSearchParam?.childrenAges || [];
      if (!isObjectEqual((childNum?.age ?? []), tempAge)) {
        tempAge = childNum?.age ?? []
        needUpdate = true;
      }

      if (needUpdate) {
        const tempHotelBaseSearchParam = {
          checkInDate: tempCheckInDate,
          checkOutDate: tempCheckOutDate,
          grownNum: tempAdultNum,
          childrenNum: tempChildNum,
          roomNum: tempRoomNum,
          childrenAges: tempAge
        }
        const updatedHotelBaseSearchParam = checkAndFixDate(tempHotelBaseSearchParam)
        setSearchParams(prev => {
          const updatedParams = {
            ...prev,
            hotelBaseSearchParam: updatedHotelBaseSearchParam
          };
          return updatedParams;
        });
      } else {
        reloadDataIfHotelShareChange()
      }
    }).catch(error => {
      console.log('getHotelBookingInfo error:', error);
      reloadDataIfHotelShareChange()
    })
  }, []);

  const reloadDataIfHotelShareChange = useCallback(() => {
    getHotelSearchData().then(shareData => {
      if (isEmpty(shareData)) {
        reloadDataIfCheckInDateHasChange()
        return
      }
      const currentSearchParams = searchParamsRef.current;
      // 仅比较关键字段
      // 如果点击了去选择城市, 则忽略比较
      const ignoreCompareKeyword = ignoreCompareKeywordActionRef.current
      if (!ignoreCompareKeyword && !isObjectEqual(currentSearchParams?.keyword, shareData?.keyword) ||
        !isObjectEqual(currentSearchParams?.mddInfo, shareData?.mddInfo) ||
        !isObjectEqual(currentSearchParams?.hotelBaseSearchParam, shareData?.hotelBaseSearchParam) ||
        !isObjectEqual(currentSearchParams?.extMap, shareData?.extMap) ||
        !isObjectEqual(currentSearchParams?.latitude, shareData?.latitude) ||
        !isObjectEqual(currentSearchParams?.longitude, shareData?.longitude) ||
        !isObjectEqual(currentSearchParams?.posAreaId, shareData?.posAreaId) ||
        !isObjectEqual(currentSearchParams?.lbs_city, shareData?.lbs_city)
      ) {
        const updatedHotelBaseSearchParam = checkAndFixDate(shareData?.hotelBaseSearchParam)
        const updatedShareData = {
          ...shareData,
          hotelBaseSearchParam: updatedHotelBaseSearchParam
        };

        if (!isObjectEqual(currentSearchParams?.keyword, shareData?.keyword)) {
          clearAllFiltersWithRecord()
        }

        // 直接使用shareData 发起请求
        setSearchParams(updatedShareData);
      } else {
        reloadDataIfCheckInDateHasChange()
      }
    }).catch(error => {
      reloadDataIfCheckInDateHasChange()
      reportInfo({
        code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_GET_JDSHARE_DATA_ERROR,
        errorDetail: {
          errorType: ErrortType.Error,
          functionId: 'getHotelSearchData',
          customMsg: {
            errorDescription: '获取酒店JDShare数据异常',
            errorInfo: error?.message,
            errorStack: error?.stack
          },
        }
      })
    });
  }, []);

  const reloadDataIfCheckInDateHasChange = () => {
    const currentSearchParams = searchParamsRef.current;
    if (checkInDateHasChange(currentSearchParams?.hotelBaseSearchParam?.checkInDate)) {
      const updatedHotelBaseSearchParam = checkAndFixDate(currentSearchParams?.hotelBaseSearchParam)
      const updateSearchParams = {
        ...currentSearchParams,
        hotelBaseSearchParam: updatedHotelBaseSearchParam
      };
      setSearchParams(updateSearchParams);
    }
  }

  const checkAndFixDate = (hotelBaseSearchParam: any) => {
    const { checkInDate } = hotelBaseSearchParam || {}

    if (checkInDateHasChange(checkInDate)) {
      hotelBaseSearchParam.checkInDate = getDefaultDate().checkInDate
      hotelBaseSearchParam.checkOutDate = getDefaultDate().checkOutDate
      setUpdatedCheckInDate(true)
      setTimeout(() => {
        setUpdatedCheckInDate(false)
      }, 3000)
    }
    return hotelBaseSearchParam
  }


  const checkInDateHasChange = (checkInDate: string) => {
    if (!checkInDate) {
      return false
    }
    if (!checkValidDate(checkInDate)) {
      return true
    }
    return false
  }

  // 处理加载更多
  const handleLoadMore = useCallback(() => {
    if (!loading && hasMore) {
      debounceFetchList(true);
    }
  }, [loading, hasMore, debounceFetchList]);

  // 在组件卸载时清除弹窗状态
  useEffect(() => {
    return () => {
      setPopupInfo({ showPopup: '' });
    };
  }, []);

  // 监听参数变化，触发数据刷新
  useEffect(() => {
    if (firstLoad.current) {
      return
    }
    const currentSearchParams = searchParamsRef.current;
    // mddInfo 发生了变化
    if (!isObjectEqual(currentSearchParams.mddInfo, searchParams.mddInfo)) {
      searchParamsRef.current = searchParams;
      clearAllFilters()
      return
    }
    searchParamsRef.current = searchParams;
    debounceFetchList(false);
  }, [searchParams])

  useEffect(() => {
    if (firstLoad.current) {
      return
    }
    sortValueRef.current = sortValue;
    debounceFetchList(false);
  }, [sortValue])

  useEffect(() => {
    if (firstLoad.current) {
      return
    }
    travelParmasRef.current = travelParmas;
    debounceFetchList(false);
  }, [travelParmas])

  // 监听筛选条件变化，更新筛选列表
  useEffect(() => {
    if (firstLoad.current) {
      return
    }
    currentPageRef.current = 1
    combiedFliterRef.current = combiedFliter;
    saveHotelFilterHistory(combiedFliter.hotel_filter)
    // 从合并的状态中获取所有筛选条件
    const { location_distance, price_star, hotel_filter } = combiedFliter;

    // 合并所有筛选条件到一个数组
    const allFilters = [
      ...location_distance,
      ...price_star,
      ...hotel_filter
    ];
    recordFilterMap(allFilters);
    debounceFetchList(false);
  }, [combiedFliter]);

  useEffect(() => {
    commonUserActionRef.current = commonUserAction;
  }, [commonUserAction]);

  useEffect(() => {
    if (firstLoad.current) {
      return
    }
    userIdentityListRef.current = userIdentityList;
    // 重置身份tab选中
    identityTabRef.current = '';
    debounceFetchList(false);
  }, [userIdentityList]);

  // 处理排序变化
  const handleSort = useCallback((option) => {
    currentPageRef.current = 1
    setSortValue(option)
  }, [])

  // ==========================筛选相关==========================

  // 辅助函数：更新特定类型的筛选值
  const updateFilterByType = useCallback((type: string, newValue: any[]) => {
    const currentCombiedFliter = combiedFliterRef.current;
    const newCombiedFliter = {
      ...currentCombiedFliter,
      [type]: newValue
    }
    setCombiedFliter(newCombiedFliter)
  }, []);

  const updateInitFliter = (filterList) => {
    const locationFilters = <any>[]
    const priceStarFilters = <any>[]
    const hotelFilters = <any>[]

    filterList.forEach(item => {
      if (item.type === locationSubFilterType.linearDistance ||
        item.type === locationSubFilterType.featureSpot ||
        item.filterType === locationSubFilterType.gis_distance ||
        item.filterType === locationSubFilterType.gis_location
      ) {
        locationFilters.push(item)
      } else if (item.filterType === priceStarSubFilterType.hotel_price_lowest ||
        item.filterType === priceStarSubFilterType.hotel_grade) {
        // 统一赋值min, max
        if (item.filterType === priceStarSubFilterType.hotel_price_lowest) {
          const [min, max] = (item?.itemId || "0,0").split(",").map((v) => (v === "" ? -1 : Number(v)));
          item.min = min
          item.max = max
        }
        priceStarFilters.push(item)
      } else {
        hotelFilters.push(item)
      }
    })
    const newCombiedFliter = {
      location_distance: locationFilters,
      price_star: priceStarFilters,
      hotel_filter: hotelFilters
    }
    combiedFliterRef.current = newCombiedFliter

    setCombiedFliter(newCombiedFliter)
    // 第一次需要单独记录
    recordFilterMap(filterList)
  }

  const handleLocationFliter = useCallback((newFilters: any[]) => {
    updateFilterByType(glabelFilterType.location_distance, newFilters);
  }, []);

  const handlePriceStarFliter = useCallback((newFilters: any[]) => {
    updateFilterByType(glabelFilterType.price_star, newFilters);
  }, []);

  const handleFilterValue = useCallback((newFilters: any) => {
    updateFilterByType(glabelFilterType.hotel_filter, newFilters);
  }, []);

  const handleQucikMatchValue = useCallback((newFilters: any) => {
    updateInitFliter(newFilters)
  }, [])

  const handleIdentitySectionSelect = useCallback((identityTab: any, index: number) => {
    if (identityTab === identityTabRef.current) {
      return
    }
    // 刷新state, 触发UI刷新
    setIdentitySectionData(prev => {
      const updatedParams = {
        ...prev,
        identityTab: identityTab
      };
      return updatedParams;
    });

    mtaTrack(false, HOTEL_SEARCH_EVENT_ID.HotelRNListTab, {
      userIdentity: userIdentityListRef.current,
      index: index,
      identityTab: identityTab
    });

    // 更新参数类型
    identityTabRef.current = identityTab;
    // 改变type, 触发网络请求
    debounceFetchList(false);
  }, [])

  // 添加一个清除所有筛选条件的方法
  const clearAllFilters = useCallback(() => {
    setCombiedFliter({
      location_distance: [],
      price_star: [],
      hotel_filter: []
    });
  }, []);

  // 清空单个筛选
  const clearFilter = useCallback((item) => {
    const key = item.metaData.filterType + '-' + item.metaData.itemId;

    const { location_distance, price_star, hotel_filter } = combiedFliter;
    const newLocation = location_distance.filter(
      filterItem => `${filterItem.type || ''}-${filterItem.value || ''}` !== key
    );
    const newPriceStar = price_star.filter(
      filterItem => `${filterItem.filterType || ''}-${filterItem.itemId || ''}` !== key
    );
    const newFilter = hotel_filter.filter(
      filterItem => `${filterItem.filterType || ''}-${filterItem.itemId || filterItem.value || ''}` !== key
    );

    const newCombiedFliter = {
      location_distance: newLocation,
      price_star: newPriceStar,
      hotel_filter: newFilter
    }
    setCombiedFliter(newCombiedFliter);
  }, [combiedFliter]);

  const clearAllFiltersWithRecord = () => {
    combiedFliterRef.current = {
      location_distance: [],
      price_star: [],
      hotel_filter: []
    }
    recordFilterMap([])
  }

  const getFiltersByType = (type: string) => {
    const currentCombiedFliter = combiedFliterRef.current;
    if (isEmpty(currentCombiedFliter)) {
      return [];
    }
    const modifiedPrice_star = currentCombiedFliter.price_star.map(item => ({
      ...item,
      filterKey: 'price_star'
    }));

    if (type === glabelFilterType.all) {
      return [
        ...currentCombiedFliter.location_distance,
        ...modifiedPrice_star,
        ...currentCombiedFliter.hotel_filter
      ];
    }
    return currentCombiedFliter[type] || [];
  }

  const recordFilterMap = useCallback((allFilters: any[]) => {
    // 更新 filterMap
    const newFilterMap = new Set<string>();
    allFilters.forEach(item => {
      // 使用 filterType 和 itemId 作为唯一标识
      const key = `${item.filterType || ''}-${item.itemId || ''}`;
      newFilterMap.add(key);
    });
    setFilterMap(newFilterMap);
  }, [combiedFliter]);

  // 关闭筛选面板
  const handleFilterClose = useCallback((trackConfirm?: boolean, extraData?: any) => {
    if (trackConfirm) {
      const { locationItems, priceStarItems, hotelFilterItems } = convertFilterToMetaData()
      mtaTrack(false, HOTEL_SEARCH_EVENT_ID.HotelRNListMainFilterConfirmNew, {
        ...extraData,
        itemList: [...locationItems, ...priceStarItems, ...hotelFilterItems]
      })
    }
    setActiveFilter(null);
  }, []);

  // 处理 SortBar 项点击
  const handleFilterClick = useCallback((filterType: SortType) => {
    setActiveFilter(activeFilter === filterType ? null : filterType);
  }, [activeFilter]);

  // 处理排序选项点击
  const clearKeyword = useCallback(() => {
    clearAllFiltersWithRecord()
    setSearchParams(prev => {
      const updatedParams = { ...prev, keyword: '' };
      // 清空关键词时，需要删除 extMap, 因为extMap包含searchItemId
      if ('extMap' in updatedParams) {
        delete updatedParams.extMap;
      }
      // 清空关键词时，需要删除 searchItemId
      if ('searchItemId' in updatedParams) {
        delete updatedParams.searchItemId;
      }
      if (updatedParams.mddInfo?.type === POSITION_TYPE.POI) {
        updatedParams.mddInfo = {
          ...updatedParams.mddInfo,
          type: POSITION_TYPE.CITY
        };
      }
      return updatedParams;
    });
  }, []);

  const clearDistanceFilter = useCallback(() => {
    const { location_distance } = combiedFliter;
    // 过滤掉 type 为 linearDistance 的元素
    const filteredLocation = location_distance.filter(item =>
      item.filterType !== locationSubFilterType.gis_distance
    );
    // 更新 location 类型的筛选值
    updateFilterByType(glabelFilterType.location_distance, filteredLocation);
  }, [combiedFliter, updateFilterByType]);

  const convertFilterToMetaData = () => {
    const { location_distance, price_star, hotel_filter } = combiedFliterRef.current;
    const locationItems = location_distance.map(item => ({
      itemName: item.title,
      filterType: item.type,
      itemId: item.value,
      groupCode: item.groupCode || MTAPlaceHolderString
    }))
    const priceStarItems = price_star.map(item => ({
      itemName: item.label || item.itemName,
      filterType: item.filterType,
      itemId: item.itemId,
      groupCode: item.groupCode || MTAPlaceHolderString
    }))
    const hotelFilterItems = hotel_filter.map(item => ({
      itemName: item.label || item.itemName,
      filterType: item.filterType,
      itemId: item.itemId,
      groupCode: item.groupCode || MTAPlaceHolderString
    }))

    return {
      locationItems,
      priceStarItems,
      hotelFilterItems
    }
  }

  // 点击搜索框 位置 | 住离时间 | 几间几人-> 弹起弹层 -> 点击完成之后
  const updateMdd = useCallback((value) => {
    const {
      checkInDate,
      checkOutDate,
      adultNum,
      childNum,
      roomNum,
      hotelCity,
      hotelKeyword
    } = value || {};

    // 组装hotelBaseSearchParam
    const hp = {
      checkInDate,
      checkOutDate,
      roomNum: roomNum?.value || 1,
      grownNum: adultNum?.value || 1,
      childrenNum: childNum?.value || 0,
      childrenAges: childNum?.age || []
    }
    const {
      provinceId,
      geoId,
      cityId,
      districtId,
      townId,
      level,
      is_lbs,
      geoName,
      lat,
      lon,
      destType
    } = hotelCity || {}

    // 组装mddInfo
    let mddInfo = {
      showName: geoName,
      latitude: hotelKeyword?.lat ?? lat ?? '',
      longitude: hotelKeyword?.lon ?? lon ?? '',
      province: isEmpty(provinceId) ? 0 : Number(provinceId),
      city: isEmpty(cityId) ? 0 : Number(cityId),
      county: isEmpty(districtId) ? 0 : Number(districtId),
      street: isEmpty(townId) ? 0 : Number(townId),
      level: isEmpty(level) ? 2 : Number(level),
      type: is_lbs ? POSITION_TYPE.LOCATE : destType
    }

    const currentSearchParams = searchParamsRef.current;

    let posAreaId = currentSearchParams?.posAreaId
    let latitude = currentSearchParams?.latitude
    let longitude = currentSearchParams?.longitude
    let lbs_city = currentSearchParams?.lbs_city

    if (is_lbs) {
      latitude = lat ?? ''
      longitude = lon ?? ''
      lbs_city = hotelCity || {}
      posAreaId = `${isEmpty(provinceId) ? '' : provinceId},${isEmpty(cityId) ? '' : geoId || cityId},${isEmpty(districtId) ? '' : districtId},${isEmpty(townId) ? '' : townId}`
    }

    // 组装新的搜索参数
    const newSearchParams = {
      keyword: hotelKeyword?.name ?? '',
      mddInfo: mddInfo,
      hotelBaseSearchParam: hp,
      extMap: hotelKeyword?.extMap ?? {},
      latitude: latitude,
      longitude: longitude,
      posAreaId: posAreaId,
      lbs_city: lbs_city
    }
    // 如果搜索参数没有变化，则不发起请求
    if (isObjectEqual(newSearchParams, currentSearchParams)) {
      return
    }
    // 更新搜索参数
    setSearchParams(newSearchParams)
  }, [searchParams]);

  const updateTravelDate = useCallback((value) => {
    const { checkInDate, checkOutDate } = value
    setSearchParams(prev => {
      const updatedParams = {
        ...prev,
        hotelBaseSearchParam: {
          ...prev.hotelBaseSearchParam,
          checkInDate,
          checkOutDate
        }
      };
      return updatedParams;
    });
  }, [])

  const cardOnClick = useCallback((info, other) => {
    // 处理优惠点击上报优惠标签埋点（即优惠券入口点击）HotelRN_List_Discount
    const beltCodeStr = info?.beltInfoVO?.beltCode || MTAPlaceHolderString;

    if (other?.trigger === 'discount') {
      const tagInfo = other?.tagInfo;
      // 特殊逻辑: 当价格和discount都没有的时候，直接跳转到详情页
      const promotionList = info?.promotionLayerVO?.promotionDetailList || [];
      if (isEmpty(promotionList)) {
        jumpTo({ to: 'web', params: { url: decodeURIComponent(info?.jumpUrl) } })
        return
      }
      mtaTrack(false, HOTEL_SEARCH_EVENT_ID.HotelRNListDiscountNew, {
        ...info?.eventData,
        promotionName: tagInfo?.name || MTAPlaceHolderString,
      });
      setPopupInfo({
        showPopup: 'discount',
        cardInfo: {
          cardProps: info
        },
        toPage: (cardInfo) => {
          mtaTrack(false, HOTEL_SEARCH_EVENT_ID.HotelRNListDiscountLayerOrderNew, {
            ...info?.eventData,
            promotionName: tagInfo?.name || MTAPlaceHolderString,
          })
          // 先关闭弹窗，再执行跳转
          setPopupInfo({ showPopup: '' });
          jumpTo({ to: 'web', params: { url: decodeURIComponent(cardInfo?.jumpUrl) } })
        }
      })
      return
    } else if (other?.trigger === 'img') {
      // 添加卡片图片点击埋点, HotelRN_List_HotelPic
      mtaTrack(false, HOTEL_SEARCH_EVENT_ID.HotelRNListHotelPicNew, { ...info?.eventData, 'beltCode': beltCodeStr });
    } else if (other?.trigger === 'room') {
      other?.jumpUrl && jumpTo({ to: 'web', params: { url: decodeURIComponent(other?.jumpUrl) } })
      return
    } else {
      // 添加酒店卡片点击埋点, HotelRN_List_Hotel
      mtaTrack(false, info?.isNatural ? HOTEL_SEARCH_EVENT_ID.HotelRNListHotelNew : HOTEL_SEARCH_EVENT_ID.HotelRNListRecHotelNew, { ...info?.eventData, 'beltCode': beltCodeStr });
    }
    jumpTo({ to: 'web', params: { url: decodeURIComponent(info?.jumpUrl) } })
  }, []);

  const openWebView = useCallback((url) => {
    setWebViewUrl(url)
    setWebViewVisible(true)
  }, []);

  const iFrameCallBack = useCallback((searchInfo) => {
    setWebViewUrl('')
    setWebViewVisible(false)
    if (!isEmpty(searchInfo)) {
      const currentSearchParams = searchParamsRef.current;
      if (!isObjectEqual(currentSearchParams?.keyword, searchInfo?.keyword)) {
        clearAllFiltersWithRecord()
      }
      setSearchParams(searchInfo)
    } else {
      reportInfo({
        code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_IFRAME_CALLBACK_ERROR,
        errorDetail: {
          errorType: ErrortType.Error,
          functionId: 'iFrameCallBack',
          customMsg: {
            errorDescription: 'iFrameCallBack seachInfo为空',
          },
        }
      })
    }
  }, []);

  const searchWithOutshareRoom = () => {
    const newTravelParams = {
      ...travelParmas,
      queryMode: queryModeType.SHARE_TO_BUSINESS
    }
    setTravelParams(newTravelParams)
  }

  const showTogglePanel = () => {
    setActiveFilter(null)
  }

  const ignoreCompareKeywordAction = (ignore) => {
    ignoreCompareKeywordActionRef.current = ignore
  }
  const handleiIdentityAuth = (url) => {
    if (url) {
      try {
        jumpTo({ to: 'web', params: { url: decodeURIComponent(url) } })
      } catch (error) {
        console.error('Failed to jump to web:', error);
      }
    } else {
      console.log('跳转失败', url);
    }
    mtaTrack(false, HOTEL_SEARCH_EVENT_ID.HotelRNListIdentification, {
      userIdentity: userIdentityListRef.current,
      identityTab: identitySectionData?.identityTab
    });
  }

  const mtaTrack = (isExposure, eventId, eventData) => {
    try {
      sendMtaEvent(
        eventId,
        commonUserActionRef.current || {},
        eventData,
        isExposure
      )
    } catch (error) {
      console.log('mtaTrack error', error)
    }
  }

  return {
    loading,
    loadMoreStatus,
    list,
    hasMore,
    searchParamsRef,
    activeFilter,
    sortValue,
    combiedFliterRef,
    filterPanelVOList,
    outsideFilterPanelVOList,
    identitySectionData,
    filterMap,
    mtaTrack,
    initializeData,
    reloadDataIfNeed,
    reloadTravelSearch,
    updateMdd,
    updateTravelDate,
    handleLoadMore,
    handleSort,
    handleFilterClose,
    handleFilterClick,
    handleLocationFliter,
    handlePriceStarFliter,
    handleFilterValue,
    handleQucikMatchValue,
    clearDistanceFilter,
    clearAllFilters,
    clearFilter,
    clearKeyword,
    handleIdentitySectionSelect,
    firstLoad,
    extraParamsRef,
    travelParmasRef,
    cardOnClick,
    openWebView,
    webViewUrl,
    webViewVisible,
    iFrameCallBack,
    popupInfo,
    showPopupInfo,
    closePopup,
    searchWithOutshareRoom,
    isTravel,
    isShareRoom,
    showTogglePanel,
    reportPV,
    getAddress,
    getFiltersByType,
    ignoreCompareKeywordAction,
    updatedCheckInDate,
    identityAuthLayerVO,
    handleiIdentityAuth,
    commonUserAction,
    userIdentityListRef
  }
}

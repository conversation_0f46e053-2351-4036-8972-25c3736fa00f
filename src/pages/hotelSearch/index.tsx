import Taro from '@tarojs/taro'
import React, { useEffect, useCallback, useContext, useRef } from 'react'
import { View } from '@/BaseComponents/atoms'
import withPage, { BasePageContext } from "@/common/withPage"
import SearchBar from './components/searchBar'
import SortBar from './components/sortBar'
import HotelList from './components/hotelList'
import HotelSkeleton from '../result/widgets/Hotel/HotelSkeleton'
import { SortFilter, PriceStarFilter, HotelFilter } from './components/filter'
import LocationFilterContainer from './components/filter/locationFilter/locationFilterContainer'
import WebView from './components/webView'
import { useHotelSearch } from './hooks/useHotelSearch'
import styles from './index.module.scss'
import { isWeb, isRn } from '@/common/common'
import { pt, statusBarHeight, initMarkCounter } from '@ltfe/ltfe-core-lib/lib/utiles'
import DiscountPop from '../VResult/DiscountPop'
import { isEmpty } from '@/utils/isType'
import { M_PAGE, ModuleName } from '@/common/mta'
import { glabelFilterType } from './constants/filterType'
import IdentitySection from './components/identitySection'
import PotentialIdentity from './components/potentialIdentity'
import { PERFORMANCE_LABEL } from '@/common/performance'
import { useSimpleFirstScreenMonitor } from '@/utils/FirstScreenMonitor/SimpleMonitor'

const HotelSearch: React.FC = () => {
  // 搜索栏 + 状态栏 + 筛选栏高度
  const fliterTop = isWeb ? (pt(36) + statusBarHeight + pt(40) + pt(14)) : (pt(44) + statusBarHeight + pt(40))
  
  // 首屏监控
  const { startMonitoring } = useSimpleFirstScreenMonitor()
  const firstScreenReported = useRef(false)
  
  const {
    registerDidShow,
    offDidShow,
    getBackParams,
    clearBackParams,
    basePageInfo
  } = useContext(BasePageContext)

  const {
    loading,
    loadMoreStatus,
    list,
    hasMore,
    searchParamsRef,
    activeFilter,
    sortValue,
    combiedFliterRef,
    filterPanelVOList,
    outsideFilterPanelVOList,
    identitySectionData,
    filterMap,
    mtaTrack,
    initializeData,
    reloadDataIfNeed,
    reloadTravelSearch,
    updateMdd,
    updateTravelDate,
    handleLoadMore,
    handleSort,
    handleFilterClose,
    handleFilterClick,
    handleLocationFliter,
    handlePriceStarFliter,
    handleFilterValue,
    handleQucikMatchValue,
    clearDistanceFilter,
    clearAllFilters,
    clearFilter,
    clearKeyword,
    cardOnClick,
    handleIdentitySectionSelect,
    firstLoad,
    travelParmasRef,
    extraParamsRef,
    openWebView,
    webViewUrl,
    webViewVisible,
    iFrameCallBack,
    popupInfo,
    showPopupInfo,
    closePopup,
    searchWithOutshareRoom,
    isTravel,
    isShareRoom,
    showTogglePanel,
    reportPV,
    getAddress,
    getFiltersByType,
    ignoreCompareKeywordAction,
    updatedCheckInDate,
    identityAuthLayerVO,
    userIdentityListRef,
    handleiIdentityAuth,
    commonUserAction
  } = useHotelSearch()
  // didShow 处理函数
  const didShow = useCallback(() => {
    if (firstLoad.current) {
      return
    }
    if (isTravel()) {
      const sugParams = getBackParams()
      if (!isEmpty(sugParams)) {
        reloadTravelSearch(sugParams)
        clearBackParams()
      }
      reportPV(basePageInfo?.pvId)
      return
    }
    // 非首次加载，使用当前状态重新获取数据
    reloadDataIfNeed()
    reportPV(basePageInfo?.pvId)
  }, [])

  useEffect(() => {
    initMarkCounter(ModuleName, PERFORMANCE_LABEL.FIRSTSCREEN_TIME_KEY, 3)

    // 启动首屏监控（仅在RN环境下）
    if (isRn && !firstScreenReported.current) {
      startMonitoring((duration, success) => {
        firstScreenReported.current = true
        console.log(`[酒店搜索] 首屏渲染${success ? '完成' : '超时'}，耗时: ${duration}ms`)
        alert(duration)
        
        // 这里可以上报首屏渲染耗时到埋点系统
        try {
          // 使用项目现有的埋点系统上报
          const mtaData = {
            firstScreenDuration: duration,
            firstScreenSuccess: success,
            timestamp: Date.now()
          }
          console.log('[酒店搜索] 首屏渲染耗时统计:', mtaData)
          // 可以在这里调用具体的埋点上报接口
        } catch (error) {
          console.error('[酒店搜索] 首屏渲染耗时上报失败:', error)
        }
      })
    }
    
    initializeData()
    reportPV(basePageInfo?.pvId)
  }, [])

  useEffect(() => {
    if (!isWeb) return;
    const title = isShareRoom() ? '发起拼房' : '酒店列表';
    Taro.setNavigationBarTitle({ title });
  }, [isWeb, isShareRoom]);

  // 注册和清理 didShow
  useEffect(() => {
    registerDidShow(didShow)
    return () => {
      offDidShow(didShow)
    }
  }, [registerDidShow, offDidShow, didShow])

  return (
    <View className={styles.container}>
      <View className={styles.content}>
        <SearchBar
        updatedCheckInDate={updatedCheckInDate}
          searchParams={searchParamsRef.current}
          travelParams={travelParmasRef.current}
          extraParams={extraParamsRef.current}
          commonUserAction={commonUserAction}
          clear={clearKeyword}
          update={updateMdd}
          updateTravelDate={updateTravelDate}
          openWebView={openWebView}
          isTravel={isTravel()}
          mtaTrack={mtaTrack}
          showTogglePanel={showTogglePanel}
          ignoreCompareKeywordAction={ignoreCompareKeywordAction}
        />

        <View>
          {
            filterPanelVOList.length > 0 && <SortBar
              allFilterList={getFiltersByType(glabelFilterType.all)}
              filterMap={filterMap}
              filterPanelVOList={filterPanelVOList}
              outsideFilterPanelVOList={outsideFilterPanelVOList}
              sortType={sortValue.sortType}
              sortValue={sortValue}
              activeFilterType={activeFilter}
              onFilterClick={handleFilterClick}
              quickMatchClick={handleQucikMatchValue}
              selectedFilters={{
                location_distance: combiedFliterRef.current.location_distance,
                price_star: combiedFliterRef.current.price_star,
                hotel_filter: combiedFliterRef.current.hotel_filter,
                sort: sortValue.filterType
              }}
              mtaTrack={mtaTrack}
            />
          }
          {
            !!identitySectionData && <IdentitySection
              data={identitySectionData}
              onSelect={handleIdentitySectionSelect}
              userIdentityList={userIdentityListRef.current}
              mtaTrack={mtaTrack}
            />
          }
        </View>

        <SortFilter
          style={{ top: fliterTop }}
          visible={activeFilter === 'sort'}
          value={sortValue.sortType}
          onChange={(option) => {
            handleSort(option);
            handleFilterClose();
          }}
          onClose={handleFilterClose}
          mtaTrack={mtaTrack}
        />

        <LocationFilterContainer
          style={{ top: fliterTop }}
          visible={activeFilter === glabelFilterType.location_distance}
          address={getAddress()}
          value={combiedFliterRef.current.location_distance}
          onChange={handleLocationFliter}
          onClose={handleFilterClose}
          mtaTrack={mtaTrack}
        />

        <PriceStarFilter
          style={{ top: fliterTop }}
          visible={activeFilter === glabelFilterType.price_star}
          filterMap={filterMap}
          filterPanelVOList={filterPanelVOList}
          value={combiedFliterRef.current.price_star}
          onChange={handlePriceStarFliter}
          onClose={handleFilterClose}
          mtaTrack={mtaTrack}
        />

        <HotelFilter
          style={{ top: fliterTop }}
          visible={activeFilter === glabelFilterType.hotel_filter}
          allFilterList={getFiltersByType(glabelFilterType.hotel_filter)}
          filterMap={filterMap}
          filterPanelVOList={filterPanelVOList}
          onChange={handleFilterValue}
          onClose={handleFilterClose}
          mtaTrack={mtaTrack}
        />
        {
          loading ? <HotelSkeleton /> : <HotelList
            fliterValue={combiedFliterRef.current.hotel_filter}
            locationValue={combiedFliterRef.current.location_distance}
            priceStarValue={combiedFliterRef.current.price_star}
            data={list}
            loading={loading}
            hasMore={hasMore}
            showTravelPopupInfo={showPopupInfo}
            onLoadMore={handleLoadMore}
            loadMoreStatus={loadMoreStatus}
            clearDistanceFilter={clearDistanceFilter}
            clearAllFilters={clearAllFilters}
            clearFilter={clearFilter}
            cardOnClick={cardOnClick}
            mtaExpo={mtaTrack}
            searchWithOutshareRoom={searchWithOutshareRoom}
          />
        }

        {/* 潜在身份认证浮层组件 */}
        {!!identityAuthLayerVO && <PotentialIdentity data={identityAuthLayerVO} onPress={handleiIdentityAuth} />}

      </View>

      {
        webViewVisible && !isEmpty(webViewUrl) && <WebView
          show={webViewVisible}
          uri={webViewUrl}
          iFrameCallBack={iFrameCallBack}
        />
      }

      <DiscountPop
        showPopup={popupInfo.showPopup}
        description={popupInfo.description}
        toPage={popupInfo.toPage}
        cardInfo={popupInfo.cardInfo}
        onClose={closePopup}
      />

    </View>
  )
}

export default withPage({ pageName: M_PAGE.HotelSearch })(HotelSearch)


export const fetchHotelList = async (
  apiFetch,
  params: any
): Promise<any> => {
  try {
    const [error, response] = await apiFetch('HOTEL_LIST', params, true, { ignoreAddress: true , customPvId: params?.customPvId})
    if (error) {
      return error
    }
    return response
  } catch (error) {
    return error
  }
}

export const fetchUserIdentityList = async (
  apiFetch,
  params: any
): Promise<any> => {
  try {
    const [error, response] = await apiFetch('USER_IDENTITY_LIST', params, true, { ignoreAddress: true })
    if (error) {
      return error
    }
    return response
  } catch (error) {
    return error
  }
}

export const fetchDuccConfig = async (
  apiFetch
): Promise<any> => {
  try {
    const params = { keys: ['official_standard_description'] }
    const [error, response] = await apiFetch('GET_DUCC', params, true, { ignoreAddress: true })
    if (error) {
      return error
    }
    return response
  } catch (error) {
    return error
  }
}

export const fetchHotelListWithDuccConfig = async (
  apiFetch,
  params: any,
  needDuccConfig = false
): Promise<any> => {
  try {
    const requests = [fetchHotelList(apiFetch, params)];
    if (needDuccConfig) {
      requests.push(fetchDuccConfig(apiFetch));
    }
    const responses = await Promise.all(requests);
    const hotelListResponse = responses && responses.length > 0
      ? responses[0]
      : { code: -1 };

    if (!hotelListResponse || +hotelListResponse.code !== 0) {
      return { code: -1 };
    }
    const result: any = {
      code: 0,
      hotelList: hotelListResponse
    };

    if (needDuccConfig) {
      const duccConfigResponse = responses && responses.length > 1
        ? responses[1] : { code: -1 };

      if (!duccConfigResponse || +duccConfigResponse.code !== 0) {
        result.duccConfig = {}
      } else {
        result.duccConfig = duccConfigResponse;
      }
    }
    return result;
  } catch (error) {
    return error;
  }
}

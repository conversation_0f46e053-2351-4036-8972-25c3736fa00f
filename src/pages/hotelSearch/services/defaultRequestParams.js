import { getDefaultDate } from '../utils/utils'


// WARNING: 上线一定要check
// 线上- 终极兜底
const defaultRequestParams = {
    'fromSource': 'hotel_vertical',
    // 兜底空搜
    'keyword': '',
    // 兜底酒店1000
    'priceChannel': '1000',
    // 兜底 -100
    'channelId': '-100',
    'hotelBaseSearchParam': {
        ...getDefaultDate(),
        'roomNum': 1,
        'grownNum': 1,
        'childrenNum': 0,
        'childrenAges': []
    },
    // 兜底北京
    'mddInfo': {
        'county': 0,
        'city': 1,
        'street': 0,
        'showName': '北京',
        'longitude': '',
        'level': 2,
        'latitude': '',
        'type': '2',
        'province': 1
    },
    // 位置兜底朝阳公园
    'latitude': '39.855181',
    'longitude': '116.6812398',
    'posAreaId': '1,72,55674,0',
    'businessType': '1',
    'virtualLocation': 1
}

// const defaultRequestParams = {
//     "latitude": "39.944093",
//     "longitude": "116.482276",
//     "posAreaId": "1,72,55674,0",
//     "virtualLocation": 1,
//     "bizVersion": "1.67",
//     "platformCode": "h5",
//     "pvId": "506c18211345429c8a1b397a6de321b1",
//     "logId": "1b221f1ec4134ca99a2cddaedd053c00",
//     "channelId": "MSEAR",
//     "fromSource": "hotel_vertical",
//     "keyword": "",
//     "priceChannel": "1000",
//     "hotelBaseSearchParam": {
//         "checkInDate": "2025-06-26",
//         "checkOutDate": "2025-06-27",
//         "roomNum": 1,
//         "grownNum": 1,
//         "childrenNum": 0,
//         "childrenAges": []
//     },
//     "businessType": "1",
//     "pageSize": 20,
//     "page": 1,
//     "filterList": [
//         {
//             "filterType": "hotel_grade",
//             "itemId": "4",
//             "itemName": "4星/钻",
//             "itemType": "button",
//             "itemDesc": "高档",
//             "group": false,
//             "itemBehavior": "normal",
//             "multi": 0,
//             "exposeItemCount": 6,
//             "filterKey": "price_star",
//             "groupCode": -100
//         },
//         {
//             "filterType": "hotel_grade",
//             "itemId": "3",
//             "itemName": "3星/钻",
//             "itemType": "button",
//             "itemDesc": "舒适",
//             "group": false,
//             "itemBehavior": "normal",
//             "multi": 0,
//             "exposeItemCount": 6,
//             "filterKey": "price_star",
//             "groupCode": -100
//         },
//         {
//             "filterType": "hotel_bed_type",
//             "itemId": "1,11",
//             "itemName": "双床房",
//             "itemType": "button",
//             "group": false,
//             "itemBehavior": "normal",
//             "multi": 1,
//             "exposeItemCount": 6,
//             "filterKey": "hotel_filter",
//             "groupCode": -100
//         }
//     ],
//     "mddInfo": {
//         "type": "2",
//         "showName": "北京",
//         "province": 1,
//         "city": 1,
//         "county": 0,
//         "street": 0,
//         "level": 2,
//         "latitude": "39.90966",
//         "longitude": "116.65716"
//     },
//     "sortType": "default",
//     "orderType": "desc"
// }

// 测试 - 垂搜排序改变接口异常
// const defaultRequestParams = {
//     "hotelBaseSearchParam": {
//         "checkOutDate": "2025-05-29",
//         "roomNum": 1,
//         "checkInDate": "2025-05-28",
//         "grownNum": 1,
//         "childrenNum": 0,
//         "childrenAges": []
//     },
//     "pageSize": 20,
//     "keyword": "",
//     "pvId": "6ff82fd7f6f64ef5b529a409ed842df5",
//     "index": 2,
//     "sortType": "good_comment_first",
//     "posAreaId": "1,1,,",
//     "extMap": {},
//     "latitude": "39.78952",
//     "logId": "ff01cf76765743f4afb46348322ec9c2",
//     "lbs_city": {
//         "showGeoId": "1",
//         "district": null,
//         "province": "北京",
//         "destType": "3",
//         "geoName": "北京",
//         "spellShort": "bj",
//         "reGeoNameDetail": "北京市通州区经海六路东尚·E园",
//         "lon": "116.566937",
//         "showGeoName": "北京",
//         "city": "北京",
//         "geoId": 36,
//         "town": null,
//         "name": "东尚·E园13号楼",
//         "cityId": "1",
//         "level": "2",
//         "gridAddressId": null,
//         "parentId": null,
//         "provinceId": "1",
//         "spell": "beijing",
//         "townId": null,
//         "lat": "39.78952",
//         "districtId": null,
//         "reGeoName": "东尚·E园13号楼",
//         "is_lbs": true
//     },
//     "orderType": "desc",
//     "longitude": "116.566937",
//     "queryMode": "",
//     "mddInfo": {
//         "county": 0,
//         "city": 1,
//         "street": 0,
//         "showName": "北京",
//         "longitude": "",
//         "level": 2,
//         "latitude": "",
//         "type": "2",
//         "province": 1
//     },
//     "bizVersion": "1.67",
//     "platformCode": "apple",
//     "page": 1,
//     "filterName": "好评优先",
//     "filterType": "good_comment_first",
//     "filterList": []
// }

// 测试 -合并搜索参数状态(差旅用)
// const defaultRequestParams = {
//   'fromSource': 'hotel_booking_public',
//   'queryMode': '3',
//   'keyword': '',  // 兜底空搜
//   'priceChannel': '3020', // 兜底酒店1000
//   'channelId': '-100',  // 兜底 -100
//   'hotelBaseSearchParam': {
//     ...getDefaultDate(),
//     'roomNum': 1,
//     'grownNum': 1,
//     'childrenNum': 0,
//     'childrenAges': []
//   },
//   'latitude': '39.855181',
//   'longitude': '116.563967',
//   'posAreaId': '1,1',
//   'businessType': '1',
//   'pageSize': 20,
//   'distance': '10000',
//   'page': 1,
//   'travelStandard': '0,300',
//   'shareTravelStandard': '0,400',
//   'travelInfo': {
//     'startDate': '2025-06-08',
//     'endDate': '2025-06-10',
//     'travelId': '112333',
//     'processInstanceId': 'abcdddddd'
//   },
//   'mddInfo': {
//     'type': '2',
//     'showName': '北京',
//     'latitude': '39.855181',
//     'longitude': '116.6812398',
//     'province': 1,
//     'city': 1,
//     'county': 55674,
//     'street': 0,
//     'level': 2
//   },
//   'virtualLocation': 1
// }

// 测试 - 传入筛选用
// const defaultRequestParams = {
//     "latitude": "39.944093",
//     "longitude": "116.482276",
//     "posAreaId": "1,72,55674,0",
//     "virtualLocation": 1,
//     "bizVersion": "1.67",
//     "platformCode": "h5",
//     "pvId": "421e97ddbddc4e1fbd841c4f25d0bf5a",
//     "logId": "48864ff83b524a648e8924a1531b379e",
//     "channelId": "travelIndex",
//     "fromSource": "hotel_vertical",
//     "keyword": "天涯海角",
//     "priceChannel": 1000,
//     "hotelBaseSearchParam": {
//         "checkInDate": "2025-05-1",
//         "checkOutDate": "2025-05-2",
//         "roomNum": 1,
//         "grownNum": 1,
//         "childrenNum": 0,
//         "childrenAges": []
//     },
//     "businessType": "1",
//     "pageSize": 20,
//     "page": 1,
//     "filterList": [
//         {
//             "title": "1公里内",
//             "type": "linearDistance",
//             "value": "1000"
//         },
//         {
//             "level": 3,
//             "label": "3星/钻",
//             "type": "star",
//             "filterType": "hotel_grade",
//             "itemId": "3",
//             "itemName": "3星/钻"
//         }
//     ],
//     "mddInfo": {
//         "type": "2",
//         "showName": "北京",
//         "province": 1,
//         "city": 1,
//         "county": 0,
//         "street": 0,
//         "level": 2,
//         "latitude": "39.9033066",
//         "longitude": "116.3977998"
//     }
// }


// const defaultRequestParams = {
//     "fromSource": "hotel_vertical",
//     "keyword": "",
//     "priceChannel": 1000,
//     "channelId": "travelIndex",
//     "hotelBaseSearchParam": {
//         "checkInDate": "2025-05-22",
//         "checkOutDate": "2025-05-23",
//         "roomNum": 1,
//         "grownNum": 1,
//         "childrenNum": 0,
//         "childrenAges": []
//     },
//     "latitude": "39.78832529658871",
//     "longitude": "116.56412618388299",
//     "posAreaId": "1,1,,",
//     "businessType": "1",
//     "pageSize": 20,
//     "page": 1,
//     "virtualLocation": 0,
//     "filterList": [
//         {
//             "filterType": "hotel_facility",
//             "itemId": "651-0",
//             "itemName": "免费停车",
//             "itemType": "button",
//             "group": false,
//             "itemBehavior": "normal",
//             "multi": 0,
//             "exposeItemCount": 6,
//             "filterKey": "hotel_filter",
//             "groupCode": -100
//         }
//     ],
//     "mddInfo": {
//         "type": "2",
//         "showName": "秦皇岛",
//         "province": 5,
//         "city": 248,
//         "county": 48378,
//         "street": 0,
//         "level": 2,
//         "latitude": "",
//         "longitude": ""
//     },
//     "extMap": false,
//     "lbs_city": {
//         "lat": "39.78832529658871",
//         "lon": "116.56412618388299",
//         "city": "北京",
//         "cityId": "1",
//         "district": null,
//         "districtId": null,
//         "geoId": 36,
//         "geoName": "北京",
//         "gridAddressId": null,
//         "level": "2",
//         "parentId": null,
//         "province": "北京",
//         "provinceId": "1",
//         "showGeoId": "1",
//         "showGeoName": "北京",
//         "spell": "beijing",
//         "spellShort": "bj",
//         "town": null,
//         "townId": null,
//         "destType": "3",
//         "reGeoName": "京东大厦D座",
//         "reGeoNameDetail": "北京市通州区科创十一街亦庄开发区科创十一街8号院西南侧约100米",
//         "is_lbs": true,
//         "name": "京东大厦D座"
//     },
//     "sortType": "good_comment_first",
//     "orderType": "desc"
// }

export default defaultRequestParams

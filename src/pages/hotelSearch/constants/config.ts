export const SORT_TYPES = {
  sort: '智能排序',
  location_distance: '位置区域',
  price_star: '价格/星级',
  hotel_filter: '筛选',
  mddInfo: '目的地',
  checkInOutDate: '入住离店'

} as const;

export type SortType = keyof typeof SORT_TYPES;

export const ORDER_TYPES = {
  ASC: 1,
  DESC: 2
} as const

export const PAGE_SIZE = 20

export enum LoadMoreStatus {
  IDLE = 'idle',
  LOADING = 'loading',
  ERROR = 'error',
  NO_MORE = 'no_more'
}

// 排序选项
export const SORT_OPTIONS = [
  {
    filterName: '智能排序',
    filterType: 'default',
    sortType: 'default',
    orderType: 'desc',
    index: 1
  }, {
    filterName: '好评优先',
    filterType: 'good_comment_first',
    sortType: 'good_comment_first',
    orderType: 'desc',
    index: 2
  }, {
    filterName: '评价数多→少',
    filterType: 'comment_count_desc',
    sortType: 'comment_count_desc',
    orderType: 'desc',
    index: 3
  }, {
    filterName: '低价优先',
    filterType: 'price_asc',
    sortType: 'price_asc',
    orderType: 'asc',
    index: 4
  }, {
    filterName: '高价优先',
    filterType: 'price_desc',
    sortType: 'price_desc',
    orderType: 'desc',
    index: 5
  }, {
    filterName: '高星优先',
    filterType: 'star_desc',
    sortType: 'star_desc',
    orderType: 'desc',
    index: 6
  }, {
    filterName: '直线距离近→远',
    filterType: 'distance_asc',
    sortType: 'distance_asc',
    orderType: 'desc',
    index: 7
  }
];


export enum POSITION_TYPE {
  POI = '1', //  poi地址
  CITY = '2', // 选择城市
  LOCATE = '3', // 实时定位
}

export const MTAPlaceHolderString = '-100'
export const MTAPlaceHolderNumber = -100

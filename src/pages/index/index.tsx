
import { View, Loading } from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import HistoricalSearch from './widgets/HistoricalSearch';
import Recommend from './widgets/Recommend';
import SearchBar from './widgets/SearchBar';
import { useIndexService } from './index.service';
import withPage from '@/common/withPage';
import PositionTip from '@/BusinessComponents/PositionTip';
import { IOScrollView } from '@/BaseComponents/IntersectionObserver'
import { Sug } from '@ltfe/ltfe-core-lib/lib'
import { isEmpty } from '@/utils/isType';
import Rank from './widgets/Rank';
import { BIZ_VERSION } from '@/common/useFetch';

function Index() {
  const {
    data,
    hotRecommend,
    historyRef,
    recommendRef,
    handleWordClick,
    onChange,
    searchWord,
    searchRef,
    handleInputBlur,
    setHideDelStatus,
    commonUserAction,
    handleWordClickBySug,
    sugListParams,
    handleMtaClick,
    handleMtaExpo,
    hotRankingList,
    rankRef,
    isHide,
    setIsHide
  } = useIndexService()

  return (
    <View className={styles.wrapper}>
      <SearchBar ref={searchRef} handleWordClick={handleWordClick} onChange={onChange} setHideDelStatus={setHideDelStatus}></SearchBar>
      <IOScrollView
        // @ts-ignore
        className={styles.container}
        onScroll={handleInputBlur}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        // @ts-ignore
        keyboardShouldPersistTaps={'handled'}
      >
        {searchWord && !isEmpty(sugListParams) &&  (
            <Sug.SugList
              sourceType={Sug.SOURCE_TYPE.GLOBAL_SEARCH}
              handleMtaClick={handleMtaClick}
              handleMtaExpo={handleMtaExpo}
              params={{ keyword: searchWord, ...(sugListParams ?? {}) }}
              handleWordClick={handleWordClickBySug}
              bizVersion={BIZ_VERSION}
              handleInputBlur={handleInputBlur}
            />
          )
        }
        <View style={{ display: searchWord ? 'none' : 'flex' }}>
          <PositionTip />
          <HistoricalSearch ref={historyRef} handleVisible={setIsHide} ></HistoricalSearch>
          {
            data.type === 'loading' ? <View className={styles.loading}><Loading /></View> : (
              <>
                <Recommend ref={recommendRef} isHide={isHide} data={hotRecommend} commonUserAction={commonUserAction} handleWordClick={handleWordClick}></Recommend>
                <Rank ref={rankRef} isHide={isHide} data={hotRankingList}></Rank>
              </>
            )
          }
        </View>
      </IOScrollView>
    </View>
  )
}

Index.displayName = 'index'

export default withPage({ pageName: 'index' })(Index)

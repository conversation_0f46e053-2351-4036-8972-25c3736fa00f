@import "@/assets/theme.scss";

.wrapper {
    display: flex;
    flex-direction: row;
    justify-content: center;
    flex: 1;
    // margin: 0 20px;
    padding: 0 20px;
    margin-top: 20px;
    padding-top: 27px;
    border-top-width: 10px;
    border-color: var(--primaryBgColor);
}

.tips {
    color: #7C859C;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    flex-grow: 0;
    flex-shrink: 0;
}

.textQu {
    color: var(--primaryHLTextColor);
    font-size: 14px;
    font-weight: 500;
}

.text {
    color: var(--primaryHLTextColor);
    font-size: 14px;
    font-weight: 500;
    flex-grow: 0;
    flex-shrink: 1;
}
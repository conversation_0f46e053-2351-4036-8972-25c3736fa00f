import { useEffect, useRef, useState } from "react";
import { usePrevious } from "@/utils/hooks";
import { isEmpty, isUndefined } from "@/utils/isType";
import { pt, isAndroid } from '@ltfe/ltfe-core-lib/lib/utiles';
import { isWeb } from "@/common/common";
import { pxTransform } from "@tarojs/taro";
import { MTA_NONE, mtaExposure } from "@/common/mta";
import IdleQueue from "@/utils/IdleQueue";

export function useLexicalChunks(props) {
    const { data = [], showIcon = false, handleClick, handleIconClick, showRows = 2 } = props
    const [renderData, setRenderData] = useState<any[]>([])

    const refCalc = useRef(); // 计算容器
    const containerWidth = useRef(0); // 放词的容器宽度
    const calcWidthArr = useRef([]) // 计算词后的宽度数组
    const isClickShowMore = useRef(false) // 是否已经点击过展示更多
    const prevShowIcon = usePrevious(showIcon); // 是否展示icon的前置状态
    const expoEndData = useRef<any>({}) // 埋点信息

    // 获取容器的宽度
    const handelLayout = (e) => {
        if (containerWidth.current) {
            return
        }
        const { width } = e.nativeEvent.layout
        containerWidth.current = width
        calcCallBack(calcWidthArr.current)
    }

    // 计算宽度的方法
    const calcCallBack = (val, _showRows = showRows) => {
        // showRows  期望的展示行数
        calcWidthArr.current = val
        if (!isEmpty(calcWidthArr.current) && !containerWidth.current) {
            return
        }
        const result: any[] = []
        let maxWidth = containerWidth.current // 容器宽度
        let totalWidth = 0 // 总宽度
        const moreIconWidth = getPt(24) // 展示更多的icon宽度
        let rows = 1 // 实际行数

        if (showIcon) { // 展示叉号的时候不计算宽度 直接渲染
            for (let i = 0; i < val.length; i++) {
                result.push({
                    displayName: data[i].displayName,
                    realName: data[i]?.realName,
                    jumpUrl: data[i]?.jumpUrl,
                    style: {
                        width: 'auto'
                    }
                })
            }
        } else { // 计算宽度
            for (let i = 0; i < val.length; i++) {
                const item = val[i]
                const { width } = item || { width: getPt(120) }
                const gap = getPt(10)
                const minWidth = getPt(100)// 100 是展示四个字打点的宽度
                let realWidth = width + gap // 每个块的真实宽度
                let renderWidth = 0 // 每个块的渲染高度
                if (realWidth >= maxWidth * 0.5) { // 如果元素的实际宽度还比父容器的宽度的一半还大 则给元素宽度设置为父元素宽度的40%
                    renderWidth = maxWidth * 0.5 - gap // 把块之间的间距空出来
                    realWidth = maxWidth * 0.5
                }

                if ((maxWidth - totalWidth) >= realWidth) { // 如果剩余空间能放下该元素 则将该元素放进来
                    totalWidth += realWidth
                    result.push({
                        displayName: data[i].displayName,
                        realName: data[i]?.realName,
                        jumpUrl: data[i]?.jumpUrl,
                        renderWidth,
                        style: {
                            width: renderWidth ? renderWidth : 'auto'
                        }
                    })
                } else { // 如果剩余空间放不下该元素
                    // 计算剩余空间大小
                    const restWidth = maxWidth - totalWidth
                    if (restWidth < minWidth) { // 如果剩余空间小于100 剩余空间将不展示内容
                        realWidth = restWidth
                        totalWidth = 0
                        rows += 1
                        if ((rows > _showRows && i !== val.length - 1) ||
                            rows > _showRows && i === val.length - 1
                        ) {
                            if (restWidth < moreIconWidth) { // 如果剩余空间小于展示更多icon宽度 查看上一个元素的宽度 判断宽度是否大于100 大于就给出icon的宽度
                                if (result[i - 1].renderWidth > minWidth) {
                                    result[i - 1].style.width = result[i - 1].renderWidth - moreIconWidth
                                } else { // 不要最后一个元素
                                    result.pop()
                                }
                            }
                            result.push({
                                showMoreIcon: true,
                            })
                            break
                        }
                        i -= 1
                    } else { // 如果剩余空间大于100 则将该元素进行压缩 将元素的渲染宽度设置为剩余空间宽度打点展示
                        realWidth = restWidth
                        totalWidth = 0
                        renderWidth = restWidth - gap
                        result.push({
                            displayName: data[i].displayName,
                            realName: data[i]?.realName,
                            jumpUrl: data[i]?.jumpUrl,
                            renderWidth,
                            style: {
                                width: renderWidth
                            }
                        })
                        rows += 1
                        if (rows > _showRows && i !== val.length - 1) {
                            if (result[i].renderWidth > minWidth) { // 如果最后一个元素宽度大于最小管宽度 让该元素匀出icon的宽度
                                result[i].style.width = result[i].renderWidth - moreIconWidth
                            } else { // 不要最后一个元素
                                result.pop()
                            }
                            result.push({
                                showMoreIcon: true,
                            })
                            break
                        }
                    }
                }
            }
        }
        setRenderData(result)
    }

    // 获取元素的宽度
    useEffect(() => {
        const ptFn = isWeb ? pxTransform : getPt;
        (refCalc.current as any)?.getLayoutList?.({
            texts: data.map((item) => item.displayName),
            textStyle: { fontSize: ptFn(14), padding: ptFn(10) },
            callback: calcCallBack
        });
    }, [data])

    // 监听图标的展示隐藏 图标展示情况变化时 需要重新计算展示
    useEffect(() => {
        // 这块的判断是 第一次进来和showIcon没有变化的时候不执行这块
        if (!isUndefined(prevShowIcon) && showIcon !== prevShowIcon) {
            const _showRows = isClickShowMore.current ? 100 : showRows
            calcCallBack(calcWidthArr.current, _showRows)
        }
    }, [showIcon])

    // 点击展示更多的操作
    const handleShowMore = () => {
        calcCallBack(calcWidthArr.current, 100)
        isClickShowMore.current = true
    }

    // WEB 和 rn 的样式适配
    const getPt = (num) => {
        if (isAndroid) {
            return num
        }
        return pt(num)
    }

    // 点击处理 如果是删除状态 点击词不进行跳转 进行删除
    const onClick = (item, index) => {
        if (showIcon) {
            handleIconClick?.(item, index)
        } else {
            handleClick?.(item, index)
        }
    }

    // 曝光埋点
    const handelExpo = (visible, index, item) => {
        const { srvJson = {}, eid, pageId, page } = props?.mtaData || {}
        const { displayName, realName } = item || {}

        if (visible && !expoEndData.current?.[`${displayName}_${index}`]) {
            IdleQueue.add(mtaExposure, eid, pageId, page, {
                index,
                displayName: displayName || MTA_NONE,
                realName: realName || MTA_NONE,
                jumpLink: MTA_NONE,
                search_o2o_coordinates: MTA_NONE,
                search_fouraddrid: MTA_NONE,
                ...srvJson
                }
            )
            expoEndData.current[`${displayName}_${index}`] = true
        }
    }

    return {
        handelLayout,
        renderData,
        showIcon,
        onClick,
        handleIconClick,
        refCalc,
        handleShowMore,
        handelExpo,
    }

}
@import "@/assets/theme.scss";

.container {
    overflow: hidden;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    column-gap: 10px;
    row-gap: 8px;
}

.wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 30px;
    background-color: var(--primaryBgColor);
    color: var(--primaryTextColor);
    font-size: 14px;
    font-weight: 400;
    border-radius: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
}

.textInfo {
    font-size: 14px;
    padding: 0 10px;
    flex-shrink: 1;
}

.textInfoAndroid {
    padding-bottom: 2px;
}

.closeIcon {
    display: inline-block;
    width: 15px;
    height: 15px;
    // margin-left: 4px;
    flex-shrink: 0;
    margin-right: 10px;
}

.showMoreIcon {
    width: 12px;
    height: 5px;
}
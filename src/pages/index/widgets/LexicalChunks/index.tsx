import {View, Text, Image} from '@/BaseComponents/atoms'
import CalcTextLayout from '@/BaseComponents/CalcTextLayout'
import styles from './index.module.scss'
import {useLexicalChunks} from './index.service'
import {isEmpty} from '@/utils/isType'
import {getImageUrl} from '@/assets/imgs'
import {InView} from '@/BaseComponents/IntersectionObserver'
import {isAndroid} from '@/common/common'
import EventCatch from '@/Components/EventCatch'
import classNames from 'classnames'

/**
 * 词块
 * @param props
 * @returns
 */

interface Props {
    data: {
        displayName: string;
        realName: string;
    }[];
    showIcon?: boolean;
    handleClick: (data, index) => void;
    handleIconClick?: (data, index) => void;
    showRows?: number;
    mtaData?: any
    mtaExpo?: any
    itemTextStyle?: any
    newMtaExp?: any
}

const ImgConfig = {
    wordDelete: getImageUrl('wordDelete'), // 删除
    showMore: getImageUrl('showMore') // 展示更多
}

const LexicalChunks = (props: Props) => {
    const {
        renderData,
        showIcon,
        handelLayout,
        onClick,
        handleIconClick,
        refCalc,
        handleShowMore,
        handelExpo
    } = useLexicalChunks(props)
    const { mtaExpo, itemTextStyle = {}, newMtaExp } = props

   const handleMtaExpo = (visible, index, item) => {
    if (typeof mtaExpo === "function") {
      mtaExpo({visible, index, item})
      return
    }
     if (typeof newMtaExp === "function") {
       newMtaExp({visible, index, item})
       return
     }
     handelExpo(visible, index, item)
   }
        
    return (
        <>
            <CalcTextLayout ref={refCalc}/>
            <View className={styles.container} onLayout={handelLayout}>
                {
                    !isEmpty(renderData) && renderData.map((item, index) => {
                        if (item?.displayName) {
                            return (
                                <InView onChange={(visible) => {
                                    handleMtaExpo(visible, index, item)
                                    // typeof mtaExpo === "function" ? mtaExpo({visible, index, item}) : handelExpo(visible, index, item)
                                }} onClick={() => onClick(item, index)} key={index} className={`${styles.wrapper}`}
                                        style={item?.style}>
                                    <Text style={[item?.style, itemTextStyle]} className={classNames({
                                        [styles.textInfo]: true,
                                        [styles.textInfoAndroid]: isAndroid
                                    })} numberOfLines={1}>
                                        {item.displayName}
                                    </Text>
                                    {
                                        !!showIcon &&
                                        (
                                            <View style={{flexShrink: 0}} onClick={() => handleIconClick(item, index)}>
                                                <Image className={styles.closeIcon} src={ImgConfig['wordDelete']}
                                                       mode="scaleToFill"></Image>
                                            </View>
                                        )
                                    }
                                </InView>
                            )
                        }
                        if (item?.showMoreIcon) {
                            return (
                                <View key={index} onClick={handleShowMore}>
                                    <Image key={index} className={styles.showMoreIcon} src={ImgConfig['showMore']}
                                           mode="scaleToFill"></Image>
                                </View>
                            )
                        }
                    })
                }
            </View>
        </>
    )
}

export default EventCatch(LexicalChunks)

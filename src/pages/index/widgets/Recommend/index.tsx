import {View, Text} from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import LexicalChunks from '../LexicalChunks'
import {useRecommend} from './index.service'
import React, {useImperativeHandle} from 'react'
import {isEmpty} from '@/utils/isType'
import classNames from 'classnames'

/**
 * 热门推荐
 * @param props
 * @returns
 */

interface Props {
    data: {
        title: string;
        hotWords: {
            displayName: string;
            realName: string;
        }[];
    },
    commonUserAction: any
    handleWordClick: (word: any, params: any, jumpUrl: any) => void
}

const Recommend = (props: Props, ref) => {
    const {
        title,
        hotWords,
        onWordClick,
        isHide,
        mtaData
    } = useRecommend(props)

    if (isEmpty(hotWords)) {
        return null
    }

    return (
        <View className={styles.wrapper} style={{display: isHide ? 'none' : 'flex'}}>
            <Text className={classNames({
                [styles.title]: true,
                bold: true
            })}>{title}</Text>
            <LexicalChunks
                data={hotWords}
                showIcon={false}
                handleClick={onWordClick}
                showRows={2}
                mtaData={mtaData.current}
            />
        </View>
    )
}

export default Recommend

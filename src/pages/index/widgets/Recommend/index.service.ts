import { M_EVENTID, M_PAGE, M_PAGEID, MTA_NONE, newMta } from "@/common/mta";
import IdleQueue from "@/utils/IdleQueue";
import { useEffect, useRef, useState } from "react";

export function useRecommend(props) {
    const { data = {}, handleWordClick, commonUserAction = {}, isHide } = props
    const { title, hotWords = [] } = data
    const mtaData = useRef({
        srvJson: {},
        eid: M_EVENTID.TravelSearchHotWordExpo,
        pageId: M_PAGEID.TravelSearch,
        page: M_PAGE.SearchIndex
    })

    useEffect(() => {
        mtaData.current.srvJson = commonUserAction
    }, [commonUserAction])

    // 词的点击
    const onWordClick = (item, index) => {
        handleWordClick?.(item)
        // 上报埋点
        const jsonParams = {
            index,
            displayName: item?.displayName || MTA_NONE,
            realName: item?.realName || MTA_NONE,
            jumpLink: MTA_NONE,
            search_o2o_coordinates: MTA_NONE,
            search_fouraddrid: MTA_NONE,
            ...commonUserAction
        }
        IdleQueue.add(newMta, M_EVENTID.TravelSearchHotWord, M_PAGEID.TravelSearch, M_PAGE.SearchIndex, jsonParams)
    }

    return {
        title,
        hotWords: hotWords,
        onWordClick,
        isHide,
        mtaData,
    }
}

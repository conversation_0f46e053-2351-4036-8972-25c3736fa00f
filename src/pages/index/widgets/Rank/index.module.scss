.container {
    margin-top: 24px;
    display: flex;
    flex-direction: row;
    flex: 1;
}

.wrapper {
    display: flex;
    flex-direction: row;
    gap: 8px;
    margin-left: 16px;
    margin-bottom: 8px;
}

.linearWrapper {
    width: 212px;
    height: 546px;
    border: 0.5px solid #FFE9D6;
    border-radius: 12px;
    padding-left: 10px;
    padding-top: 12px;
    padding-right: 10px;
}

.onlyOne {
    width: 343px;
}

.bgBox {
    border-radius: 12px;
    overflow: hidden;
}

.rankImage {
    width: 58px;
    height: 16px;
}

.content {
    margin-top: 14px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.lastRank {
    margin-right: 16px;
}

.titleContainer {
    height: 20px;
    display: flex;
    flex-direction: row;
}

.city {
    color: #622200;
    font-size: 14px;
    font-weight: 600;
}

.point {
    color: #622200;
    font-size: 20px;
    height: 20px;
    margin-left: 2px;
    margin-right: 2px;
}

.pointAndroid { 
    margin-top: -6px;
}

.pointLineHeight {
    line-height: 20px;
}

.cityContent {
    display: flex;
    height: 20px;
    flex-direction: row;
    align-items: center;
    margin-top: 1px;
    flex: 1;
    margin-right: 10px;
}

.imgContent {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 20px;
}

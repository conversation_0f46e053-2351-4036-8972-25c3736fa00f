import { useMemo } from 'react';
import styles from './rankItem.module.scss'
import { View, Text, Image } from '@/BaseComponents/atoms'
import { getImageUrl } from "@/assets/imgs";
import useJumpTo from '@/common/useJumpTo'
import { InView } from '@/BaseComponents/IntersectionObserver'
import { M_PAGE, M_PAGEID, M_EVENTID, newMta, mtaEp, MTA_NONE } from "@/common/mta";
import { useRef } from 'react'
import IdleQueue from "@/utils/IdleQueue";
import classNames from 'classnames'
import { isAndroid, isWeb } from '@/common/common';

const IMG = {
  line: getImageUrl('lineSpacing')
}

enum STYLE_CODE {
  TEXT = 'Text',
  IMAGE = 'Image'
}

const RankItem = (props) => {
  const { data = {}, index, rankInfo = {} } = props
  const jumpTo = useJumpTo()
  const rankDesc = data?.labelTagListMap?.rankingDesc || []
  const rankIndex = data?.labelTagListMap?.rankingIndex?.[0] || []
  const expoEndData = useRef<any>({}) // 埋点信息

  const handleClick = () => {
    const { itemId, jumpUrl  } = data || {}
    const { rankId, title, bizType, city } = rankInfo || {}

    // 上报埋点
    const jsonParams = {
      index,
      rankId: rankId || MTA_NONE,
      rankName: title || MTA_NONE,
      bizType: bizType || MTA_NONE,
      city: city || MTA_NONE,
      itemId: itemId || MTA_NONE,
      jumpUrl: jumpUrl || MTA_NONE,
    }
    IdleQueue.add(newMta, M_EVENTID.TravelSearchRank, M_PAGEID.TravelSearch, M_PAGE.SearchIndex, jsonParams)

    if (data?.jumpUrl) {
      jumpTo({to: 'web', params: {url: decodeURIComponent(data?.jumpUrl)}})
    }
  }

    // 曝光埋点
    const handelExpo = (visible) => {
      const { itemId, jumpUrl  } = data || {}
      const { rankId, title, bizType, city } = rankInfo || {}
      if (visible && !expoEndData.current?.[`${rankId}_${itemId}_${index}`]) {
          IdleQueue.add(mtaEp, M_EVENTID.TravelSearchRankExpo, M_PAGEID.TravelSearch, M_PAGE.SearchIndex, {
              index,
              rankId: rankId || MTA_NONE,
              rankName: title || MTA_NONE,
              bizType: bizType || MTA_NONE,
              city: city || MTA_NONE,
              itemId: itemId || MTA_NONE,
              jumpUrl: jumpUrl || MTA_NONE,
          })
          expoEndData.current[`${rankId}_${itemId}_${index}`] = true
      }
  }

  const rankDescribe = useMemo((): React.ReactNode[] => {
    return rankDesc.map((item, index) => {
      // 根据索引判断是否需要添加分隔符，并返回副标题的React节点
      return (
        <>
          {index > 0 ? <Image src={IMG.line} className={styles.image} /> : null}
          {
            item.styleCode === STYLE_CODE.TEXT && 
            <Text className={styles.subText} style={{ color: item?.fontColor || "#A2ABBF" }}>
              {item?.listShowName}
            </Text>
          }
        </>
      );
    });
  }, [rankDesc]);

  return (
    <InView
      onChange={(visible) => {
        handelExpo(visible)
      }}
      className={styles.wrapper}
      onClick={handleClick}>
      <View className={styles.rankIndex} style={{
          backgroundColor: rankIndex?.bgColor
        }}
      >
        <Text
          className={classNames({[styles.rank]: true, bold: true, [styles.rankAndroid]: isAndroid && !isWeb})}
          style={{
              color: rankIndex?.fontColor,
          }}
        >{rankIndex?.listShowName}</Text>
      </View>
      <View className={styles.content}>
        <Text numberOfLines={1} className={styles.title}>{data?.title}</Text>
        <View className={styles.subTitle}>
          <Text className={styles.subContainer} numberOfLines={1}>{rankDescribe}</Text>
        </View>
      </View>
    </InView>
  )
}

export default RankItem
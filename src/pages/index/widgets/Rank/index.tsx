import styles from './index.module.scss'
import {View, Text, Image, LinearGradient} from '@/BaseComponents/atoms'
import {IOScrollView} from '@/BaseComponents/IntersectionObserver'
import RankItem from './rankItem'
import {isAndroid, isWeb} from '@/common/common'
import classNames from 'classnames'
import {isEmpty} from '@/utils/isType'
import {getImageUrl} from '@/assets/imgs'
import {pt} from '@ltfe/ltfe-core-lib/lib/utiles'
import useJumpTo from '@/common/useJumpTo'
import React from 'react'

const ImgConfig = {
    rankImage: getImageUrl('rankImage') // 榜单
}

const Rank = (props) => {
    const {data = [], isHide} = props
    const jumpTo = useJumpTo()

    if (isEmpty(data)) {
        return null
    }

    const handleClick = (jumpUrl) => {
        if (!isEmpty(jumpUrl)) {
            jumpTo({to: 'web', params: {url: decodeURIComponent(jumpUrl)}})
        }
    }

    return (
        <IOScrollView
            style={{display: isHide ? 'none' : 'flex'}}
            className={styles.container}
            horizontal
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps={'handled'}
        >
            <View className={styles.wrapper}>
                {
                    data.map((item, index) => {
                        return (
                            <LinearGradient
                                locations={[0, 0.1]}
                                colors={isWeb ? ['#FFDAC6', '#FFF', '#FFF'] : ['#FFDAC6', '#FFF']}
                                className={classNames({
                                    [styles.bgBox]: true,
                                    [styles.lastRank]: (data.length > 1 && index === data.length - 1),
                                    [styles.onlyOne]: data.length == 1
                                })}
                                key={`${item.title}_${index}`}
                            >
                                {/* 这块给内容包一层设置边框和圆角 直接写在渐变上圆角会失效 */}
                                <View
                                    onClick={() => {
                                        handleClick(item?.rankingJumpUrl)
                                    }}
                                    className={classNames({
                                        [styles.linearWrapper]: true,
                                        [styles.onlyOne]: data.length == 1
                                    })}>
                                    <View className={styles.titleContainer}>
                                        <View className={styles.imgContent}>
                                            {
                                                (item?.rankingImage && item?.rankingImageW && item?.rankingImageH) ? (
                                                    <Image
                                                        style={{width: pt(item.rankingImageW), height: pt(item.rankingImageH)}}
                                                        src={item.rankingImage}
                                                        mode="scaleToFill"
                                                    />
                                                ) : (
                                                    <Image
                                                        className={styles.rankImage}
                                                        src={ImgConfig['rankImage']}
                                                        mode="scaleToFill"
                                                    />
                                                )
                                            }
                                            {
                                                item?.city ? (
                                                    <Text className={classNames({
                                                        [styles.point]: true,
                                                        [styles.pointAndroid]: isAndroid && !isWeb,
                                                        [styles.pointLineHeight]: isWeb
                                                    })}>·</Text>
                                                ) : null
                                            }
                                        </View>
                                        {
                                            item?.city ? (
                                                <View className={styles.cityContent}>
                                                    <Text numberOfLines={1}
                                                          className={classNames({[styles.city]: true, bold: true})}>{item?.city ? `${item.city}` : ''}</Text>
                                                </View>
                                            ) : null
                                        }

                                    </View>
                                    <View className={styles.content}>
                                        {
                                            item.hotRankingDetailList.map((ele, index) => {
                                                return <RankItem key={`${ele.title}_${index}`} data={ele}
                                                                 rankInfo={item} index={index}></RankItem>
                                            })
                                        }
                                    </View>
                                </View>
                            </LinearGradient>
                        )
                    })
                }
            </View>
        </IOScrollView>
    )
}

export default Rank

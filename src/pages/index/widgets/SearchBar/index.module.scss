@import "@/assets/theme.scss";

.navBar {
    display: flex;
    flex-direction: row;
    flex: 1;
    padding-left: 42px;
    // margin-bottom: 7px;
}

.navBarWeb {
    margin-top: 4px;
    padding-left: 12px;
    flex: 0 0 auto;
}

.searchContainer {
    border-radius: 6px;
    border: 1px solid var(--primaryHLTextColor);
    background: #FFF;
    height: 34px;
    padding-left: 12px;
    display: flex;
    flex-direction: row;
    flex: 1;
    align-items: center;
    margin-right: 8px;
    justify-content: space-between;
}

.searchContainerWeb {
    margin-bottom: 10px;
    // align-items: center;
}

.search {
    border-radius: 4px;
    background: #fff;
    padding-right: 4px;
    margin-right: 4px;
    font-size: 15px;
    flex: 1;
    color: #1a1a1a;
}

.searchAndroid {
    margin-left: -8px;
    border-radius: 4px;
    background: #fff;
    padding-right: 4px;
    margin-right: 4px;
    font-size: 15px;
    flex: 1;
    color: #1a1a1a;
    background-color: transparent;
    height: 50px;
    margin-top:  4px;
}

.searchWeb {
    border-radius: 4px;
    background: #fff;
    padding-right: 4px;
    margin-right: 4px;
    font-size: 15px;
    flex: 1;
    color: #1a1a1a;
    background-color: transparent;
    height: 34px;
    line-height: 16px;
    margin-top:  11px;
}

.searchBtn {
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 13px;
    border-radius: 4px;
    background: var(--primaryHLTextColor);
    margin-right: 2px;
    margin-right: 14px;
}

.searchBtnText {
    font-size: 15px;
    color: #FFF;
    font-weight: 400;
}

.closeIcon {
    width: 15px;
    height: 15px;
    margin-right: 12px;
}

.cancel {
    width: 20px;
    height: 20px;
    margin-left: -5px;
    margin-top: 7px;
    margin-right: 6px;
}

import {useContext, useRef, useState} from 'react'
import {Keyboard} from 'react-native'
import Exit from '@/utils/exit'
import globalInfoModel from '@/store/model/globalInfo.model'
import {isEmpty, isFunction} from '@/utils/isType'
import {BasePageContext} from '@/common/withPage'
import {useDidHide, useDidShow} from '@tarojs/taro'
import {M_EVENTID, M_PAGE, M_PAGEID, MTA_NONE, newMta} from '@/common/mta'
import {urlDecode} from '@/utils/urlDecode'
import IdleQueue from '@/utils/IdleQueue'
import {hideKeyboard} from '@/utils/keyboard'

export function useSearchBarService(props) {
    const {handleWordClick, onChange, setHideDelStatus, keyword, displayName, realName} = props
    const searchWord = useRef({})
    const focus = true // 本地调试的时候可以将该处改为false 方便刷新等操作
    const [inputVal, setInputVal] = useState(keyword ? keyword : '')
    const timer = useRef(null as any)
    const inputRef = useRef(null)
    const placeHolder = {
        displayName: displayName ?? urlDecode(globalInfoModel.staticParams?.displayName),
        realName: realName ?? urlDecode(globalInfoModel.staticParams?.realName)
    }
    const {getBackParams, clearBackParams} = useContext(BasePageContext)

    useDidShow(() => {
        // goback 携带参数
        const pageParams: any = getBackParams() || {}
        if (!isEmpty(pageParams)) {
            const displayName = props.displayName ?? urlDecode(pageParams.displayName)
            const realName = props.realName ?? urlDecode(pageParams.realName)
            if (displayName && realName) {
                searchWord.current = {
                    displayName,
                    realName
                }
                // 如果从搜索结果页面回来有明词也有暗词 这时候给suglist的是暗词进行查询
                setInputVal(displayName)
                // 把词给出去
                onChange(realName)
            } else {
                handleClose()
            }

            clearBackParams()
        }
        setTimeout(() => {
            if (isFunction(inputRef.current?.focus)) {
                inputRef.current?.focus()
            }
        }, 500)
    })

    useDidHide(() => {
        handleInputBlur()
    })

    // 点击搜索按钮 判断搜索词是否存在 如果存在用搜索词进行搜索跳转 如果没有用暗词进行搜索跳转
    const handleSearch = () => {
        const word: any = !isEmpty(searchWord.current) ? searchWord.current : placeHolder

        handleWordClick(word)
        // 埋点
        const jsonParams = {
            displayName: word?.displayName || word || MTA_NONE,
            realWord: word?.realName || word || MTA_NONE
        }
        IdleQueue.add(newMta, M_EVENTID.TravelSearchSearchIcon, M_PAGEID.TravelSearch, M_PAGE.SearchIndex, jsonParams)

        handleInputBlur()
    }


    // 输入框内容变化 防抖
    const onInput = (value) => {
        setInputVal(value)
        if (timer.current) {
            clearTimeout(timer.current) // 如果定时器已经存在，清除它
        }
        timer.current = setTimeout(() => { // 设置一个新的定时器
            // 把词给出去
            searchWord.current = {
                displayName: value,
                realName: value
            }
            onChange(value)
            if (!isEmpty(value)) {
                setHideDelStatus?.()
            }
        }, 100)

    }

    // 返回到酒旅首页
    const goBack = () => {
        Exit()
    }

    // 点击叉号
    const handleClose = () => {
        setInputVal('')
        onChange('')
        searchWord.current = {}
    }

    // 外层滚动
    const handleInputBlur = () => {
        try {
            hideKeyboard()
            // // 当滚动时，让输入框失去焦点
            // if (isFunction(inputRef.current?.blur)) {
            //     inputRef.current?.blur()
            // }
        } catch (error) {

        }
    }

    return {
        handleSearch,
        onInput,
        placeHolder,
        focus,
        inputVal,
        goBack,
        handleClose,
        handleInputBlur,
        inputRef
    }
}

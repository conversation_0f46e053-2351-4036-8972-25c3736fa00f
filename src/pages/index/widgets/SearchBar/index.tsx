import React, { useImperativeHandle } from 'react'
import { View, Text, TextInput, Image } from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import { useSearchBarService } from './index.service'
import { isEmpty } from '@/utils/isType'
import { getImageUrl } from '@/assets/imgs'
import NavBar from '@/BaseComponents/NavBar'
import classNames from 'classnames'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import { isWeb, isAndroid } from '@/common/common'
import EventCatch from '@/Components/EventCatch'
import {hideKeyboard} from '@/utils/keyboard'

/**
 * 搜索条
 * @param props
 * @returns
 */

interface Props {
    handleWordClick: (word: any, params: any, jumpUrl: any) => void;
    onChange: (word) => void;
    setHideDelStatus: () => void;
}

const ImgConfig = {
    circleClose: getImageUrl('circleClose'), // 删除
    backIcon: getImageUrl('backIcon')
}

const SearchBar = React.forwardRef((props: Props, ref) => {
    const { hasCancel, onCancel, queryMode, isTravel = false } = props
    // 差旅+拼房来源屏蔽搜索框
    const hiddenSearchText = isTravel && (queryMode === '1' || queryMode === '2')
    const {
        handleSearch,
        onInput,
        placeHolder,
        inputVal,
        goBack,
        handleClose,
        inputRef,
        handleInputBlur
    } = useSearchBarService(props)

    const placeHolderText = () => {
        // 差旅打补丁
        if (isTravel) {
            if (queryMode === '1' || queryMode === '2') {
                return '请输入位置/酒店'
            } else {
                return '请输入位置/品牌/酒店'
            }
        }
        return placeHolder?.displayName
    }

    useImperativeHandle(ref, () => {
        return {
            handleInputBlur
        }
    })

    return (
        <NavBar leftFn={goBack} hideBorder={true} style={{ marginBottom: pt(10) }} center={
            <View className={classNames({
                [styles.navBar]: true,
                [styles.navBarWeb]: isWeb
            })}>
                {
                    hasCancel ? <View onClick={onCancel}><Image className={styles.cancel} src={ImgConfig['backIcon']}
                        mode="scaleToFill" /></View> : null
                }
                <View className={classNames({
                    [styles.searchContainer]: true,
                    [styles.searchContainerWeb]: isWeb
                })}>
                    <TextInput
                        className={isWeb ? styles.searchWeb : isAndroid ? styles.searchAndroid : styles.search}
                        onChangeText={onInput}
                        placeholder={placeHolderText()}
                        value={inputVal}
                        ref={inputRef}
                        markedTextCallChange={true}
                        onSubmitEditing={hiddenSearchText ? () => {
                            hideKeyboard()
                        } : handleSearch}
                    />
                    {
                        !isEmpty(inputVal) && (
                            <View onClick={handleClose}>
                                <Image className={styles.closeIcon} src={ImgConfig['circleClose']}
                                    mode="scaleToFill"></Image>
                            </View>
                        )
                    }
                </View>
                {
                    !hiddenSearchText && <View onClick={handleSearch} className={styles.searchBtn}>
                        <Text className={styles.searchBtnText}>搜索</Text>
                    </View>
                }
            </View>
        } />
    )
})

export default React.memo(EventCatch(SearchBar))

import HistoricalSearchModel from "./index.model";
import { useState, useEffect, useContext, useRef } from "react";
import { isEmpty, isString } from "@/utils/isType";
import useJumpTo from "@/common/useJumpTo";
import { M_EVENTID, M_PAGE, M_PAGEID, MTA_NONE, newMta } from "@/common/mta";
import { BasePageContext } from "@/common/withPage";
import IdleQueue from "@/utils/IdleQueue";
import { useDidShow } from "@tarojs/taro";
import { reportInfo } from "@/common/reporter";
import { errorCodeConstantMapping, ErrortType } from "@/common/reporter/errorMapping";

export function useHistoricalSearch(props) {

    const model = new HistoricalSearchModel(props)
    const [isDel, setIsDel] = useState(false) // 是否是删除状态
    const [historyWords, setHistoryWords] = useState([]) // 历史搜索词
    const [showDialog, setShowDialog] = useState(false) // 是否展示弹窗
    const basePageContext = useContext(BasePageContext) // 页面数据
    const pvId = basePageContext?.basePageInfo?.pvId // 获取pvid

    //  埋点信息
    const mtaData = useRef({
        srvJson: {
            pvid: pvId,
            logid: pvId,
        },
        eid: M_EVENTID.TravelSearchHistoryWordExpo,
        pageId: M_PAGEID.TravelSearch,
        page: M_PAGE.SearchIndex
    })


    useDidShow(() => {
        // 初始化获取历史搜索词
        model.getHistoryWords().then((words) => {
            if (words) {
                const tempWords = sliceWords(words)
                setHistoryWords(tempWords)
            }
        }).catch((error) => {
            reportInfo({
                code: errorCodeConstantMapping?.LOADERROR_BUSINESS_IMMEDIATE_ATTENTION,
                errorDetail: {
                  errorType: ErrortType.Info,
                  customMsg: {
                        compName: '历史词渲染异常',
                        errorInfo: error?.message,
                        errorStack: error?.stack,
                    },
                }
            })
        })
    })

    // 截取数据
    const sliceWords = (words, nums = 20) => {
        if (isEmpty(words)) {
            return []
        }
        return words.slice(0, nums) || []
    }

    // 设置是否是点击了删除 进入删除状态 flag true 删除状态 false 正常状态
    const handleDelStatus = async (flag) => {
        if (flag) { // flag 为true 点击删除图标时上传点击埋点
            IdleQueue.add(newMta, M_EVENTID.TravelSearchClear, M_PAGEID.TravelSearch, M_PAGE.SearchIndex)
        }
        const words = await model.getHistoryWords()
        setIsDel(flag)
        if (flag) {
            setHistoryWords(words)
        } else {
            const tempWords = sliceWords(words)
            setHistoryWords(tempWords)
        }
        model.handleVisible(flag) // 设置是否展示其他组件 删除状态下只展示历史搜索
    }

    const jumpTo = useJumpTo()

    // 词的点击操作 将词存起来 跳转页面
    const handleWordClick = async (word, params = {}, jumpUrl?) => {
        // 如果搜索词是字符串 则改造成对象
        if (isString(word)) {
            word = {
                displayName: word,
                realName: word,
                jumpUrl: jumpUrl,
            }
        }
        if (!isEmpty(word.displayName)) {
            if (isDel) {
                handleDelStatus(false)
            }
            await model.setHistoryWord(word)

            if (jumpUrl || word.jumpUrl) {
                jumpTo({
                    to: 'web',
                    params: {
                        url: jumpUrl || word.jumpUrl
                    }
                })
            } else {
                jumpTo({
                    to: 'result',
                    params: {
                        displayName: encodeURIComponent(word.displayName),
                        realName: encodeURIComponent(word.realName),
                        extParams: JSON.stringify(params)
                    }
                })
            }
        }
    }

    // 词的单个删除操作
    const handleDelWord = async (word, index) => {
        const words = await model.deleteHistoryWord(word)
        if (isEmpty(words)) { // 历史搜索词没有的时候关闭删除状态
            handleDelStatus(false)
        }
        setHistoryWords(words)

        // 埋点
        const jsonParams = {
            pvid: pvId,
            logid: pvId,
            index,
            displayName: word?.displayName || word || MTA_NONE,
            realName: word?.realName || word || MTA_NONE,
            search_o2o_coordinates: MTA_NONE,
            search_fouraddrid: MTA_NONE,
        }
        IdleQueue.add(newMta, M_EVENTID.TravelSearchSingleDelete, M_PAGEID.TravelSearch, M_PAGE.SearchIndex, jsonParams)
    }

    // 设置二次确认弹窗状态
    const handleShowDialogState = (flag) => {
        setShowDialog(flag)
    }

    // 删除所有历史搜索
    const handleRemoveAll = () => {
        model.removeHistoryWords()
        setShowDialog(false)
        setHistoryWords([])
        handleDelStatus(false)
    }

    // 设置取消删除状态
    const hideDelStatus = () => {
        if (isDel) {
            handleDelStatus(false)
        }
    }

    // 词的点击
    const handleHistoryWordClick = (item, index) => {
        handleWordClick?.(item)
        const { displayName, realName } = item || {}
        // 上报埋点
        const jsonParams = {
            pvid:  pvId,
            logid: pvId,
            index,
            displayName: displayName || MTA_NONE,
            realName: realName || MTA_NONE,
            jumpLink: MTA_NONE,
            search_o2o_coordinates: MTA_NONE,
            search_fouraddrid: MTA_NONE,
        }
        IdleQueue.add(newMta, M_EVENTID.TravelSearchHistoryWord, M_PAGEID.TravelSearch, M_PAGE.SearchIndex, jsonParams)
    }

    return {
        isDel,
        showDialog,
        historyWords,
        handleDelStatus,
        handleWordClick,
        handleDelWord,
        handleRemoveAll,
        handleShowDialogState,
        hideDelStatus,
        handleHistoryWordClick,
        mtaData,
    }
}
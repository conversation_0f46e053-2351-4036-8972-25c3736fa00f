import localStorage from "@/utils/LocalStorage";

export default class HistoricalSearchModel {
    private mainDataKey = '_travelSearchHisWord'
    public handleVisible

    constructor(props) {
        // 初始化
        const { handleVisible } = props
        this.handleVisible = handleVisible
    }

    // 获取历史搜索词
    async getHistoryWords () {
        const historyWords = await localStorage.getItem(this.mainDataKey)
        return historyWords
    }

    // 设置历史搜索词
   async setHistoryWord (searchWord) {
        // 将搜索词存储到localStorage中
        let history = await localStorage.getItem(this.mainDataKey)
        if (history && history.length) {
            // 判断是否有当前搜索词的规则
            // 判断 realName是否存在，如果存在 找realName相同的，把这个数据放到第一位
            // 如果没有realName 就添加数据
            const index = history.findIndex((item) => {
                return item.realName === searchWord.realName
            })
            if (index !== -1) { // 有这个数据 就把该数据删掉
                history.splice(index, 1)
            }
            // 把该数据放到第一位
            history.unshift(searchWord)
        } else {
            history = [searchWord]
        }
        // 判断是否超过20条 超过就删除
        if (history.length > 20) {
            history.splice(20, history.length - 20)
        }
        await localStorage.setItem(this.mainDataKey, history)
        return history
    }

    // 删除单个历史搜索词
    async deleteHistoryWord (searchWord) {
        let history = await localStorage.getItem(this.mainDataKey)
        if (history && history.length) {
            const index = history.findIndex((item) => {
                return item.realName === searchWord.realName
            })
            if (index !== -1) { // 有这个数据 就把该数据删掉
                history.splice(index, 1)
            }
        }
        await localStorage.setItem(this.mainDataKey, history)
        return history
    }

    // 清空历史搜索词
    async removeHistoryWords () {
        await localStorage.removeItem(this.mainDataKey)
    }

}

.wrapper {
    display: flex;
    margin: 10px 16px 0 16px;
}

.titleContainer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.title {
    color: #0E0E0E;
    font-size: 16px;
    font-weight: 500 bold;
    margin-bottom: 12px;
}

.container {
    overflow: hidden;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: baseline;
    column-gap: 10px;
    row-gap: 8px;
}

.delIcon {
    width: 15px;
    height: 15px;
    margin-left: 4px;
}

.delContainer {
    display: flex;
    flex-direction: row;
}

.delText {
    color: #5E6880;
    font-size: 12px;
}

.delMargin {
    margin-right: 16px;
}

.dialogTitle {
    color: #000;
    font-size: 14px;
    text-align: center;
    font-weight: 500;
}

.dialogBtnContent {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 20px;
}

.commonBtn {
    border-radius: 6px;
    height: 35px;
    width: 125px;
}

.commonBtnText {
    text-align: center;
    line-height: 35px;
}

.cancelBtn {
    @extend .commonBtn;
    background-color: #fff;
    border-width: 1px;
    border-color: #013B94;

}

.cancelText {
    @extend .commonBtnText;
    color: #013B94;
}

.conformBtn {
    @extend .commonBtn;
    background-color: #013B94;
}

.conformText {
    @extend .commonBtnText;
    color: #fff;
}
import { View, Text, Image } from '@/BaseComponents/atoms'
import Dialog from '@/BaseComponents/Dialog'
import styles from './index.module.scss'
import { useHistoricalSearch } from './index.service'
import { isEmpty } from '@/utils/isType'
import LexicalChunks from '../LexicalChunks'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import React, { useImperativeHandle } from 'react'
import { getImageUrl } from '@/assets/imgs'
import classNames from 'classnames'

/**
 * 历史搜索
 * @param props
 * @returns
 */

interface Props {
    handleVisible: (word) => void,
}

const ImgConfig = {
    delete: getImageUrl('delete'), // 删除
}

const HistoricalSearch = React.forwardRef((props: Props, ref) => {
    const {
        isDel,
        historyWords,
        showDialog,
        handleDelStatus,
        handleWordClick,
        handleDelWord,
        handleRemoveAll,
        handleShowDialogState,
        hideDelStatus,
        handleHistoryWordClick,
        mtaData,
    } = useHistoricalSearch(props)

    useImperativeHandle(ref, () => {
        return {
            handleWordClick,
            hideDelStatus,
        }
    })

    if (isEmpty(historyWords)) {
        return null
    }


    return (
        <View className={styles.wrapper}>
            <View className={styles.titleContainer}>
                <Text className={classNames({
                    [styles.title]: true,
                    bold: true
                })}>历史搜索</Text>
                {
                    !isDel ?
                        <View onClick={handleDelStatus.bind(this, true)}>
                            <Image
                                className={styles.delIcon}
                                src={ImgConfig['delete']}
                                mode="scaleToFill"
                            />
                        </View> :
                        <View className={styles.delContainer}>
                            <View onClick={handleShowDialogState.bind(this, true)}>
                                <Text onClick={() => {}} className={`${styles.delText} ${styles.delMargin}`}>全部删除</Text>
                            </View>
                            <View onClick={handleDelStatus.bind(this, false)}>
                                <Text className={styles.delText}>完成</Text>
                            </View>
                        </View>
                }
            </View>
            <LexicalChunks
                data={historyWords}
                showIcon={isDel}
                handleClick={handleHistoryWordClick}
                handleIconClick={handleDelWord}
                mtaData={mtaData.current}
            />
            <Dialog
                show={showDialog}
                onClose={handleShowDialogState.bind(this, false)}
                dialogStyle={{ padding: pt(20)}}
            >
                <View>
                    <Text className={styles.dialogTitle}>确认删除全部历史记录？</Text>
                    <View className={styles.dialogBtnContent}>
                        <View
                            className={styles.cancelBtn}
                            onClick={handleShowDialogState.bind(this, false)}
                        >
                            <Text className={styles.cancelText}>取消</Text>
                        </View>
                        <View
                            className={styles.conformBtn}
                            onClick={handleRemoveAll}
                        >
                            <Text className={styles.conformText}>确认</Text>
                        </View>
                    </View>
                </View>
            </Dialog>
        </View>
    )
})

export default HistoricalSearch

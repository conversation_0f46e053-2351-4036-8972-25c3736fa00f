import {useDidShow} from '@tarojs/taro'
import {useCallback, useEffect, useRef, useState, useContext} from 'react'
import useFetch from '@/common/useFetch'
import {M_PAGE, M_PAGEID, mtaPv, M_EVENTID, newMta, mtaExposure, MTA_NONE} from '@/common/mta'
import {isWeb} from '@/common/common'
import {isEmpty} from '@/utils/isType'
import {reportInfo} from '@/common/reporter'
import {errorCodeConstantMapping, ErrortType} from '@/common/reporter/errorMapping'
import globalInfoModel from '@/store/model/globalInfo.model'
import addressModel from '@/store/model/address.model'
import IdleQueue from '@/utils/IdleQueue'
import {BasePageContext} from '@/common/withPage'

export function useIndexService() {
    const [data, setData] = useState({type: 'loading', resData: {}})
    const [searchWord, setSearchWord] = useState('')
    const historyRef = useRef(null)
    const recommendRef = useRef(null)
    const rankRef = useRef(null)
    const searchRef = useRef(null)
    const {apiFetch} = useFetch()
    const [sugListParams, setSugListParams] = useState<any>()
    const basePageContext = useContext(BasePageContext) // 页面数据
    const pvId = basePageContext?.basePageInfo?.pvId // 获取pvid
    const [isHide, setIsHide] = useState(false)

    useEffect(() => {
        getSugListParams()
    }, [])

    // 刷新接口
    useDidShow(() => {
        getData()
        mtaPv(M_PAGEID.TravelSearch, M_PAGE.SearchIndex)
    })

    if (isWeb) {
        try {
            document.getElementsByTagName('body')[0].style.overflow = 'hidden'
            document.getElementsByTagName('body')[0].style.overscrollBehavior = 'none'
        } catch (error) {

        }
    }

    const getSugListParams = useCallback(async () => {
        let addressInfo, dateInfo
        try {
            addressInfo = await addressModel.getAddress

        } catch (error) {
            addressInfo = error
        }
        // 获取地址信息
        const address = {
            latitude: (addressInfo?.address?.latitude)?.toString(),
            longitude: (addressInfo?.address?.longitude)?.toString(),
            posAreaId: addressInfo?.address?.posAreaId,
            virtualLocation: addressInfo?.address?.virtualLocation // 虚拟定位 1 是 0 否
        }
        try {
            dateInfo = await globalInfoModel.getHotelBaseInfo()
        } catch (error) {
            reportInfo({
                code: errorCodeConstantMapping?.TRYCATCHERROR_CATCH_CAPTURE_CODE_EXCEPTION,
                errorDetail: {
                    errorType: ErrortType.Info,
                    customMsg: {
                        errorDescription: '搜索sug页面获取需要的时间catch',
                        errorInfo: error?.message,
                        errorStack: error?.stack
                    }
                }
            })
        }
        const staticParams = globalInfoModel.staticParams

        setSugListParams({
            ...(address ?? {}),
            ...(dateInfo ?? {}),
            channelId: staticParams.channel,
            fromSource: 'horizontal',
            pvId: pvId
        })
    }, [])

    // 获取数据
    const getData = async () => {
        try {
            const [err, res] = await apiFetch('TRIP_SEARCH_MIDDLE', {}, true)
            if (err) {
                setData({type: 'error', resData: {}})
            } else {
                setData({type: 'success', resData: res.result || {}})
                if (isEmpty(res?.result?.hotRecommendVO)) {
                    reportInfo({
                        code: errorCodeConstantMapping?.LOADERROR_BUSINESS_IMMEDIATE_ATTENTION,
                        errorDetail: {
                            errorType: ErrortType.Info,
                            customMsg: {
                                compName: '热门推荐渲染异常'
                            }
                        }
                    })
                }
            }
        } catch (error) {
            setData({type: 'success', resData: {}})
        }
    }

    // 点击词的操作
    const handleWordClick = (word, params, jumpUrl) => {
        (historyRef.current as any)?.handleWordClick(word, params, jumpUrl)
        // 进行监控上报 historyRef.current 为undefined的时候
        if (isEmpty(historyRef.current)) {
            reportInfo({
                code: errorCodeConstantMapping?.INTERACTIVEERROR_NAVIGATION_ERROR_OPENAPP_ROUTER,
                errorDetail: {
                    errorType: ErrortType.Info,
                    customMsg: {
                        errorDescription: '点击词的时候不取不到historyRef.current'
                    }
                }
            })
        }
    }

    // suglist中搜索词的点击处理
    const handleWordClickBySug = (data) => {
        handleWordClick(data?.mainStr ?? '', {extMap: data?.extMap}, data?.jumpUrl ? decodeURIComponent(data.jumpUrl) : undefined)
    }

    // 处理是否展示热门推荐
    const handleVisible = (flag) => {
        (recommendRef.current as any)?.handleVisible(flag)
        // (rankRef.current as any)?.handleRankVisible(flag)
    }

    // 搜索条的change事件
    const onChange = (word) => {
        setSearchWord(word)
    }

    // 滚动
    const handleInputBlur = () => {
        searchRef.current?.handleInputBlur()
    }

    // 设置历史搜索的删除状态为正常状态
    const setHideDelStatus = () => {
        historyRef.current?.hideDelStatus()
    }

    const getTagList = (data) => {
        const keys = ['hotelSugRight', 'hotelSugTitleAfterV2', 'scenicSugTitleAfter']
        const sugTagListMap = data?.sugTagListMap || {}
        const result = <any>[]
        for (const key in sugTagListMap) {
            if (Object.prototype.hasOwnProperty.call(sugTagListMap, key)) {
                if (keys.includes(key)) {
                    const ele = sugTagListMap[key] || []
                    ele.forEach(item => {
                        result.push({
                            trackId: item?.trackId || MTA_NONE,
                            labelName: item?.listShowName || MTA_NONE
                        })
                    })
                }
            }
        }
        return result.length ? result : MTA_NONE
    }

    // 点击埋点
    const handleMtaClick = (data) => {
        const {sugItem, sugIndex, commonUserAction} = data
        const tagList = getTagList(sugItem)
        IdleQueue.add(newMta, M_EVENTID.TravelSearchAutoWord, M_PAGEID.TravelSearch, M_PAGE.SearchSug, {
            ...(commonUserAction ?? {}),
            index: sugIndex,// 位置
            keyword: sugItem?.displayName || MTA_NONE,
            displayName: sugItem?.mainStr || MTA_NONE,//展示词
            realName: sugItem?.mainStr || MTA_NONE,// 搜索暗纹
            search_o2o_coordinates: MTA_NONE, // 目标词经纬度
            search_fouraddrid: MTA_NONE, // 目标词四级地址
            sugType: sugItem?.sugType ? sugItem?.sugType * 1 : MTA_NONE,
            tagList,
            subSugType: sugItem?.sugItem || MTA_NONE,
            rankId: sugItem?.wareRankVO?.rankId || MTA_NONE
        })
    }

    // 曝光埋点
    const handleMtaExpo = (data) => {
        const {sugItem, sugIndex, commonUserAction} = data
        const tagList = getTagList(sugItem)
        IdleQueue.add(mtaExposure, M_EVENTID.TravelSearchAutoWordExpo, M_PAGEID.TravelSearch, M_PAGE.SearchSug, {
            ...(commonUserAction ?? {}),
            index: sugIndex,// 位置
            keyword: sugItem?.displayName || MTA_NONE,
            displayName: sugItem?.mainStr || MTA_NONE,//展示词
            realName: sugItem?.mainStr || MTA_NONE,// 搜索暗纹
            search_o2o_coordinates: MTA_NONE, // 目标词经纬度
            search_fouraddrid: MTA_NONE, // 目标词四级地址
            sugType: sugItem?.sugType ? sugItem?.sugType * 1 : MTA_NONE,
            tagList,
            subSugType: sugItem?.sugItem || MTA_NONE,
            rankId: sugItem?.wareRankVO?.rankId || MTA_NONE
        })
    }

    return {
        data,
        hotRecommend: (data.resData as any)?.hotRecommendVO,
        hotRankingList: (data.resData as any)?.hotRankingVOList,
        commonUserAction: (data.resData as any)?.commonUserAction,
        historyRef,
        recommendRef,
        handleWordClick,
        handleWordClickBySug,
        handleVisible,
        onChange,
        searchWord,
        searchRef,
        handleInputBlur,
        setHideDelStatus,
        sugListParams,
        handleMtaClick,
        handleMtaExpo,
        rankRef,
        isHide,
        setIsHide
    }
}

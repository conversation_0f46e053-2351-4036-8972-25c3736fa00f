import {View, Text} from '@/BaseComponents/atoms'
import React from 'react'
import {MapperComp} from '@/Components'
import Styles from './index.module.scss'
import { JDLoadingView } from '@jdreact/jdreact-core-lib';
import _ from 'lodash'

const getExpendedList = (list, expendedCount, expended)=> {
    if(typeof +expendedCount === 'number' && !expended) {
        return list.slice(0, expendedCount)
    }
    return list
}

function Group(props) {
    const {slot, actions, list, data, style = {}, expendCount, expended, loading, parents} = props
    if(loading) {
        return <View className={Styles.loading}><JDLoadingView /></View>
    }

    return (
        <View style={style}>
            {
                Array.isArray(list) ?
                    getExpendedList(list, expendCount, expended).map((item, index) => {
                        const {sameKey} = item
                        return MapperComp(slot, {
                            actions,
                            storage: {
                                dataItem: {
                                    ...item,
                                    expended: data && sameKey ? data[sameKey] : '',
                                    index,
                                    parents: _.omit(parents, ['items', 'metaData.items'])
                                }
                            }
                        })
                    }) :
                    <Text>request children list is not array</Text>
            }
        </View>
    )
}

export default Group

import {Text} from '@/BaseComponents/atoms'
import {InView} from '@/BaseComponents/IntersectionObserver'
import React from 'react'
import styles from './index.module.scss'
import {safeRun} from '@/Components/Filter/utils'
import EventCatch from '@/Components/EventCatch'

function SelectItem(props) {
    const {lineCount = 4, index, word, numberOfLines = 2, dataItem, onClick, mtaExpo} = props
    return (
        <InView className={styles.itemBox} style={{width: `${100 / lineCount}%`}} onChange={(visible)=> {
            mtaExpo({
                visible,
                props
            })
        }}>
            <Text className={styles.itemContent} numberOfLines={numberOfLines} ellipsizeMode="tail" onClick={() => {
                safeRun(onClick, dataItem)
            }}
                  style={{borderRight: typeof index === 'number' && (index + 1) % lineCount === 0 ? 'none' : ''}}>
                {word}
            </Text>
        </InView>
    )
}

export default EventCatch(SelectItem)

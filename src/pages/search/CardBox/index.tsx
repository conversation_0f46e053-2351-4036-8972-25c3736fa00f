import {View, Text, Image} from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import classNames from 'classnames'
import React from 'react'
import Scene from '@/Components/Scene.js'
import _ from 'lodash'

function CardBox(props) {
    const {title = '历史记录', rightAction, children, actions, ...otherProps} = props
    return (
        <View className={styles.cardBox}>
            <View className={styles.cardBoxContent}>
                <View className={styles.cardTitleBox}>
                    <Text className={classNames('blod', styles.cardBoxTitle)}>{title}</Text>
                    <Scene schema={rightAction} storage={otherProps} actions={actions}/>
                </View>
                <View className={styles.cardContentBox}>
                    <Scene schema={children} storage={otherProps} actions={actions}/>
                </View>
            </View>
        </View>
    )
}

export default CardBox

import withPage, {BasePageContext} from '@/common/withPage'
import React, {useContext, useEffect, useState} from 'react'
import {Map} from 'immutable'
import Scene from '@/Components/Scene.js'
import {extendsMapper} from '@/Components/index'
import schema from './defaultSchema.json'
import LRUCatch from '@/Components/utils/lru'
import localStorage from '@/utils/LocalStorage'
import {formatLocationData} from '@/Components/Filter/element/Location'
import {findIndexByKey} from '@/Components/Filter/utils'
import LexicalChunks from '@/pages/index/widgets/LexicalChunks'
import DelIcon from './DelIcon'
import Expend from './Expend'
import SearchBar from '@/pages/index/widgets/SearchBar'
import Card from './CardBox'
import Group from './Group'
import SelectItem from './SelectItem'
import Block from './Block'
import SugList from './SugList'
import Dialog, {dialog} from '@/pages/VResult/Dialog'

import {updateForStorageByObj, deCodeDebBase64ParseSafe, enCodeDebBase64ParseSafe} from '@/Components/utils/index'

import CatchAsyncData from '@/Components/CatchAsyncData'
import useFetch, {BIZ_VERSION, PLAT_FORM_CODE} from '@/common/useFetch'

import _ from 'lodash'
import NoData from '@/pages/result/widgets/NoData'
import IdleQueue from '@/utils/IdleQueue'
import {mtaEp, mtaPv, newMta} from '@/common/mta'
import {IOScrollView} from '@/BaseComponents/IntersectionObserver'
import useJumpTo from '@/common/useJumpTo'

extendsMapper({
    search: SearchBar,
    delIcon: DelIcon,
    expend: Expend,
    card: Card,
    group: Group,
    itemBtn: SelectItem,
    history: LexicalChunks,
    block: Block,
    sugList: SugList,
    dialog: Dialog,
    error: NoData
})

const genTreeList = (list) => {
    const rootNodeList = list.filter(item => item.level === 1)

    return rootNodeList.map(item => {
        const {childrenSameKey} = item
        item.items = list.filter(subItem => {
            return childrenSameKey.includes(subItem.sameKey)
        }).map(subItem => {
            const _subItem = {
                ..._.omit(subItem, ['parents']),
                'filterKey': 'location_distance',
                'filterType': 'gis_location',
                metaData: {
                    'geohash': _.get(subItem, ['metaData', 'geohash']),
                    'latitude': _.get(subItem, ['metaData', 'latitude']),
                    'longitude': _.get(subItem, ['metaData', 'longitude']),
                    'id': _.get(subItem, ['metaData', 'id']),
                    'title': _.get(subItem, ['metaData', 'title']),
                    'type': _.get(subItem, ['metaData', 'type']),
                    'value': _.get(subItem, ['metaData', 'value']),
                    'itemName': _.get(subItem, ['metaData', 'title']),
                    'filterType': 'gis_location',
                    'itemId': JSON.stringify({
                        'geohash': _.get(subItem, ['metaData', 'geohash']),
                        'geoHash': _.get(subItem, ['metaData', 'geoHash']),
                        'id': _.get(subItem, ['metaData', 'id']),
                        'category': _.get(subItem, ['metaData', 'type']),
                        'itemName': _.get(subItem, ['metaData', 'title'])
                    }),
                    'groupCode': -100
                }
            }

            const {childrenSameKey} = subItem
            _subItem.items = list.filter(subItem => {
                return childrenSameKey.includes(subItem.sameKey)
            }).map(child => {
                return {
                    ..._.omit(child, ['parents']),
                    'filterKey': 'location_distance',
                    'filterType': 'gis_location',
                    metaData: {
                        'geohash': _.get(child, ['metaData', 'geohash']),
                        'latitude': _.get(child, ['metaData', 'latitude']),
                        'longitude': _.get(child, ['metaData', 'longitude']),
                        'id': _.get(child, ['metaData', 'id']),
                        'title': _.get(child, ['metaData', 'title']),
                        'type': _.get(child, ['metaData', 'type']),
                        'value': _.get(child, ['metaData', 'value']),
                        'itemName': _.get(child, ['metaData', 'title']),
                        'filterType': 'gis_location',
                        'itemId': JSON.stringify({
                            'geohash': _.get(child, ['metaData', 'geohash']),
                            'geoHash': _.get(child, ['metaData', 'geoHash']),
                            'id': _.get(child, ['metaData', 'id']),
                            'category': _.get(child, ['metaData', 'type']),
                            'itemName': _.get(child, ['metaData', 'title'])
                        }),
                        'groupCode': -100
                    }
                }
            })

            return _subItem
        })
        return item
    })
}

const updateCatch = (data) => {
    const processId = `${_.get(data, ['travelInfo', 'processInstanceId'])}_${_.get(data, ['travelInfo', 'travelId'])}`

    return new Promise((resolve) => {
        localStorage.getItem('processCatch').then(processData => {
            const _LRUCatch = new LRUCatch({defaultCatch: Array.isArray(processData) ? processData : []})
            const urlInfo = {
                processId,
                searchInfo: data
            }
            const {list} = _LRUCatch.put(processId, urlInfo, 'processId')
            localStorage.setItem('processCatch', list)
            resolve()
        }).catch(() => {
            const _LRUCatch = new LRUCatch({defaultCatch: []})
            const urlInfo = {
                processId,
                searchInfo: data
            }
            const {list} = _LRUCatch.put(processId, urlInfo, 'processId')
            localStorage.setItem('processCatch', list)
            resolve()
        })
    })
}

const commonUserActionRef = {current: null}
const mddInfoRef = {current: null}

const Search = (props) => {
    const jumpTo = useJumpTo()
    const {searchHistory, sugInfo, searchInfo, commonData} = props
    const { queryMode, isFromVResult } = sugInfo 
    const {pageId, pageName} = schema.mta.params
    const {apiFetch} = useFetch()
    const basePageContext = useContext(BasePageContext) // 页面数据
    const [LRUInstance] = useState(new LRUCatch({
        defaultCatch: searchHistory
    }))
    const [storage, setStorage] = useState(Map({
        history: searchHistory,
        locationLoading: true,
        location: [],
        delFlag: false, //删除状态
        expendBox: {}, // 展开状态
        errorInfo: false,
        commonUserAction: {},
        sugParams: {
            ...sugInfo,
            pvId: _.get(basePageContext, ['basePageInfo', 'pvId']),
            bizVersion: BIZ_VERSION,
            platformCode: PLAT_FORM_CODE
        }
    }))

    useEffect(() => {
        IdleQueue.add(mtaPv, pageId, pageName, commonData)

        apiFetch('MID_LOCATION', {
            virtualLocation: 1,
            ..._.pick(sugInfo, ['fromSource', 'virtualLocation', 'channelId', 'queryMode', 'distance']),
            provinceCode: _.get(sugInfo, ['sugCityInfo', 'province']),
            cityCode: _.get(sugInfo, ['sugCityInfo', 'city']),
            districtCode: _.get(sugInfo, ['sugCityInfo', 'district']),
            townCode: _.get(sugInfo, ['sugCityInfo', 'town'])
        }, true).then(res => {
            const [code, resList] = res

            if (!code) {
                setStorage(updateForStorageByObj(storage, {
                    locationLoading: false,
                    location: genTreeList(formatLocationData(_.get(resList, 'result.filters', []))).reduce((pre, cur) => cur.maxDeep === 2 ? pre.concat(cur.items) : pre.concat(cur), []),
                    mddInfo: _.get(resList, ['result', 'mddInfo'], {}),
                    commonUserAction: _.get(resList, ['result', 'commonUserAction'], {})
                }))
                commonUserActionRef.current = _.get(resList, ['result', 'commonUserAction'], {})
                mddInfoRef.current = _.get(resList, ['result', 'mddInfo'], {})
            } else {
                setStorage(updateForStorageByObj(storage, {
                    errorInfo: searchHistory.length === 0,
                    locationLoading: false
                }))
            }
        })
    }, [])

    const updateData = (target) => {
        const {displayName} = target
        const {list, info} = LRUInstance.put(displayName, target, 'displayName')

        // 更新缓存
        localStorage.setItem(sugInfo.historyKey, list)

        return {list, info}
    }

    const jump = (searchData, enCodeOBj, target) => {
        if (!isFromVResult && !!queryMode && (queryMode === '1' || queryMode === '2')) {
            const poiName = _.get(target, ['metaData', 'title'])
            const poiLat = _.get(target, ['metaData', 'latitude'])
            const poiLng = _.get(target, ['metaData', 'longitude'])
            const url = decodeURIComponent(`https://hotel.m.jd.com/share?poiName=${poiName}&poiLat=${poiLat}&poiLng=${poiLng}`)
            window.location.href = url
        } else {
            updateCatchWithReplace(searchData, enCodeOBj)
        }
    }

    const updateCatchWithReplace = (searchData, enCodeOBj) => {
        updateCatch(searchData).then(() => {
            // 补丁: 防止差旅单进垂搜直接读缓存导致各种信息错乱
            enCodeOBj.isFromMidSearch = true
            window.location.replace(`/search/vresult?hotelSearchData=${enCodeDebBase64ParseSafe(enCodeOBj)}`)
        })
    }

    const goBackAction = (searchInfo) => {
        if (!isFromVResult && !!queryMode && (queryMode === '1' || queryMode === '2')) {
            window.history.back(); 
        } else {
            window.location.replace(`/search/vresult?hotelSearchData=${enCodeDebBase64ParseSafe(searchInfo)}`)
        }
    }

    return (
        <IOScrollView>
            <Scene schema={schema.schema} storage={storage} actions={{
                setDeepMerge: (value) => {
                    const {delFlag} = value
                    if (delFlag) {
                        IdleQueue.add(newMta, 'HotelTravel_Search_Clear', pageId, pageName, {...commonData, ...storage.getIn(['commonUserAction'])})
                    }

                    setStorage(updateForStorageByObj(storage, value))
                },
                onClickFilter: (target) => {
                    let mddInfo = storage.getIn(['mddInfo'])
                    // 拼房
                    let keyword = ''
                    if (!!queryMode && (queryMode === '1' || queryMode === '2')) {
                        mddInfo.type = '1'
                        mddInfo.latitude = _.get(target, ['metaData', 'latitude'],'')
                        mddInfo.longitude = _.get(target, ['metaData', 'longitude'],'')
                        keyword = _.get(target, ['metaData', 'itemName'],'')
                    }

                    const filterMetaData = [target]

                    IdleQueue.add(mtaEp, 'HotelTravel_Search_Filter', pageId, pageName, {
                        ...commonData, ...storage.getIn(['commonUserAction']),
                        itemId: _.get(target, ['dataItem', 'metaData', 'itemId'], -100),
                        itemName: _.get(target, ['dataItem', 'metaData', 'itemName'], -100),
                        itemValue: _.get(target, ['dataItem', 'metaData', 'value'], -100),
                        filterId: _.get(target, ['dataItem', 'filterKey'], -100),
                        filterType: _.get(target, ['dataItem', 'filterType'], -100)
                    })

                    // 组装新的搜索条件
                    const newSearchInfo = {
                        ...searchInfo,
                        mddInfo,
                        keyword: keyword,
                        filterMetaData
                    }

                    // filterMetaData 可能过长导致参数丢失, 所以用一个新的对象enCode
                    const enCodeOBj = {
                        ...searchInfo,
                        mddInfo,
                        keyword: keyword
                    }


                    // 操作历史记录
                    updateData({
                        displayName: _.get(target, ['metaData', 'title']),
                        searchInfo: newSearchInfo,
                        filterMetaData: filterMetaData
                    })

                    jump(newSearchInfo, enCodeOBj, target)
                },
                onClick: (target) => {
                    const {userAction} = target
                    const mddInfo = _.get(mddInfoRef, ['current'], {})

                    IdleQueue.add(newMta, 'HotelTravel_Search_AutoWord', pageId, pageName, {
                        ...commonData,
                        ...commonUserActionRef.current,
                        ...userAction,
                        hotelId: _.get(target, ['sugHotelVO', 'hotelId'], -100),
                        ..._.get(target, ['userAction'], {}),
                        tagList: Array.isArray(_.get(target, ['subTitle'], [])) && _.get(target, ['subTitle'], []).length > 0 ? _.get(target, ['subTitle'], []).map(item => {
                            return {
                                labelName: _.get(item, ['text'], -100)
                            }
                        }) : -100,
                        search_o2o_coordinates: _.get(props, ['sugMddInfo', 'latitude']) ? `${_.get(props, ['sugMddInfo', 'latitude'])},${_.get(props, ['sugMddInfo', 'longitude'])}` : -100
                    })

                    // 拼房条件下不传extMap
                    const extMap =  ['1', '2'].includes(queryMode) ? false : _.get(target, 'extMap', false);
                    // 组装新的搜索条件
                    const newSearchInfo = {
                        ...searchInfo,
                        keyword: _.get(target, ['mainStr'], ''),
                        mddInfo: Object.assign(mddInfo, _.get(target, ['sugMddInfo'], {})),
                        extMap: extMap,
                        filterList: []
                    }

                    // 操作历史记录
                    updateData({
                        displayName: _.get(target, ['mainStr'], ''),
                        searchInfo: newSearchInfo
                    })

                    const middleInfoTarget = {
                        metaData: {
                            title: target?.displayName,
                            latitude: target?.poiInfoVO?.latitude,
                            longitude: target?.poiInfoVO?.longitude
                        }
                    }
                    jump(newSearchInfo, newSearchInfo, middleInfoTarget)
                },
                onHistoryClick: (target) => {
                    const {displayName} = target
                    localStorage.getItem(sugInfo.historyKey).then((list) => {
                        if (!Array.isArray(list)) return
                        const index = findIndexByKey(list, target, 'displayName')
                        IdleQueue.add(newMta, 'HotelTravel_Search_History', pageId, pageName, {
                            ...commonData,
                            ...storage.getIn(['commonUserAction']),
                            historyword: displayName,
                            index
                        })
                        let historySearchInfo = _.get(list, [index, 'searchInfo'])

                        historySearchInfo = {
                            ...historySearchInfo,
                            hotelBaseSearchParam: { ...searchInfo.hotelBaseSearchParam }
                        }
                        const clickTarget = list[index]

                        updateData(clickTarget)

                        /*
                            title: _.get(clickTarget, 'displayName'),
                            latitude: _.get(clickTarget, 'filterMetaData[0].metaData.latitude'),
                            longitude: _.get(clickTarget, 'filterMetaData[0].metaData.longitude')
                        */

                        const jumpTarget = {
                            metaData: {
                            title: _.get(historySearchInfo, 'keyword'),
                            latitude: _.get(historySearchInfo, 'mddInfo.latitude'),
                            longitude: _.get(historySearchInfo, 'mddInfo.longitude')
                            }
                        }
                        jump(historySearchInfo, historySearchInfo, jumpTarget)
                    })
                },
                delHistory: (target) => {
                    localStorage.getItem(sugInfo.historyKey).then(list => {
                        if (!Array.isArray(list)) return
                        const index = findIndexByKey(list, target, 'displayName')
                        const newList = list.slice(0, index).concat(list.slice(index + 1, list.length))
                        localStorage.setItem(sugInfo.historyKey, newList)
                        LRUInstance.set(newList)
                        // 刷新历史记录
                        setStorage(updateForStorageByObj(storage, {
                            history: newList,
                            delFlag: true
                        }))
                    }).catch((error) =>{
                        console.log(error)
                    })
                },
                delAll: () => {
                    dialog({
                        title: '确认删除全部历史记录？',
                        onOk: () => {
                            const newList = []
                            localStorage.setItem(sugInfo.historyKey, newList)
                            LRUInstance.set([])
                            // 刷新历史记录
                            setStorage(updateForStorageByObj(storage, {
                                history: newList,
                                delFlag: false
                            }))
                        }
                    })
                },
                onSearch: (info) => {
                    const displayName = info.displayName === '' ? storage.getIn([
                        'sugParams',
                        'keyword'
                    ]) : info.displayName

                    const mddInfo = storage.getIn(['mddInfo'])

                    // 组装新的搜索条件
                    const newSearchInfo = {
                        ...searchInfo,
                        mddInfo,
                        keyword: displayName,
                        filterList: [],
                        filterMetaData: []
                    }
                    // 组装元数据

                    if (displayName && displayName !== '') {
                        // 操作历史记录
                        updateData({
                            displayName: displayName,
                            searchInfo: newSearchInfo,
                            filterMetaData: []
                        })
                    }

                    IdleQueue.add(newMta, 'HotelTravel_Search_Search', pageId, pageName, {
                        ...commonData,
                        ...storage.getIn(['commonUserAction']),
                        keyword: displayName
                    })
                    // 跳转条件选择
                    updateCatchWithReplace(newSearchInfo, newSearchInfo)
                },
                historyExpo: ({visible, item, index}) => {
                    if (visible) {
                        const {displayName} = item
                        IdleQueue.add(mtaEp, 'HotelTravel_Search_HistoryExpo', pageId, pageName, {
                            ...commonData,
                            ...storage.getIn(['commonUserAction']),
                            historyword: displayName,
                            index
                        })
                    }
                },
                sugExpo: (sugItem) => {
                    const {commonUserAction, sugIndex} = sugItem
                    IdleQueue.add(mtaEp, 'HotelTravel_Search_AutoWordExpo', pageId, pageName, {
                        ...commonData,
                        ...commonUserAction,
                        index: sugIndex,
                        hotelId: _.get(sugItem, ['sugItem', 'sugHotelVO', 'hotelId'], -100),
                        ..._.get(sugItem, ['sugItem', 'userAction'], {}),
                        tagList: Array.isArray(_.get(sugItem, ['sugItem', 'subTitle'], [])) && _.get(sugItem, ['sugItem', 'subTitle'], []).length > 0 ? _.get(sugItem, ['sugItem', 'subTitle'], []).map(item => {
                            return {
                                labelName: _.get(item, ['text'], -100)
                            }
                        }) : -100,
                        search_o2o_coordinates: _.get(props, ['sugItem', 'sugMddInfo', 'latitude']) ? `${_.get(props, ['sugItem', 'sugMddInfo', 'latitude'])},_.get(props, ['sugItem', 'sugMddInfo', 'longitude'])` : -100
                    })
                },
                filterExpo: (itemInfo) => {
                    const {visible, props} = itemInfo
                    if (visible) {
                        IdleQueue.add(mtaEp, 'HotelTravel_Search_FilterExpo', pageId, pageName, {
                            ...commonData, ...storage.getIn(['commonUserAction']),
                            itemId: _.get(props, ['dataItem', 'metaData', 'itemId'], -100),
                            itemName: _.get(props, ['dataItem', 'metaData', 'itemName'], -100),
                            itemValue: _.get(props, ['dataItem', 'metaData', 'value'], -100),
                            filterId: _.get(props, ['dataItem', 'filterKey'], -100),
                            filterType: _.get(props, ['dataItem', 'filterType'], -100)
                        })
                    }
                },
                goBack: () => {
                    goBackAction(searchInfo)
                }
            }}/>
        </IOScrollView>
    )
}

Search.displayName = 'search'

Search.preLoad = (props) => new Promise((resolve, reject) => {
    const searchInfo = deCodeDebBase64ParseSafe(_.get(props, ['urlParams', 'searchInfo']))
    const isTravel = typeof _.get(searchInfo, ['travelInfo', 'processInstanceId']) === 'string' && typeof _.get(searchInfo, ['travelInfo', 'travelId']) === 'string'
    const queryMode = _.get(searchInfo, ['queryMode'])
    const distance = _.get(searchInfo, ['distance'])
    const isFromVResult = _.get(searchInfo, ['isFromVResult'])

    const defaultInfo = _.pick(searchInfo, ['keyword', 'channelId', 'priceChannel', 'fromSource', 'virtualLocation', 'latitude', 'longitude', 'posAreaId'])
    const {mddInfo, hotelBaseSearchParam = {}} = searchInfo
    const travelId = _.get(searchInfo, ['travelInfo', 'travelId'])
    const processInstanceId = _.get(searchInfo, ['travelInfo', 'processInstanceId'])

    const sugInfo = {
        ...defaultInfo,
        ..._.pick(hotelBaseSearchParam, ['checkInDate', 'checkOutDate']),
        sugCityInfo: _.pick(mddInfo, ['type', 'showName', 'latitude', 'longitude', 'province', 'city', 'county', 'street', 'level']),
        historyKey: 'searchHistory',
        isTravel,
        queryMode,
        distance,
        isFromVResult
    }

    if (travelId && processInstanceId) {
        let historyKey = `searchHistory-${travelId}-${processInstanceId}`
        // 差旅拼房历史词区分
        if (!isFromVResult && (queryMode === '1' || queryMode === '2')) {
            historyKey = `searchHistory-roomShare-${travelId}-${processInstanceId}`
        }
        sugInfo.historyKey = historyKey
    }

    const commonData = {
        channel: _.get(searchInfo, ['channelId'], -100),
        o2o_coordinates: `${_.get(searchInfo, ['latitude'], -100)},${_.get(searchInfo, ['longitude'], -100)}`,
        fouraddrid: _.get(searchInfo, ['posAreaId'], -100),
        fromSource: _.get(searchInfo, ['fromSource'], -100),
        offerChannel: _.get(searchInfo, ['priceChannel'], -100)
    }

    localStorage.getItem(sugInfo.historyKey).then(res => {
        resolve({
            searchHistory: Array.isArray(res) ? res : [],
            sugInfo,
            searchInfo,
            commonData
        })
    }).catch(() => reject({
        searchHistory: [],
        sugInfo,
        searchInfo,
        commonData
    }))
})

export default withPage({pageName: 'search'})(CatchAsyncData(Search))

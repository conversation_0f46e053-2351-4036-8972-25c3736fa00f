{"defaultParams": {}, "mta": {"params": {"pageId": "HotelTravel_Search", "pageName": "差旅酒店搜索中间页"}}, "schema": [{"type": "search", "props": {"eventTypes": {"onChange": {"keys": ["setDeepMerge"], "format": [{"setToObj": ["sugParams", "keyword"]}]}, "handleWordClick": {"keys": ["onSearch"]}, "onCancel": {"keys": ["goBack"]}}, "hasCancel": true}, "propsPaths": {"keyword": ["storage", "sugParams", "keyword"], "isTravel": ["storage", "sugParams", "isTravel"], "queryMode": ["storage", "sugParams", "queryMode"]}}, {"type": "error", "joint": [{"keys": ["errorInfo"], "value": true}], "props": {"style": {"paddingTop": 80}, "text": "请输入关键词搜索"}}, {"type": "sugList", "joint": [{"keys": ["sugParams", "keyword"], "valueType": "string"}], "props": {"sourceType": "HOTEL_SEARCH", "eventTypes": {"handleWordClick": {"keys": ["onClick"]}, "handleMtaExpo": {"keys": ["sugExpo"]}}}, "propsPaths": {"params": ["storage", "sugParams"]}}, {"type": "card", "joint": [{"keys": ["history", "length"], "symbol": "unequal", "value": 0, "joint": [{"keys": ["sugParams", "keyword"], "valueType": "string", "symbol": "unequal"}]}], "props": {"rightAction": [{"type": "delIcon", "props": {"eventTypes": {"onChange": {"keys": ["setDeepMerge"], "format": [{"setToObj": ["delFlag"]}]}, "delAll": {"keys": ["delAll"]}}}, "propsPaths": {"delFlag": ["storage", "delFlag"]}}], "children": [{"type": "history", "props": {"eventTypes": {"handleClick": {"keys": ["onHistoryClick"]}, "handleIconClick": {"keys": ["delHistory"]}, "mtaExpo": {"keys": ["historyExpo"]}}}, "propsPaths": {"data": ["storage", "data"], "showIcon": ["storage", "showIcon"]}}]}, "propsPaths": {"delFlag": ["storage", "delFlag"], "showIcon": ["storage", "delFlag"], "data": ["storage", "history"]}}, {"type": "group", "joint": [{"keys": ["sugParams", "keyword"], "valueType": "string", "symbol": "unequal", "joint": [{"keys": ["delFlag"], "value": true, "symbol": "unequal"}]}], "props": {"slot": {"type": "card", "props": {"rightAction": [{"type": "expend", "joint": [{"keys": ["data", "items", "length"], "symbol": ">", "value": 8}], "props": {"eventTypes": {"onChange": {"keys": ["setDeepMerge"], "format": [{"setToObj": "expendBox"}]}}}, "propsPaths": {"type": ["storage", "data", "<PERSON><PERSON><PERSON>"], "expended": ["storage", "data", "expended"], "list": ["storage", "data", "items"]}}], "children": [{"type": "group", "props": {"expendCount": 8, "style": {"flexDirection": "row", "flexWrap": "wrap"}, "slot": {"type": "itemBtn", "props": {"eventTypes": {"onClick": {"keys": ["onClickFilter"]}, "mtaExpo": {"keys": ["filterExpo"]}}}, "propsPaths": {"index": ["storage", "dataItem", "index"], "word": ["storage", "dataItem", "metaData", "title"], "dataItem": ["storage", "dataItem"]}}}, "propsPaths": {"list": ["storage", "data", "items"], "data": ["storage", "expendBox"], "expended": ["storage", "data", "expended"], "parents": ["storage", "data"]}}]}, "propsPaths": {"data": ["storage", "dataItem"], "title": ["storage", "dataItem", "metaData", "title"]}}}, "propsPaths": {"list": ["storage", "location"], "data": ["storage", "expendBox"], "loading": ["storage", "locationLoading"], "delFlag": ["storage", "delFlag"]}}, {"type": "block", "joint": [{"keys": ["sugParams", "keyword"], "valueType": "string", "symbol": "unequal", "joint": [{"keys": ["locationLoading"], "value": false, "joint": [{"keys": ["delFlag"], "value": true, "symbol": "unequal", "joint": [{"keys": ["location", "length"], "symbol": "unequal", "value": 0}]}]}]}], "props": {"word": "没有更多数据了", "style": {"padding": "40px", "textAlign": "center", "fontSize": "14px", "color": "#5E6880"}}}, {"type": "dialog"}]}
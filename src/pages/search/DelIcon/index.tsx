import {View, Text, Image} from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import React from 'react'
import {getImageUrl} from '@/assets/imgs'
import EventCatch from '@/Components/EventCatch'

const ImgConfig = {
    delete: getImageUrl('delete') // 删除
}

const DelIcon = (props) => {
    const {delFlag, onChange, delAll} = props

    return <View>
        {
            !delFlag ?
                <View onClick={() => onChange(true)}>
                    <Image
                        className={styles.delIcon}
                        src={ImgConfig['delete']}
                        mode="scaleToFill"
                    />
                </View> :
                <View className={styles.delContainer}>
                    <View>
                        <Text onClick={delAll} className={styles.delText}>全部删除</Text>
                    </View>
                    <View onClick={() => onChange(false)}>
                        <Text className={styles.delText}>完成</Text>
                    </View>
                </View>
        }
    </View>
}

export default EventCatch(DelIcon)

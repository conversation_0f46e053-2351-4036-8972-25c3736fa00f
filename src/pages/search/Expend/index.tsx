import {View, Text, Image} from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import React from 'react'
import classNames from 'classnames'
import EventCatch from '@/Components/EventCatch'

const Expend = (props) => {
    const {expended, onChange, type} = props

    return <View onClick={() => {
        onChange({
            [type]: !expended
        })
    }}>
        <View className={styles.iconWordBox}>
            <Text className={styles.expendedBox}>
                {expended ? '收起' : '展开'}</Text>
            <Image
                className={classNames(styles.expendedIcon, expended ? styles.expendedUp : styles.expendedDown)}
                src={'https://img14.360buyimg.com/imagetools/jfs/t1/135893/18/49718/411/67164594F901f961b/03a5baab453a73c4.png'}/>
        </View>
    </View>
}

export default EventCatch(Expend)

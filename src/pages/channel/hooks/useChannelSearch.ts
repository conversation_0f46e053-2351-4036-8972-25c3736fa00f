import {
  useState,
  useCallback,
  useEffect,
  useRef
} from 'react'

import {
  fetchChannelList
} from '../services/api'

import { MTAPlaceHolderString, LoadMoreStatus, PAGE_SIZE, SortType } from '../../hotelSearch/constants/config'

import useFetch from '@/common/useFetch'

import globalInfoModel from '@/store/model/globalInfo.model'

import {
  isEmpty
} from '@/utils/isType'

import {
  deCodeDebBase64ParseSafe,
  enCodeDebBase64ParseSafe
} from '@/Components/utils'

import {
  isObjectEqual
} from '../../hotelSearch/utils/utils'
import defaultRequestParams from '../../hotelSearch/services/defaultRequestParams'

import useJumpTo from "@/common/useJumpTo";
import { sendMtaEvent } from '../utils/mtaUtils'
import { mtaPv } from '@/common/mta'
import { M_PAGEID, M_PAGE } from '@/common/mta'
import { reportInfo } from '@/common/reporter'
import { errorCodeConstantMapping, ErrortType } from '@/common/reporter/errorMapping'
import { buildHotelCardEventData } from './assembleData'

import { glabelFilterType, priceStarSubFilterType, locationSubFilterType } from '../../hotelSearch/constants/filterType'
import { CardType } from '../constants/type'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'

import { isWeb } from '@/common/common'
import { getChannelSearchData, saveChannelSearchData } from '../../hotelSearch/utils/shareDataUtils'

import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween';
import { ChANNEL_EVENT_ID } from '../constants/mtaParamEvents'
import { encrypt } from '@ltfe/ltfe-core-lib/lib/utiles'

// 扩展 isBetween 插件
dayjs.extend(isBetween);


const searchPath = '/search/channel?jdreactkey=JDReactLifeTravelSearch&jdreactapp=JDReactLifeTravelSearch&transparentenable=true&initPath=pages/channel/index&hotelSearchData='

export const useChannelSearch = () => {
  // API 请求工具
  const { apiFetch } = useFetch()
  // 跳转工具
  const jumpTo = useJumpTo()
  // Refs
  // 当前请求页码
  const currentPageRef = useRef(1)
  // 第一次初始化发起的请求
  const firstLoad = useRef(true)

  const timer = useRef(null as any)
  // 加载状态
  const [loading, setLoading] = useState(true)
  const [loadMoreStatus, setLoadMoreStatus] = useState<LoadMoreStatus>(LoadMoreStatus.IDLE)
  const [hasMore, setHasMore] = useState(true)

  // url获取得到的额外参数
  const extraParamsRef = useRef<any>({})

  const [searchParams, setSearchParams] = useState<any>({})
  // 创建一个ref来跟踪最新的searchParams
  const searchParamsRef = useRef(searchParams);
  // 拆分filter所有筛选条件类型
  const [combiedFliter, setCombiedFliter] = useState({
    location_distance: <any>[],
    price_star: <any>[]
  })
  const combiedFliterRef = useRef(combiedFliter);
  // 给选择使用
  const [filterMap, setFilterMap] = useState(new Set<string>())

  // 数据列表状态
  // 记录自然流量数据
  const [_, setRawList] = useState<any[]>([])
  // 整体列表数据
  const [list, setList] = useState<any[]>([])
  // 筛选面板数据
  const [filterPanelVOList, setFilterPanelVOList] = useState([])
  // 添加commonUserAction状态
  const [commonUserAction, setCommonUserAction] = useState<any>({})
  const commonUserActionRef = useRef(commonUserAction);

  // 筛选状态 - 拆分成三个state
  const [activeFilter, setActiveFilter] = useState<SortType | null>(null)
  // 是否展示住离日期
  const [showDatePop, setShowDatePop] = useState(false)
  const [baibuHotelCalendarRange, setBaibuHotelCalendarRange] = useState<any>({})
  const [baibuPriceConfigVO, setBaibuPriceConfigVO] = useState<any>({})
  const [baibuHeadConfig, setBaibuHeadConfig] = useState<any>({})

  // iFrame
  const [webViewUrl, setWebViewUrl] = useState('')
  const [webViewVisible, setWebViewVisible] = useState(false)

  const noDataPlaceHolderHeight = pt(358)
  const cardHeight = pt(179)
  const toleranceHeight = pt(60)

  // 获取四级地址
  const getAddress = () => {
    const currentestSearchParams = searchParamsRef.current || {}
    // 拿到mddInfo
    const { mddInfo = {} } = currentestSearchParams || {}  // 从mddInfo中拿字段
    const { province = '', city = '', county = '', street = '' } = mddInfo || {}
    return `${province},${city},${county},${street}`
  }

  // 上报PV埋点
  const reportPV = () => {
    const requestParams = searchParamsRef.current;
    // 获取埋点参数
    const pvParams = {
      channel: requestParams?.channelId || MTAPlaceHolderString,
      search_o2o_coordinates: `${requestParams?.longitude || MTAPlaceHolderString},${requestParams?.latitude || MTAPlaceHolderString}`,
      fouraddrid: requestParams?.posAreaId || MTAPlaceHolderString,
      checkOutDate: requestParams?.hotelBaseSearchParam?.checkOutDate || MTAPlaceHolderString,
      checkInDate: requestParams?.hotelBaseSearchParam?.checkInDate || MTAPlaceHolderString,
      cityCode: requestParams?.mddInfo?.city ? String(requestParams?.mddInfo?.city) : MTAPlaceHolderString
    }
    // 发送PV埋点
    mtaPv(M_PAGEID.Channel, M_PAGE.Channel, pvParams);
  }

  const getCommonParams = () => {
    return commonUserActionRef.current;
  }

  // 删除不需要的参数
  const removeUnnecessaryParams = (params: any) => {
    if (!params) return params;

    const fieldsToRemove = [
      'lbs_city',
      'extMap',
      'filterType',
      'orderType',
      'filterName'
    ];

    // 删除其他不需要的字段
    fieldsToRemove.forEach(field => {
      if (field in params) {
        delete params[field];
      }
    });
    return params;
  };

  const refFetchNo = useRef(0)

  const debounceFetchList = (isLoadMore = false, initParams?: any) => {
    if (!isLoadMore) {
      setLoading(true);
    } else {
      setLoadMoreStatus(LoadMoreStatus.LOADING);
    }
    if (timer.current) {
      clearTimeout(timer.current)
    }
    timer.current = setTimeout(() => {
      fetchList(isLoadMore, initParams)
    }, 100)
  }

  // 发起网络请求
  const fetchList = async (isLoadMore = false, initParams?: any) => {
    const currentPage = isLoadMore ? currentPageRef.current : 1;
    // 防止短时间内多次调用
    try {
      // 构建请求参数
      let requestParams
      if (initParams) {
        requestParams = initParams
      } else {
        requestParams = {
          ...extraParamsRef.current,
          ...searchParamsRef.current,
          filterList: getFiltersByType(glabelFilterType.all),
          page: currentPage,
          pageSize: PAGE_SIZE,
        };
      }

      // 删除不需要的参数
      removeUnnecessaryParams(requestParams);
      if (isWeb && currentPage === 1) {
        const replaceUrl = `${searchPath}${enCodeDebBase64ParseSafe(requestParams)}`;
        history.replaceState(null, '', replaceUrl);
      }

      saveChannelSearchData(requestParams?.mddInfo)

      refFetchNo.current++;
      const fetchNo = refFetchNo.current

      const response = await fetchChannelList(
        apiFetch,
        requestParams
      )
      if (fetchNo < refFetchNo.current) {
        return;
      }

      if (response?.code !== '0') {
        resportRequestError({
          errorDescription: '获取频道数据异常',
          errorInfo: response || {},
          requestParam: requestParams || {},
          initParams: initParams,
          currentPage: currentPage
        })
      }
      // 调用抽离出的方法处理响应数据
      processResponseData(response, currentPage, isLoadMore);
    } catch (error) {
      processResponseData({}, currentPage, isLoadMore);
      if (!isLoadMore) {
        setLoadMoreStatus(LoadMoreStatus.IDLE);
      } else {
        setLoadMoreStatus(LoadMoreStatus.ERROR);
      }
      resportRequestError({
        errorDescription: '获取频道数据异常 try/catch',
        errorInfo: error,
        initParams: initParams
      })
    } finally {
      setLoading(false);
      firstLoad.current = false;
    }
  }

  // 处理响应数据并更新状态
  const processResponseData = (response, currentPage, isLoadMore) => {
    const {
      naturalCardVOList = [], // 自然流量
      hasNextPage = false,
      baibuHeadConfig = {},
      filterPanelVOList = [],
      baibuHotelCalendarRange = {},
      commonUserAction = {}, // 从响应中获取commonUserAction
      baibuPriceConfigVO = {}, // 额外的样式数据
      baibuDateShowConfig = {}, // 返回的样式日期
      countSummaryVO = {}
    } = response?.result || {};

    // 更新commonUserAction状态
    if (!isEmpty(commonUserAction)) {
      commonUserActionRef.current = commonUserAction
      setCommonUserAction(commonUserAction);
    }

    if (currentPage === 1) {
      setBaibuHeadConfig(baibuHeadConfig)
      setBaibuHotelCalendarRange(baibuHotelCalendarRange);
      setBaibuPriceConfigVO(baibuPriceConfigVO);
      setFilterPanelVOList(filterPanelVOList);
      // 根据后端返回重置住离日期
      resetCheckInOutDate(baibuDateShowConfig);
    }

    // 处理每个酒店卡片的埋点数据
    if (!isEmpty(naturalCardVOList)) {
      naturalCardVOList.forEach((item, index) => {
        // 使用抽取的方法构建埋点数据
        const eventData = buildHotelCardEventData(item, index);
        if (eventData) {
          item.eventData = eventData;
        }
      });
    }

    // 1. 无数据
    if (currentPage === 1 && response?.code !== '0') {
      setList([{ cardType: CardType.Error, showTopCorner: true }, { cardType: CardType.PlaceHolder, height: noDataPlaceHolderHeight }]);
      setHasMore(false);
      setLoadMoreStatus(LoadMoreStatus.IDLE);
      return;
    }

    setRawList(prevRawList => {
      const updatedRawList = isLoadMore ? [...prevRawList, ...naturalCardVOList] : naturalCardVOList;
      // 使用更新后的列表构建显示列表
      const showList = <any>[];

      showList.push({ cardType: CardType.FilterBar });
      if (currentPage === 1 && isEmpty(updatedRawList)) {
        setList([{ cardType: CardType.FilterBar }, { cardType: CardType.NoData, countSummaryVO: countSummaryVO }, { cardType: CardType.PlaceHolder, height: noDataPlaceHolderHeight }]);
        setHasMore(false);
        setLoadMoreStatus(LoadMoreStatus.IDLE);
        return
      }

      if (!isEmpty(updatedRawList)) {
        showList.push(...updatedRawList);
        // 需要补位高度
        if (currentPage === 1 && updatedRawList?.length <= 3 && hasNextPage === false) {
          // fix: 底部容错
          const height = (4 - updatedRawList?.length) * cardHeight - toleranceHeight
          showList.push({ cardType: CardType.PlaceHolder, height: height })
          setList(showList);
          setHasMore(false);
          setLoadMoreStatus(LoadMoreStatus.NO_MORE);
          return
        }
      }

      setList(showList);

      let hasMore = hasNextPage;
      if (!isEmpty(updatedRawList)) {
        if (!isLoadMore) {
          if (hasMore) {
            setLoadMoreStatus(LoadMoreStatus.IDLE);
          } else {
            setLoadMoreStatus(LoadMoreStatus.NO_MORE);
          }
        } else {
          if (response?.code !== '0') {
            // 失败场景可以重试, 所以hasMore设置为true
            hasMore = true;
            setLoadMoreStatus(LoadMoreStatus.ERROR);
          } else {
            if (hasMore) {
              setLoadMoreStatus(LoadMoreStatus.IDLE);
            } else {
              setLoadMoreStatus(LoadMoreStatus.NO_MORE);
            }
          }
        }
      }

      setHasMore(hasMore);
      // 失败场景下hasMore设置为true, 但是currentPageRef不能累加
      if (hasNextPage) {
        currentPageRef.current = currentPage + 1;
      }
      return updatedRawList;
    });
  }

  const resetCheckInOutDate = (config) => {
    const { checkInDate, checkOutDate } = config || {}
    if (isEmpty(checkInDate) || isEmpty(checkOutDate)) {
      return
    }
    const currentestSearchParams = searchParamsRef.current || {}
    const { hotelBaseSearchParam = {} } = currentestSearchParams || {}
    searchParamsRef.current = {
      ...currentestSearchParams,
      hotelBaseSearchParam: {
        ...hotelBaseSearchParam,
        checkInDate,
        checkOutDate
      }
    }
  }

  const resportRequestError = (customMsg) => {
    reportInfo({
      code: errorCodeConstantMapping?.PAGE_CHANNEL_SEARCH_FETCH_LIST_ERROR,
      errorDetail: {
        errorType: ErrortType.Error,
        functionId: 'fetchChannelList',
        customMsg: customMsg,
      }
    })
  }

  // 初始化加载数据
  const initializeData = () => {
    const initialParams = getInitialRequestParams();
    initializeParams(initialParams);
    // 直接使用初始参数调用 fetchList
    fetchList(false, initialParams);
  }

  // 获取初始请求参数
  const getInitialRequestParams = () => {
    const staticParams = globalInfoModel.staticParams ?? {}
    const hotelSearchData = staticParams?.hotelSearchData || '';
    let requestParams
    if (isEmpty(hotelSearchData)) {
      reportInfo({
        code: errorCodeConstantMapping?.PAGE_CHANNEL_SEARCH_PARAMS_ERROR,
        errorDetail: {
          errorType: ErrortType.Error,
          functionId: 'getInitialRequestParams',
          customMsg: {
            errorDescription: '页面参数解析为空，使用默认参数'
          },
        }
      })
      // 使用默认参数
      requestParams = defaultRequestParams
    } else {
      // 优先从url上获取
      requestParams = deCodeDebBase64ParseSafe(hotelSearchData)
    }
    return requestParams
  }

  // 初始化参数处理核心参数
  const initializeParams = useCallback((searchParams?: any) => {
    if (searchParams) {
      // 提取核心参数
      const {
        keyword: kw,
        // 城市中心经纬度
        // mddinfo里是否有四级地址
        mddInfo: md,
        hotelBaseSearchParam: hbsp,
        filterList: fl,
        extMap: em,
        // 用户经纬度需要用不用
        latitude: lt,
        longitude: lg,
        posAreaId: pa,
        lbs_city: lbs_city,
        ...otherParams
      } = searchParams;

      extraParamsRef.current = otherParams;

      const initSearchParams = {
        keyword: kw || '',
        mddInfo: md || {},
        hotelBaseSearchParam: hbsp || {},
        extMap: em || {},
        latitude: lt || '',
        longitude: lg || '',
        posAreaId: pa || '',
        lbs_city: lbs_city || {}
      }

      // 设置核心参数到状态
      searchParamsRef.current = initSearchParams;
      setSearchParams(initSearchParams);

      // 初始化时, 设置fliterList
      updateInitFliter(fl || [])
    }
  }, []);


  // 重新加载数据
  const reloadDataIfNeed = useCallback(() => {
    getChannelSearchData().then(shareData => {
      const currentSearchParams = searchParamsRef.current;
      if (isEmpty(shareData)) {
        return
      }
      // 仅比较关键字段
      if (!isObjectEqual(currentSearchParams?.mddInfo, shareData?.mddInfo)) {
        const updatedShareData = {
          ...currentSearchParams,
          mddInfo: shareData?.mddInfo
        };
        searchParamsRef.current = updatedShareData;
        clearAllFilters()
        return
      }
    }).catch(error => {
      reportInfo({
        code: errorCodeConstantMapping?.PAGE_CHANNEL_LIST_GET_JDSHARE_DATA_ERROR,
        errorDetail: {
          errorType: ErrortType.Error,
          functionId: 'getChannelSearchData',
          customMsg: {
            errorDescription: '获取频道JDShare数据异常',
            errorInfo: error?.message,
            errorStack: error?.stack
          },
        }
      })
    });

  }, []);

  const refreshData = useCallback(() => {
    const currentSearchParams = searchParamsRef.current;
    debounceFetchList(false, currentSearchParams);
  }, []);

  const iFrameCallBack = useCallback((searchInfo) => {
    setWebViewUrl('')
    setWebViewVisible(false)
    if (!isEmpty(searchInfo)) {
      const currentSearchParams = searchParamsRef.current;
      // 仅比较关键字段
      if (!isObjectEqual(currentSearchParams?.mddInfo, searchInfo?.mddInfo)) {
        const updatedShareData = {
          ...currentSearchParams,
          mddInfo: searchInfo?.mddInfo
        };
        searchParamsRef.current = updatedShareData;
        clearAllFilters()
      }
    }
  }, []);


  // 处理加载更多
  const handleLoadMore = useCallback(() => {
    if (!loading && hasMore) {
      debounceFetchList(true);
    }
  }, [loading, hasMore, debounceFetchList]);

  // 监听参数变化，触发数据刷新
  useEffect(() => {
    if (firstLoad.current) {
      return
    }
    searchParamsRef.current = searchParams;
    debounceFetchList(false);
  }, [searchParams])

  // 监听筛选条件变化，更新筛选列表
  useEffect(() => {
    if (firstLoad.current) {
      return
    }
    currentPageRef.current = 1
    combiedFliterRef.current = combiedFliter;
    // 从合并的状态中获取所有筛选条件
    const { location_distance, price_star } = combiedFliter;

    // 合并所有筛选条件到一个数组
    const allFilters = [
      ...location_distance,
      ...price_star
    ];
    recordFilterMap(allFilters);
    debounceFetchList(false);
  }, [combiedFliter]);

  useEffect(() => {
    commonUserActionRef.current = commonUserAction;
  }, [commonUserAction]);

  // ==========================筛选相关==========================

  // 辅助函数：更新特定类型的筛选值
  const updateFilterByType = useCallback((type: string, newValue: any[]) => {
    setCombiedFliter(prev => {
      return {
        ...prev,
        [type]: newValue
      }
    });
  }, []);

  const updateInitFliter = (filterList) => {
    const locationFilters = <any>[]
    const priceStarFilters = <any>[]

    filterList.forEach(item => {
      if (item.type === locationSubFilterType.linearDistance ||
        item.type === locationSubFilterType.featureSpot ||
        item.filterType === locationSubFilterType.gis_distance ||
        item.filterType === locationSubFilterType.gis_location
      ) {
        locationFilters.push(item)
      } else if (item.filterType === priceStarSubFilterType.hotel_grade) {
        priceStarFilters.push(item)
      }
    })
    const newCombiedFliter = {
      location_distance: locationFilters,
      price_star: priceStarFilters
    }
    combiedFliterRef.current = newCombiedFliter

    setCombiedFliter(newCombiedFliter)
    // 第一次需要单独记录
    recordFilterMap(filterList)
  }

  const handleLocationFliter = useCallback((newFilters: any[]) => {
    updateFilterByType(glabelFilterType.location_distance, newFilters);
    // 百补筛选展示埋点
    mtaTrack(false, ChANNEL_EVENT_ID.ItravelSubsidyMainFilterLayerConfirm, {
      filterPanelName: "位置距离",
      filterPanelCode: glabelFilterType.location_distance,
      itemList: newFilters,
    })
  }, []);

  const handlePriceStarFliter = useCallback((newFilters: any[]) => {
    updateFilterByType(glabelFilterType.price_star, newFilters);
    // 百补筛选展示埋点
    mtaTrack(false, ChANNEL_EVENT_ID.ItravelSubsidyMainFilterLayerConfirm, {
      filterPanelName: "星级档次",
      filterPanelCode: glabelFilterType.price_star,
      itemList: newFilters,
    })
  }, []);

  // 添加一个清除所有筛选条件的方法
  const clearAllFilters = useCallback(() => {
    setCombiedFliter({
      location_distance: [],
      price_star: []
    });
  }, []);

  const getFiltersByType = (type: string) => {
    const currentCombiedFliter = combiedFliterRef.current;
    if (isEmpty(currentCombiedFliter)) {
      return [];
    }
    const modifiedPrice_star = currentCombiedFliter.price_star.map(item => ({
      ...item,
      filterKey: 'price_star'
    }));

    if (type === glabelFilterType.all) {
      return [
        ...currentCombiedFliter.location_distance,
        ...modifiedPrice_star
      ];
    }
    return currentCombiedFliter[type] || [];
  }

  const recordFilterMap = useCallback((allFilters: any[]) => {
    // 更新 filterMap
    const newFilterMap = new Set<string>();
    allFilters.forEach(item => {
      // 使用 filterType 和 itemId 作为唯一标识
      const key = `${item.filterType || ''}-${item.itemId || ''}`;
      newFilterMap.add(key);
    });
    setFilterMap(newFilterMap);
  }, [combiedFliter]);

  // 关闭筛选面板
  const handleFilterClose = () => {
    setActiveFilter(null);
  }

  // 处理 SortBar 项点击
  const handleFilterClick = useCallback((filterType: SortType) => {
    if (filterType === 'mddInfo') {
      // 跳转城市选择
      const enterType = 'promo'
      if (isWeb) {
        const hotelSearchData = !isEmpty(searchParamsRef.current) ? encrypt.Base64Encode(encrypt.Base64Encode(searchParamsRef.current)) : ''
        const url = `https://hotel.m.jd.com/?routerName=location&fromPage=baibuList&enterType=${enterType}&hotelSearchData=${hotelSearchData}`
        setWebViewUrl(url)
        setWebViewVisible(true)
      } else {
        const url = `https://hotel.m.jd.com/?routerName=location&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=location&fromPage=baibuList&enterType=${enterType}`
        jumpTo({ to: 'web', params: { url: url } })
      }
      setShowDatePop(false)
      setActiveFilter(null);
      return
    } else if (filterType === 'checkInOutDate') {
      // 跳转日期选择
      setShowDatePop(true);
      setActiveFilter(null);
      return
    }
    // 唤起弹层
    setActiveFilter(activeFilter === filterType ? null : filterType);
  }, [activeFilter]);

  const updateCheckInOutDate = useCallback((value) => {
    const { checkInDate, checkOutDate } = value
    const currentSearchParams = searchParamsRef.current;
    setSearchParams({
      ...currentSearchParams,
      hotelBaseSearchParam: {
        ...currentSearchParams.hotelBaseSearchParam,
        checkInDate,
        checkOutDate
      }
    });
  }, [])

  const isDateBetween = (dateStr, startStr, endStr) => {
    // const dateStr = dayjs(timestamp).format('YYYY-MM-DD');
    try {
      const date = dayjs(dayjs(dateStr)?.format('YYYY-MM-DD'));
      const start = dayjs(startStr);
      const end = dayjs(endStr);
      // 校验日期是否有效
      if (!date?.isValid() || !start?.isValid() || !end?.isValid()) {
        return false
      }
      // 判断日期是否在范围内（包含边界）
      return date.isBetween(start, end, null, '[]');
    } catch (error) {
      return false
    }
  }

  const cardOnClick = useCallback((info, index) => {
    const { hotelCardVO, eventData } = info;
    // 添加酒店卡片点击埋点, HotelRN_List_Hotel
    mtaTrack(false, ChANNEL_EVENT_ID.ItravelSubsidyHotel, { ...eventData }, '', '', {
      trafficMapInfo: {
        trafficmap_position_id: String(index)
      }
    });
    jumpTo({ to: 'web', params: { url: decodeURIComponent(hotelCardVO?.jumpUrl) } });
  }, []);


  const mtaTrack = (isExposure, eventId, eventData, pageParam?: string, eventParam?: string, extData?: any) => {
    try {
      sendMtaEvent(
        eventId,
        commonUserActionRef.current || {},
        eventData,
        isExposure,
        pageParam,
        eventParam,
        extData
      )
    } catch (error) {
      console.log('mtaTrack error', error)
    }
  }

  return {
    loading,
    loadMoreStatus,
    list,
    hasMore,
    searchParamsRef,
    activeFilter,
    combiedFliterRef,
    filterPanelVOList,
    filterMap,
    mtaTrack,
    initializeData,
    reloadDataIfNeed,
    refreshData,
    updateCheckInOutDate,
    handleLoadMore,
    handleFilterClose,
    handleFilterClick,
    handleLocationFliter,
    handlePriceStarFliter,
    clearAllFilters,
    firstLoad,
    extraParamsRef,
    cardOnClick,
    reportPV,
    getCommonParams,
    getAddress,
    getFiltersByType,
    showDatePop,
    setShowDatePop,
    baibuHeadConfig,
    baibuHotelCalendarRange,
    webViewUrl,
    webViewVisible,
    iFrameCallBack,
    baibuPriceConfigVO,
    isDateBetween
  }
}

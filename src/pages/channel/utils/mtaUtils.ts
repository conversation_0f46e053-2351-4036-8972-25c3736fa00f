import IdleQueue from '@/utils/IdleQueue';
import { newMta, mtaEp } from '@/common/mta';
import {M_PAGEID, M_PAGE} from '@/common/mta';

/**
 * 发送MTA埋点事件
 * @param {string} eventId - 事件ID
 * @param {object} commonUserAction - 通用用户行为数据
 * @param {object} eventData - 事件特定数据
 * @param {boolean} isExposure - 是否为曝光事件，默认为false（点击事件）
 */
export const sendMtaEvent = (
  eventId: string,
  commonUserAction: Record<string, any> = {},
  eventData: Record<string, any> = {},
  isExposure: boolean = false,
  pageParam?: string,
  eventParam?: string,
  extData?: any
) => {
  const mtaMethod = isExposure ? mtaEp : newMta;
  
  IdleQueue.add(
    mtaMethod,
    eventId,
    M_PAGEID.Channel,
    M_PAGE.Channel,
    {
      ...commonUserAction,
      ...eventData
    },
    pageParam,
    eventParam,
    extData
  );
};

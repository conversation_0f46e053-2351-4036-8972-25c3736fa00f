import React, { useEffect, useRef, useCallback, useContext } from 'react'
import { View } from '@/BaseComponents/atoms'
import { JDLoadingView, JDTrafficMap, TrafficMapComponent } from '@jdreact/jdreact-core-lib';
import withPage, { BasePageContext } from "@/common/withPage"
import Header from './components/header'
import styles from './index.module.scss'
import { M_PAGEID, M_PAGE } from '@/common/mta'
import { useChannelSearch } from './hooks/useChannelSearch'
import { pt, statusBarHeight, isAndroid, isIOS } from '@ltfe/ltfe-core-lib/lib/utiles'
import LocationFilterContainer from './components/filter/locationFilter/locationFilterContainer'
import PriceStarFilter from './components/filter/priceStarFilter'
import { glabelFilterType } from '../hotelSearch/constants/filterType'
import List from './components/list'
import { SortType } from '../hotelSearch/constants/config'
import WebViewBox from '../hotelSearch/components/webView'
import { isEmpty } from '@/utils/isType'
import DatePop from './components/datePop';

const Channel: React.FC = () => {
    const refRootNode = useRef<any>()
    const fliterTop =  statusBarHeight + pt(44) + pt(34)

    const {
        registerDidShow,
        offDidShow
    } = useContext(BasePageContext)

    const {
        initializeData,
        searchParamsRef,
        filterPanelVOList,
        activeFilter,
        handleFilterClick,
        getAddress,
        combiedFliterRef,
        filterMap,
        handleLocationFliter,
        handlePriceStarFliter,
        handleFilterClose,
        mtaTrack,
        showDatePop,
        setShowDatePop,
        loading,
        list,
        hasMore,
        handleLoadMore,
        reloadDataIfNeed,
        refreshData,
        cardOnClick,
        loadMoreStatus,
        firstLoad,
        webViewVisible,
        webViewUrl,
        iFrameCallBack,
        updateCheckInOutDate,
        baibuHotelCalendarRange,
        reportPV,
        isDateBetween,
        baibuPriceConfigVO,
        baibuHeadConfig,
        getCommonParams
    } = useChannelSearch()

    const listRef = useRef<any>(null)
    const jdtrafficmapRef = useRef<any>(null)

    // didShow 处理函数
    const didShow = useCallback(() => {
        prepareJDTrafficMap()
        renderDoneJDTrafficMap()
        if (firstLoad.current) {
            return
        }
        // 非首次加载，使用当前状态重新获取数据
        reloadDataIfNeed()
        reportPV()
    }, [])

    useEffect(() => {
        initializeData()
        reportPV()
    }, [])

    // 注册和清理 didShow
    useEffect(() => {
        registerDidShow(didShow)
        return () => {
            offDidShow(didShow)
        }
    }, [registerDidShow, offDidShow, didShow])

    const renderDoneJDTrafficMap = () => {
        // 安卓必须调用renderDone
        try {
          // 调用流量地图onResume
          if (isAndroid) {
            setTimeout(() => {
              jdtrafficmapRef?.current && jdtrafficmapRef?.current?.renderDone()
            }, 500)
          }
        } catch (error) {
          console.log(error)
        }
    }

    //   注册流量地图
    const prepareJDTrafficMap = () => {
        try {
        if (JDTrafficMap) {
            if (isAndroid) {
            JDTrafficMap.prepare({
                page_id: M_PAGEID.Channel
            })
            } else if (isIOS) {
            setTimeout(() => {
                JDTrafficMap.prepare({
                page_id: M_PAGEID.Channel
                })
            }, 500)
            }
        }
        } catch (error) {
        console.log(error)
        }
    }

    const onFilterClick = (filterType: SortType) => {
        if (listRef?.current && listRef?.current?.scrollToTop) {
            listRef.current.scrollToTop()
        }

        let timeout = 100
        if (filterType === 'mddInfo') {
            timeout = 200
        } else if (filterType === 'checkInOutDate') {
            timeout = 0
        }
        // 如果是吸顶
        if (listRef?.current && listRef?.current?.isTop()) {
            timeout = 0
        }

        setTimeout(() => {
            handleFilterClick(filterType);
        }, timeout);
    }

    const onLocationChange = (newFilters: any[]) => {
        handleLocationFliter(newFilters);
    }

    const onPriceStarChange = (newFilters: any[]) => {
        handlePriceStarFliter(newFilters);
    }

    return (
        <View
            ref={refRootNode}
            className={styles.container}>
            {
                loading && <View className={styles.loading}><JDLoadingView /></View>
            }
            <Header haederConfig={baibuHeadConfig} onCountDownEnd={refreshData}/>
            <List
                ref={listRef}
                firstLoad={firstLoad.current}
                searchParams={searchParamsRef.current}
                commonMtaParams={getCommonParams()}
                selectedFilters={{
                    location_distance: combiedFliterRef.current.location_distance,
                    price_star: combiedFliterRef.current.price_star
                }}
                activeFilterType={activeFilter}
                filterPanelVOList={filterPanelVOList}
                baibuPriceConfigVO={baibuPriceConfigVO}
                onFilterClick={onFilterClick}
                loading={loading}
                data={list}
                hasMore={hasMore}
                onLoadMore={handleLoadMore}
                onReload={refreshData}
                cardOnClick={cardOnClick}
                loadMoreStatus={loadMoreStatus}
                mtaExpo={mtaTrack}
                handleScrollCallback={handleFilterClose}
            />

            <LocationFilterContainer
                style={{ top: fliterTop }}
                visible={activeFilter === glabelFilterType.location_distance}
                address={getAddress()}
                value={combiedFliterRef.current.location_distance}
                onChange={onLocationChange}
                onClose={handleFilterClose}
                mtaTrack={mtaTrack}
            />

            <PriceStarFilter
                style={{ top: fliterTop }}
                visible={activeFilter === glabelFilterType.price_star}
                filterMap={filterMap}
                filterPanelVOList={filterPanelVOList}
                value={combiedFliterRef.current.price_star}
                onChange={onPriceStarChange}
                onClose={handleFilterClose}
                mtaTrack={mtaTrack}
            />

            {showDatePop && <DatePop
                calendarRange={baibuHotelCalendarRange}
                updateCheckInOutDate={updateCheckInOutDate}
                setShowDatePop={setShowDatePop}
                isDateBetween={isDateBetween}
                checkInDate={searchParamsRef.current?.hotelBaseSearchParam?.checkInDate}
                checkOutDate={searchParamsRef.current?.hotelBaseSearchParam?.checkOutDate}
                mtaTrack={mtaTrack}
            />}

            {
                webViewVisible && !isEmpty(webViewUrl) && <WebViewBox
                    type='channelList'
                    show={webViewVisible}
                    uri={webViewUrl}
                    iFrameCallBack={iFrameCallBack}
                />
            }
            <TrafficMapComponent
                ref={jdtrafficmapRef}
                pageId={M_PAGEID.Channel}
            />
        </View>
    )
}

export default withPage({ pageName: M_PAGE.Channel })(Channel)

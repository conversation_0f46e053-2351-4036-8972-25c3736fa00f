import { StyleSheet } from "react-native";
import { THEME_FONT } from '@ltfe/ltfe-core-lib/lib/utiles/theme';
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';

const st = StyleSheet.create({
    cdContainer: { 
        marginRight: pt(4), 
        height: pt(16) 
    },
    cdHMS: {
        alignItems: 'center', 
        justifyContent: 'center',
        width: pt(23), 
        height: pt(16), 
        borderRadius: pt(2), 
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        fontSize: pt(12),
        lineHeight: pt(16),
        color: '#FA1826', 
        textAlign: 'center',
        fontWeight: THEME_FONT.fontWeight.SemiBold,
        overflow: 'hidden'
    },
    cdColon: { 
        width: pt(12), 
        fontSize: pt(12), 
        lineHeight: pt(15), 
        color: '#ffffff', 
        textAlign: 'center' 
    },
})
export default st


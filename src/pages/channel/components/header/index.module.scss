.container {
    display: flex;
    position: absolute;
    height: 100%;
    width: 100%;
}

.bgView {
    position: absolute;
    width: 100%;
}

.bgImgbg {
    width: 100%;
    position: absolute;
    top: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.bgImg {
    width: 375px;
    height: 195px;
}

.title {
    display: flex;
    height: 44px;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    justify-content: space-between;
}

.titleLeft {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-left: 10px;
}

.backIcon {
    width: 7.5px;
    height: 14.5px;
    margin-left: 6px;
    margin-right: 7px;

}

.backImg {
    width: 86px;
    height: 24px
}

.titleRight {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-right: 12px;
}

.rightTxt {
    color: #FFFFFF;
    font-size: 12px;
    // font-weight: 'Regular';
    margin-right: 8px;
    font-family: 'PingFangSC';
}
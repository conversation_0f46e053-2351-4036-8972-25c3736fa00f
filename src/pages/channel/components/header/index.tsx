import { View, Image, Text } from '@/BaseComponents/atoms'
import { JDCountDown } from '@jdreact/jdreact-core-lib';
import { Dimensions, TouchableOpacity } from 'react-native';
import { pt, statusBarHeight } from '@ltfe/ltfe-core-lib/lib/utiles'
import Taro from '@tarojs/taro';
import styles from './index.module.scss';
import st from './style';
import { useEffect, useRef } from 'react';
import NavBar from '@/BaseComponents/NavBar';
import { isWeb } from '@/common/common';

interface JDCountDownRef {
    resetTimer: (config: { startDate: number; endDate?: number; leftTime?: number }) => void;
}

const deviceWidth = Dimensions.get('window').width;

const ChannelHeader = ({ haederConfig, onCountDownEnd }) => {
    const bgHeight = statusBarHeight + pt(44) + pt(60) + pt(45);
    const jdCountDownRef = useRef<JDCountDownRef>(null);

    useEffect(() => {
        jdCountDownRef.current?.resetTimer({ startDate: new Date().getTime(), leftTime: haederConfig.countdownTime })
    }, [haederConfig.countdownTime]);

    const goBack = () => {
        if (isWeb) {
            return
        }
        Taro.navigateBack({ delta: 1 })
    }

    return (
        <View className={styles.container}>
            <NavBar
                style={{height: 0, width: 0, display: 'none'}} 
                barStyle='light-content'
            />
            <View className={styles.bgImgbg} style={{ height: bgHeight }} >
                {haederConfig?.bgImgUrl && haederConfig?.bgImgHeight ? (
                    <Image className={styles.bgImg} style={{ width: deviceWidth, height: deviceWidth * (haederConfig?.bgImgHeight || 226) / (haederConfig?.bgImgWidth || 375) }} src={haederConfig.bgImgUrl} />
                ) : (
                    <Image className={styles.bgImg} style={{ width: deviceWidth, height: deviceWidth * 0.6 }} src={require('@/assets/images/baibubg.png')} />
                )}
            </View>
            <View className={styles.bgView} style={{ height: bgHeight }} >
                <View style={{height: statusBarHeight}}></View>
                <View className={styles.title} style={{height: pt(44)}}>
                    <TouchableOpacity activeOpacity={1} onPress={goBack}>
                        <View className={styles.titleLeft}>
                            {!isWeb && (haederConfig?.backIconUrl ? (
                                <Image 
                                    className={styles.backIcon}
                                    style={{
                                        width: haederConfig?.backIconWidth ? pt(haederConfig?.backIconWidth) : undefined,
                                        height: haederConfig?.backIconHeight ? pt(haederConfig?.backIconHeight) : undefined,
                                    }}
                                    src={haederConfig?.backIconUrl} />
                            ) : (
                                <Image className={styles.backIcon} src={require('@/assets/images/backIcon.png')} />
                            ))}
                            {haederConfig?.titleImgUrl ? (
                                <Image
                                    className={styles.backImg}
                                    style={{
                                        width: haederConfig?.titleImgWidth ? pt(haederConfig?.titleImgWidth) : undefined,
                                        height: haederConfig?.titleImgHeight ? pt(haederConfig?.titleImgHeight) : undefined
                                    }}
                                    src={haederConfig?.titleImgUrl} />
                            ) : (
                                <Image className={styles.backImg} src={require('@/assets/images/backTitleImg.png')} />
                            )}
                        </View>
                    </TouchableOpacity>
                    {haederConfig?.countdownTime && <View className={styles.titleRight}>
                        <Text className={styles.rightTxt} style={{ color: haederConfig?.timeTextColor || '#FFFFFF' }}>{haederConfig?.timeText || ''}</Text>
                        <JDCountDown
                            key={`countdownTime-${haederConfig.countdownTime}`}
                            ref={jdCountDownRef}
                            startDate={new Date().getTime()}
                            leftTime={haederConfig.countdownTime || 360000}
                            onEnd={onCountDownEnd}
                            interval={1000}
                            containerStyle={st.cdContainer}
                            hoursStyle={st.cdHMS}
                            minsStyle={st.cdHMS}
                            secsStyle={st.cdHMS}
                            firstColonStyle={st.cdColon}
                            secondColonStyle={st.cdColon}
                        />
                    </View>}
                </View>
            </View>
        </View>
    )
}

export default ChannelHeader;

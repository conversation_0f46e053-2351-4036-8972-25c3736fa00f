import { View, Text, Image } from '@/BaseComponents/atoms'
import Calendar from '@ltfe/ltfe-core-lib/lib/hotel-search-panel/calendar-pop'
import { pt, deviceHeight } from '@ltfe/ltfe-core-lib/lib/utiles'
import styles from './index.module.scss';
import { isEmpty } from '@/utils/isType'
import { Popup } from '@ltfe/ltfe-core-lib'
import { MTAPlaceHolderString } from '../../constants/config';
import { ChANNEL_EVENT_ID } from '../../constants/mtaParamEvents';

const DatePop = ({
    calendarRange,
    setShowDatePop,
    updateCheckInOutDate,
    isDateBetween,
    checkInDate,
    checkOutDate,
    mtaTrack
}) => {

    const {
        beginDate,
        endDate,
        title,
        titleBgColor = '#FFF1F1',
        titleColor = '#FF0400',
        dateBgImgUrl,
        dateSelectedBgImgUrl,
        dateBgImgHeight,
        dateBgImgWidth,
        toastMessage,
    } = calendarRange || {}

    const showBubaiStyle = !isEmpty(beginDate) && !isEmpty(endDate);
    const datePopTile = showBubaiStyle ? '百亿补贴日历' : '选择日期';

    mtaTrack(true, ChANNEL_EVENT_ID.ItravelSubsidyCalenderLayerExpo, {
        baibuCheckInBeginDate: beginDate || MTAPlaceHolderString,
        baibuCheckInEndDate: endDate || MTAPlaceHolderString
    })

    return (
        <Popup
            title={datePopTile}
            speed={200}
            show={true}
            height={deviceHeight * 5 / 6}
            onHide={() => setShowDatePop(false)}
        >
            <View className={styles.container} >
                {
                    showBubaiStyle && !isEmpty(title) && <View className={styles.title} style={{ backgroundColor: titleBgColor }}>
                        <Text className={styles.titleTxt} style={{ color: titleColor }}>{title}</Text>
                    </View>
                }
                <Calendar
                    checkInDate={checkInDate}
                    checkOutDate={checkOutDate}
                    allCanUsedCalendar={''}
                    isUseCustomCalendarData={true}
                    basicConfig={{
                        theme: showBubaiStyle ? 'jd' : 'booking',
                        weekHeight: pt(32)
                    }}
                    onUpdate={(pickDate) => {
                        const { departDate, arriveDate } = pickDate
                        updateCheckInOutDate({
                            checkInDate: departDate,
                            checkOutDate: arriveDate
                        })
                        setShowDatePop(false)
                    }}
                    isCustomTag={showBubaiStyle}
                    renderCustomComponent={(dayRes) => {
                        const { type, status, data, isBetween } = dayRes;
                        if (type === 'day') return false;
                        if (type === 'customTag' && isDateBetween(data, beginDate, endDate)) {
                            return <View>
                                {status === "normal" && <Image style={{ marginTop: pt(2), marginBottom: pt(1), backgroundColor: isBetween ? "#FFF1F1" : "#FFFFFF", width: pt(Number(dateBgImgWidth)) || pt(37), height: pt(Number(dateBgImgHeight)) || pt(8.5) }} src={dateBgImgUrl || ''} />}
                                {status === "active" && <Image style={{ marginTop: pt(2), marginBottom: pt(1), backgroundColor: "#FF0400", width: pt(Number(dateBgImgWidth)) || pt(37), height: pt(Number(dateBgImgHeight)) || pt(8.5) }} src={dateSelectedBgImgUrl || ''} />}
                            </View>
                        }
                    }}
                    intervalChecks={showBubaiStyle ? [
                        {
                            timeType: "departDate",
                            timeInterval: [beginDate, endDate],
                            errMsg: toastMessage || "校验失败"
                        }
                    ] : []}
                />
            </View>
        </Popup>
    )
}

export default DatePop;

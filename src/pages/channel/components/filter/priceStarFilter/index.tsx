import React, { useState, useEffect, CSSProperties, memo } from "react";
import { View, Text } from '@/BaseComponents/atoms'
import styles from "./index.module.scss";
import { isEmpty } from "@/utils/isType";
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import { HOTEL_SEARCH_EVENT_ID } from '../../../../hotelSearch/constants/mtaParamEvents';
import { glabelFilterType, hotelPriceStarType } from '@/pages/hotelSearch/constants/filterType'
interface StarLevel {
    level: number;
    label: string;
    type: "star";
    filterType?: string;
    itemId?: string;
    itemName?: string;
    groupCode?: string;
}

interface PriceStarFilterProps {
    value?: StarLevel[];
    filterMap?: Set<any>;
    onChange: (values: StarLevel[]) => void;
    onClose: (trackConfirm?: boolean, extraData?: any) => void;
    visible: boolean;
    filterPanelVOList?: any[];
    style: CSSProperties;
    mtaTrack: (isExposure, eventId, eventData) => void
}

const PriceStarFilter: React.FC<PriceStarFilterProps> = ({
    value = [],
    onChange,
    onClose,
    visible,
    filterPanelVOList = [],
    style,
    mtaTrack
}) => {
    // 添加内部状态来存储临时的选择
    const [tempValue, setTempValue] = useState<StarLevel[]>(value || []);

    // 当外部 value 变化时，同步更新内部状态
    useEffect(() => {
        setTempValue(value || []);
    }, [value]);

    // 当组件显示时，同步外部 value 到内部状态
    useEffect(() => {
        if (visible) {
            setTempValue(value || []);
        }
    }, [visible, value]);

    const getStarLevels = () => {
        const priceStarPanel = filterPanelVOList?.find(
            (panel) => panel?.filterPanelCode === glabelFilterType.price_star
        );
        const starGroup = priceStarPanel?.filterList?.find(
            (group) => group?.groupCode === hotelPriceStarType.hotel_grade
        );

        if (!starGroup?.itemList) return [];

        return (starGroup.itemList || []).map((item) => {
            return {
                level: Number(item?.itemId || 0),
                label: item?.itemName || "",
                type: "star",
                filterType: item?.filterType || "",
                itemId: item?.itemId || "",
                itemName: item?.itemName || "",
                itemDesc: item?.itemDesc || "",
                groupCode: starGroup?.groupCode || ''
            };
        });
    };

    const STAR_LEVELS = getStarLevels();

    const isFilterSelected = (item) => {
        return tempValue?.some(filterItem => 
            filterItem?.filterType === item?.filterType && filterItem?.itemId === item?.itemId
        );
    }

    // 处理星级选项点击
    const handleStarClick = (starLevel: any) => {
        if (!starLevel) return;

        const isSelected = isFilterSelected(starLevel);

        let newValue = [...(tempValue || [])];

        if (isSelected) {
            // 如果已选中，则取消选择
            newValue = newValue.filter(
                (item) => item?.itemId !== starLevel?.itemId
            );
        } else {
            // 否则添加该星级（多选模式）
            const newStar: StarLevel = {
                level: Number(starLevel?.itemId || 0),
                label: starLevel?.itemName || "",
                type: "star",
                filterType: starLevel?.filterType || "",
                itemId: starLevel?.itemId || "",
                itemName: starLevel?.itemName || "",
                groupCode: starLevel?.groupCode || ''
            };
            // 添加新的星级项，保留其他星级项
            newValue.push(newStar);
        }

        setTempValue(newValue);
    };

    const handleClear = () => {
        mtaTrack(false, HOTEL_SEARCH_EVENT_ID.HotelRNListFilterClearNew, {
            displayName: '清空',
            filterPanelName: '星级',
            filterPanelCode: glabelFilterType.price_star,
            index: 3
        })
        onClose(true, {
            displayName: `查看结果`,
            filterPanelName: '星级',
            filterPanelCode: glabelFilterType.price_star,
        })
        if (isEmpty(value)) {
            return;
        }
        onChange([]);
     
    };

    if (!visible) return null;

    const panelStyle = styles.panel_channel

    const renderChannelStyle = () => {
        return (
            <View style={style} className={styles.container}>
                <View className={styles.mask} onClick={() => onClose()} />
                <View className={panelStyle}>
                    <View className={styles.section}>
                        <View className={styles.options}>
                            {STAR_LEVELS.map((star, index) => {
                                // 检查当前星级是否被选中
                                const isSelected = isFilterSelected(star);

                                return (
                                    <View
                                        key={`${star?.level || 0}-${index}`}
                                        className={`${styles.option} ${isSelected ? styles.selected_red : ""}`}
                                        style={{ width: pt(109), minHeight: pt(44), marginRight: pt(8), marginBottom: pt(8) }}
                                        onClick={() => handleStarClick(star)}
                                    >
                                        <Text
                                            className={
                                                isSelected
                                                    ? styles.selectedText_red
                                                    : styles.optionText
                                            }
                                        >
                                            {star?.label || ""}
                                        </Text>
                                        {star.itemDesc ? <Text
                                            className={
                                                isSelected
                                                    ? styles.selectDesc_red
                                                    : styles.optionDesc
                                            }
                                        >
                                            {star.itemDesc}
                                        </Text> : null}
                                    </View>
                                );
                            })}
                        </View>
                    </View>
             

                    <View className={styles.footer}>
                        <View className={styles.clearBtn} style={{ marginRight: 16 }} onClick={handleClear}>
                            <Text className={styles.clearBtn} style={{ borderWidth: 0, paddingLeft: 0 }} >
                                清空
                            </Text>
                        </View>
                        <View className={styles.confirmBtn_container_red} onClick={() => {
                            onChange(tempValue);
                            onClose(true, {
                                displayName: `查看结果`,
                                filterPanelName: '星级',
                                filterPanelCode: glabelFilterType.price_star,
                            })
                        }}>
                            <Text className={styles.confirmBtn}>查看结果</Text>
                        </View>
                    </View>
                </View>
            </View>
        )
    }

    return (
        renderChannelStyle()
    );
};

export default memo(PriceStarFilter);

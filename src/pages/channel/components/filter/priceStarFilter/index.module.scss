.container {
  position: absolute;
  top: -30px;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  height: 100%;
}

.mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
}

.panel {
  position: absolute;
  left: 0;
  right: 0;
  background: #fff;
  padding-top: 16px;
}

.panel_channel {
  position: absolute;
  left: 0;
  right: 0;
  background: #fff;
  padding-top: 16px;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

.priceSectionView {
  display: flex;
  padding-right: 16px;
  flex-direction: row;
  align-items: center;
  margin-bottom: 12px;
}

.section {
  margin-bottom: 12px;
  padding-left: 16px;
}
.sectionTitleView{
  display: flex;
  padding-right: 16px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
.sectionTitle {
  font-size: 14px;
  font-weight: bold;
  color: #1A1A1A;
  padding: 4px 0;
}
.sectionTitleNum {
  font-size: 16px;
  color: #0068FF;
  margin-left: 8px;
  font-family: 'JDZhengHT';
}
.sectionTitlDesc {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.sectionTitlDescTxt {
  color: #0068FF;
  font-size: 12px;
}

.descImg {
  width: 8px;
  height: 3px;
  transform: rotate(90deg);
}

.options {
  flex-direction: row;
  flex-wrap: wrap;
  display: flex;
}

.option {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F5F7FA;
  border-radius: 4px;
}

.optionText {
  font-size: 12px;
  color: #1A1A1A;
}

.optionDesc {
  font-size: 10px;
  color: #505259;
  margin-top: 4px;
}

.selected {
  background: #EFF5FF;
  border: 0.5px solid #006EEB;
}

.selected_red {
  background: #FFEBF1;
  border: 0.5px solid #FF0400;
}

.selectedPriceText {
  font-size: 14px;
  color: #006EEB;
  font-family: 'JDZhengHT';
}

.priceText{
  font-size: 14px;
  color: #5E6880;
  font-family: 'JDZhengHT';
}

.selectedText {
  font-size: 12px;
  color: #006EEB;
}

.selectedText_red {
  font-size: 12px;
  color: #FF0400;
}

.selectDesc {
  font-size: 10px;
  color: #006EEB;
  margin-top: 4px;
}

.selectDesc_red {
  font-size: 10px;
  color: #FF0400;
  margin-top: 4px;
}


.sliderSection {
  padding: 20px 32px;
  margin-bottom: 20px;
  
}

.silerPriceContainer {
  justify-content: space-between;
  flex-direction: row;
}

.silerPriceText {
  font-size: 14px;
  color: #888B94;
}

.tip {
  padding-left: 16px; 
  margin-top: -6px;
  margin-bottom: 18px;
  font-size: #7C849C;
}

.tipTxt {
  font-size: 12px;
  color: #888B94; 
}

.footer {
  flex-direction: row;
  height: 60px;
  padding: 16px;
  padding-top: 10px;
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #EBEDF0;
}

.clearBtn {
  flex: 1;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 22px;
  font-size: 16px;
  border: 0.8px solid #505259;
  border-radius: 6px;
  color: #505259;
}

.confirmBtn_container {
  flex: 1;
  height: 40px;
  align-items: center;
  justify-content: center;
  line-height: 40px;
  background-color: #013B94;
  border-radius: 6px;
}

.confirmBtn_container_red {
  flex: 1;
  height: 40px;
  align-items: center;
  justify-content: center;
  line-height: 40px;
  background-color: #FF0400;
  border-radius: 6px;
}

.confirmBtn {
  text-align: center;
  font-size: 16px;
  color: #fff;
}

.linkIcon {
  margin-left: 4px;
  width: 4px;
  height: 8px;
}
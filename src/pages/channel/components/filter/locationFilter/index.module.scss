.container {
  position: absolute;
  top: -30px;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  z-index: 100;
}

.mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
}

.panel {
  position: absolute;
  background: #fff;
  height: 60%;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.panel_channel {
  position: absolute;
  background: #fff;
  height: 60%;
  display: flex;
  flex-direction: column;
  width: 100%;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

.content {
  flex: 1;
  flex-direction: row;
  display: flex;
}

.leftColumn {
  background: #F5F7FA;
}

.rightColumn {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.leftContainer {
  position: relative;
  height: 50px;
}

.activeContainer {
  background: #fff;
}

.leftTip {
  position: absolute;
  background-color: #FF0400;
  border-radius: 2px;
  height: 4px;
  width: 4px;
  left: 8px;
  top: 24px;
}

.leftItem {
  height: 50px;
  line-height: 50px;
  font-size: 14px;
  color: #191919;
  margin-left: 16px;
}

.activeItem {
  color: #006EEB;
  font-weight: bold;
}

.activeItem_red {
  color: #FF0400;
  font-weight: bold;
}

.rightItem {
  height: 60px;
  display: flex;
  flex-direction: row;
  margin-left: 12px;
  margin-right: 12px;
  border-bottom-width: 1px;
  border-bottom-color: #F5F7FA;
  justify-content: space-between;
  align-items: center;
}

.hotContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.hotTitle {
  flex: 1;
  min-width: 200px;
  margin-top: 13px;
  font-size: 14px;
  color: #1a1a1a;
  margin-right: 6px;
}

.hotSubContainer {
  margin-bottom: 10px;
  flex-direction: row;
}

.hotSubTitle {
  font-size: 12px;
  color: #FF0400;
  margin-right: 4px;
}

.hotSubTitleNormal {
  font-size: 12px;
  color: #7C869C;
  margin-right: 4px;
}

.hotSubDesc {
  font-size: 12px;
  color: #7C869C;
}


.rightItemTitle {
  flex: 1;
  font-size: 14px;
  color: #1a1a1a;
  margin-right: 6px;
}

.rightItemDesc {
  margin-top: 4px;
  font-size: 12px;
  color: #7C849C;
  font-family: PingFangSC-Regular;
  font-size: 12px;
  font-weight: Regular;
}

.rightImgContainer {
  height: 18px;
  width: 18px;
}

.rightItemImg {
  height: 18px;
  width: 18px;
}

.selectedItem {
  color: #006EEB;
}

.selectedItem_red {
  color: #FF0400;
}

.footer {
  flex-direction: row;
  height: 60px;
  padding: 16px;
  padding-top: 10px;
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #EBEDF0;
}

.clearBtn {
  flex: 1;
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 16px;
  border: 0.8px solid #5E6880;
  color: #5E6880;
  border-radius: 6px;
}

.confirmBtn_container {
  flex: 1;
  height: 40px;
  align-items: center;
  justify-content: center;
  line-height: 40px;
  background-color: #013B94;
  border-radius: 6px;
}

.confirmBtn_container_red {
  flex: 1;
  height: 40px;
  align-items: center;
  justify-content: center;
  line-height: 40px;
  background-color: #FF0400;
  border-radius: 6px;
}

.confirmBtn {
  text-align: center;
  font-size: 16px;
  color: #fff;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  width: 100%;
  background-color: #FFFFFF;
}

.errorContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  width: 100%;
  background-color: #FFFFFF;
}

.reloadText {
  color: #FF0400;
  margin-top: 10px;
}

.middleColumn {
  flex: 1;
  display: flex;
  min-width: 90px;
  flex-direction: column;
  background: #fff;
  border-right-width: 1px;
  border-right-color: #F5F7FA;
}

.middleItem {
  flex-direction: column;
  justify-content: center;
  height: 60px;
  margin-left: 12px;
  margin-right: 12px;
  display: flex;
  border-bottom-width: 1px;
  border-bottom-color:  #F5F7FA;
}

.middleItemTitle {
  flex: 1;
  font-size: 14px;
  color: #333;
  min-width: 200px;
  height: 60px;
  line-height: 60px;
}

.activeMiddleItem {
  color: #0068FF;
}

.activeMiddleItem_red {
  color: #FF0400;
}

.selectIcon {
  width: 10px;
  height: 10px;
  border-width: 1px;
  border-color: #F5F7FA;
  position: absolute;
  right: -18px;
  top: 22px;
  background-color: #fff;
  transform: rotate(45deg);
  opacity: 1;
}
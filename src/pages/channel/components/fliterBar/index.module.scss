
@import "@/assets/theme.scss";

.container {
  position: relative;
  padding-left: 16px;
  padding-right: 10px;
  height: 44px;
  width: 100%;
  background-color: #fff;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

.borderContainer {
  position: absolute;
  left: -10px;
  top: 0;
  right: -10px;
  width: 365px;
  height: 44px;
}

.mapContainer {
  flex: 1;
  height: 44px;
}

.horizontalScroll {
  display: flex;
  margin-top: 2px;
  width: 100%;
  height: 44px;
}
.item {
  min-width: 100px;
  height: '100%';
  margin: 0 8px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}
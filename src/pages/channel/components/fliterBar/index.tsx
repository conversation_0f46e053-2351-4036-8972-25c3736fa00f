import { useMemo, memo, useEffect, useRef, forwardRef } from 'react'
import { View, Image } from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import { InView, IOScrollView } from '@/BaseComponents/IntersectionObserver'
import { SortType, ORDER_TYPES } from '../../../hotelSearch/constants/config'
import FilterItem from './fliterItem'
import { isEmpty } from '@/utils/isType'
import { sendMtaEvent } from '../../utils/mtaUtils'
import { ChANNEL_EVENT_ID } from '../../constants/mtaParamEvents'

interface FilterBarProps {
    searchParams?: any;
    commonMtaParams?: any;
    onFilterClick: (filterType: SortType) => void;
    selectedFilters?: {
        [key: string]: any[];
    };
    activeFilterType: any;
    filterPanelVOList?: any[];
}

const FliterBar = forwardRef<any, FilterBarProps>(({
    searchParams,
    commonMtaParams,
    onFilterClick,
    activeFilterType,
    selectedFilters = {},
    filterPanelVOList = [],
}, ref) => {
    const expoMap = useRef({}) // 埋点信息

    function formatDateToShort(dateStr?: string) {
        if (!dateStr) return '';
        const [, month, day] = dateStr.split('-');
        return `${month.padStart(2, '0')}.${day.padStart(2, '0')}`;
    }
    // 构建排序项列表
    const filterItems = useMemo(() => {
        // 如果没有目的地
        const { mddInfo = {}, hotelBaseSearchParam = {} } = searchParams || {}
        if (isEmpty(mddInfo) || isEmpty(mddInfo?.showName)) {
            return [];
        }
        // 如果没有住离时间
        if (isEmpty(hotelBaseSearchParam.checkInDate) || isEmpty(hotelBaseSearchParam.checkOutDate)) {
            return [];
        }
        // 如果没有筛选项
        if (isEmpty(filterPanelVOList)) {
            return [];
        }

        const mddInfoItem = {
            key: 'mddInfo',
            label: mddInfo?.showName,
            type: 'mddInfo'
        };

        const showDate = '住' + `${formatDateToShort(hotelBaseSearchParam?.checkInDate)} ` + '离' + `${formatDateToShort(hotelBaseSearchParam?.checkOutDate)}`;
        const checkInDateItem = {
            key: 'checkInOutDate',
            label: showDate,
            type: 'checkInOutDate'
        };

        // 从 filterPanelVOList 中提取其他筛选项
        const otherFilterItems = filterPanelVOList.map(panel => ({
            key: panel.filterPanelCode,
            label: panel.filterPanelName,
            type: panel.filterPanelCode,
            options: panel.filterList || []
        }));
        // 合并排序项和其他筛选项
        return [mddInfoItem, checkInDateItem, ...otherFilterItems];
    }, [filterPanelVOList]);


    // 处理筛选项点击事件，添加埋点HotelRN_List_MainFilter
    const handleFilterClick = (filterType: SortType, item: any, index: number) => {
        handleClickMta(filterType, item, index)
        // 调用原有的点击处理函数
        onFilterClick(filterType);
    };

    // 检查筛选项是否有选中的值
    const hasSelectedFilters = (key: string): boolean => {
        return selectedFilters &&
            selectedFilters[key] &&
            (Array.isArray(selectedFilters[key]) ?
                selectedFilters[key].length > 0 :
                !!selectedFilters[key]);
    };

    // 获取筛选项选中的数量
    const getSelectedCount = (key: string): number => {
        if (!selectedFilters || !selectedFilters[key]) {
            return 0;
        }
        return selectedFilters[key].length;
    };

    const handleClickMta = (filterType: SortType, item: any, index: number) => {
        let eventID = ''
        let eventParams = {}
        let extInfo = {}
        if (filterType === 'mddInfo') {
            eventID = ChANNEL_EVENT_ID.Itravel_Subsidy_CityEntrance
            eventParams = commonMtaParams
        } else if (filterType === 'checkInOutDate') {
            eventID = ChANNEL_EVENT_ID.Itravel_Subsidy_TimeEntrance
            eventParams = commonMtaParams
        } else {
            eventID = ChANNEL_EVENT_ID.Itravel_Subsidy_MainFilter
            eventParams = {
                filterPanelCode: item.key || '',
                filterPanelName: item.label || '',
                index: index + 1,
                ...commonMtaParams
            }
            extInfo = {
                trafficMapInfo: {
                    trafficmap_extfloor_id: item?.key || ''
                }
            }
            console.log('extInfo', extInfo)
        }
     
        sendMtaEvent(eventID, {}, eventParams, false, '', '', extInfo)
    }

    const handelExpoMta = (visible: boolean, item, index) => {
        if (visible && !expoMap?.current?.[item.key]) {
            let eventID = ''
            let eventParams = {}
            const filterType = item.key
            let extInfo = {}
            if (filterType === 'mddInfo') {
                eventID = ChANNEL_EVENT_ID.Itravel_Subsidy_CityEntranceExpo
                eventParams = commonMtaParams
            } else if (filterType === 'checkInOutDate') {
                eventID = ChANNEL_EVENT_ID.Itravel_Subsidy_TimeEntranceExpo
                eventParams = commonMtaParams
            } else {
                eventID = ChANNEL_EVENT_ID.Itravel_Subsidy_MainFilterExpo
                eventParams = {
                    filterPanelCode: item.key || '',
                    filterPanelName: item.label || '',
                    index: index + 1,
                    ...commonMtaParams
                }
                extInfo = {
                    trafficMapInfo: {
                        trafficmap_extfloor_id: item?.key || ''
                    }
                }
            }
            sendMtaEvent(eventID, {}, eventParams, true, '', '', extInfo)
            if (expoMap?.current) {
                expoMap.current[item.key] = true;
            }
        }
    }

    return (
        !isEmpty(filterItems) ?
            <View
                className={styles.container}
                ref={ref}>
                <Image
                    className={styles.borderContainer}
                    src='https://img11.360buyimg.com/imagetools/jfs/t1/318769/26/14357/31720/686b9b0dF2dbc5de2/e91a070e3147ab30.png' />
                <IOScrollView
                    // @ts-ignore
                    className={styles.horizontalScroll}
                    scrollX
                    horizontal
                    showsVerticalScrollIndicator={false}
                    showsHorizontalScrollIndicator={false}
                >
                    {filterItems.map((item, index) => {
                        const type = item.key;
                        const selectedCount = getSelectedCount(item.key);
                        return (
                            <InView
                                key={item.key}
                                onChange={(visible) => {
                                    handelExpoMta(visible, item, index)
                                }}>
                                <FilterItem
                                    key={item.key}
                                    type={item.key}
                                    label={item?.label}
                                    item={item}
                                    index={index}
                                    active={activeFilterType === type || hasSelectedFilters(item.key)}
                                    orderType={activeFilterType === type ? ORDER_TYPES.ASC : ORDER_TYPES.DESC}
                                    onClick={() => handleFilterClick(item.key, item, index)}
                                    selectedCount={selectedCount}
                                />
                            </InView>
                        );
                    })}

                </IOScrollView >
            </View> : null
    );
});

export default memo(FliterBar);

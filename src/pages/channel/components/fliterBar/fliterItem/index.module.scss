
@import "@/assets/theme.scss";

.sortItem {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  // height: 44px;
  margin-right: 18px;
}


.filterArrowDown {
  width: 6px;
  height: 4px;
  margin-left: 4px;
}

.filterArrowUp {
  width: 6px;
  height: 4px;
  margin-left: 4px;
  transform: rotate(180deg);
}

.sortText {
  font-size: 14px;
  text-align: center;
  color: #1A1A1A;
}

.sortTextAct {
  font-size: 14px;
  text-align: center;
  font-weight: var(--fontActWeight);
  color: #FF0400;
}

.filterCountBox {
  width: 14px;
  height: 14px;
  border-radius: 7px;
  margin-left: 4px;
  background-color: #FF0400;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filterCount {
  color: #fff;
  text-align: center;
  font-size: 10px;
  line-height: 14px;
}

import React, { memo } from 'react'
import { View, Text, Image } from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import { ORDER_TYPES } from '../../../../hotelSearch/constants/config'
import { getImg } from '@/Components/Filter/utils'
import { getBoldStyle } from '@/BaseComponents/atoms/utils/boldStyle'
import { ChANNEL_EVENT_ID } from '../../../constants/mtaParamEvents'
import {
  RNTrafficMapView
} from '@jdreact/jdreact-core-lib';
import { M_PAGEID } from '@/common/mta'

interface FilterItemProps {
  type: string
  label: string
  active: boolean
  orderType: number
  onClick: () => void
  selectedCount?: number
  item?: any,
  index?: number
}

const FilterItem: React.FC<FilterItemProps> = ({
  label,
  active,
  orderType,
  onClick,
  selectedCount = 0,
  item,
  index = 0
}) => {

  // 处理点击事件
  const handleClick = () => {
    onClick()
  }

  const getEventId = (item) => {
    const filterType = item?.key || ''
    let eventID = ''
    if (filterType === 'mddInfo') {
      eventID = ChANNEL_EVENT_ID.Itravel_Subsidy_CityEntranceExpo
    } else if (filterType === 'checkInOutDate') {
      eventID = ChANNEL_EVENT_ID.Itravel_Subsidy_TimeEntranceExpo
    } else {
      eventID = ChANNEL_EVENT_ID.Itravel_Subsidy_MainFilterExpo
    }
    return eventID
  }

  const getExt_floor_id = (item) => {
    const filterType = item?.key || ''
    return (filterType === 'checkInOutDate' || filterType === 'mddInfo') ? '' : filterType
  }

  return (
    <RNTrafficMapView
      key={index}
      style={{
        flex: 1,
        display: 'flex'
      }}
      trafficMapInfo={{
        page_id: M_PAGEID.Channel,
        event_id: getEventId(item),
        floor_id: '',
        ext_floor_id: getExt_floor_id(item),
        floor_material_id: '',
        floor_material_name: '',
        floor_position_id: ''
      }} >
      <View
        className={styles.sortItem}
        onClick={handleClick}>
        <Text
          style={getBoldStyle()}
          numberOfLines={1}
          className={active ? styles.sortTextAct : styles.sortText}>
          {label}
        </Text>
        {
          selectedCount > 0 && (
            <View
              className={styles.filterCountBox}>
              <Text
                style={getBoldStyle()}
                className={styles.filterCount}>
                {selectedCount}
              </Text>
            </View>
          )
        }
        {
          active ?
            <Image
              className={orderType === ORDER_TYPES.ASC ? styles.filterArrowDown : styles.filterArrowUp}
              src={getImg('filterArrowActRed')}
            /> :
            <Image
              className={styles.filterArrowDown}
              src={getImg('filterArrow')}
            />
        }
      </View>
    </RNTrafficMapView>
  );
}

export default memo(FilterItem)

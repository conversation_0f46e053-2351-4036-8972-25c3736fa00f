import { getImageUrl } from "@/assets/imgs";
import { View, Image, Text } from "@/BaseComponents/atoms";
import styles from './index.module.scss'
import { safeRun } from '@/Components/Filter/utils'

function NoData(props) {
    const {
        type = 'network_error',
        text = '糟糕，数据走丢了',
        imgUrl,
        showTopCorner = false,
        onClick
    } = props

    const renderNetWorkError = () => (
        <View className={showTopCorner ? styles.wrTopCorner : styles.wr}>
            <Image
                className={styles.emptyIcon}
                src={require('@/assets/images/channel_net_error.png')}
            />
            <Text className={styles.txt}>
                {text}
            </Text>
            <View className={styles.btn}
                onClick={() => safeRun(onClick)}>
                <Text className={styles.btnText}>刷新重试</Text>
            </View>
        </View>
    )

    // 当前城市没有符合条件的商品
    const renderTip = () => (
        <View className={styles.wr}>
            <Image
                className={styles.emptyIcon}
                src={imgUrl ? imgUrl : getImageUrl('emptyIcon')}
            />
            <Text className={styles.txt}>
                {text}
            </Text>
        </View>
    )

    // 主渲染逻辑
    return (
        <>
            {type === 'network_error' && renderNetWorkError()}
            {type === 'tip' && renderTip()}
        </>
    )
}

export default NoData

import { StyleSheet } from "react-native";
import { isWeb, pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import { THEME_FONT } from '@ltfe/ltfe-core-lib/lib/utiles/theme';

const styles = StyleSheet.create({
    container: {
        flex: 1,
        width: '100%',
        marginTop: pt(-1),
        backgroundColor: '#F2F3F5'
    },

    content: {
        width: pt(355),
        height: pt(169),
        borderRadius: pt(8),
        backgroundColor: "#FFFFFF",
        marginLeft: pt(10),
        marginTop: pt(1),
        paddingTop: pt(10),
        paddingBottom: pt(10),
        marginBottom: pt(10),
        paddingLeft: pt(8),
        paddingRight: pt(8),
        flexDirection: 'row',
        zIndex: 2
    },
    leftImg: {
        width: pt(102),
        height: pt(149),
        borderRadius: pt(6)
    },
    right: {
        flex: 1,
        marginLeft: pt(10),
        // width: pt(226),
        flexShrink: 1,
    },
    rightTitleView: {
        flexDirection: 'row',
        alignItems: 'center',
        overflow: 'hidden',
        flexShrink: 1,
    },
    titleTagView: {
        flexDirection: 'row',
        flex: 1,
    },
    titleTagView1:{
        flexDirection: 'row',
    },
    titleTag: {
        width: pt(46),
        height: pt(14),
        marginRight: pt(6),
        marginTop: pt(1),
    },
    title: {
        fontSize: pt(14),
        lineHeight: pt(16),
        marginRight: pt(4),
        color: "#840200",
        fontWeight: THEME_FONT.fontWeight.Medium,
    },
    afterTitleTxt:{ 
        fontSize: pt(14), 
        fontWeight: THEME_FONT.fontWeight.Medium,
        lineHeight: pt(16),
    },
    line: {
        width: 1,
        height: pt(12),
        marginRight: pt(4),
        backgroundColor: '#D9D9D9'
    },
    sellTag: {
        height: pt(16),
        paddingLeft: pt(4),
        paddingRight: pt(4),
        borderRadius: pt(2),
        borderWidth: pt(0.5),
        borderColor: 'rgba(176,129,50,0.4)',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: pt(4)
    },
    sellTagTxt: {
        color: '#B08132',
        fontSize: pt(11),
        lineHeight: isWeb ? pt(12) : undefined
    },
    locationTag: {
        height: pt(14),
        flexDirection: 'row',
        alignItems: 'center',
        flexShrink: 1,
        lineHeight: pt(14),
    },
    locationTagTxt: {
        color: '#8C8C8C',
        fontSize: pt(11),
        lineHeight: pt(14),
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        whiteSpace: 'pre',
        includeFontPadding: false
    },
    locationTagLine: {
        color: '#8C8C8C',
        fontSize: pt(11),
        lineHeight: pt(14),
        textAlignVertical: 'center',
        top: isWeb ? pt(-1) : 0,
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        whiteSpace: 'pre'
    },

    tagWr: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center'
    },
    tagWrTag: {
        display: 'flex',
        flexDirection: 'row',
        marginTop: pt(4),
        alignItems: 'center'
    },
    tagWrMarginTop: {
        marginTop: pt(6)
    },
    location_text: {
        lineHeight: pt(14),
        fontSize: pt(12),
        fontWeight: '400',
        color: '#505259',
    },
    location_no: {
        flexShrink: 0,
    },
    location: {
        flexShrink: 1,
        flexGrow: 0
    },

    discountTag: {
        marginLeft: pt(6),
        marginBottom: pt(-5),
        marginTop: pt(8),
        zIndex: 1,
        backgroundColor: 'rgba(0,0,0,0)'
    },
    discountTagBg: {
        flexDirection: 'row', 
        overflow: 'hidden',
        borderTopRightRadius: pt(2),
        borderBottomRightRadius: pt(2),
    },
    discountTagImg1: {
        height: pt(20),
        width: pt(8)
    },
    discountTagImg2: { 
        height:  pt(20),  
        marginLeft: pt(-1),
        borderTopRightRadius: pt(2),
        borderBottomRightRadius: pt(2),
    },
    discountTagView: {
        position: 'absolute',
        height: pt(20),
        alignItems: 'center',
        paddingTop: pt(2),
        paddingHorizontal: pt(5),
    },
    discountTagTxt: {
        color: '#FF0400',
        fontSize: pt(10),
        // fontWeight: 'Semibold',
        fontWeight: THEME_FONT.fontWeight.Medium,
        lineHeight: pt(12),
    },
    priceBgImg: {
        // width: pt(227),
        height: pt(47),
        position: 'absolute',
        borderRadius: pt(6)
    },
    priceBgView: {
        height: pt(46),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    priceView: {
        color: '#FFFFFF',
        fontSize: pt(12),
        marginLeft: pt(4),
        marginBottom: pt(-2)
    },
    price: {
        color: '#FFFFFF',
        fontSize: pt(24),
        fontFamily: 'JDZhengHT-Regular'
    },
    priceDesc: {
        color: '#FFFFFF',
        fontSize: pt(10),
        fontWeight: 'bold',
        fontFamily: 'JDZhengHT-Regular',
        marginLeft: pt(2)
    },
    oPrice: {
        marginLeft: pt(6),
        marginBottom: pt(4),
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: pt(11),
        lineHeight: pt(12),
        textDecorationLine: 'line-through',
        fontFamily: 'JDZhengHT-Regular'
    },
    btnQiang: {
        width: pt(70),
        justifyContent: 'center',
        alignItems: 'center'
    },
    qImg: {
        width: pt(23),
        height: pt(24)
    },
    qTxt: {
        color: "#963804",
        fontSize: pt(10)
    },
    linearGradient: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: pt(44),
        zIndex: 1
    }
})

export default styles;
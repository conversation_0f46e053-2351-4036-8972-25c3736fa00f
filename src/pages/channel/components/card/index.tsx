import React, { useEffect, useState } from 'react';
import { Image, TouchableOpacity, View, Text, Platform, Dimensions } from 'react-native';
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import styles from './style';
import { getClippedText } from './helper';
import { isWeb } from '@/common/common';
import {
    RNTrafficMapView
} from '@jdreact/jdreact-core-lib';
import { M_PAGEID } from '@/common/mta'
import {ChANNEL_EVENT_ID} from '../../constants/mtaParamEvents';

// import { isEmpty } from '@/utils/isType';
// import TatoText from '@tarojs/components';
// import CSSStyle from './index.module.scss';
// import { JDText } from '@jdreact/jdreact-core-lib';
import LinearGradient from '@jdreact/jdreact-core-linear-gradient'
import { isEmpty } from '@/utils/isType';

const deviceWidth = Dimensions.get('window').width;

const ChannelCard = ({
    item,
    index,
    priceConfigVO,
    cardOnClick
}) => {
    
    const [onRowTitle, setOnRowTitle] = useState('')  // 酒店名称
    const [titleAfterList, setTitleAfterList] = useState<string[]>([])  // 房型名称数组-可能会两行展示，会将房型名称切开后放进去
    const [showTwoRowTitle, setShowTwoRowTitle] = useState(true)  //  是否展示第二行房型
    const [discountPriceWidth, setDiscountPriceWidth] = useState(0)  // 优惠价格文字长度用于计算优惠气泡长度
    const { hotelCardVO } = item;
    const { hotelLocationDistance = [], hotelTitleBefore = [], hotelScore = [] } = hotelCardVO?.promotionTagListMap || {};
    // 列表内边距左右各10pt、卡片内边距左右各8pt、图片宽度102pt、商品图距右边内容距离为10pt
    const rigthContentWidth = deviceWidth - pt(20) - pt(16) - pt(102) - pt(10);

    useEffect(() => {
        let oneRowWidth = rigthContentWidth;
        let onRowTitle: string = '';
        let titleAfterList: string[] = [];
        if (!isEmpty(hotelTitleBefore) && hotelTitleBefore?.length > 0) {
            oneRowWidth = oneRowWidth - pt(46) - pt(6);
        }

        const title = hotelCardVO?.name || "";
        const hotelTitleAfter = item?.hotelCardVO?.promotionTagListMap?.['hotelTitleAfter'] || [];
        const roomTypeName: string = !isEmpty(hotelTitleAfter) && !isEmpty(hotelTitleAfter[0]) ? hotelTitleAfter[0]?.listShowName : '';
        const title1Obj = getClippedText(title, oneRowWidth, pt(14));

        // 右侧可用长度 - getClippedText返回的酒店名称长度 小于 竖线+一个字的长度时， 第一行只展示tag和标题
        if (oneRowWidth - title1Obj?.totalWidth <= pt(23)) {  // 第一行只显示tag和酒店名称
            onRowTitle = title;
            titleAfterList = [roomTypeName]
        }
        // 右侧可用长度 - getClippedText返回的酒店名称长度 大于 竖线+一个字的长度时， 第一行需要展示 tag、标题、房型
        if (oneRowWidth - title1Obj?.totalWidth > pt(23)) {
            const titile2Space = oneRowWidth - title1Obj.totalWidth - pt(9);
            const title2Obj = getClippedText(roomTypeName, titile2Space, pt(14));
            onRowTitle = title;
            // 房型名称长度 大于 返回的房型名称长度 = 证明房型标题未完全展示，需要两行来展示房型
            if (roomTypeName?.length && title2Obj?.result?.length && roomTypeName?.length > title2Obj?.result?.length) {
                setShowTwoRowTitle(true);
                const onwRowtitle2Length = title2Obj.result.length;
                titleAfterList = [roomTypeName.slice(0, onwRowtitle2Length), roomTypeName.slice(onwRowtitle2Length)];
            } else {
                setShowTwoRowTitle(false);
                titleAfterList = title2Obj?.result ? [title2Obj.result] : [];
            }
        }

        setOnRowTitle(onRowTitle);
        setTitleAfterList(titleAfterList)
    }, [hotelCardVO?.name])

    const handleDiscountPriceLayout = (event) => {
        let width = event?.nativeEvent?.layout?.width || 0;
        width = Platform.OS === 'android' ? Number(width) + pt(2) : Number(width);
        setDiscountPriceWidth(width);
    };

    return (
        <RNTrafficMapView key={index} style={styles.container} trafficMapInfo={{
            page_id: M_PAGEID.Channel,
            event_id: ChANNEL_EVENT_ID.ItravelSubsidyHotelExpo,
            floor_id: '',
            ext_floor_id: '',
            floor_material_id: '',
            floor_material_name: '',
            floor_position_id: String(index)
        }}>
            {index === 1 && <LinearGradient
                start={{ x: 0, y: 0 }}
                end={{ x: 0, y: 1 }}
                colors={isWeb ? ['#FFFFFF', '#F2F3F5', '#F2F3F5'] : ['#FFFFFF', '#F2F3F5']}
                style={styles.linearGradient}
            >
            </LinearGradient>}
            <TouchableOpacity
                style={styles.content}
                activeOpacity={1}
                onPress={() => cardOnClick(item, index)}>
                <Image style={styles.leftImg} source={{ uri: hotelCardVO?.picUrl || '' }} />
                <View style={[styles.right, { width: rigthContentWidth }]}>
                    <View>
                        <View style={styles.rightTitleView}  >
                            <View style={titleAfterList.length == 1 && showTwoRowTitle ? styles.titleTagView : styles.titleTagView1}>
                                {!isEmpty(hotelTitleBefore) && hotelTitleBefore?.length > 0 &&
                                    <Image style={[styles.titleTag, { width: pt(Number(hotelTitleBefore[0]?.imageWidth || 46)), height: pt(Number(hotelTitleBefore[0]?.imageHeight || 14)) }]} source={{ uri: hotelTitleBefore[0]?.imageUrl || '' }} />}
                                <Text style={[styles.title, titleAfterList.length == 1 && showTwoRowTitle ? { flex: 1 } : {}]} numberOfLines={1} ellipsizeMode={"tail"} >
                                    {onRowTitle}
                                </Text>
                            </View>
                            {(titleAfterList.length == 2 || !showTwoRowTitle) && !!titleAfterList[0] ? <View style={styles.line} /> : null}
                            {(titleAfterList.length == 2 || !showTwoRowTitle) && !!titleAfterList[0] ? <Text style={styles.afterTitleTxt} numberOfLines={1} >{titleAfterList[0] || ''}</Text> : null}
                        </View>
                        {!!showTwoRowTitle && titleAfterList?.length > 0 && <View style={[styles.rightTitleView, { marginTop: pt(2), width: pt(226) }]}>
                            <Text style={styles.afterTitleTxt} numberOfLines={1} >{titleAfterList.length < 2 ? titleAfterList[0] : titleAfterList[1]}</Text>
                        </View>}
                    </View>
                    <View style={{ flexDirection: 'row', marginTop: pt(8) }} >
                        {!isEmpty(hotelScore) && hotelScore?.length > 0 &&
                            hotelScore.map((item, index) => {
                                if (item.listShowName !== '') {
                                    return <View key={item.listShowName + index} style={styles.sellTag}>
                                        <Text style={styles.sellTagTxt}>{item.listShowName}</Text>
                                    </View>
                                } else {
                                    return <View key={index} />
                                }
                            })
                        }
                    </View>

                    <View style={{ flexDirection: 'row', paddingTop: pt(7), width: rigthContentWidth }} >
                        {!isEmpty(hotelLocationDistance) && hotelLocationDistance?.length > 0 &&
                            hotelLocationDistance.map((item, index) => {
                                return <Text key={item?.listShowName + index} style={styles.locationTag} numberOfLines={1} ellipsizeMode='middle'>
                                    {/* <Text style={styles.locationTagTxt} numberOfLines={1}>{item.listShowName + `${hotelLocationDistance.length - 1 !== index ? "  |  " : ""}`} */}
                                    <Text style={styles.locationTagTxt} numberOfLines={1}>{item.listShowName}
                                        {hotelLocationDistance.length - 1 !== index ? <Text style={[styles.locationTagTxt, { color: 'rgba(140,140,140,0.3)' }]}>{" | "}</Text> : null}
                                    </Text>
                                </Text>
                            })
                        }
                    </View>

                    <View style={{ flex: 1 }} />

                    {!!hotelCardVO?.discountPrice && !!priceConfigVO?.discountPriceBubbleBgUrl && <View style={styles.discountTag}>
                        <View style={[styles.discountTagBg, { maxWidth: pt(discountPriceWidth + 2),}]}>
                            <Image style={styles.discountTagImg1} source={{ uri: 'https://img10.360buyimg.com/imagetools/jfs/t1/317013/27/14634/1414/686bcadeF1cd8f46f/cde38b51d5f26de1.png' }}></Image>
                            <Image style={[styles.discountTagImg2, { width: pt(discountPriceWidth) - pt(8) }]} resizeMode='stretch' source={{ uri: 'https://img11.360buyimg.com/imagetools/jfs/t1/296919/33/21047/4747/686bcadeFec95c698/11b69a2f77283cc0.png' }}></Image>
                        </View>
                        <View style={styles.discountTagView} onLayout={handleDiscountPriceLayout}>
                            <Text style={[styles.discountTagTxt, { color: priceConfigVO?.discountPriceColor || "" }]}>{hotelCardVO?.discountPriceText}</Text>
                        </View>
                    </View>}

                    <View>
                        <Image style={[styles.priceBgImg, { width: !!priceConfigVO?.priceBgWidth ? pt(Number(priceConfigVO?.priceBgWidth || 227)) : rigthContentWidth }]} source={{ uri: priceConfigVO?.priceBgUrl || '' }}></Image>
                        <View style={styles.priceBgView}>
                            <View style={{ marginTop: Platform.OS === 'android' ? pt(4) : 0 }}>
                                {!!hotelCardVO?.price && <View style={{flexDirection: 'row', alignItems: 'flex-end'}}>
                                    <Text style={styles.priceView}>￥
                                        <Text style={styles.price}>{String(hotelCardVO?.price)}</Text>
                                    </Text>
                                    <Text style={[styles.priceDesc, { fontSize: pt(Number(priceConfigVO?.priceAfterTextFontSize || 10)), color: priceConfigVO?.priceAfterTextColor || "#FFF" }]}>{priceConfigVO?.priceAfterText || ""}</Text>
                                </View>}
                                {!!hotelCardVO?.originPrice && <Text style={styles.oPrice}>￥{hotelCardVO.originPrice}</Text>}
                            </View>
                            <View style={styles.btnQiang}>
                                <Image style={[styles.qImg, { width: pt(Number(priceConfigVO?.buyImgWidth)) || pt(23), height: pt(Number(priceConfigVO?.buyImgHeight)) || pt(24) }]} source={{ uri: priceConfigVO?.buyImgUrl || '' }}></Image>
                                {!!hotelCardVO?.roomStockText && <Text numberOfLines={1} style={[styles.qTxt, { color: priceConfigVO?.marketPriceColor || "#963804" }]}>{hotelCardVO?.roomStockText}</Text>}
                            </View>
                        </View>
                    </View>
                </View>
            </TouchableOpacity>
        </RNTrafficMapView>
    )
}


export default ChannelCard;


// 中文符号正则
const CHINESE_PUNCTUATION = /[，。、；：？！“”‘’（）【】「」『』《》〈〉〔〕—…·]/;
// 英文符号正则
const ENGLISH_PUNCTUATION = /[,.;:?!\-\[\](){}'"`~@#$%^&*_+=|\\<>/]/;

/**
 * 根据固定宽度估算可显示的文本
 * @param text 原始文本
 * @param maxWidth 最大宽度(单位：像素)
 * @param fontSize 字体大小
 * @param fontFamily 字体系列（可选）
 * @returns 截断后的文本
 */
export const getClippedText = (
    text: string,
    maxWidth: number,
    fontSize: number = 14,
    fontFamily: string = ''
): any => {
    // 添加安全边距，避免文字露出半截
    // 安全边距设置为字体大小的30%
    // const safetyMargin = fontSize * 0;
    // 应用安全边距
    // maxWidth = maxWidth - safetyMargin;
    if (!text) return '';
    
    // 字符宽度估算因子
    const widthFactors = {
        chinese: 1.05,       // 中文字符 - 略微增加系数
        chinesePunctuation: 0.85, // 中文符号 - 增加系数
        english: 0.65,       // 英文字母和数字
        englishPunctuation: 0.4,  // 英文符号
        specialPunctuation: 1.0,  // 特殊中文符号如【】《》等
        other: 0.7          // 其他字符
    };

    // 特殊符号正则
    const SPECIAL_PUNCTUATION = /[【】《》「」『』〖〗]/;
    
    // 计算字符宽度
    const getCharWidth = (char: string): number => {
        if (/[\u4e00-\u9fa5]/.test(char)) {
            return fontSize * widthFactors.chinese;
        } else if (SPECIAL_PUNCTUATION.test(char)) {
            // 特殊符号如【】等使用更高的宽度系数
            return fontSize * widthFactors.specialPunctuation;
        } else if (CHINESE_PUNCTUATION.test(char)) {
            return fontSize * widthFactors.chinesePunctuation;
        } else if (ENGLISH_PUNCTUATION.test(char)) {
            return fontSize * widthFactors.englishPunctuation;
        } else if (/[a-zA-Z0-9]/.test(char)) {
            return fontSize * widthFactors.english;
        } else {
            return fontSize * widthFactors.other;
        }
    };
    
    // 记录实际使用宽度的日志函数
    // const logWidthUsage = (text: string, maxWidth: number, result: string) => {
    //     if (result !== text) {
    //         console.log(`文本截断: "${text}" -> "${result}", maxWidth=${maxWidth.toFixed(2)}, fontSize=${fontSize}`);
    //     }
    // };

    // 估算每个字符的宽度并累加
    let totalWidth = 0;
    let result = '';

    for (let i = 0; i < text.length; i++) {
        const char = text[i];
        const charWidth = getCharWidth(char);

        // 如果添加当前字符后会超出最大宽度，则停止添加
        if (totalWidth + charWidth > maxWidth) {
            // 如果当前是标点符号，可以尝试不包含它
            // 这样可以避免在行尾留下孤立的标点
            if (i > 0 && (CHINESE_PUNCTUATION.test(char) || ENGLISH_PUNCTUATION.test(char))) {
                // 如果前一个字符不是标点，则保留结果
                if (!CHINESE_PUNCTUATION.test(text[i-1]) && !ENGLISH_PUNCTUATION.test(text[i-1])) {
                    break;
                }
            } else {
                break;
            }
        }

        totalWidth += charWidth;
        result += char;
    }

    // 确保不会显示半个字
    // if (result !== text) {
    //     // 在日志中记录截断情况
    //     logWidthUsage(text, maxWidth, result);
    // }
    
    return {result,totalWidth};
};
import { useRef, useEffect, memo, forwardRef, useImperativeHandle } from 'react'
import { View } from '@/BaseComponents/atoms'
import { IOFlatList } from '@/BaseComponents/IntersectionObserver'
import { InView } from '@/BaseComponents/IntersectionObserver'
import styles from './index.module.scss'
import ChannelCard from '../card'
import { Loading } from '@/BaseComponents/atoms'
import { safeRun } from '@/Components/Filter/utils'
import ListTips, { TIP_TYPE } from '@/pages/result/widgets/ListBottomTip'
import { isAndroid } from '@/common/common'
import { LoadMoreStatus } from '../../../hotelSearch/constants/config'
import NoData from '../noData'
import { CardType } from '../../constants/type'
import { pt, statusBarHeight, safeAreaBottom } from '@ltfe/ltfe-core-lib/lib/utiles'
import { SortType } from '../../../hotelSearch/constants/config'
import FliterBar from '../fliterBar'
import HotelSkeleton from '../../../result/widgets/Hotel/HotelSkeleton'
import { isNumber } from '@/utils/isType'
import { ChANNEL_EVENT_ID } from '../../constants/mtaParamEvents'

interface ListProps {
    firstLoad: boolean
    data: any[]
    loading: boolean
    hasMore: boolean
    onLoadMore: () => void
    onReload: () => void
    cardOnClick?: (info: any, index: number) => void
    mtaExpo?: (isExposure, eventId, eventData, pageParam, eventParam, extData) => void
    mtaClick?: (params: any) => void
    loadMoreStatus?: LoadMoreStatus
    searchParams?: any;
    commonMtaParams?: any;
    onFilterClick: (filterType: SortType) => void;
    selectedFilters?: {
        [key: string]: any[];
    };
    activeFilterType: any;
    baibuPriceConfigVO?: any;
    filterPanelVOList?: any[];
    handleScrollCallback?: () => void;
}

export interface ListRefType {
    scrollTo: (y?: number, animated?: boolean) => void;
    scrollToTop: () => void;
}

const List = forwardRef<ListRefType, ListProps>(({
    firstLoad,
    data,
    loading,
    hasMore,
    onLoadMore,
    onReload,
    cardOnClick,
    mtaExpo,
    loadMoreStatus,
    searchParams,
    commonMtaParams,
    onFilterClick,
    selectedFilters,
    activeFilterType,
    filterPanelVOList,
    baibuPriceConfigVO,
    handleScrollCallback
}, ref) => {
    const listRef = useRef<any>(null)
    const expoEndData = useRef<any>({}) // 埋点信息
    const refLoading = useRef(false)
    const currentOffsetY = useRef(0);
    const stickTop = pt(60)
    const placeHolderHeight = statusBarHeight + pt(38) + pt(60)
    const listMariginTop = statusBarHeight + pt(36);

    useImperativeHandle(ref, () => ({
        scrollTo: (y = 0, animated = true) => {
            if (currentOffsetY.current > stickTop) {
                return;
            }
            if (listRef?.current && listRef?.current?.scrollToOffset) {
                listRef.current.scrollToOffset({ offset: y, animated })
            }
        },
        scrollToTop: () => {
            if (currentOffsetY.current > stickTop) {
                return;
            }
            if (listRef?.current && listRef?.current?.scrollToOffset) {
                listRef.current.scrollToOffset({ offset: stickTop, animated: true })
            }
        },
        isTop: () => {
            return currentOffsetY.current >= stickTop
        }
    }))

    // 监听滚动事件
    const handleScroll = (event) => {
        const y = event?.nativeEvent?.contentOffset?.y ?? event?.target?.scrollTop ?? 0;
        if (currentOffsetY.current > y) {
            if (handleScrollCallback) {
                handleScrollCallback()
            }
        }
        currentOffsetY.current = y;
    };

    useEffect(() => {
        if (loading) {
            if (currentOffsetY.current > stickTop) {
                listRef?.current?.scrollToOffset({ offset: stickTop, animated: false })
            }
        }
    }, [loading])


    useEffect(() => {
        let st2
        if (refLoading.current) {
            st2 = setTimeout(() => {
                refLoading.current = false
            }, 20)
        }
        return () => {
            clearTimeout(st2)
        }
    }, [data])

    const renderItem = (info) => {
        const { item, index } = info
        const { cardType, height, showTopCorner, countSummaryVO } = item
        const cardUuid = cardType || item?.hotelCardVO?.id || index
        switch (cardType) {
            case CardType.FilterBar:
                return <FliterBar
                    searchParams={searchParams}
                    commonMtaParams={commonMtaParams}
                    selectedFilters={selectedFilters}
                    activeFilterType={activeFilterType}
                    filterPanelVOList={filterPanelVOList}
                    onFilterClick={onFilterClick}
                />
            case CardType.NoData:
                return <NoData
                    type='tip'
                    text={countSummaryVO?.noResultShowText}
                    imgUrl={countSummaryVO?.noResultShowBgImg}
                />
            case CardType.Error:
                return <NoData
                    showTopCorner={showTopCorner}
                    type='network_error'
                    onClick={onReload}
                />
            case CardType.PlaceHolder:
                if (isNumber(height)) {
                    return <View style={{ height, width: '100%' }}></View>
                }
                return null
            default:
                return <InView key={cardUuid} onChange={(visible) => {
                    const eventData = item?.eventData
                    if (visible && !expoEndData?.current?.[item?.hotelCardVO?.id]) {
                        const extInfo = {
                            trafficMapInfo: {
                                trafficmap_position_id: String(index)
                            }
                        }
                        mtaExpo && mtaExpo(true, ChANNEL_EVENT_ID.ItravelSubsidyHotelExpo, { ...eventData }, '', '', extInfo)
                        if (expoEndData?.current) {
                            expoEndData.current[item?.hotelCardVO?.id] = true;
                        }
                    }
                }} index={index}>
                    <ChannelCard
                        index={index}
                        key={cardUuid}
                        cardOnClick={cardOnClick}
                        priceConfigVO={baibuPriceConfigVO}
                        item={item} />
                </InView>
        }
    }

    const footerRender = (loadMoreStatus) => {
        switch (loadMoreStatus) {
            case LoadMoreStatus.IDLE:
                return <View style={{ marginBottom: safeAreaBottom + pt(30), width: '100%' }}></View>
            case LoadMoreStatus.LOADING:
                return <View style={{ marginBottom: safeAreaBottom + pt(30), width: '100%'}}>
                    <Loading />
                </View>
            case LoadMoreStatus.ERROR:
                return <ListTips
                    customStyle={{ marginBottom: safeAreaBottom }}
                    type={TIP_TYPE.RETRY_BY_NETERROR}
                    onRetry={() => {
                        safeRun(onLoadMore)
                    }} />
            case LoadMoreStatus.NO_MORE:
                return <ListTips
                    customStyle={{ marginBottom: safeAreaBottom }}
                    title={'暂无更多商品～'} />
            default:
                return null
        }
    }

    if (!loading && data?.length === 0) {
        return <View />
    }

    return (
        (firstLoad && loading) ?
            <View
                style={{
                    marginTop: placeHolderHeight,
                    width: '100%',
                    height: '100%',
                    backgroundColor: '#fff',
                    borderTopLeftRadius: pt(12),
                    borderTopRightRadius: pt(12)
                }}>
                <View
                    style={{
                        height: pt((10)),
                        width: '100%'
                    }}
                />
                <HotelSkeleton />
            </View>
            : <IOFlatList
                // @ts-ignore
                className={styles.listBox}
                style={{
                    flex: 1,
                    marginTop: listMariginTop,
                    borderTopLeftRadius: pt(12),
                    borderTopRightRadius: pt(12)
                }}
                onScroll={handleScroll}
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                bounces={false}
                scrollEventThrottle={16}
                scrollEnabled={true}
                stickyHeaderIndices={[1]}
                data={data}
                ListHeaderComponent={() => {
                    return <View
                        style={{
                            height: stickTop,
                            width: '100%'
                        }} />
                }}
                renderItem={renderItem}
                keyExtractor={(_, index) => index.toString()}
                decelerationRate={isAndroid ? 0.985 : 0.994}
                windowSize={21}
                initialNumToRender={10}
                onEndReached={() => {
                    if (!refLoading.current && hasMore && loadMoreStatus !== LoadMoreStatus.LOADING) {
                        refLoading.current = true
                        safeRun(onLoadMore)
                    }
                }}
                ListFooterComponent={() => footerRender(loadMoreStatus)}
                ref={listRef}
            />
    )
})

export default memo(List)

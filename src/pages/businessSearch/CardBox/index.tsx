
import React, {memo, useState} from 'react'
import {View, Text} from '@/BaseComponents/atoms'
import Item from '../SelectItem'
import Expend from '../Expend'
import styles from './index.module.scss'
import classNames from 'classnames'

function CardBox(props) {
    const {data = {}, onClick, handleFilterExp} = props
    const {maxDepth = 1, title,type, items} = data || {}
    // 维护所有分组的展开状态
    const [expandedState, setExpandedState] = useState({})
    
    // 展开收起
    const onChange = (params) => {
        const [key, value] = Object.entries(params)[0]
        setExpandedState(prev => ({
            ...prev,
            [key]: value
        }))
    }

    if (!items?.length) {
        return null
    }


    // 递归渲染函数，可以处理任意深度
    const renderByDepth = (currentItems, currentTitle, currentDepth, maxDepth = 1, type) => {
        // 如果已经到达最大深度，渲染最终的选项列表
        if (currentDepth >= maxDepth) {
            const isExpanded = !!expandedState[type]
            const displayItems = isExpanded ? currentItems : currentItems.slice(0, 8)
            return (
                <View className={styles.cardBox} key={currentTitle}>
                    <View className={styles.cardBoxContent}>
                        <View className={styles.cardTitleBox}>
                            <Text className={classNames('blod', styles.cardBoxTitle)}>{currentTitle}</Text>
                            {
                                !!(currentItems?.length > 8) && 
                                <Expend 
                                    expended={isExpanded} 
                                    type={type} 
                                    onChange={(params) => onChange(params)} 
                                />
                            }
                            
                        </View>
                        <View className={styles.cardContentBox}>
                            {
                                displayItems.map((item, index) => {
                                    return item?.value ? 
                                            <Item 
                                                key={index} 
                                                index={index} 
                                                dataItem={item} 
                                                word={item?.value}
                                                onClick={(info) => { onClick && onClick(info)}}
                                                mtaExpo={handleFilterExp}
                                            /> : null
                                })
                            }
                        </View>
                    </View>
                </View>
            )
        }
        
        // 如果还没到最大深度，继续递归渲染子项
        return (
            <>
                {currentItems.map((item, index) => {
                    // 检查是否有子项
                    if (item.items && item.items.length > 0) {
                        return renderByDepth(item.items, item.title, currentDepth + 1, maxDepth, item?.type)
                    } else {
                        return null
                    }
                })}
            </>
        )
    }

    // 从第一层开始递归渲染
    return renderByDepth(items, title, 1, maxDepth ?? 1, type)
}

export default memo(CardBox)
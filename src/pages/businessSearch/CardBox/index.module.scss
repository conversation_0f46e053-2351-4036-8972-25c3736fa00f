@import "@/assets/theme.scss";

.cardBox {
    background-color: var(--primaryBgColor);
    padding-bottom: 8px;
}

.cardBoxContent {
    background-color: #fff;
    padding: 16px 16px 2px 16px;
}

.cardBoxTitle {
    font-size: 16px;
    color: #0e0e0e;
    font-weight: 500;
}

.cardTitleBox {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.cardContentBox {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin-top: 12px;

}

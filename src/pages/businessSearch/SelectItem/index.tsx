import {Text} from '@/BaseComponents/atoms'
import {InView} from '@/BaseComponents/IntersectionObserver'
import React, {memo} from 'react'
import styles from './index.module.scss'
import {safeRun} from '@/Components/Filter/utils'
interface Props {
    lineCount?: number
    index?: number
    word: string
    numberOfLines?: number
    dataItem?: any
    onClick?: (params: any) => void
    mtaExpo?: (visible: boolean, data: any, index?: number) => void
}
function SelectItem(props: Props) {
    const {lineCount = 4, index, word, numberOfLines = 2, dataItem, onClick, mtaExpo} = props
    return (
        <InView className={styles.itemBox} style={{width: `${100 / lineCount}%`}} onChange={(visible)=> {
            mtaExpo && mtaExpo(visible, dataItem, index)
        }}>
            <Text 
                className={styles.itemContent} 
                numberOfLines={numberOfLines} 
                onClick={() => {
                    onClick && onClick(dataItem)
                }}
                style={{borderRight: typeof index === 'number' && (index + 1) % lineCount === 0 ? 'none' : ''}}
            >
                {word}
            </Text>
        </InView>
    )
}

export default memo(SelectItem)

@import "@/assets/theme.scss";

.cardBox {
    background-color: var(--primaryBgColor);
    padding-bottom: 8px;
}

.cardBoxContent {
    background-color: #fff;
    padding: 16px;
}

.cardBoxTitle {
    font-size: 16px;
    color: #0e0e0e;
    font-weight: 500;
}

.cardTitleBox {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.cardContentBox {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin-top: 12px;

}

.dialogTitle {
    color: #000;
    font-size: 14px;
    text-align: center;
    font-weight: 500;
}

.dialogBtnContent {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 20px;
}

.commonBtn {
    border-radius: 6px;
    height: 35px;
    width: 125px;
}

.commonBtnText {
    text-align: center;
    line-height: 35px;
}

.cancelBtn {
    @extend .commonBtn;
    background-color: #fff;
    border-width: 1px;
    border-color: #013B94;

}

.cancelText {
    @extend .commonBtnText;
    color: #013B94;
}

.conformBtn {
    @extend .commonBtn;
    background-color: #013B94;
}

.conformText {
    @extend .commonBtnText;
    color: #fff;
}

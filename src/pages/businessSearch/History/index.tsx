import {View, Text } from '@/BaseComponents/atoms'
import  { memo, useState } from 'react'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import classNames from 'classnames'
import DelIcon from '../DelIcon'
import LexicalChunks from '@/pages/index/widgets/LexicalChunks'
import Dialog from '@/BaseComponents/Dialog'
import styles from './index.module.scss'


function History(props) {
    const { data = [], onClick, onDeleteAll, handleHistoryExp } = props
    const [delFlag, setDelFlag] = useState(false)
    const [showDialog, setShowDialog] = useState(false) // 是否展示弹窗
    const onClickDelIcon = () => {
        setDelFlag(!delFlag)
    }

    const handleShowDialogState = (flag) => {
        setShowDialog(flag)
    }

    const handleDelAll = () => {
        onClickDelIcon()
        onDeleteAll && onDeleteAll()
    }

    if (data?.length === 0) {
        return null
    }
    return (<>
        <View className={styles.cardBox}>
            <View className={styles.cardBoxContent}>
                <View className={styles.cardTitleBox}>
                    <Text className={classNames('blod', styles.cardBoxTitle)}>历史记录</Text>
                    <DelIcon delFlag={delFlag} onChange={onClickDelIcon} delAll={() => {setShowDialog(true)}} />
                </View>
                <LexicalChunks
                    data={data}
                    showIcon={delFlag}
                    handleClick={(info, index) => { onClick && onClick({...info, clickIndex: index})}}
                    handleIconClick={(info) => { onClick && onClick(info, 'delete')}}
                    newMtaExp={handleHistoryExp}
                    itemTextStyle={{color: '#505259'}}
                />
            </View>
        </View>

        <Dialog
                show={showDialog}
                onClose={() => handleShowDialogState(false)}
                dialogStyle={{ padding: pt(20)}}
            >
                <View>
                    <Text className={styles.dialogTitle}>确认删除全部历史记录？</Text>
                    <View className={styles.dialogBtnContent}>
                        <View
                            className={styles.cancelBtn}
                            onClick={() => handleShowDialogState(false)}
                        >
                            <Text className={styles.cancelText}>取消</Text>
                        </View>
                        <View
                            className={styles.conformBtn}
                            onClick={handleDelAll}
                        >
                            <Text className={styles.conformText}>确认</Text>
                        </View>
                    </View>
                </View>
            </Dialog>
    </>)
}


export default memo(History)

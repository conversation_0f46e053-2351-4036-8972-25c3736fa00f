import { View } from '@/BaseComponents/atoms'
import { StyleSheet } from 'react-native'
import  { memo } from 'react'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import CardBox from '../CardBox'
import Block from '../Block'


function Group(props) {
    const { data = [], onClick, handleFilterExp } = props
    
    if (data?.length === 0) {
        return null
    }
    return (
        <View>
            {
                data?.map((group, index) => {
                    return (
                        <CardBox
                            key={index} 
                            data={group} 
                            onClick={(info) => { onClick && onClick(info)}}
                            handleFilterExp={handleFilterExp}
                        />
                    )
                })
            }
            <Block word={'没有更多数据了'} style={styles.bottomTip} />
        </View>
    )
}

const styles = StyleSheet.create({
    bottomTip: {
        padding: pt(40),
        textAlign: "center",
        fontSize: pt(14),
        color: "#5E6880"
    }
})

export default memo(Group)

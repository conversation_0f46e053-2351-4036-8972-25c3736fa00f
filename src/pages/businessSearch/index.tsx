import Taro from "@tarojs/taro"
import { StyleSheet } from 'react-native'
import withPage, {BasePageContext} from '@/common/withPage'
import {useContext, useEffect, useState, useRef, memo} from 'react'
import {mtaEp, mtaPv, newMta, M_PAGE, M_PAGEID, M_EVENTID } from '@/common/mta'
import { isEmpty } from "@/utils/isType"
import { deCodeDebBase64ParseSafe } from '@/Components/utils/index'
import useFetch, {BIZ_VERSION, PLAT_FORM_CODE} from '@/common/useFetch'
import IdleQueue from '@/utils/IdleQueue'
import { getCommonData, getSugParams } from "./utils" 
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import { reportInfo } from '@/common/reporter'
import { errorCodeConstantMapping, ErrortType } from '@/common/reporter/errorMapping';

import { JDLoadingView } from '@jdreact/jdreact-core-lib';
import {Sug} from '@ltfe/ltfe-core-lib/lib'
import {IOScrollView} from '@/BaseComponents/IntersectionObserver'
import { View } from '@/BaseComponents/atoms'
import localStorage from '@/utils/LocalStorage'
import SearchBar from '@/pages/index/widgets/SearchBar'
import History from './History'
import Group from './Group'
import NoData from '@/pages/result/widgets/NoData'
import Styles from './index.module.scss'

const Search = () => {
    const {apiFetch} = useFetch()
    const { basePageInfo, setBackParams, getPageParams } = useContext(BasePageContext)
    const args = getPageParams()
    const searchInfo = deCodeDebBase64ParseSafe(args?.searchInfo || '{}')
    const { 
        travelInfo, 
        queryMode, 
        distance, 
        isFromVResult, 
        keyword, 
        channelId, 
        priceChannel, 
        fromSource,
        virtualLocation,
        latitude,
        longitude,
        posAreaId,
    } = searchInfo || {}
    const {travelId = '', processInstanceId = ''} = travelInfo || {}
    

    const [locationLoading, setLocationLoading] = useState(true)
    const [showDefault, setShowDefault] = useState(false)
    const [commonUserAction, setCommonUserAction] = useState(null)
    const [searchWord, setSearchWord] = useState(keyword || '')
    const [mddInfo, setMddInfo] = useState(searchInfo?.mddInfo || {})
    const [filters, setFilters] = useState([])
    const [history, setHistory] = useState<any>([])
    const mtaMapRef = useRef({})
    // 是拼房并且不是从VResult跳转过来的，存一份历史记录，否则存另一份历史记录
    const storageKey = !isFromVResult && (queryMode === '1' || queryMode === '2') ? `travel_splicing_orders_${travelId}_${processInstanceId}_businessSearchHistory` : `travel_${travelId}_${processInstanceId}_businessSearchHistory`
    const commonData = getCommonData(channelId, latitude, longitude, posAreaId, fromSource, priceChannel)
    const sugListParams = getSugParams(searchInfo, {bizVersion: BIZ_VERSION, platformCode: PLAT_FORM_CODE, pvId: basePageInfo?.pvId})

    useEffect(() => {
        IdleQueue.add(mtaPv, M_PAGEID.BusinessSearch, M_PAGE.BusinessSearch, commonData)
        fetchData()
        if (!travelId || !processInstanceId || !mddInfo?.province || !mddInfo?.city) {
            reportInfo({
                code: errorCodeConstantMapping?.PAGE_VERTICAL_LIST_LOCATION_ERROR,
                errorDetail: {
                    errorType: ErrortType.Info,
                    customMsg: {
                        errorDescription: '链接上获取的参数异常',
                        params: searchInfo || {},
                        originParams:  getPageParams()
                    },
                }
            })
        }
    }, [])

    useEffect(() => {
        localStorage.getItem(storageKey).then((res) => {
            setHistory(res || [])
        })
    }, [])

    const fetchData =  async () => {
        const params = {
            fromSource: fromSource, // 'hotel_booking_public', // 
            virtualLocation: virtualLocation || 1,
            channelId: channelId,
            queryMode: queryMode,
            distance: distance,
            provinceCode: mddInfo?.province,
            cityCode: mddInfo?.city,
            districtCode: mddInfo?.district,
            townCode: mddInfo?.town,
        }
        const [error, res] = await apiFetch('MID_LOCATION', params, true)
        const { code, result } = res || {}
        
        if (error || code !== '0' || !result) {
            setShowDefault(history?.length === 0)
            setLocationLoading(false)
            reportInfo({
                code: errorCodeConstantMapping?.PAGE_BUSINESS_SEARCH_FETCH_FAIL,
                errorDetail: {
                    errorType: ErrortType.Info,
                    functionId: 'MID_LOCATION',
                    customMsg: {
                        errorDescription: '获取差旅搜索中间页面数据失败',
                        errorInfo: error || {},
                        respData: res || {},
                        requestParam: params || {}
                    },
                }
            })
            return
        }
        
        setCommonUserAction(result?.commonUserAction || {})
        setMddInfo(result?.mddInfo || {})
        setFilters(result?.filters || [])
        setLocationLoading(false)
    }

    const saveHistory = async (data) => {
        let newHistory = await localStorage.getItem(storageKey) || []

        const existIndex = newHistory.findIndex(item => item.displayName === data.displayName)
        if (existIndex !== -1) {
            // 已存在则移动到第一个
            const [existedItem] = newHistory.splice(existIndex, 1)
            newHistory.unshift(existedItem)
        } else {
            // 不存在则添加到第一个
            newHistory.unshift(data)
        }

        // 更新本地存储和状态
        localStorage.setItem(storageKey, newHistory)
        setHistory(newHistory)
    }

    // 点击筛选信息
    const onClickItem = (info) => {
        const data = {
            ...info,
            displayName: info?.title ?? info?.displayName,
            travelSearchType: 'filter'
        }
        onJump(data)
        saveHistory(data) // 更新历史记录
        IdleQueue.add(newMta, M_EVENTID.HotelTravelSearchFilter, M_PAGEID.BusinessSearch, M_PAGE.BusinessSearch, {
            ...commonData, 
            ...(commonUserAction || {}),
            itemId: info?.id || -100,
            itemName: info?.title || -100,
            itemValue: info?.value || -100,
            filterId: 'location_distance',
            filterType: 'gis_location',
        })
    }

    const onClickHistory = (data, option = '') => {
        // 删除当前历史记录
        if (option === 'delete') {
            const newHistory = history.filter(item => item.displayName !== data.displayName)
            setHistory(newHistory)
            localStorage.setItem(storageKey, newHistory)

            IdleQueue.add(newMta, M_EVENTID.HotelTravelSearchClear, M_PAGEID.BusinessSearch, M_PAGE.BusinessSearch, {...commonData, ...(commonUserAction || {})})
            return
        }

        const curItem = history[data?.clickIndex]
        if (isEmpty(curItem)) {
            return
        }
        onJump(curItem)
        saveHistory(curItem) // 更新历史记录

        IdleQueue.add(newMta, M_EVENTID.HotelTravelSearchHistory, M_PAGEID.BusinessSearch, M_PAGE.BusinessSearch, {
            ...commonData,
            ...(commonUserAction || {}),
            historyword: data?.displayName || -100,
            index: data?.clickIndex ?? -100,
        })
        
    }
    

    const onDeleteAll = () => {
        localStorage.removeItem(storageKey)
        setHistory([])
    }


    const onJump = (data) => {
        if (!isFromVResult && !!queryMode && (queryMode === '1' || queryMode === '2')) {
            // 如果点击的是suglist的，经纬度和名称和普通的筛选项取的字段不一样
            const poiName = data?.travelSearchType === 'sugList' ? data?.displayName : data?.title ?? data?.displayName
            const poiLat = data?.travelSearchType === 'sugList' ? data?.poiInfoVO?.latitude : data?.latitude ?? ''
            const poiLng =  data?.travelSearchType === 'sugList' ? data?.poiInfoVO?.longitude : data?.longitude ?? ''
            const url = decodeURIComponent(`https://hotel.m.jd.com/share?poiName=${poiName}&poiLat=${poiLat}&poiLng=${poiLng}`)
            window.location.href = url
        } else {
            setBackParams(data)
            Taro.navigateBack({
                delta: 1,
            });
        }
    }

    const goBackAction = () => {
        if (!isFromVResult && !!queryMode && (queryMode === '1' || queryMode === '2')) {
            window.history.back(); 
            return
        }
        Taro.navigateBack({
            delta: 1,
        });
    }

    // sug点击埋点
    const handleMtaClick = (data) => {
        const {commonUserAction, sugIndex, sugItem} = data || {}
        const { sugHotelVO, sugMddInfo, userAction } = sugItem || {}
        let tagList: any = []
        if (Array.isArray(sugItem?.subTitle)) {
            sugItem?.subTitle.forEach(item => {
                tagList.push({
                    labelName: item?.text
                })
            })
        }
        IdleQueue.add(newMta, M_EVENTID.HotelTravelSearchAutoWord, M_PAGEID.BusinessSearch, M_PAGE.BusinessSearch,  {
            ...commonData,
            ...(commonUserAction || {}),
            ...(userAction || {}),
            index: sugIndex,
            hotelId: sugHotelVO?.hotelId -100,
            tagList: tagList?.length ? tagList : -100,
            search_o2o_coordinates: sugMddInfo?.latitude ? `${sugMddInfo?.latitude},${sugMddInfo?.longitude}` : -100,
        })
    }

    // sug曝光埋点
    const handleMtaExpo = (data) => {
        const {commonUserAction, sugIndex, sugItem} = data || {}
        const { sugHotelVO, sugMddInfo, userAction } = sugItem || {}
        let tagList: any = []
        if (Array.isArray(sugItem?.subTitle)) {
            sugItem?.subTitle.forEach(item => {
                tagList.push({
                    labelName: item?.text
                })
            })
        }
        IdleQueue.add(mtaEp, M_EVENTID.HotelTravelSearchAutoWordExpo, M_PAGEID.BusinessSearch, M_PAGE.BusinessSearch, {
            ...commonData,
            ...(commonUserAction || {}),
            index: sugIndex,
            hotelId: sugHotelVO?.hotelId || -100,
            ...(userAction || {}),
            tagList: tagList?.length ?  tagList : -100,
            search_o2o_coordinates: sugMddInfo?.latitude ? `${sugMddInfo?.latitude},${sugMddInfo?.longitude}` : -100,
        })
    }

    // 点击sugItem
    const handleSugWordClick = (info) => {
        const data = {
            ...info,
            travelSearchType: 'sugList'
        }
        onJump(data)
        saveHistory(data)
    }

    // 输入框变化
    const onInputChange = (value) => {
        setSearchWord(value)
    }

    // 点击键盘的搜索
    const onSearch = (info) => {
        const data = {
            displayName: info.displayName,
            keyword: info.displayName,
            travelSearchType: 'keyword'
        }
        onJump(data)
        if (!isEmpty(info.displayName)) {
            saveHistory(data)
        }

        IdleQueue.add(newMta, M_EVENTID.HotelTravelSearchSearch, M_PAGEID.BusinessSearch, M_PAGE.BusinessSearch, {
            ...commonData,
            ...(commonUserAction || {}),
            keyword: info?.displayName ?? -100,
        })
    }

    const handleHistoryExp = ({visible, item, index}) => {
        if (visible) {
            const key = `history_${item?.displayName}`
            if (mtaMapRef.current[key]) {
                return
            }
            mtaMapRef.current[key] = true
            const {displayName} = item
            IdleQueue.add(mtaEp, M_EVENTID.HotelTravelSearchHistoryExpo, M_PAGEID.BusinessSearch, M_PAGE.BusinessSearch, {
                ...commonData,
                ...(commonUserAction || {}),
                historyword: displayName,
                index
            })
        }
    }

    // 筛选项曝光
    const handleFilterExp = (visible, item) => {
        if (visible) {
            const key = `filter_${item?.id}${item?.title}`
            if (mtaMapRef.current[key]) {
                return
            }
            mtaMapRef.current[key] = true
            IdleQueue.add(mtaEp, M_EVENTID.HotelTravelSearchFilterExpo, M_PAGEID.BusinessSearch, M_PAGE.BusinessSearch, {
                ...commonData, 
                ...(commonUserAction || {}),
                itemId: item?.id || -100,
                itemName: item?.title ||  -100,
                itemValue: item?.value || -100,
                filterId: 'location_distance',
                filterType: 'gis_location',
            })
        }
    }


    return (<View style={st.wrapper}>
        <SearchBar
            hasCancel={true}
            queryMode={queryMode}
            isTravel={true}
            keyword={searchWord}
            displayName={searchWord}
            realName={searchWord}
            onChange={onInputChange}
            handleWordClick={onSearch}
            onCancel={goBackAction}
        />
        <IOScrollView style={st.container}>
            {
                locationLoading && <View className={Styles.loading}><JDLoadingView /></View>  
            }
            {
                !locationLoading && !showDefault && <>
                    <History 
                        data={history} 
                        onClick={onClickHistory} 
                        onDeleteAll={onDeleteAll}
                        handleHistoryExp={handleHistoryExp} 
                    />

                    <Group data={filters} onClick={onClickItem} handleFilterExp={handleFilterExp} />
                </>
                
            }

            {
                !locationLoading && showDefault && <NoData text="请输入关键词搜索" style={{paddingTop: 80}} />
            } 
        </IOScrollView>

        {
            searchWord && !isEmpty(sugListParams) &&  (<View style={st.sugWrapper}>
                <Sug.SugList
                    sourceType={Sug.SOURCE_TYPE.GLOBAL_SEARCH}
                    handleMtaClick={handleMtaClick}
                    handleMtaExpo={handleMtaExpo}
                    params={{ keyword: searchWord, ...(sugListParams ?? {}) }}
                    handleWordClick={handleSugWordClick}
                    bizVersion={BIZ_VERSION}
                />
            </View>)
            }
    </View>)
}

Search.displayName = 'search'
const st = StyleSheet.create({
    wrapper: {
        width: '100%',
        height: '100%',
        backgroundColor: '#fff'
    },
    container: {
        flex: 1
    },
    sugWrapper: {
        position: 'absolute',
        top: pt(40),
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1,
        width: '100%',
        height: 'auto',
        backgroundColor: '#fff'
    }
})
export default memo(withPage({pageName: 'businessSearch'})(Search))

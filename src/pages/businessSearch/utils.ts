export const getCommonData = (channelId: number,latitude: string, longitude: string, posAreaId: string, fromSource: string, priceChannel: string) => {
    const commonData = {
        channel: channelId ||  -100,
        o2o_coordinates: `${latitude || -100},${longitude || -100}`,
        fouraddrid: posAreaId || -100,
        fromSource: fromSource || -100,
        offerChannel: priceChannel || -100
    }
    return commonData
}

export const getSugParams = (data, otherData) => {
    const params = {
        channelId: data?.channelId,
        priceChannel: data?.priceChannel,
        fromSource: data?.fromSource,
        latitude: data?.latitude || '',
        longitude: data?.longitude || '',
        posAreaId: data?.posAreaId || '',
        checkInDate: data?.hotelBaseSearchParam?.checkInDate,
        checkOutDate: data?.hotelBaseSearchParam?.checkOutDate,
        sugCityInfo: data?.mddInfo,
        isFromVResult: data?.isFromVResult,
        bizVersion: otherData?.bizVersion,
        pvId: otherData?.pvId,
        platformCode: otherData?.platformCode,
    }
    return params
}
import { isArray, isEmpty, isFunction, isObject } from "@/utils/isType";
import { E_TAB, T_FILTER_ITEM, T_REQUEST_PARAMS, T_SORT_TYPE } from "./result.model";
import ResultModel from "./result.model";
import BaseService from "@/store/model/base.service";
import useFetch from "@/common/useFetch";
import { reportInfo } from '@/common/reporter';
import { errorCodeConstantMapping, ErrortType } from '@/common/reporter/errorMapping';
import { getUuid } from '@/utils';


type T_APIFETCH = ReturnType<typeof useFetch>['apiFetch']

class ResultService extends BaseService {

    protected model: ResultModel
    public apiFetch: T_APIFETCH;
    public hideTabSkeleton: (tab: E_TAB) => void;
    protected customPvIdMap = {} as any

    constructor() {
        super()
        this.model = new ResultModel();
    }

    /**
     * 结果页接口请求
     */
    async fetchResult(params: { businessType: E_TAB, needSearchItemId } = {} as any) {
        try {
            const routeParams = this.model.getRouteParams() || {}
            const requestParams = await this.getRequestParams(params.businessType) || {};
            if (params.businessType === E_TAB.Hotel && this.model.isDefaultMddInfo) {
                delete requestParams['mddInfo']
            }

            if (params.needSearchItemId !== true) {
                delete routeParams['searchItemId']
            } else {
                delete params['needSearchItemId']
            }

            const _params = {
                ...routeParams,
                ...requestParams,
                ...params,
            }
            // try {
            //     reportInfo({
            //         code: errorCodeConstantMapping?.PAGE_RESULT_SEARCH_FETCH_PARAMS,
            //         errorDetail: {
            //             errorType: ErrortType.Info,
            //             customMsg: {
            //                 errorDescription: `大搜结果页面请求入参: ${JSON.stringify(_params)}`
            //             },
            //         }
            //     })
            // } catch (error) {

            // }
            // 如果有page 
            let customPvId = ''
            const fisrtUuid = getUuid()
            if (isEmpty(_params.page)) {
                 // 即使customPvId无法获取, 底层也做了数据兼容
                 customPvId = fisrtUuid
            } else if (_params.page === 1) {
                this.customPvIdMap[params.businessType] = fisrtUuid
                customPvId = fisrtUuid
            } else {
                customPvId = this.customPvIdMap[params.businessType]
            }
            const [err, res] = await this.apiFetch('TRIP_SEARCH_INDEX', _params, true, { customPvId: customPvId })

            if (isEmpty(_params.page)) {
                let businessType = E_TAB.Comprehensive
                if (res?.result?.tabType) {
                    // 这里要设置全局兜底页面
                    businessType = res?.result?.tabType
                }
                this.customPvIdMap[businessType] = fisrtUuid
            }

            console.log('fetchResult res', err, res)
            // const response = response;
            // const spotRes = spotRes;
            // if (err) {
            //     return {}
            // }  else {
            //     return res // res
            // }

            return [err, res]
        } catch (error) {
            console.error('fetchResult error', error)
            return [error, null]
        }
    }

    // 获取身份认证
    public async getUserIdentityList() {
        try {
            const params = {
                bizType: 1,
                type: -1,
                extraParam: { resource: 1 }
            }
            const [error, response] = await this.apiFetch('USER_IDENTITY_LIST', params, true, { ignoreAddress: true })
            if (error) {
                return error
            }
            return response
        } catch (error) {
            return error
        }
    }


    /* 获取筛选数据
     * @param tab
     * @returns
     */
    public getFillterList(tab: E_TAB) {
        const filterList = this.getCurrentRequestParams(tab).filterList
        return filterList
    }

    /**
     * 获取请求参数
     * @param tab - 当前选项卡
     * @returns 请求参数对象
     */
    public async getRequestParams(tab: E_TAB): Promise<T_REQUEST_PARAMS> {

        const filterItemMap = this.getCurrentRequestParams(tab)
        const requestParams = {} as T_REQUEST_PARAMS
        const hotelBaseSearchParam = await this.model.getHotelBaseSearchParam();
        if (tab === E_TAB.Hotel) {
            return Object.assign(requestParams, filterItemMap, { hotelBaseSearchParam, mddInfo: this.model.mddInfo })
        }
        return Object.assign(requestParams, filterItemMap, { hotelBaseSearchParam });
    }

    /**
     * 获取当前请求参数(用于设置)
     * @param tab - 当前选项卡
     * @returns 当前请求参数
     */
    public getCurrentRequestParams(tab: E_TAB): T_REQUEST_PARAMS {
        if (!this.model.allRequestParams[tab]) {
            this.model.allRequestParams[tab] = {} as T_REQUEST_PARAMS;
        }
        return this.model.allRequestParams[tab];
    }

    /**
     * 检查指定选项卡是否具有筛选项。
     * @param tab - 要检查的选项卡
     * @returns 返回布尔值，指示是否存在筛选项
     */
    public hasFilterItems(tab: E_TAB): boolean {
        /**
         * 当有筛选项时，即：
         * 1、有筛选: this._filterItemsMap[tab]
         * 2、有目的地信息: this._filterItemsMap[tab].mddInfo
         * 3、有排序类型: this._filterItemsMap[tab].sortType
         */
        const allRequestParam = this.model.allRequestParams[tab];
        const hasFilters = (!!allRequestParam?.filterList && allRequestParam?.filterList?.length > 0)
            || !!this.model.sortTypeMap[tab];
        if (tab == E_TAB.Hotel) {
            return (
                hasFilters
                || !!allRequestParam?.mddInfo
            )
        }

        return hasFilters
    }

    /**
     * 重置过滤项映射表中特定选项卡的数据。
     * @param tabIndexOrResetAll 要重置的选项卡
     */
    public resetFilterItemsMap(tabIndexOrResetAll: E_TAB | boolean) {
        if (tabIndexOrResetAll === true) {
            this.model.allRequestParams = {} as any;
            return
        } else {
            delete this.model.allRequestParams[tabIndexOrResetAll as E_TAB]
        }
    }


    /**
     * 设置筛选项映射表中特定选项的值。
     * @param tab - 要设置值的选项
     * @param value - 要设置的值
     */
    public setFilterItemsMap(tab: E_TAB, { filterList: value }) {
        this.model.originFilterListMap[tab] = value;
        const filterList = value?.map?.(x => x?.metaData)
        const current = this.getCurrentRequestParams(tab)
        Object.assign(current, { filterList })
    }


    /**
     * 设置指定标签页的MDD信息。
     * @param tab - 标签页
     * @param mddInfo - MDD信息
     */
    public setMddInfo(tab: E_TAB, { mddInfo, isDefaultMddInfo }) {
        this.model.isDefaultMddInfo = !!isDefaultMddInfo;
        this.model.mddInfo = mddInfo;
        // const current = this.getCurrentRequestParams(tab)
        // Object.assign(current, { mddInfo })
    }

    /**
     * 设置是否默认mddinfo 用于请求时 true 请求的时候不加mddinfo false的时候加mddinfo。
     */
    public setIsDefaultMddInfo(isDefaultMddInfo) {
        this.model.isDefaultMddInfo = !!isDefaultMddInfo;
    }

    /**
     * 设置排序类型
     * @param tab - 选项卡
     * @param sortItem - 排序项
     */
    public setSortType(tab: E_TAB, sortItem: T_SORT_TYPE) {
        this.model.sortTypeMap[tab] = sortItem
        const current = this.getCurrentRequestParams(tab)
        Object.assign(current, {
            sortType: sortItem.sortType,
            orderType: sortItem.orderType
        })
    }

    /**
     * 重置指定选项卡的排序类型。
     * @param tab 指定的选项卡
     */
    public resetSortType(tab: E_TAB) {
        delete this.model.sortTypeMap[tab]
        const current = this.getCurrentRequestParams(tab)
        // @ts-ignore
        delete current['sortType']
        // @ts-ignore
        delete current['orderType']
    }

    /**
     * 当过滤条件改变时执行操作
     * @param tab 当前标签页
     * @param payload 过滤条件数据
     */
    public onFilterChange(tab: E_TAB, payload: any, callback?: () => void) {
        // console.log('payload', tab, payload)
        // return // 现在回调有问题，待修复 @王鑫垚
        const [labelValues, filterList] = payload;
        // console.log(
        //     'labelValues, codes, filterList',
        //     labelValues, filterList
        // )

        if (isArray(labelValues?.sortType) && isObject(labelValues.sortType?.[0]?.metaData)) {
            this.setSortType(tab, { ...(labelValues.sortType?.[0]?.metaData || {}) })
        } else {
            this.resetSortType(tab)
        }

        this.setFilterItemsMap(tab, {
            filterList
        })

        callback?.()
        // console.log('this.model.allRequestParams', this.model.allRequestParams)
    }

    /**
     * 获取原始过滤器列表。
     */
    public getOriginFilterList(tab: E_TAB) {
        return this.model.originFilterListMap[tab];
    }

    /**
     * 删除指定标签页下的原始过滤器列表中的特定索引项。
     * @param tab 指定的标签页
     * @param index 要删除的索引
     * @returns 返回更新后的过滤器列表
     */
    public deleteOriginFilterList(tab: E_TAB, index: number) {
        if (index === -1) {
            this.model.originFilterListMap[tab] = []
            this.setFilterItemsMap(tab, { filterList: [] })
            return []
        } else {
            const filterList = this.model.originFilterListMap[tab] || []
            // console.log(' this.model.originFilterListMap[tab]', this.model.originFilterListMap[tab])
            filterList.splice(index, 1)
            this.setFilterItemsMap(tab, { filterList })
            return filterList;
        }
    }

    public setPvIdMap(tab: E_TAB, uuid: string) {
        /** tabIndex变化，pvId要改变 */
        this.model.pvIdMap[tab] = uuid;
    }

    public recordActivedTabs(tab: E_TAB) {
        /**记录已激活的tabIndex */
        this.model.activedTabs.add(tab);
    }

    public setCurrentTabIndex(tab: number) {
        this.model.currentTabIndex = tab;
    }

    /**
     * 设置初始选项卡数据
     * @param tab 选项卡
     * @param data 数据
     */
    public setInitTabData(tab: E_TAB, data: any) {
        this.model.initTabData[tab] = data;
    }

    /**
     * 获取初始选项卡数据
     * @param tab - 选项卡类型
     * @returns 选项卡对应的数据
     */
    public getInitTabDatas() {
        return this.model.initTabData
    }

    // 设置是否已经loading true 已经loading flase 没有
    public setHasLoadedMap(tab: E_TAB) {
        this.model.hasLoadedMap[tab] = true
    }

    // 获取是否已经loading true 已经loading flase 没有
    public getHasLoadedMap(tab: E_TAB) {
        return this.model.hasLoadedMap[tab]
    }
    public getHasLoadedMaps() {
        return this.model.hasLoadedMap
    }
    public getHasLoadedMaps() {
        return this.model.hasLoadedMap
    }
}

export default ResultService;

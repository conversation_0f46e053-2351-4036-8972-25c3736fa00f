import { createContext } from "react";
import ResultService from "./result.service";
import { getResultStoreBase } from ".";
import { WithSelectors } from "@/store/tools";

type T_ReturnType_getResultStoreBase = ReturnType<typeof getResultStoreBase>

export type T_ResultContext = {
    service: ResultService,
    // resultStoreBase: UseBoundStore<StoreApi<ResultState>>,
    useResultStore: WithSelectors<T_ReturnType_getResultStoreBase>
}

export default createContext<T_ResultContext>({} as T_ResultContext);




export type T_ModuleContext = {
    data: any,
    [key: string]: any
}

export const ModuleContext = createContext<T_ModuleContext>({} as T_ModuleContext);
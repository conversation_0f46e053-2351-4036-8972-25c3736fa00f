import { getCurrentPageInstance } from "@/common/common";
import { getBaseInfo } from "@/common/hotelBaseInfo";
import BaseModel from "@/store/model/base.model";
import globalInfoModel from "@/store/model/globalInfo.model";
import { T_HotelBaseSearchParam } from "@/store/model/globalTypes";
import { isEmpty, isObject } from "@/utils/isType";
import { getJsonObjectOrRawValue } from "@/utils/Json";

export enum E_TAB {
    NONE = -1,
    /**
     * 综合
     */
    Comprehensive = '0',
    /**
     * 酒店
     */
    Hotel = '1',
    /**
     * 景点
     */
    ScenerySpot = '2'
}

export const TAB_MAP = {
    '-1': E_TAB.NONE,
    0: E_TAB.Comprehensive,
    1: E_TAB.ScenerySpot,
    2: E_TAB.Hotel,
}

export type T_FILTER_ITEM = {
    sortType: string,
    orderType: string,
    latitude: string,
    longitude: string,
    /**四级地址，形如：1,2,3,4 */
    posAreaId: string
}

export type T_MDDINFO = {
    /**目的地类型 POI("1", "POI") CITY("2", "城市") */
    type: string,
    showName: string,
    latitude: string,
    longitude: string,
    aggCityId: string
}

export type T_REQUEST_PARAMS = {
    /**酒店基础条件，酒店、综合场景下必传 */
    hotelBaseSearchParam?: T_HotelBaseSearchParam,
    /**目的地搜索 */
    mddInfo?: T_MDDINFO,
    /**
     * 指定搜索的ItemId,用于sug中酒店、景点跳转到对应tab后出现当前的item
     */
    searchItemId?: string,
    /**存放所有请求过滤条件(包含酒店的位置距离) */
    filterList?: T_FILTER_ITEM[],
} & T_SORT_TYPE

export type T_SORT_TYPE = {
    sortType: string,
    orderType: string
}

export type T_SORT_TYPE_MAP = {
    [P in E_TAB]: T_SORT_TYPE
}

export default class ResultModel extends BaseModel {

    initTabData: {
        [P in E_TAB]: {
            data: any,
            error: any
        }
    } = {} as any

    /**
     * 记录筛选面板组件吐出来的筛选项
     */
    originFilterListMap: {
        [P in E_TAB]: any[]
    } = {} as any

    /**
     * Tab选中的索引
     */
    currentTabIndex = -1;

    /**
     * 已经激活过的Tab
     */
    activedTabs = new Set<E_TAB>()

    /**pvId集合 */
    pvIdMap: {
        [P in E_TAB]: string
    } = {} as any

    /**
     * 记录各Tab下筛选项，用于Tab筛选
     * http://j-api.jd.com/fe-app-view/demandManage/readOnly/30122?interfaceType=1&methodId=1499375&type=1
     */
    allRequestParams: {
        [P in E_TAB]: T_REQUEST_PARAMS
    } = {} as any

    /**
     * 是否初始化赋值的mddInfo
     */
    isDefaultMddInfo?: boolean = false;
    mddInfo?: T_MDDINFO;

    /**
     * 排序字段信息
     */
    public sortTypeMap: T_SORT_TYPE_MAP = {} as any;


    /**
     * 获取酒店基本搜索参数
     * @returns 酒店基本搜索参数
     */
    public async getHotelBaseSearchParam(): Promise<T_HotelBaseSearchParam | void> {
        const args = await globalInfoModel.getHotelBaseInfo()
        if (isObject(args) && Object.keys(args).length > 0) {
            return {
                checkInDate: args.checkInDate,
                checkOutDate: args.checkOutDate,
                roomNum: (args as any).roomNum?.value || 1,
                grownNum: (args as any).adultNum?.value || 1,
                childrenNum: (args as any).childNum?.value || 0,
                childrenAges: (args as any).childNum?.age || []
            } as T_HotelBaseSearchParam
        }
    }

    // 获取路由上的参数
    getRouteParams() {
        const routeParams: any = getCurrentPageInstance().router?.params || {};
        const extParams = getJsonObjectOrRawValue(decodeURI(routeParams?.extParams))
        const extMap = extParams?.extMap
        let _extParams = {} as any
        if (isObject(extMap)) {
            for (let key in extMap) {
                const value = extMap[key] as any;
                _extParams[key] = !isEmpty(value) ? decodeURI(value) : ''
            }
        }
        return {
            keyword: !isEmpty(routeParams?.realName) ? decodeURI(routeParams.realName) : '',
            ..._extParams
        }
    }

    hasLoadedMap: {
        [P in E_TAB]: boolean
    } = {
        [E_TAB.Comprehensive]: false,
        [E_TAB.Hotel]: false,
        [E_TAB.ScenerySpot]: false,
    } as any

}

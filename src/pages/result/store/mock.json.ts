export const response = {
    'tabType': '0',
    'curPage': 1,
    'hasNextPage': false,
    'countSummaryVO': {
        'hotelCount': 0,
        'scenicCount': 0,
        'resultCount': 8,
        'resultCountShowText': '8'
    },
    'filterPanelVOList': [
        {
            'filterPanelName': '筛选',
            'filterPanelCode': 'main_filter',
            'showFilter': true,
            'filterList': [
                {
                    'groupCode': 'hotel_filter',
                    'groupName': '酒店',
                    'filterName': '全部酒店',
                    'filterType': 'hotel_main',
                    'multi': 1,
                    'filterBehavior': 'normal',
                    'itemList': [
                        {
                            'filterType': 'hotel_main',
                            'itemId': '-909090',
                            'itemName': '全部酒店',
                            'itemType': 'button',
                            'group': false,
                            'itemBehavior': 'all'
                        }
                    ]
                },
                {
                    'groupCode': 'hotel_filter',
                    'groupName': '酒店',
                    'filterName': '星级',
                    'filterType': 'hotel_grade',
                    'multi': 0,
                    'filterBehavior': 'normal',
                    'itemList': [
                        {
                            'filterType': 'hotel_grade',
                            'itemId': '2',
                            'itemName': '经济型',
                            'itemType': 'button',
                            'group': false,
                            'itemBehavior': 'normal'
                        },
                        {
                            'filterType': 'hotel_grade',
                            'itemId': '3',
                            'itemName': '3星/舒适',
                            'itemType': 'button',
                            'group': false,
                            'itemBehavior': 'normal'
                        },
                        {
                            'filterType': 'hotel_grade',
                            'itemId': '4',
                            'itemName': '4星/高档',
                            'itemType': 'button',
                            'group': false,
                            'itemBehavior': 'normal'
                        },
                        {
                            'filterType': 'hotel_grade',
                            'itemId': '5',
                            'itemName': '5星/豪华',
                            'itemType': 'button',
                            'group': false,
                            'itemBehavior': 'normal'
                        }
                    ]
                },
                {
                    'groupCode': 'hotel_filter',
                    'groupName': '酒店',
                    'filterName': '酒店评分',
                    'filterType': 'hotel_score',
                    'multi': 1,
                    'filterBehavior': 'normal',
                    'itemList': [
                        {
                            'filterType': 'hotel_meal',
                            'itemId': '4.5,',
                            'itemName': '4.5分以上',
                            'itemType': 'button',
                            'group': false,
                            'itemBehavior': 'normal'
                        },
                        {
                            'filterType': 'hotel_meal',
                            'itemId': '4.0,',
                            'itemName': '4.0分以上',
                            'itemType': 'button',
                            'group': false,
                            'itemBehavior': 'normal'
                        },
                        {
                            'filterType': 'hotel_meal',
                            'itemId': '3.5,',
                            'itemName': '3.5分以上',
                            'itemType': 'button',
                            'group': false,
                            'itemBehavior': 'normal'
                        },
                        {
                            'filterType': 'hotel_meal',
                            'itemId': '3.0,',
                            'itemName': '3.0分以上',
                            'itemType': 'button',
                            'group': false,
                            'itemBehavior': 'normal'
                        }
                    ]
                },
                {
                    'groupCode': 'hotel_filter',
                    'groupName': '酒店',
                    'filterName': '点评数',
                    'filterType': 'hotel_comment_count',
                    'multi': 1,
                    'filterBehavior': 'normal',
                    'itemList': [
                        {
                            'filterType': 'hotel_comment_count',
                            'itemId': '500,',
                            'itemName': '500条以上',
                            'itemType': 'button',
                            'group': false,
                            'itemBehavior': 'normal'
                        },
                        {
                            'filterType': 'hotel_comment_count',
                            'itemId': '200,',
                            'itemName': '200条以上',
                            'itemType': 'button',
                            'group': false,
                            'itemBehavior': 'normal'
                        },
                        {
                            'filterType': 'hotel_comment_count',
                            'itemId': '100,',
                            'itemName': '100条以上',
                            'itemType': 'button',
                            'group': false,
                            'itemBehavior': 'normal'
                        }
                    ]
                },
                {
                    'groupCode': 'scenic_filter',
                    'groupName': '景点',
                    'filterName': '全部景点',
                    'filterType': 'scenic_main',
                    'multi': 1,
                    'filterBehavior': 'normal',
                    'itemList': [
                        {
                            'filterType': 'scenic_main',
                            'itemId': '-909090',
                            'itemName': '全部景点',
                            'itemType': 'button',
                            'group': false,
                            'itemBehavior': 'all'
                        }
                    ]
                }
            ]
        }
    ],
    'naturalCardVOList': [
        {
            'cardType': '0',
            'trafficCardVOList': [
                {
                    'type': 2,
                    'fromCityId': '1',
                    'fromCityName': '北京',
                    'toCityId': '2',
                    'toCityName': '上海',
                    'jumpUrl': 'https://jipiao.m.jd.com/?initRoute=flightList&fromtype=104&depDate=2024-11-05&arrDate=2024-11-06&queryModule=1&depCity=北京&arrCity=上海&jdreactkey=JDReactAirtickets&jdreactapp=JDReactAirtickets&transparentenable=true&jdreactAppendPath=flightList'
                },
                {
                    'type': 1,
                    'fromCityId': '1',
                    'fromCityName': '北京',
                    'toCityId': '2',
                    'toCityName': '上海',
                    'jumpUrl': 'https://train.m.jd.com/train?routerName=train&trainDate=2024-11-05&fromStationCode=&toStationCode=&fromStationName=北京&toStationName=上海&train_source=jipiao&jdreactkey=JDReactTrain&jdreactapp=JDReactTrain&transparentenable=true&jdreactAppendPath=train'
                }
            ]
        },
        {
            'cardType': '2',
            'scenicCardVO': {
                'id': '2004',
                'name': '芒砀山地质公园-凉亭',
                'picUrl': 'https://img30.360buyimg.com/mptrip/jfs/t1/177812/20/16232/702098/60ff6c50E9d1e4c89/d42c9ce593354012.png',
                'price': '9.9',
                'free': false,
                'jumpUrl': 'https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=2004&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail',
                'promotionTagListMap': {
                    'mainScenicScore': [
                        {
                            'styleCode': 'scenicScore',
                            'trackId': 'scenicScore',
                            'listShowName': '暂无评分',
                            'template': '%s分'
                        }
                    ],
                    'mainScenicLocationDistance': [
                        {
                            'styleCode': 'scenicLocation',
                            'trackId': 'scenicLocation',
                            'listShowName': '河南-商丘市-永城市-S201芒砀山地质公园内'
                        }
                    ],
                    'mainScenicPromotion': [
                        {
                            'styleCode': 'scenicBooking',
                            'trackId': 'scenicBooking',
                            'listShowName': '今日可定'
                        },
                        {
                            'styleCode': 'scenicBooking',
                            'trackId': 'scenicBooking',
                            'listShowName': '明日可定'
                        }
                    ]
                }
            }
        },
        {
            'cardType': '2',
            'scenicCardVO': {
                'id': '2003',
                'name': '东拉山大峡谷',
                'picUrl': 'https://img30.360buyimg.com/mptrip/jfs/t1/217619/36/30399/244632/64a26435F6ce4d7ab/8aaf538e836aae05.jpg',
                'free': false,
                'jumpUrl': 'https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=2003&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail',
                'promotionTagListMap': {
                    'mainScenicScore': [
                        {
                            'styleCode': 'scenicScore',
                            'trackId': 'scenicScore',
                            'listShowName': '暂无评分',
                            'template': '%s分'
                        }
                    ],
                    'mainScenicLocationDistance': [
                        {
                            'styleCode': 'scenicLocation',
                            'trackId': 'scenicLocation',
                            'listShowName': '四川-雅安市宝兴县陇东镇'
                        }
                    ],
                    'mainScenicTitleAfter': [
                        {
                            'styleCode': 'scenicLevel',
                            'trackId': 'scenicLevel',
                            'listShowName': '4A',
                            'template': '%sA'
                        }
                    ]
                }
            }
        },
        {
            'cardType': '2',
            'scenicCardVO': {
                'id': '2002',
                'name': '雾灵西峰高山滑水',
                'picUrl': 'https://img30.360buyimg.com/mptrip/jfs/t1/173385/27/39556/197271/64e9261eF3c6f6f79/9dc58aae3f5e8480.png',
                'free': false,
                'jumpUrl': 'https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=2002&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail',
                'promotionTagListMap': {
                    'mainScenicScore': [
                        {
                            'styleCode': 'scenicScore',
                            'trackId': 'scenicScore',
                            'listShowName': '5.0分',
                            'template': '%s分'
                        }
                    ],
                    'mainScenicLocationDistance': [
                        {
                            'styleCode': 'scenicLocation',
                            'trackId': 'scenicLocation',
                            'listShowName': '北京-新城子镇沙滩村雾灵西峰风景区内'
                        }
                    ]
                }
            }
        },
        {
            'cardType': '2',
            'scenicCardVO': {
                'id': '2001',
                'name': '上海爱琴海购物公园冰雪大世界',
                'picUrl': 'https://img30.360buyimg.com/mptrip/jfs/t1/26498/17/4480/74670/5c31c2afEeb40d194/5788f87fc580f748.jpg',
                'free': false,
                'jumpUrl': 'https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=2001&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail',
                'promotionTagListMap': {
                    'mainScenicScore': [
                        {
                            'styleCode': 'scenicScore',
                            'trackId': 'scenicScore',
                            'listShowName': '暂无评分',
                            'template': '%s分'
                        }
                    ],
                    'mainScenicLocationDistance': [
                        {
                            'styleCode': 'scenicLocation',
                            'trackId': 'scenicLocation',
                            'listShowName': '上海-上海市闵行区虹井路120弄-6号爱琴海购物公园7层'
                        }
                    ]
                }
            }
        },
        {
            'cardType': '2',
            'scenicCardVO': {
                'id': '1213',
                'name': '黄山尖',
                'picUrl': 'https://img30.360buyimg.com/mptrip/jfs/t1/136065/35/40412/77078/65a97624Fb80a60b7/f28632ecfe222a19.jpg',
                'price': '55',
                'free': false,
                'jumpUrl': 'https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=1213&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail',
                'promotionTagListMap': {
                    'mainScenicScore': [
                        {
                            'styleCode': 'scenicScore',
                            'trackId': 'scenicScore',
                            'listShowName': '暂无评分',
                            'template': '%s分'
                        }
                    ],
                    'mainScenicLocationDistance': [
                        {
                            'styleCode': 'scenicLocation',
                            'trackId': 'scenicLocation',
                            'listShowName': '浙江-杭州市淳安县千岛湖景区黄山尖'
                        }
                    ],
                    'mainScenicPromotion': [
                        {
                            'styleCode': 'scenicBooking',
                            'trackId': 'scenicBooking',
                            'listShowName': '今日可定'
                        },
                        {
                            'styleCode': 'scenicBooking',
                            'trackId': 'scenicBooking',
                            'listShowName': '明日可定'
                        }
                    ]
                }
            }
        },
        {
            'cardType': '2',
            'scenicCardVO': {
                'id': '1212',
                'name': '云蒙山皇家森林公园',
                'picUrl': 'https://img30.360buyimg.com/mptrip/jfs/t1/9187/10/11992/38087/5c31c441E5152024c/ce6bd3a585ad9e32.jpg',
                'free': false,
                'jumpUrl': 'https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=1212&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail',
                'promotionTagListMap': {
                    'mainScenicScore': [
                        {
                            'styleCode': 'scenicScore',
                            'trackId': 'scenicScore',
                            'listShowName': '5.0分',
                            'template': '%s分'
                        }
                    ],
                    'mainScenicLocationDistance': [
                        {
                            'styleCode': 'scenicLocation',
                            'trackId': 'scenicLocation',
                            'listShowName': '北京-北京-丰宁公路81公里处'
                        }
                    ]
                }
            }
        },
        {
            'cardType': '2',
            'scenicCardVO': {
                'id': '878',
                'name': '大理苍山石门关景区',
                'picUrl': 'https://img30.360buyimg.com/mptrip/jfs/t1/127347/11/29161/107290/63fbc8b8Fa6205667/e33b85d11e6f44ea.jpg',
                'price': '20',
                'free': false,
                'jumpUrl': 'https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=878&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail',
                'promotionTagListMap': {
                    'mainScenicScore': [
                        {
                            'styleCode': 'scenicScore',
                            'trackId': 'scenicScore',
                            'listShowName': '暂无评分',
                            'template': '%s分'
                        }
                    ],
                    'mainScenicLocationDistance': [
                        {
                            'styleCode': 'scenicLocation',
                            'trackId': 'scenicLocation',
                            'listShowName': '云南-大理白族自治州漾濞县苍山西镇石门关景区'
                        }
                    ],
                    'mainScenicPromotion': [
                        {
                            'styleCode': 'scenicBooking',
                            'trackId': 'scenicBooking',
                            'listShowName': '今日可定'
                        },
                        {
                            'styleCode': 'scenicBooking',
                            'trackId': 'scenicBooking',
                            'listShowName': '明日可定'
                        }
                    ],
                    'mainScenicTitleAfter': [
                        {
                            'styleCode': 'scenicLevel',
                            'trackId': 'scenicLevel',
                            'listShowName': '4A',
                            'template': '%sA'
                        }
                    ]
                }
            }
        },
        {
            'cardType': '2',
            'scenicCardVO': {
                'id': '999',
                'name': '民国大杂院',
                'picUrl': 'https://img30.360buyimg.com/mptrip/jfs/t1/176206/24/28502/48702/62fcbc39E436748e4/91d8ee43f97c2b30.jpg',
                'price': '25',
                'free': false,
                'jumpUrl': 'https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=999&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail',
                'promotionTagListMap': {
                    'mainScenicScore': [
                        {
                            'styleCode': 'scenicScore',
                            'trackId': 'scenicScore',
                            'listShowName': '暂无评分',
                            'template': '%s分'
                        }
                    ],
                    'mainScenicLocationDistance': [
                        {
                            'styleCode': 'scenicLocation',
                            'trackId': 'scenicLocation',
                            'listShowName': '浙江-宁波市奉化市溪口镇溪南路265号'
                        }
                    ],
                    'mainScenicPromotion': [
                        {
                            'styleCode': 'scenicBooking',
                            'trackId': 'scenicBooking',
                            'listShowName': '今日可定'
                        },
                        {
                            'styleCode': 'scenicBooking',
                            'trackId': 'scenicBooking',
                            'listShowName': '明日可定'
                        }
                    ]
                }
            }
        }, {
            'cardType': '1',
            'hotelCardVO': {
                'id': '87027',
                'name': '梨树戴维斯宾馆',
                'picUrl': 'https://img12.360buyimg.com/hotel/jfs/t1/147310/18/29945/111508/634f5bfaEda8ebfe9/d9080032e904414f.jpg',
                'price': '300',
                'originPrice': '300',
                'discountPrice': '300',
                'jumpUrl': 'https://hotel.m.jd.com?routerName=detail&hotelId=87027&cityId=138&cityName=梨树&checkInDate=2024-11-06&checkOutDate=2024-11-07&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detail',
                'promotionTagListMap': {
                    'hotelPromotion': [
                        {
                            'styleCode': 'TagList',
                            'trackId': 'hotelFacilities',
                            'listShowName': '免费停车',
                            'template': '%s'
                        },
                        {
                            'styleCode': 'TagList',
                            'trackId': 'hotelFacilities',
                            'listShowName': '公用区wifi',
                            'template': '%s'
                        }
                    ],
                    'hotelLocationDistance': [
                        {
                            'styleCode': 'Text',
                            'trackId': 'hotelLocationDistance',
                            'listShowName': '距四平商城9公里'
                        }
                    ],
                    'hotelScore': [
                        {
                            'styleCode': 'Score',
                            'trackId': 'hotelScore',
                            'listShowName': '4.1',
                            'template': '%s'
                        }
                    ],
                    'hotelTitleAfter': [
                        {
                            'styleCode': 'Tag',
                            'trackId': 'hotelGrade',
                            'listShowName': '经济'
                        }
                    ]
                }
            }
        },
        {
            'cardType': '1',
            'hotelCardVO': {
                'id': '87046',
                'name': '四平朗月宾馆',
                'picUrl': 'https://img12.360buyimg.com/hotel/jfs/t1/44578/31/23047/28321/63c44625F0113782c/fe0c7fba18fa6ee5.jpg',
                'price': '400',
                'originPrice': '500',
                'discountPrice': '400',
                'jumpUrl': 'https://hotel.m.jd.com?routerName=detail&hotelId=87046&cityId=119&cityName=四平&checkInDate=2024-11-06&checkOutDate=2024-11-07&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detail',
                'promotionTagListMap': {
                    'hotelPromotion': [
                        {
                            'styleCode': 'TagList',
                            'trackId': 'hotelFacilities',
                            'listShowName': '公用区wifi',
                            'template': '%s'
                        }
                    ],
                    'hotelLocationDistance': [
                        {
                            'styleCode': 'Text',
                            'trackId': 'hotelLocationDistance',
                            'listShowName': '距四平商城12公里'
                        }
                    ],
                    'hotelScore': [
                        {
                            'styleCode': 'Score',
                            'trackId': 'hotelScore',
                            'listShowName': '4.6',
                            'template': '%s'
                        },
                        {
                            'styleCode': 'Text',
                            'trackId': 'hotelCommentCount',
                            'listShowName': '2点评',
                            'template': '%s点评'
                        }
                    ],
                    'hotelTitleAfter': [
                        {
                            'styleCode': 'Tag',
                            'trackId': 'hotelGrade',
                            'listShowName': '经济'
                        }
                    ]
                }
            }
        },
        {
            'cardType': '1',
            'hotelCardVO': {
                'id': '86998',
                'name': '舒美达宾馆(四平宏信店)',
                'picUrl': 'https://img12.360buyimg.com/hotel/jfs/t1/231355/30/6158/52139/656df1a3Ff51e1853/5b138e4352b16b7e.jpg',
                'price': '300',
                'originPrice': '300',
                'discountPrice': '300',
                'jumpUrl': 'https://hotel.m.jd.com?routerName=detail&hotelId=86998&cityId=119&cityName=四平&checkInDate=2024-11-06&checkOutDate=2024-11-07&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detail',
                'promotionTagListMap': {
                    'hotelPromotion': [
                        {
                            'styleCode': 'TagList',
                            'trackId': 'hotelFacilities',
                            'listShowName': '免费停车',
                            'template': '%s'
                        },
                        {
                            'styleCode': 'TagList',
                            'trackId': 'hotelFacilities',
                            'listShowName': '公用区wifi',
                            'template': '%s'
                        },
                        {
                            'styleCode': 'TagList',
                            'trackId': 'hotelFacilities',
                            'listShowName': '棋牌室',
                            'template': '%s'
                        }
                    ],
                    'hotelLocationDistance': [
                        {
                            'styleCode': 'Text',
                            'trackId': 'hotelLocationDistance',
                            'listShowName': '距四平商城13公里'
                        }
                    ],
                    'hotelScore': [
                        {
                            'styleCode': 'Score',
                            'trackId': 'hotelScore',
                            'listShowName': '4.5',
                            'template': '%s'
                        },
                        {
                            'styleCode': 'Text',
                            'trackId': 'hotelCommentCount',
                            'listShowName': '22点评',
                            'template': '%s点评'
                        }
                    ],
                    'hotelTitleAfter': [
                        {
                            'styleCode': 'Tag',
                            'trackId': 'hotelGrade',
                            'listShowName': '经济'
                        }
                    ]
                }
            }
        }
    ],
    'recommendCardVOList': [
        {
            'cardType': '2',
            'scenicCardVO': {
                'id': '3001',
                'name': '《春江花月夜·唯美扬州》实景演出',
                'picUrl': 'https://img30.360buyimg.com/mptrip/jfs/t1/16519/28/4506/173642/5c31c2aaE6ff15d8c/b5e9b22aa4650510.jpg',
                'free': false,
                'jumpUrl': 'https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=3001&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail',
                'promotionTagListMap': {
                    'mainScenicScore': [
                        {
                            'styleCode': 'scenicScore',
                            'trackId': 'scenicScore',
                            'listShowName': '暂无评分',
                            'template': '%s分'
                        }
                    ],
                    'mainScenicLocationDistance': [
                        {
                            'styleCode': 'scenicLocation',
                            'trackId': 'scenicLocation',
                            'listShowName': '江苏-江苏省扬州市邗江区长春路瘦西湖东门万花园内'
                        }
                    ]
                }
            }
        },
        {
            'cardType': '2',
            'scenicCardVO': {
                'id': '3002',
                'name': '承德小布达拉宫',
                'picUrl': 'https://img30.360buyimg.com/mptrip/jfs/t1/19022/14/4475/108576/5c31c361Ed3989f66/7c606098f40882cd.jpg',
                'free': false,
                'jumpUrl': 'https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=3002&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail',
                'promotionTagListMap': {
                    'mainScenicScore': [
                        {
                            'styleCode': 'scenicScore',
                            'trackId': 'scenicScore',
                            'listShowName': '暂无评分',
                            'template': '%s分'
                        }
                    ],
                    'mainScenicLocationDistance': [
                        {
                            'styleCode': 'scenicLocation',
                            'trackId': 'scenicLocation',
                            'listShowName': '河北-河北省承德市狮子沟北侧北环路'
                        }
                    ]
                }
            }
        },
        {
            'cardType': '2',
            'scenicCardVO': {
                'id': '3004',
                'name': '上海玛雅海滩水公园',
                'picUrl': 'https://img30.360buyimg.com/mptrip/jfs/t1/84622/5/23412/140857/63cd6cfcF16565d9d/b7ec795a08977edf.jpg',
                'free': false,
                'jumpUrl': 'https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=3004&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail',
                'promotionTagListMap': {
                    'mainScenicScore': [
                        {
                            'styleCode': 'scenicScore',
                            'trackId': 'scenicScore',
                            'listShowName': '5.0分',
                            'template': '%s分'
                        }
                    ],
                    'mainScenicLocationDistance': [
                        {
                            'styleCode': 'scenicLocation',
                            'trackId': 'scenicLocation',
                            'listShowName': '上海-上海市松江区佘山镇林荫新路1222号'
                        }
                    ]
                }
            }
        }
    ],
    'outsideFilter': {
        'filterPanelName': '筛选',
        'filterPanelCode': 'main_filter',
        'filterList': [
            {
                'groupCode': 'hotel_fiter',
                'groupName': '酒店',
                'filterName': '全部酒店',
                'filterType': 'hotel_main',
                'multi': 1,
                'itemList': [
                    {
                        'filterType': 'hotel_main',
                        'itemId': '-909090',
                        'itemName': '全部酒店',
                        'itemBehavior': 'all'
                    }
                ]
            },
            {
                'groupCode': 'hotel_fiter',
                'groupName': '酒店',
                'filterName': '星级',
                'filterType': 'hotel_grade',
                'multi': 0,
                'itemList': [
                    {
                        'filterType': 'hotel_grade',
                        'itemId': '2',
                        'itemName': '经济型'
                    },
                    {
                        'filterType': 'hotel_grade',
                        'itemId': '3',
                        'itemName': '3星/舒适'
                    },
                    {
                        'filterType': 'hotel_grade',
                        'itemId': '4',
                        'itemName': '4星/高档'
                    },
                    {
                        'filterType': 'hotel_grade',
                        'itemId': '5',
                        'itemName': '5星/豪华'
                    }
                ]
            },
            {
                'groupCode': 'hotel_filter',
                'groupName': '酒店',
                'filterName': '品牌',
                'filterType': 'hotel_brand',
                'multi': 0,
                'itemList': [
                    {
                        'filterType': 'hotel_brand',
                        'itemId': '1',
                        'itemName': '希尔顿'
                    }
                ]
            },
            {
                'groupCode': 'hotel_filter',
                'groupName': '酒店',
                'filterName': '酒店评分',
                'filterType': 'hotel_score',
                'multi': 1,
                'itemList': [
                    {
                        'filterType': 'hotel_meal',
                        'itemId': '4.5,',
                        'itemName': '4.5分以上'
                    },
                    {
                        'filterType': 'hotel_meal',
                        'itemId': '4.0,',
                        'itemName': '4.0分以上'
                    },
                    {
                        'filterType': 'hotel_meal',
                        'itemId': '3.5,',
                        'itemName': '3.5分以上'
                    },
                    {
                        'filterType': 'hotel_meal',
                        'itemId': '3.0,',
                        'itemName': '3.0分以上'
                    }
                ]
            },
            {
                'groupCode': 'hotel_filter',
                'groupName': '酒店',
                'filterName': '点评数',
                'filterType': 'hotel_comment_count',
                'multi': 1,
                'itemList': [
                    {
                        'filterType': 'hotel_comment_count',
                        'itemId': '500,',
                        'itemName': '500条以上'
                    },
                    {
                        'filterType': 'hotel_comment_count',
                        'itemId': '200,',
                        'itemName': '200条以上'
                    },
                    {
                        'filterType': 'hotel_comment_count',
                        'itemId': '100,',
                        'itemName': '100条以上'
                    }
                ]
            },
            {
                'groupCode': 'scenic_fiter',
                'groupName': '景点',
                'filterName': '全部景点',
                'filterType': 'scenic_main',
                'multi': 1,
                'itemList': [
                    {
                        'filterType': 'scenic_main',
                        'itemId': '-909090',
                        'itemName': '全部景点',
                        'itemBehavior': 'all'
                    }
                ]
            },
            {
                'groupCode': 'scenic_fiter',
                'groupName': '景点',
                'filterName': '景点类型',
                'filterType': 'scenic_category',
                'multi': 0,
                'itemList': [
                    {
                        'filterType': 'scenic_category',
                        'itemId': '11',
                        'itemName': '亲子乐园'
                    }
                ]
            },
            {
                'groupCode': 'scenic_fiter',
                'groupName': '门票预订',
                'filterName': '门票预订',
                'filterType': 'scenic_booking',
                'multi': 0,
                'itemList': [
                    {
                        'filterType': 'scenic_booking',
                        'itemId': '1',
                        'itemName': '可订今日',
                        'itemType': 'button',
                        'group': false,
                        'itemBehavior': 'normal'
                    },
                    {
                        'filterType': 'scenic_booking',
                        'itemId': '2',
                        'itemName': '可订明日',
                        'itemType': 'button',
                        'group': false,
                        'itemBehavior': 'normal'
                    },
                    {
                        'filterType': 'scenic_booking',
                        'itemId': '0',
                        'itemName': '免费景点',
                        'itemType': 'button',
                        'group': false,
                        'itemBehavior': 'normal'
                    }
                ]
            },
            {
                'groupCode': 'scenic_fiter',
                'groupName': '景区评分',
                'filterName': '景区评分',
                'filterType': 'scenic_score',
                'multi': 1,
                'itemList': [
                    {
                        'filterType': 'scenic_score',
                        'itemId': '4.5,',
                        'itemName': '4.5分及以上',
                        'itemType': 'button',
                        'group': false,
                        'itemBehavior': 'normal'
                    }
                ]
            },
            {
                'groupCode': 'scenic_fiter',
                'groupName': '点评条数',
                'filterName': '点评条数',
                'filterType': 'scenic_comment_count',
                'multi': 1,
                'itemList': [
                    {
                        'filterType': 'scenic_comment_count',
                        'itemId': '100,',
                        'itemName': '100条以上',
                        'itemType': 'button',
                        'group': false,
                        'itemBehavior': 'normal'
                    },
                    {
                        'filterType': 'scenic_comment_count',
                        'itemId': '50,',
                        'itemName': '50条以上',
                        'itemType': 'button',
                        'group': false,
                        'itemBehavior': 'normal'
                    }
                ]
            },
            {
                'groupCode': 'scenic_fiter',
                'groupName': '景区级别',
                'filterName': '景区级别',
                'filterType': 'scenic_level',
                'multi': 1,
                'itemList': [
                    {
                        'filterType': 'scenic_level',
                        'itemId': '5,',
                        'itemName': '5A',
                        'itemType': 'button',
                        'group': false,
                        'itemBehavior': 'normal'
                    },
                    {
                        'filterType': 'scenic_level',
                        'itemId': '4,',
                        'itemName': '4A及以上',
                        'itemType': 'button',
                        'group': false,
                        'itemBehavior': 'normal'
                    }
                ]
            }
        ]
    }
}

export const spotRes = {
    "naturalCardVOList": [
        {
            "cardType": "2",
            "scenicCardVO": {
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/246805/25/4037/89170/65b028e9Ffdd475bb/3f72644f6060eb48.jpg",
                "id": "7447",
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=7447&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "price": "75",
                "free": false,
                "name": "京东大峡谷",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "距您直线67公里",
                            "styleCode": "Text",
                            "trackId": "scenicYouDistance",
                            "template": "距您直线%s"
                        }
                    ],
                    "scenicScore": [
                        {
                            "listShowName": "5.0分",
                            "styleCode": "Text",
                            "trackId": "scenicScore"
                        },
                        {
                            "listShowName": "41条点评",
                            "styleCode": "Text",
                            "trackId": "scenicCommentCount",
                            "template": "%s条点评"
                        }
                    ],
                    "scenicTitleAfter": [
                        {
                            "listShowName": "4A",
                            "styleCode": "Tag",
                            "trackId": "scenicLevel",
                            "template": "%sA"
                        }
                    ],
                    "scenicPromotion": [
                        {
                            "listShowName": "今日可定",
                            "styleCode": "TagList",
                            "trackId": "scenicBooking"
                        }
                    ]
                }
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "free": false,
                "id": "3426",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "廊坊",
                            "styleCode": "Text",
                            "trackId": "scenicLocation"
                        }
                    ],
                    "scenicScore": [
                        {
                            "listShowName": "5.0分",
                            "styleCode": "Text",
                            "trackId": "scenicScore"
                        },
                        {
                            "listShowName": "1条点评",
                            "styleCode": "Text",
                            "trackId": "scenicCommentCount",
                            "template": "%s条点评"
                        }
                    ]
                },
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=3426&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "name": "京东第一温泉度假村",
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/48286/34/21385/119596/63fbbaa9F286ef451/5354fd46c2894499.jpg"
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/235924/38/23347/98418/66dab6acF6f40bec3/86de417f84ac802e.jpg",
                "id": "7583",
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=7583&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "price": "9.9",
                "free": false,
                "name": "京东大溶洞",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "距您直线71公里",
                            "styleCode": "Text",
                            "trackId": "scenicYouDistance",
                            "template": "距您直线%s"
                        }
                    ],
                    "scenicScore": [
                        {
                            "listShowName": "5.0分",
                            "styleCode": "Text",
                            "trackId": "scenicScore"
                        },
                        {
                            "listShowName": "37条点评",
                            "styleCode": "Text",
                            "trackId": "scenicCommentCount",
                            "template": "%s条点评"
                        }
                    ],
                    "scenicPromotion": [
                        {
                            "listShowName": "今日可定",
                            "styleCode": "TagList",
                            "trackId": "scenicBooking"
                        }
                    ]
                }
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "廊坊",
                            "styleCode": "Text",
                            "trackId": "scenicLocation"
                        }
                    ]
                },
                "id": "5436",
                "free": false,
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=5436&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "name": "京东第一温泉"
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "free": false,
                "id": "145907",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "距您直线32公里",
                            "styleCode": "Text",
                            "trackId": "scenicYouDistance",
                            "template": "距您直线%s"
                        }
                    ]
                },
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=145907&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "name": "京东植物工厂",
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/107892/11/25433/79066/62383890Ef7ee76ac/b020c4b0596b39f3.jpg"
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "free": false,
                "id": "198602",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "距您直线25公里",
                            "styleCode": "Text",
                            "trackId": "scenicYouDistance",
                            "template": "距您直线%s"
                        }
                    ]
                },
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=198602&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "name": "京东校园使用",
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/222197/14/32982/85777/64afc78eF32d79117/c4e05c5f04a903ec.jpg"
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "free": false,
                "id": "55261",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "距您直线38公里",
                            "styleCode": "Text",
                            "trackId": "scenicYouDistance",
                            "template": "距您直线%s"
                        }
                    ]
                },
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=55261&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "name": "燕郊京东第一温泉",
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/132905/39/12434/219247/5f88b6bdEd5ffb022/590e703d496a695a.jpg"
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "free": false,
                "id": "111535",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "距您直线73公里",
                            "styleCode": "Text",
                            "trackId": "scenicYouDistance",
                            "template": "距您直线%s"
                        }
                    ]
                },
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=111535&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "name": "京东石林峡景区",
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/183698/18/16227/85021/6101059cEac0e4904/58a1386e478af8b6.jpg"
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/103272/10/25967/98029/640ba92cF269915bf/01980151bc866bcc.jpg",
                "id": "53498",
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=53498&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "price": "25.7",
                "free": false,
                "name": "京东龙泉谷旅游景区",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "唐山",
                            "styleCode": "Text",
                            "trackId": "scenicLocation"
                        }
                    ],
                    "scenicPromotion": [
                        {
                            "listShowName": "今日可定",
                            "styleCode": "TagList",
                            "trackId": "scenicBooking"
                        }
                    ]
                }
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "free": false,
                "id": "52905",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "南昌",
                            "styleCode": "Text",
                            "trackId": "scenicLocation"
                        }
                    ]
                },
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=52905&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "name": "多奇妙儿童主题乐园(京东大道店)",
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/155484/15/2327/82924/5f889063Eaa617105/8ed1b529f765f358.jpg"
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/122547/35/35984/112841/64afa11cF02659f13/60f672c5ea42462a.jpg",
                "id": "5270",
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=5270&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "price": "400",
                "free": false,
                "name": "大连海昌发现王国主题公园",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "大连",
                            "styleCode": "Text",
                            "trackId": "scenicLocation"
                        }
                    ],
                    "scenicScore": [
                        {
                            "listShowName": "5.0分",
                            "styleCode": "Text",
                            "trackId": "scenicScore"
                        },
                        {
                            "listShowName": "100条点评",
                            "styleCode": "Text",
                            "trackId": "scenicCommentCount",
                            "template": "%s条点评"
                        }
                    ],
                    "scenicComment": [
                        {
                            "listShowName": "\"享受欢乐  尽在海昌\"",
                            "styleCode": "Summary",
                            "trackId": "scenicComment",
                            "template": "\"%s\""
                        }
                    ],
                    "scenicTitleAfter": [
                        {
                            "listShowName": "5A",
                            "styleCode": "Tag",
                            "trackId": "scenicLevel",
                            "template": "%sA"
                        }
                    ],
                    "scenicPromotion": [
                        {
                            "listShowName": "明日可定",
                            "styleCode": "TagList",
                            "trackId": "scenicBooking"
                        }
                    ]
                }
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/227637/10/28118/152112/671f707dFa6539caf/f8fa63ef721ce0e1.jpg",
                "id": "81007",
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=81007&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "price": "88",
                "free": false,
                "name": "北京环球度假区",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "距您直线19公里",
                            "styleCode": "Text",
                            "trackId": "scenicYouDistance",
                            "template": "距您直线%s"
                        }
                    ],
                    "scenicScore": [
                        {
                            "listShowName": "5.0分",
                            "styleCode": "Text",
                            "trackId": "scenicScore"
                        },
                        {
                            "listShowName": "10w条点评",
                            "styleCode": "Text",
                            "trackId": "scenicCommentCount",
                            "template": "%s条点评"
                        }
                    ],
                    "scenicPromotion": [
                        {
                            "listShowName": "今日可定",
                            "styleCode": "TagList",
                            "trackId": "scenicBooking"
                        }
                    ]
                }
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/248024/35/23349/86336/672cc851F7f505f82/723dc763fc1d426e.jpg",
                "id": "4724",
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=4724&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "price": "30",
                "free": false,
                "name": "上海迪士尼度假区",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "上海",
                            "styleCode": "Text",
                            "trackId": "scenicLocation"
                        }
                    ],
                    "scenicScore": [
                        {
                            "listShowName": "5.0分",
                            "styleCode": "Text",
                            "trackId": "scenicScore"
                        },
                        {
                            "listShowName": "5000条点评",
                            "styleCode": "Text",
                            "trackId": "scenicCommentCount",
                            "template": "%s条点评"
                        }
                    ],
                    "scenicComment": [
                        {
                            "listShowName": "\"点亮心中奇梦\"",
                            "styleCode": "Summary",
                            "trackId": "scenicComment",
                            "template": "\"%s\""
                        }
                    ],
                    "scenicPromotion": [
                        {
                            "listShowName": "今日可定",
                            "styleCode": "TagList",
                            "trackId": "scenicBooking"
                        }
                    ]
                }
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/18508/7/4522/85875/5c31c0f1E2caff454/977d34385f21ab54.jpg",
                "id": "374",
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=374&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "price": "3",
                "free": false,
                "name": "九寨沟",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "阿坝",
                            "styleCode": "Text",
                            "trackId": "scenicLocation"
                        }
                    ],
                    "scenicScore": [
                        {
                            "listShowName": "5.0分",
                            "styleCode": "Text",
                            "trackId": "scenicScore"
                        },
                        {
                            "listShowName": "100条点评",
                            "styleCode": "Text",
                            "trackId": "scenicCommentCount",
                            "template": "%s条点评"
                        }
                    ],
                    "scenicComment": [
                        {
                            "listShowName": "\"五彩缤纷，有“童话世界”之誉\"",
                            "styleCode": "Summary",
                            "trackId": "scenicComment",
                            "template": "\"%s\""
                        }
                    ],
                    "scenicTitleAfter": [
                        {
                            "listShowName": "5A",
                            "styleCode": "Tag",
                            "trackId": "scenicLevel",
                            "template": "%sA"
                        }
                    ],
                    "scenicPromotion": [
                        {
                            "listShowName": "今日可定",
                            "styleCode": "TagList",
                            "trackId": "scenicBooking"
                        }
                    ]
                }
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/209038/36/33361/87097/6709ae60Fc373dbaa/7b7881fa65903375.jpg",
                "id": "57",
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=57&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "price": "25",
                "free": false,
                "name": "黄山风景区",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "黄山",
                            "styleCode": "Text",
                            "trackId": "scenicLocation"
                        }
                    ],
                    "scenicScore": [
                        {
                            "listShowName": "5.0分",
                            "styleCode": "Text",
                            "trackId": "scenicScore"
                        },
                        {
                            "listShowName": "16条点评",
                            "styleCode": "Text",
                            "trackId": "scenicCommentCount",
                            "template": "%s条点评"
                        }
                    ],
                    "scenicComment": [
                        {
                            "listShowName": "\"邂逅山顶星空\"",
                            "styleCode": "Summary",
                            "trackId": "scenicComment",
                            "template": "\"%s\""
                        }
                    ],
                    "scenicTitleAfter": [
                        {
                            "listShowName": "5A",
                            "styleCode": "Tag",
                            "trackId": "scenicLevel",
                            "template": "%sA"
                        }
                    ],
                    "scenicPromotion": [
                        {
                            "listShowName": "今日可定",
                            "styleCode": "TagList",
                            "trackId": "scenicBooking"
                        }
                    ]
                }
            }
        }
    ],
    "recommendCardVOList": [
        {
            "cardType": "2",
            "scenicCardVO": {
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/246805/25/4037/89170/65b028e9Ffdd475bb/3f72644f6060eb48.jpg",
                "id": "7447",
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=7447&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "price": "75",
                "free": false,
                "name": "京东大峡谷",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "距您直线67公里",
                            "styleCode": "Text",
                            "trackId": "scenicYouDistance",
                            "template": "距您直线%s"
                        }
                    ],
                    "scenicScore": [
                        {
                            "listShowName": "5.0分",
                            "styleCode": "Text",
                            "trackId": "scenicScore"
                        },
                        {
                            "listShowName": "41条点评",
                            "styleCode": "Text",
                            "trackId": "scenicCommentCount",
                            "template": "%s条点评"
                        }
                    ],
                    "scenicTitleAfter": [
                        {
                            "listShowName": "4A",
                            "styleCode": "Tag",
                            "trackId": "scenicLevel",
                            "template": "%sA"
                        }
                    ],
                    "scenicPromotion": [
                        {
                            "listShowName": "今日可定",
                            "styleCode": "TagList",
                            "trackId": "scenicBooking"
                        }
                    ]
                }
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "free": false,
                "id": "3426",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "廊坊",
                            "styleCode": "Text",
                            "trackId": "scenicLocation"
                        }
                    ],
                    "scenicScore": [
                        {
                            "listShowName": "5.0分",
                            "styleCode": "Text",
                            "trackId": "scenicScore"
                        },
                        {
                            "listShowName": "1条点评",
                            "styleCode": "Text",
                            "trackId": "scenicCommentCount",
                            "template": "%s条点评"
                        }
                    ]
                },
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=3426&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "name": "京东第一温泉度假村",
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/48286/34/21385/119596/63fbbaa9F286ef451/5354fd46c2894499.jpg"
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/235924/38/23347/98418/66dab6acF6f40bec3/86de417f84ac802e.jpg",
                "id": "7583",
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=7583&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "price": "9.9",
                "free": false,
                "name": "京东大溶洞",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "距您直线71公里",
                            "styleCode": "Text",
                            "trackId": "scenicYouDistance",
                            "template": "距您直线%s"
                        }
                    ],
                    "scenicScore": [
                        {
                            "listShowName": "5.0分",
                            "styleCode": "Text",
                            "trackId": "scenicScore"
                        },
                        {
                            "listShowName": "37条点评",
                            "styleCode": "Text",
                            "trackId": "scenicCommentCount",
                            "template": "%s条点评"
                        }
                    ],
                    "scenicPromotion": [
                        {
                            "listShowName": "今日可定",
                            "styleCode": "TagList",
                            "trackId": "scenicBooking"
                        }
                    ]
                }
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "廊坊",
                            "styleCode": "Text",
                            "trackId": "scenicLocation"
                        }
                    ]
                },
                "id": "5436",
                "free": false,
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=5436&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "name": "京东第一温泉"
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "free": false,
                "id": "145907",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "距您直线32公里",
                            "styleCode": "Text",
                            "trackId": "scenicYouDistance",
                            "template": "距您直线%s"
                        }
                    ]
                },
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=145907&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "name": "京东植物工厂",
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/107892/11/25433/79066/62383890Ef7ee76ac/b020c4b0596b39f3.jpg"
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "free": false,
                "id": "198602",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "距您直线25公里",
                            "styleCode": "Text",
                            "trackId": "scenicYouDistance",
                            "template": "距您直线%s"
                        }
                    ]
                },
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=198602&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "name": "京东校园使用",
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/222197/14/32982/85777/64afc78eF32d79117/c4e05c5f04a903ec.jpg"
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "free": false,
                "id": "55261",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "距您直线38公里",
                            "styleCode": "Text",
                            "trackId": "scenicYouDistance",
                            "template": "距您直线%s"
                        }
                    ]
                },
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=55261&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "name": "燕郊京东第一温泉",
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/132905/39/12434/219247/5f88b6bdEd5ffb022/590e703d496a695a.jpg"
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "free": false,
                "id": "111535",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "距您直线73公里",
                            "styleCode": "Text",
                            "trackId": "scenicYouDistance",
                            "template": "距您直线%s"
                        }
                    ]
                },
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=111535&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "name": "京东石林峡景区",
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/183698/18/16227/85021/6101059cEac0e4904/58a1386e478af8b6.jpg"
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/103272/10/25967/98029/640ba92cF269915bf/01980151bc866bcc.jpg",
                "id": "53498",
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=53498&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "price": "25.7",
                "free": false,
                "name": "京东龙泉谷旅游景区",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "唐山",
                            "styleCode": "Text",
                            "trackId": "scenicLocation"
                        }
                    ],
                    "scenicPromotion": [
                        {
                            "listShowName": "今日可定",
                            "styleCode": "TagList",
                            "trackId": "scenicBooking"
                        }
                    ]
                }
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "free": false,
                "id": "52905",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "南昌",
                            "styleCode": "Text",
                            "trackId": "scenicLocation"
                        }
                    ]
                },
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=52905&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "name": "多奇妙儿童主题乐园(京东大道店)",
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/155484/15/2327/82924/5f889063Eaa617105/8ed1b529f765f358.jpg"
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/122547/35/35984/112841/64afa11cF02659f13/60f672c5ea42462a.jpg",
                "id": "5270",
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=5270&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "price": "400",
                "free": false,
                "name": "大连海昌发现王国主题公园",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "大连",
                            "styleCode": "Text",
                            "trackId": "scenicLocation"
                        }
                    ],
                    "scenicScore": [
                        {
                            "listShowName": "5.0分",
                            "styleCode": "Text",
                            "trackId": "scenicScore"
                        },
                        {
                            "listShowName": "100条点评",
                            "styleCode": "Text",
                            "trackId": "scenicCommentCount",
                            "template": "%s条点评"
                        }
                    ],
                    "scenicComment": [
                        {
                            "listShowName": "\"享受欢乐  尽在海昌\"",
                            "styleCode": "Summary",
                            "trackId": "scenicComment",
                            "template": "\"%s\""
                        }
                    ],
                    "scenicTitleAfter": [
                        {
                            "listShowName": "5A",
                            "styleCode": "Tag",
                            "trackId": "scenicLevel",
                            "template": "%sA"
                        }
                    ],
                    "scenicPromotion": [
                        {
                            "listShowName": "明日可定",
                            "styleCode": "TagList",
                            "trackId": "scenicBooking"
                        }
                    ]
                }
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/227637/10/28118/152112/671f707dFa6539caf/f8fa63ef721ce0e1.jpg",
                "id": "81007",
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=81007&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "price": "88",
                "free": false,
                "name": "北京环球度假区",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "距您直线19公里",
                            "styleCode": "Text",
                            "trackId": "scenicYouDistance",
                            "template": "距您直线%s"
                        }
                    ],
                    "scenicScore": [
                        {
                            "listShowName": "5.0分",
                            "styleCode": "Text",
                            "trackId": "scenicScore"
                        },
                        {
                            "listShowName": "10w条点评",
                            "styleCode": "Text",
                            "trackId": "scenicCommentCount",
                            "template": "%s条点评"
                        }
                    ],
                    "scenicPromotion": [
                        {
                            "listShowName": "今日可定",
                            "styleCode": "TagList",
                            "trackId": "scenicBooking"
                        }
                    ]
                }
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/248024/35/23349/86336/672cc851F7f505f82/723dc763fc1d426e.jpg",
                "id": "4724",
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=4724&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "price": "30",
                "free": false,
                "name": "上海迪士尼度假区",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "上海",
                            "styleCode": "Text",
                            "trackId": "scenicLocation"
                        }
                    ],
                    "scenicScore": [
                        {
                            "listShowName": "5.0分",
                            "styleCode": "Text",
                            "trackId": "scenicScore"
                        },
                        {
                            "listShowName": "5000条点评",
                            "styleCode": "Text",
                            "trackId": "scenicCommentCount",
                            "template": "%s条点评"
                        }
                    ],
                    "scenicComment": [
                        {
                            "listShowName": "\"点亮心中奇梦\"",
                            "styleCode": "Summary",
                            "trackId": "scenicComment",
                            "template": "\"%s\""
                        }
                    ],
                    "scenicPromotion": [
                        {
                            "listShowName": "今日可定",
                            "styleCode": "TagList",
                            "trackId": "scenicBooking"
                        }
                    ]
                }
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/18508/7/4522/85875/5c31c0f1E2caff454/977d34385f21ab54.jpg",
                "id": "374",
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=374&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "price": "3",
                "free": false,
                "name": "九寨沟",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "阿坝",
                            "styleCode": "Text",
                            "trackId": "scenicLocation"
                        }
                    ],
                    "scenicScore": [
                        {
                            "listShowName": "5.0分",
                            "styleCode": "Text",
                            "trackId": "scenicScore"
                        },
                        {
                            "listShowName": "100条点评",
                            "styleCode": "Text",
                            "trackId": "scenicCommentCount",
                            "template": "%s条点评"
                        }
                    ],
                    "scenicComment": [
                        {
                            "listShowName": "\"五彩缤纷，有“童话世界”之誉\"",
                            "styleCode": "Summary",
                            "trackId": "scenicComment",
                            "template": "\"%s\""
                        }
                    ],
                    "scenicTitleAfter": [
                        {
                            "listShowName": "5A",
                            "styleCode": "Tag",
                            "trackId": "scenicLevel",
                            "template": "%sA"
                        }
                    ],
                    "scenicPromotion": [
                        {
                            "listShowName": "今日可定",
                            "styleCode": "TagList",
                            "trackId": "scenicBooking"
                        }
                    ]
                }
            }
        },
        {
            "cardType": "2",
            "scenicCardVO": {
                "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/209038/36/33361/87097/6709ae60Fc373dbaa/7b7881fa65903375.jpg",
                "id": "57",
                "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=57&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
                "price": "25",
                "free": false,
                "name": "黄山风景区",
                "promotionTagListMap": {
                    "scenicLocationDistance": [
                        {
                            "listShowName": "黄山",
                            "styleCode": "Text",
                            "trackId": "scenicLocation"
                        }
                    ],
                    "scenicScore": [
                        {
                            "listShowName": "5.0分",
                            "styleCode": "Text",
                            "trackId": "scenicScore"
                        },
                        {
                            "listShowName": "16条点评",
                            "styleCode": "Text",
                            "trackId": "scenicCommentCount",
                            "template": "%s条点评"
                        }
                    ],
                    "scenicComment": [
                        {
                            "listShowName": "\"邂逅山顶星空\"",
                            "styleCode": "Summary",
                            "trackId": "scenicComment",
                            "template": "\"%s\""
                        }
                    ],
                    "scenicTitleAfter": [
                        {
                            "listShowName": "5A",
                            "styleCode": "Tag",
                            "trackId": "scenicLevel",
                            "template": "%sA"
                        }
                    ],
                    "scenicPromotion": [
                        {
                            "listShowName": "今日可定",
                            "styleCode": "TagList",
                            "trackId": "scenicBooking"
                        }
                    ]
                }
            }
        }
    ],
    "tabType": "2",
    "countSummaryVO": {
        "hotelCount": 0,
        "resultCountShowText": "查看结果(900+个)",
        "resultCount": 955,
        "scenicCount": 955
    },
    "hasNextPage": false,
    "selectedAreaId": "1,72,55674,0",
    "curPage": 1
}
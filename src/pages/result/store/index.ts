
import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { E_TAB, TAB_MAP } from './result.model';
import { getUuid } from '@/utils';
import ResultService from './result.service';

export interface ResultState {
    currentTabIndex: number;
    updateTabIndex: (tabIndex: number) => void;
    tabContentLoading: boolean;
    tabLoadingState: {
        [P in E_TAB]: boolean
    };
    updateTabContentLoading: (tab: E_TAB, loading: boolean) => void;
    tabComprehensiveLoading: boolean;
    tabHotelLoading: boolean;
    tabScenerySpot: boolean;
}


export function getResultStoreBase(service: ResultService) {
    const useResultStoreBase = create<ResultState>()(
        subscribeWithSelector(
            (set, get) => ({
                currentTabIndex: E_TAB.NONE.valueOf(),
                tabLoadingState: {} as any,
                tabContentLoading: true,
                tabComprehensiveLoading: true,
                tabHotelLoading: true,
                tabScenerySpot: true,
                // updateInitDataAndTabIndex: (tabIndex: number, { data, error }) => set((state) => {
                //     get().updateTabIndex(tabIndex)
                //     return {
                //         initData: {
                //             [TAB_MAP[tabIndex]]: {
                //                 data,
                //                 error
                //             }
                //         }
                //     }
                // }),
                updateTabIndex: (tabIndex: number) => set((state) => {
                    // console.log('skeleton[currentTabIndex] updateTabIndex', tabIndex)
                    if (state?.currentTabIndex === tabIndex) {
                        return state;
                    }

                    const tab = TAB_MAP[tabIndex]
                    /** tabIndex变化，pvId要改变 */
                    service.setPvIdMap(tab, getUuid());

                    /**记录已激活的tabIndex */
                    service.recordActivedTabs(tab)

                    service.setCurrentTabIndex(tabIndex)
                    service.notifyUpdate({ action: 'changeTabIndex' })

                    // console.log('resultModel.pvIdMap', resultModel.pvIdMap)
                    return {
                        currentTabIndex: tabIndex
                    };
                }),
                updateTabContentLoading: (tab: E_TAB, loading: boolean) => set((state) => {

                    if(tab === E_TAB.Comprehensive) {
                        return {
                            tabComprehensiveLoading: loading
                        }
                    }

                    if(tab === E_TAB.Hotel) {
                        return {
                            tabHotelLoading: loading
                        }
                    }
                    if(tab === E_TAB.ScenerySpot) {
                        return {
                            tabScenerySpot: loading
                        }
                    }
                    if(tab === E_TAB.NONE) {
                        return {
                            tabContentLoading: loading
                        }
                    }

                })
            })
        )
    )
    return useResultStoreBase
}



// export const useResultStore = createSelectors(useResultStoreBase)
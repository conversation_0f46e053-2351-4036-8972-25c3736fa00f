import { View, Text, Image } from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import { CLICK_TYPE, useSearchBar } from './index.service'
import { isEmpty } from '@/utils/isType'
import { getImageUrl } from '@/assets/imgs'
import { isWeb } from '@/common/common'
import classNames from 'classnames'
import NavBar from '@/BaseComponents/NavBar'

/**
 * 搜索结果页面搜索条
 * @param props 
 * @returns 
 */

interface Props {
    searchWord: { // 搜索词
        displayName: string | undefined; // 明词
        realName: string | undefined; // 暗词
    };
}

const ImgConfig = {
    resSeaClose: getImageUrl('resSeaClose'), // 删除
}

export default function SearchBar (props: Props) {
    const {
        handleClick,
        goBack,
        searchWord,
    } = useSearchBar(props)

    return (
        <NavBar className={styles.navBarWrapper} leftFn={goBack} center={
            <View className={classNames({
                [styles.navBar]: true,
                [styles.navBarWeb]: isWeb,
            })}>
                <View onClick={(e) => { handleClick(e, CLICK_TYPE.BACKFILL)}} className={styles.searchContainer}>
                    {
                        !isEmpty(searchWord) && 
                        (
                            <View onClick={(e) => { handleClick(e, CLICK_TYPE.DELETE)}} className={styles.wordContainer}>
                                <Text numberOfLines={1} className={classNames({[styles.word]: true, [styles.wordH5]: isWeb})}>{searchWord.displayName}</Text>
                                <Image className={styles.closeIcon} src={ImgConfig['resSeaClose']} mode="scaleToFill"></Image>
                            </View>
                        )
                    }
                </View>
            </View>
        }/>
    )
}
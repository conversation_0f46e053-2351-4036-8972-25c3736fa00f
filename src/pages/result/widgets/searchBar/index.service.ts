import { getCurrentPagesStack, isWeb } from "@/common/common";
import { BasePageContext } from "@/common/withPage";
import Taro from "@tarojs/taro"
import { useContext } from "react";

export enum CLICK_TYPE {
    DELETE = 'delete',
    BACKFILL = 'backfill',
}

export function useSearchBar(props) {
    const { searchWord } = props
    const { setBackParams } = useContext(BasePageContext)

    // 处理点击 跳转到搜索中间页面 展示搜索词
    const handleClick = (e, type) => {
        e?.stopPropagation?.();
        let params = ''
        if (type === CLICK_TYPE.BACKFILL) {
            setBackParams({
                displayName: searchWord.displayName,
                realName: searchWord.realName
            })
            params = `displayName=${searchWord.displayName}&realName=${searchWord.realName}`
            // Taro.redirectTo({
            //     url: `/pages/index/index?displayName=${searchWord.displayName}&realName=${searchWord.realName}`
            // })
        } else {
            setBackParams({
                displayName: '',
                realName: ''
            })
            params = 'displayName=&realName='
            // Taro.redirectTo({
            //     url: '/pages/index/index?displayName=&realName=',
            // })
        }
        if (isWeb) {
            const pages = getCurrentPagesStack();
            if (pages.length === 1) {
                Taro.redirectTo({
                    url: `/pages/index/index?${params}`
                })
                return
            }
        }
        Taro.navigateBack({
            animate: false,
        });
    }

    // 返回
    const goBack = () => {
        Taro.navigateBack({
            delta: 1,
        });
    }

    return {
        handleClick,
        searchWord,
        goBack,
    }
}
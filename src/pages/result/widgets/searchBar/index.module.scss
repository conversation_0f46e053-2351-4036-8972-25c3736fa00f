@import "@/assets/theme.scss";

.navBarWrapper {
    padding-bottom: 10px;
    background-color: #FFF;
}

.navBar {
    display: flex;
    flex-direction: row;
    flex: 1;
    padding-left: 42px;
}

.navBarWeb {
    margin-top: 4px;
    padding-left: 12px;
    flex: 0 0 auto;
}

.searchContainer {
    border-radius: 6px;
    border: 1px solid var(--primaryHLTextColor);
    background: #FFF;
    height: 34px;
    // padding: 0 8px;
    padding-left: 5px;
    padding-right: 20px;
    display: flex;
    flex-direction: row;
    flex: 1;
    align-items: center;
    margin-right: 16px;
}

.wordContainer {
    border-radius: 4px;
    background: var(--primarySubTextColor);
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 4px 8px;
}

.word {
    color: #FFF;
    font-size: 12px;
    margin-right: 4px;
    flex-shrink: 1;
}

.wordH5 {
    max-width: calc(100vw - 85px);
}

.closeIcon {
    width: 6px;
    height: 6px;
    flex: 0 0 auto;
}

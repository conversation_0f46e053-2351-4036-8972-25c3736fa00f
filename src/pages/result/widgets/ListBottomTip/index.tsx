import { Text, View } from "@/BaseComponents/atoms"
import styles from './index.module.scss'
import { memo } from "react"

export enum TIP_TYPE {
    NO_MORE, // 没有更多
    RETRY_BY_NETERROR, // 网络异常重试
    RETRY_BY_JSERROR, // js异常重试
}


const ListBottomTip = (props) => {
    const { type = TIP_TYPE.NO_MORE, onRetry, title, customStyle = {} } = props

    const handleClick = () => {
        if (type === TIP_TYPE.NO_MORE || type === TIP_TYPE.RETRY_BY_JSERROR) {
            return
        }
        onRetry?.()
    }

    // 获取兜底title
    const getRevalTitle = () => {
        let titleTemp = ''
        if (type === TIP_TYPE.NO_MORE) {
            titleTemp = '没有更多数据了'
        } else if (type === TIP_TYPE.RETRY_BY_NETERROR) {
            titleTemp = '网络异常，请点击此处重试'
        } else if (type === TIP_TYPE.RETRY_BY_JSERROR) {
            titleTemp = '应用开小差，请重试'
        }
        return titleTemp
    }


    return (
        <View style={customStyle} className={styles.wr} onClick={handleClick}>
            {
                <Text className={styles.txt} style={props.style}>{title || getRevalTitle()}</Text>
            }
        </View>
    )
}

export default memo(ListBottomTip)

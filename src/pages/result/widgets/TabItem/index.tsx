import { View, Text, Image } from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import { mergeStyle } from '@/BaseComponents/atoms/utils'
import { getBoldStyle } from '@/BaseComponents/atoms/utils/boldStyle'
import classNames from "classnames";
import { memo } from 'react';
import { isAndroid } from '@/common/common';

type TTabItemProps = {
    style: any;
    data: any;
    isTabActive?: boolean;
    isBlank?: boolean
};

function TabItem(props: TTabItemProps) {
    const { style, data, isTabActive, isBlank } = props

    const { text, iconUri, activeBgColor, activeColor } = data


    if (isBlank) {
        return (
            <View className={styles.wr} style={mergeStyle(style)}></View>
        )
    }

    return (
        <View className={classNames(styles.wr, isTabActive && styles.active)} style={mergeStyle(style, isTabActive && { backgroundColor: activeBgColor })}>
            <Image className={styles.img} src={iconUri} />

            <Text className={classNames({
                [styles.txt]: true,
                [styles.txtAndroid]: isAndroid
            })} style={mergeStyle(getBoldStyle(),isTabActive && { color: activeColor } )}>{text}</Text>
        </View>
    )
}

export default memo( TabItem)
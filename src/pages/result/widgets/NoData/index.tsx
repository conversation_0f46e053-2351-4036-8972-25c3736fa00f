import { getImageUrl } from "@/assets/imgs";
import { View, Image, Text } from "@/BaseComponents/atoms";
import styles from './index.module.scss'
import classNames from "classnames";
import withClassName from "@/BaseComponents/atoms/utils/withClassName";
import { safeRun } from '@/Components/Filter/utils'

export enum EmptyType {
    // 默认样式
    DEFAULT = 'default',
    // 没有低价拼房数据
    NO_SHARE_ROOM_LOWPRICE = 'no_lowPrice_share_room',
    // 没有拼房数据
    NO_SHARE_ROOM = 'no_share_room'
}

/**
 * 空状态展示组件 - 根据不同类型显示对应的空状态UI
 */
function NoData(props) {
    // 解构props并设置默认值，保持原有参数结构不变
    const {
        type = EmptyType.DEFAULT,
        style,
        text = "未找到符合条件的结果，请更改条件重新搜索",
        onClick,
        shareHotelTip // 拼房样式传入数据
    } = props

    // ========================= 默认空状态 =========================
    const renderDefaultEmpty = () => (
        <View className={classNames("flex1 column center", styles.wr)} style={style}>
            <Image
                className={styles.emptyIcon}
                src={getImageUrl('emptyIcon')}
            />
            <Text
                className={styles.txt}
                onClick={() => safeRun(onClick)} // 保持原有安全执行逻辑
            >
                {text}
            </Text>
        </View>
    )

    // ================= 无低价拼房空状态 =================
    const renderLowPriceEmpty = () => {
        if (!showShareRoomEmtpy()) {
            return null
        }
        // 服务端灵活调整, 前端完整兜底
        const {
            titleText = '目前只有高于您差标的房间，继续创建将只能报销差标的金额',
            imgUrl = 'https://img12.360buyimg.com/imagetools/jfs/t1/279059/6/12826/57716/67ea01e5F4f642538/919bbcb859f1704a.png',
            btnText = '不拼房预定',
            detailText = '以下酒店全部高于您的差标，高于差标的房间只能报销差标的金额'
        } = shareHotelTip

        return (
            <View className={styles.no_lowPrice_share_room_container}>

                <Image
                    className={styles.no_lowPrice_share_room_img}
                    src={imgUrl}
                />
                <Text className={styles.no_lowPrice_share_room_top_text}>
                    {titleText}
                </Text>
                <View onClick={() => safeRun(onClick)}>
                    <Text className={styles.no_lowPrice_share_room_btn} >{btnText}</Text>
                </View>
                <View className={styles.no_share_room_bottom_container}>
                    <View className={styles.no_lowPrice_share_room_bottom_text_container}>
                        <Text className={styles.no_lowPrice_share_room_bottom_text}>
                            {detailText}
                        </Text>
                    </View>
                </View>
            </View>
        )
    }

    // ================= 无拼房空状态 =================
    const renderNoShareRoomEmpty = () => {
        if (!showShareRoomEmtpy()) {
            return null
        }
        // 服务端灵活调整, 前端完整兜底
        const {
            titleText = '暂无酒店支持创建拼房',
            imgUrl = 'https://img10.360buyimg.com/imagetools/jfs/t1/275309/32/11422/52816/67ea01cbFa34628f7/a1f45d9796f064f1.png',
            btnText = '不拼房预定'
        } = shareHotelTip

        return (
            <View className={styles.no_share_room_container}>
                <Image
                    className={styles.no_share_room_img}
                    src={imgUrl}
                />
                <Text className={styles.no_lowPrice_share_room_top_text}>
                    {titleText}
                </Text>
                <View onClick={() => safeRun(onClick)}>
                    <Text className={styles.no_lowPrice_share_room_btn} >{btnText}</Text>
                </View>
            </View>
        )
    }

    const showShareRoomEmtpy = () => {
        return !!shareHotelTip && shareHotelTip.show
    }

    // 主渲染逻辑
    return (
        <>
            {type === EmptyType.DEFAULT && renderDefaultEmpty()}
            {type === EmptyType.NO_SHARE_ROOM_LOWPRICE && renderLowPriceEmpty()}
            {type === EmptyType.NO_SHARE_ROOM && renderNoShareRoomEmpty()}
        </>
    )
}

export default withClassName()(NoData)

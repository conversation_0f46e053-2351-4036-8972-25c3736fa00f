import {View, Image, Text} from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import classNames from 'classnames'
import {isEmpty, isFunction} from '@/utils/isType'
import {useContext, useEffect, useRef, useState} from 'react'
import {InView} from '@/BaseComponents/IntersectionObserver'
import {BasePageContext} from '@/common/withPage'
import IdleQueue from '@/utils/IdleQueue'
import {M_EVENTID, M_PAGE, M_PAGEID, MTA_NONE, mtaExposure, newMta} from '@/common/mta'
import {getImg} from '@/Components/Filter/utils'

export default function NoMathces(props) {
    const {data, onClear, index, businessType, commonUserAction} = props

    const [renderData, setRenderData] = useState([] as any)

    useEffect(() => {
        if (refIsClear.current) {
            refIsClear.current = false
            return
        }
        const _data = ([].concat(data)) as any
        // if (!isEmpty(_data)) {
        //     _data.unshift({
        //         metaData: {
        //             itemName: '清除全部'
        //         },
        //         clear: true
        //     })
        // }
        setRenderData(_data)
    }, [data])

    const refIsClear = useRef(false)
    const onClearFn = (item, index) => {
        const itemMetaData = item?.metaData || {}
        IdleQueue.add(newMta, M_EVENTID.TravelSearcResult_RecFilter, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
            ...getMtaParams(),
            groupCode: isEmpty(itemMetaData.groupCode) ? MTA_NONE : itemMetaData.groupCode,
            filterType: isEmpty(itemMetaData.filterType) ? MTA_NONE : itemMetaData.filterType,
            itemId: isEmpty(itemMetaData.itemId) ? MTA_NONE : itemMetaData.itemId,
            itemName: isEmpty(itemMetaData.itemName) ? MTA_NONE : itemMetaData.itemName,
            index: index
        })
        const { clear = false } = item
        if (clear) {
            // 清除全部筛选
            isFunction(onClear) && onClear(item, -1)
        } else {
            isFunction(onClear) && onClear(item, index)
            refIsClear.current = true
            setRenderData(pre => {
                pre.splice(index, 1)
                return [...pre]
            })
        }
    }

    const basePageContext = useContext(BasePageContext)
    const getMtaParams = () => {
        const commonMtaParams = basePageContext.getCommonMtaParams()
        const params = {
            ...commonMtaParams,
            businessType,
            ...(commonUserAction || {})
        }
        return params
    }

    if (!data) {
        return null
    }
    return (
        <InView index={index} className={styles.wr} onChange={(visible) => {
            visible && IdleQueue.add(mtaExposure, M_EVENTID.TravelSearcResult_RecFilterExpo, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, getMtaParams())
        }}>
            <View className={styles.delBox} onClick={() => {
                onClearFn({
                    metaData: {
                        itemName: '清除全部'
                    },
                    clear: true
                }, 0)
            }}>
                <Image src={getImg('delIcon')} className={styles.delIcon}/>
                <Text className={styles.delWord}>清空</Text>
            </View>

            <View className={styles.txt}>
                <Image src={getImg('search')} className={styles.searchIcon}/>
                <Text className={styles.tipText}>
                    {/* 以上是符合条件的酒店，修改可重新查询 */}
                    没有更多符合条件的结果，请尝试修改条件重新查询
                </Text>
            </View>

            <View className={classNames('row wrap', styles.itemWr)}>

                {
                    renderData.map((item, index) => {
                        return (
                            <View
                                key={index}
                                className={classNames('row center', styles.item, item.clear && styles.clearItem)}
                                onClick={() => onClearFn(item, index)}
                            >
                                <Text className={styles.labelTxt}>{item?.metaData.itemName}</Text>
                                {
                                    !item.clear && (
                                        <View className={styles.clearIconWr}>
                                            <Image src={getImg('blueClose')} className={styles.clearIcon}/>
                                        </View>
                                    )
                                }
                            </View>
                        )
                    })
                }
            </View>
        </InView>
    )
}

import { View, Image, Text } from "@/BaseComponents/atoms";
import styles from './index.module.scss'
import classNames from "classnames";
import NoMathces from "../NoMatches";
import { isEmpty } from "@/utils/isType";
import { getImageUrl } from "@/assets/imgs";
import {getImg} from '@/Components/Filter/utils'
// import { useSafeAreaInsets } from "react-native-safe-area-context";

export default function NoSieveData(props) {
    // const insets = useSafeAreaInsets();
    const { onClear, data, noNaturalData, commonUserAction } = props
    const noData = isEmpty(data);
    return (
        <View className={classNames("column", styles.wr)} >
            <View className={classNames("column center w100", styles.top, noData && styles.h, !noNaturalData && styles.pdT )} >
                {
                    noData ?
                        <Text className={styles.txt}>没有搜到相关结果，请尝试修改搜索词</Text>
                        :
                        <NoMathces onClear={onClear} data={data} commonUserAction={commonUserAction}/>
                }
            </View>
            <View className={styles.title_container}>
                <Image className={styles.goodIcon} src={getImg('good')} ></Image>
                <Text className={classNames('bold', styles.txt, styles.title)}>
                    为你推荐更多结果
                </Text>
            </View>
        </View>
    )
}

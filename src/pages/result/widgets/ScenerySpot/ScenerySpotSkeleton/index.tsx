import { E_TAB } from "@/pages/result/store/result.model";
import Skeleton from "../../FullLoading/Skeleton";
import { skeletonImgs, skeletonStyle } from "../../FullLoading/skeletonUtils";
import styles from './index.module.scss'

export default function ScenerySpotSkeleton() {
    
    

    return <Skeleton
        className={styles.wr}
        useCustomStyle={true}
        loading={true}
        skeletonStyle={skeletonStyle[E_TAB.ScenerySpot]}
        skeletonImg={skeletonImgs[E_TAB.ScenerySpot]}
    />
}
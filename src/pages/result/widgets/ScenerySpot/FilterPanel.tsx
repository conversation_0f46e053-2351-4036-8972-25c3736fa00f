import {useEffect, useState} from 'react'
import {View} from '@/BaseComponents/atoms'
import Filter from '@/Components/Filter'
import {objToList, safeRun} from '@/Components/Filter/utils'
import Styles from './FilterPanel.module.scss'
import {SortBarRefs} from '@/pages/result/widgets/ScenerySpot/SortBar'

type TYPE_RefExpandPanel = {
    toggle?: (option: {
        type: string,
        value?: any,
        top?: number
    }) => string
    onClosed?: () => void
    onSeleted?: ({item, index}) => void
    closeSelf?: () => void
    setValue: (v) => void
    queryState: () => { isShown: boolean }
}

export const refFilterPanelScene: TYPE_RefExpandPanel = {} as TYPE_RefExpandPanel

const ExpandFilter = (props) => {
    const [showFilterType, setShowFilterType] = useState<string | boolean>(false)
    const [top, setTop] = useState<number | any>(0)
    const [value, setValue] = useState<object | false>(false)

    const _setValue = (value) => {
        setValue(value)
        safeRun(SortBarRefs.get('onChange'), value)
    }

    refFilterPanelScene.queryState = () => {
        return {
            isShown: !!showFilterType
        }
    }

    refFilterPanelScene.toggle = (option: {
        type: string,
        value?: any,
        top?: number
    }) => {
        const {type, value, top} = option
        if (showFilterType === type) {
            safeRun(SortBarRefs.get('onPanelChange'), false)
            setShowFilterType(false)
        } else {
            setShowFilterType(type)
            safeRun(SortBarRefs.get('onPanelChange'), type)
        }
        if (value) {
            _setValue(value)
        }
        if (top) {
            setTop(top)
        }
        return type
    }

    const closeSelf = () => {
        setShowFilterType(false)
        safeRun(SortBarRefs.get('onPanelChange'), false)
        refFilterPanelScene.onClosed?.()
    }

    useEffect(() => {
        refFilterPanelScene.closeSelf = closeSelf
        refFilterPanelScene.setValue = _setValue
    }, [])

    return <View className={typeof showFilterType === 'string' ? Styles.filterBox : ''}>
        {
            typeof showFilterType === 'string' ? <View className={Styles.mask} onClick={() => closeSelf()}/> : null
        }
        <Filter {...props} top={top} value={value} showFilterType={showFilterType} formatHooks={(value) => {
            return objToList(value, ['sceneSortType', 'sortType'])
        }}/>
    </View>
}

export default ExpandFilter

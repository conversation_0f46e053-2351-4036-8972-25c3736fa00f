import {useContext, useEffect, useRef, useState} from 'react'
import {pxTransform} from '@tarojs/taro'
import {isWeb} from '@/common/common'
import {isArray, isEmpty, isNumber} from '@/utils/isType'
import {E_TAB, TAB_MAP} from '../../store/result.model'
import ResultContext from '../../store/ResultContext'
import cloneDeep from 'lodash-es/cloneDeep'
import {showToast} from '@/BaseComponents/atoms/utils/toast'
import {E_ErrorStyle} from '../ErrorView'
import get from 'lodash-es/get'
import {pt} from '@ltfe/ltfe-core-lib/lib/utiles'
import {TIP_TYPE} from '../ListBottomTip'
import {ERROR_CODE} from '@/common/useFetch'
import addressModel from "@/store/model/address.model";

export enum E_ScenerySpotItem {
    /**
     * 商卡
     */
    GOODS,
    /**
     * 筛选后：无/少结果
     */
    NO_MATCHES,
    /**
     * 推荐的标题
     */
    RECOMMEND_TITLE,
    /**
     * 没有更多
     */
    LOADING,
    /**
     * 列表底部的tip
     */
    LIST_BOTTOM_TIP,

}

const PAGESIZE = 10

export function useGetScenerySpotData({data: _pData, error: _pError, scrollTop, currentTabIndex, expoEndData}) {
    const refPageNo = useRef(1)
    const refLoading = useRef(false)
    const refNoMore = useRef(false)

    const [data, setData] = useState<any>({})
    const [renderData, setRenderData] = useState({type: 'loading', data: null as any})
    const {service, useResultStore} = useContext(ResultContext)
    const updateTabContentLoading = useResultStore.use.updateTabContentLoading()
    const _reFetch = useRef<any>()

    const isNoMoreData = () => {
        return !data?.hasNextPage
    }

    const subsAddress = () => {
        // 订阅地址 如果地址返回了才请求接口
        addressModel.subscribe((result) => {
            if (TAB_MAP[currentTabIndex] === E_TAB.ScenerySpot) {
                reFetch()
            }
        })
    }

    useEffect(() => {
        let st1, st2
        const hideTypes = ['render', 'innerLoading', 'error']
        if (hideTypes.includes(renderData.type)) {
            st1 = service.hideTabSkeleton(E_TAB.ScenerySpot)
        }
        if (refLoading.current) {
            st2 = setTimeout(() => {
                // console.log('loadNextPage ==== 2')
                refLoading.current = false
            }, 20)
        }
        return () => {
            clearTimeout(st2)
        }
    }, [data, renderData])

    const resetState = () => {
        refLoading.current = false
        refNoMore.current = false
        refPageNo.current = 1
    }

    function reFetch(params = {
        isResetData: true,
        isRestFillter: false,
        isShowToast: false
    }) {
        if (params.isResetData) {
            resetState()
            setRenderData({
                type: 'innerLoading',
                data: []
            })
        } else {
            const _renderData = cloneDeep(renderData.data)
            if (isArray(_renderData) && (_renderData[_renderData.length - 1] as any)?._type === E_ScenerySpotItem.LIST_BOTTOM_TIP) {
                _renderData.pop()
                _renderData.push({
                    _type: E_ScenerySpotItem.LOADING,
                    data: {}
                })
                setRenderData({
                    type: 'render',
                    data: _renderData
                })
            }
        }
        fetchData({
            pageSize: PAGESIZE,
            page: refPageNo.current
        }, params)
    }

    _reFetch.current = reFetch

    useEffect(() => {
        async function init() {
            if (service.getHasLoadedMap(E_TAB.ScenerySpot)) {
                return
            }
            if (renderData.type === 'loading') {
                updateTabContentLoading(E_TAB.ScenerySpot, true)
            }
            if (TAB_MAP[currentTabIndex] === E_TAB.ScenerySpot) {
                service.setHasLoadedMap(E_TAB.ScenerySpot)
                if (!isEmpty(_pError)) {
                    handleError(_pError)
                } else if (!isEmpty(_pData)) {
                    if (_pData?.result) {
                        await handleResponse(_pData.result, {}, 1)
                        refPageNo.current++
                    } else {
                        setRenderData({
                            type: 'error',
                            data: {
                                type: E_ErrorStyle.OtherError,
                                onRetryParams: {
                                    isResetData: true,
                                    isRestFillter: true,
                                    isShowToast: false
                                }
                            }
                        })
                    }
                    //updateTabContentLoading(false)
                } else {
                    await fetchData({
                        pageSize: PAGESIZE,
                        page: refPageNo.current
                    })
                }
                subsAddress()
            }
        }

        init().catch((error) => {
            console.error('E_TAB.ScenerySpot error', error)
            service.hideTabSkeleton(E_TAB.ScenerySpot)
        })

    }, [currentTabIndex, _pData, renderData])

    const refFetchNo = useRef(0)
    // 请求数据
    const fetchData = async (params = {}, other = {} as any) => {
        refLoading.current = true
        const businessType = E_TAB.ScenerySpot
        const _params = {
            businessType,
            ...params
        }

        let result
        try {
            const pageIndex = refPageNo.current
            refFetchNo.current++
            const fetchNo = refFetchNo.current
            const [error, res] = await service.fetchResult(_params)
            if (fetchNo < refFetchNo.current) {
                return
            }
            if (error) {
                handleError(error)
            } else if (res?.result) {
                result = res.result
                await handleResponse(result, other, pageIndex)
                refPageNo.current++
            } else {
                setErrorByType({
                    errorType: E_ErrorStyle.OtherError,
                    renderType: E_ScenerySpotItem.LIST_BOTTOM_TIP,
                    tipType: TIP_TYPE.RETRY_BY_JSERROR
                })
            }
        } catch (error) {
            setErrorByType({
                errorType: E_ErrorStyle.OtherError,
                renderType: E_ScenerySpotItem.LIST_BOTTOM_TIP,
                tipType: TIP_TYPE.RETRY_BY_JSERROR
            })
        }
        //updateTabContentLoading(false)
    }

    const handleError = (errorInfo) => {
        if (errorInfo?.code == ERROR_CODE.NETWORK_TIMEOUT) {
            setErrorByType({
                errorType: E_ErrorStyle.NetError,
                renderType: E_ScenerySpotItem.LIST_BOTTOM_TIP,
                tipType: TIP_TYPE.RETRY_BY_NETERROR
            })
        } else {
            setErrorByType({
                errorType: E_ErrorStyle.OtherError,
                renderType: E_ScenerySpotItem.LIST_BOTTOM_TIP,
                tipType: TIP_TYPE.RETRY_BY_JSERROR
            })
        }
    }


    const popBottomElement = (_renderData) => {
        if (_renderData[_renderData.length - 1]._type === E_ScenerySpotItem.LOADING || _renderData[_renderData.length - 1]._type === E_ScenerySpotItem.LIST_BOTTOM_TIP) {
            _renderData.pop()
        }
    }

    // 通过类型处理错误内容
    const setErrorByType = ({errorType, renderType, tipType}) => {
        if (refPageNo.current === 1) { // 如果是第一页 就展示全屏的错误提示
            setRenderData({
                type: 'error',
                data: {
                    type: errorType,
                    onRetryParams: {
                        isResetData: true,
                        isRestFillter: true,
                        isShowToast: false
                    }
                }
            })
        } else { // 有数据的情况 异常了
            const _renderData = cloneDeep(renderData.data)
            popBottomElement(_renderData)
            _renderData.push({
                _type: renderType,
                data: {
                    type: tipType,
                    onRetryParams: {
                        isResetData: false,
                        isRestFillter: false,
                        isShowToast: false
                    }
                }
            })
            setRenderData({
                type: 'render',
                data: _renderData
            })
        }
    }

    const resultIsFewer = () => {
        return refNaturalCardVOList.current.length < 20
    }
    const refLayoutInfos = useRef<any[]>([])
    const refRequestCount = useRef(0)
    const refNeedAutoLoadNextPage = useRef(0)
    const refLastNeedAutoLoadNextPage = useRef(0)
    const refNaturalCardVOList = useRef<any[]>([])

    function withDataHandle(originalFunction) {
        return async function (...args) {
            let needAutoLoadNextPage = false
            /**
             * 如果pageIndex === 1，且此时result.naturalCardVOList是空，则继续请求下一页，
             * 并累计每页下发result.naturalCardVOList的数量，同时累计请求的次数：
             * 如果小于20条，且次数>5，停止下一页请求；如果大于等于20条，停止下一页请求
             */
            const [result, , pageIndex] = args

            if (result.hasNextPage) {
                if (pageIndex === 1) {
                    refRequestCount.current++
                    if (isEmpty(result.naturalCardVOList)) {
                        refPageNo.current++
                        loadNextPage({force: true})
                        return
                    } else if (result.naturalCardVOList?.length < 20) {
                        needAutoLoadNextPage = true
                    }
                } else if (resultIsFewer()) {
                    refRequestCount.current++
                    /** 已加载的商卡数量 */
                    needAutoLoadNextPage = true
                } else if (isEmpty(result.naturalCardVOList)) {
                    refRequestCount.current++
                    /** 如果非第1页，且当前页naturalCardVOList下发为空并且有下一页 */
                    needAutoLoadNextPage = true
                }
                if (refRequestCount.current > 4 && needAutoLoadNextPage) {
                    needAutoLoadNextPage = false
                    result.hasNextPage = false
                }
                if (!needAutoLoadNextPage) {
                    refRequestCount.current = 0
                }
            }
            if (needAutoLoadNextPage) {
                refNeedAutoLoadNextPage.current = Date.now()
            }
            await originalFunction(...args)
            refPageNo.current++
        }
    }

    useEffect(() => {
        if (refNeedAutoLoadNextPage.current !== refLastNeedAutoLoadNextPage.current) {
            refLastNeedAutoLoadNextPage.current = refNeedAutoLoadNextPage.current
            const st = setTimeout(() => {
                loadNextPage({force: true})
            }, 20)
            return () => {
                clearTimeout(st)
            }
        }
    }, [renderData])

    const handleResponse = withDataHandle(async (result = {}, other = {} as any, pageIndex: number) => {
        const {isResetData = false, isRestFillter = false, isShowToast = false} = other

        let _data = cloneDeep(result)

        // if (!isResetData && !isEmpty(data.naturalCardVOList) && !isEmpty(_data.naturalCardVOList)) {
        //     const naturalCardVOList = ([] as any[]).concat(data.naturalCardVOList).concat(_data.naturalCardVOList)
        //     _data['naturalCardVOList'] = naturalCardVOList
        // }

        // if (!isResetData && !isEmpty(data.naturalCardVOList) && isEmpty(_data.naturalCardVOList)) {
        //     _data.naturalCardVOList = data.naturalCardVOList
        // }

        if (isResetData) {
            refLayoutInfos.current = []
            expoEndData.current = {}
        }

        if ((isEmpty(data.filterPanelVOList) || isRestFillter) && !isEmpty(_data.naturalCardVOList)) {
            _data['filterPanelVOList'] = _data.filterPanelVOList
        } else {
            _data['filterPanelVOList'] = data.filterPanelVOList
        }

        let _result = renderData.data || []
        const isFirstPage = pageIndex === 1

        if (isResetData) {
            refLayoutInfos.current = []
            refNaturalCardVOList.current = []
        }

        if (!isFirstPage && !isResetData && !isEmpty(renderData.data)) {
            popBottomElement(renderData.data)
        } else {
            _result = []
        }

        if (!isEmpty(_data.naturalCardVOList)) {
            refNaturalCardVOList.current = refNaturalCardVOList.current.concat(_data.naturalCardVOList)
            _result = _result.concat(handleData(_data.naturalCardVOList, E_ScenerySpotItem.GOODS))
        }

        // 如果没有下一页了 自然流量数据小于20条 有进行筛选处理
        if (!_data.hasNextPage && resultIsFewer() && isMatching()) {
            _result.push({
                _type: E_ScenerySpotItem.NO_MATCHES,
                data: service.getOriginFilterList(E_TAB.ScenerySpot)
            })
            //  推荐流量有数据
            if (!isEmpty(_data.recommendCardVOList)) {
                _result.push({
                    _type: E_ScenerySpotItem.RECOMMEND_TITLE,
                    data: {
                        title: '为你推荐更多结果'
                    }
                })
                _result = _result.concat(handleData(_data.recommendCardVOList.map(item => {
                    item.cardVoType = 'recommendCard'
                    return item
                }), E_ScenerySpotItem.GOODS, {firstCount: true}))
            }
        }

        // 没有下一页了
        if (!_data.hasNextPage) {
            if (_result.length > 0 && _result[_result.length - 1]?._type !== E_ScenerySpotItem.NO_MATCHES) {
                _result.push({
                    _type: E_ScenerySpotItem.LIST_BOTTOM_TIP,
                    data: {
                        type: TIP_TYPE.NO_MORE
                    }
                })
            }
            refNoMore.current = true
        } else {
            _result.push({
                _type: E_ScenerySpotItem.LOADING,
                data: {}
            })
        }
        if (isShowToast && isEmpty(_data.naturalCardVOList)) {
            showToast({
                title: '没有找到匹配的结果，请修改筛选条件试试',
                icon: 'none',
                duration: 2000
            })
        }

        const layoutInfos = await measureLayout(_result)
        refLayoutInfos.current = refLayoutInfos.current.concat(layoutInfos)

        setData(_data)
        setRenderData({
            type: 'render',
            data: _result
        })
    })

    const refCalc = useRef<any>()
    const measureLayout = (data) => {
        if (isEmpty(data)) {
            return []
        }
        const titleStyle = {fontSize: isWeb ? pxTransform(16) : pt(16)}
        const tagStyle = {fontSize: isWeb ? pxTransform(11) : pt(11)}
        const tagPathItem = {
            nameFullPath: 'scenicCardVO.name',
            tagFullPath: 'hotelCardVO.promotionTagListMap.scenicTitleAfter'
        }

        const textObjs: any[] = []
        for (let index = 0; index < data.length; index++) {
            const item = data[index]
            if (isNumber(item.cartIndex)) {
                continue
            }
            const cardIndex = index
            item.cardIndex = index

            if (item._type !== E_ScenerySpotItem.GOODS) {
                continue
            }

            const itemData = item.data

            textObjs.push({
                text: get(itemData, tagPathItem.nameFullPath),
                textStyle: titleStyle,
                fullPath: tagPathItem.nameFullPath,
                cardIndex
            })

            const titleAfters = get(itemData, tagPathItem.tagFullPath)
            if (titleAfters) {
                const tagIndex = titleAfters.findIndex(x => x.styleCode === 'Tag')
                if (tagIndex > -1) {
                    textObjs.push({
                        text: titleAfters[tagIndex].listShowName,
                        textStyle: tagStyle,
                        fullPath: tagPathItem.tagFullPath,
                        index: tagIndex,
                        cardIndex
                    })
                }
            }
        }

        if (isEmpty(textObjs)) {
            return []
        }

        return new Promise((resolve) => {
            refCalc.current?.getLayoutList?.({
                texts: textObjs,
                callback: (val) => {
                    // console.log('val++++', val)
                    resolve(val)
                }
            })
        })

    }

    // 处理数据 给数据包一层业务映射
    /**
     * 处理数据并返回结果数组
     * @param data 数据数组
     * @param type 数据类型
     * @param option 选项对象，可选 pushRec 是否添加推荐数据
     * @returns 结果数组
     */
    const handleData = (data, type, option?: { firstCount?: boolean }) => {
        let result = <any>[]
        for (let i = 0; i < data.length; i++) {
            result.push({
                _type: type,
                data: data[i],
                firstCount: option?.firstCount ? i === 0 : false
            })
        }

        return result
    }

    const loadNextPage = (option?: { force: true }) => {
        // console.log('loadNextPage', refLoading.current, isNoMoreData(), data)
        if (!option?.force) {
            if (refLoading.current || isNoMoreData()) {
                return
            }
        }
        // console.log('loadNextPage ==== 1', refLoading.current)
        fetchData({
            pageSize: PAGESIZE,
            page: refPageNo.current
        })
    }

    /**
     * 筛选项 (带有搜筛选项的搜索结果)
     */
    const isMatching = () => {
        return service.hasFilterItems(E_TAB.ScenerySpot)
    }

    /**
     * 自然流量
     */
    const isNaturalFlow = () => {
        if (isEmpty(data.naturalCardVOList)) {
            return false
        }
        return true
    }

    /**
     * 自然流量：没有数据
     * */
    const isNoNaturalData = () => {
        if (!isNaturalFlow()) {
            return true
        }
        return false
    }

    /**
     * 判断是否存在过滤面板VO列表
     * @returns 是否存在过滤面板VO列表
     */
    const hasFilterPanelVOList = () => {
        // console.log('data.filterPanelVOList', data.filterPanelVOList)
        return !isEmpty(data.filterPanelVOList)
    }


    return {
        loadNextPage,
        reFetch,
        data,
        isMatching,
        renderData,
        mateParams: {...(data?.commonUserAction ?? {}), businessType: data?.tabType ?? ''},
        currentTabIndex,
        isNoNaturalData,
        refCalc,
        refLayoutInfos,
        _reFetch,
        hasFilterPanelVOList
    }
}

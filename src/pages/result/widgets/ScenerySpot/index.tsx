import {View, Loading} from '@/BaseComponents/atoms'
import {memo, useCallback, useContext, useRef} from 'react'
import {E_TAB, TAB_MAP} from '../../store/result.model'
import NoData from '../NoData'
import styles from './index.module.scss'
import SortBar from './SortBar'
import useScenerySpotService from './index.service'
import Card from '@/Components/Card'
import {useSafeAreaInsets} from 'react-native-safe-area-context'
import {pt} from '@ltfe/ltfe-core-lib/lib/utiles'
import {THEME_BASE} from '@ltfe/ltfe-core-lib/lib/utiles/theme'
import ResultContext from '../../store/ResultContext'
import FilterPanel, {refFilterPanelScene} from './FilterPanel'
import {E_ScenerySpotItem} from './useGetScenerySpotData'
import NoMathces from '../NoMatches'
import RecommendTitle from '../RecommendTitle'
import {safeRun, listToObjByKey} from '@/Components/Filter/utils'
import {SortBarRefs} from './SortBar/index'
import {isEmpty} from '@/utils/isType'
import ErrorView from '../ErrorView'
import useJumpTo from '@/common/useJumpTo'
import BackTop from '@/BaseComponents/BackTop'
import {Dimensions} from 'react-native'
import CalcTextLayout from '@/BaseComponents/CalcTextLayout'
import ListBottomTip from '../ListBottomTip'
import ScenerySpotSkeleton from './ScenerySpotSkeleton'
import {
    M_EVENTID,
    M_PAGEID,
    M_PAGE,
    newMta,
    mtaExposure,
    M_PANEL_NAME,
    ORDER_TYPE,
    MTA_NONE,
    MTA_NONE_NUMBER
} from '@/common/mta'
import {InView, IOFlatList} from '@/BaseComponents/IntersectionObserver'
import _ from 'lodash'
import IdleQueue from '@/utils/IdleQueue'
import {BasePageContext} from '@/common/withPage'
import {isAndroid} from '@/common/common'
import {errorCodeConstantMapping, ErrortType} from '@/common/reporter/errorMapping'
import {reportInfo} from '@/common/reporter'

const {width} = Dimensions.get('window')
const cardWidth = pt(width - 24)

const ScenerySpot = (props) => {
    const {service} = useContext(ResultContext)
    const basePageContext = useContext(BasePageContext)
    const expoEndData = useRef({})

    const {
        data,
        mateParams,
        loadNextPage,
        onScroll,
        getScrollRef,
        renderData,
        reFetch,
        currentTabIndex,
        scrollTop,
        isShowBackTop,
        isMatching,
        isNoNaturalData,
        refCalc,
        refLayoutInfos,
        _reFetch,
        hasFilterPanelVOList
    } = useScenerySpotService(props, expoEndData)
    const mtaRef = useRef()
    mtaRef.current = {...(data?.commonUserAction ?? {}), businessType: data?.tabType ?? ''}
    const refIsClear = useRef(false)
    const onClear = useCallback((item, index) => {
        refIsClear.current = true
        const newFilterList = service.deleteOriginFilterList(E_TAB.ScenerySpot, index)
        refFilterPanelScene.setValue(listToObjByKey(newFilterList, 'filterKey'))
    }, [])


    const jumpTo = useJumpTo()

    // 渲染列表中的每一项
    const renderItem = useCallback(({item, index}) => {
        try {
            const {_type, data, cardIndex, firstCount} = item as any || {}
            switch (_type) {
                case E_ScenerySpotItem.GOODS: {
                    // 商品
                    const layoutInfo = refLayoutInfos.current?.filter(x => x?.cardIndex === cardIndex)
                    return renderGoods(data, index, layoutInfo, firstCount)
                }

                case E_ScenerySpotItem.NO_MATCHES: // 没匹配上筛选
                    return <NoMathces data={data} onClear={onClear} index={index} businessType={E_TAB.ScenerySpot}
                                      commonUserAction={mateParams}/>

                case E_ScenerySpotItem.RECOMMEND_TITLE: // 推荐的标题
                    return <RecommendTitle data={data}/>

                case E_ScenerySpotItem.LIST_BOTTOM_TIP: // 没有更多数据
                    return <ListBottomTip type={data.type} onRetry={() => (_reFetch.current)(data.onRetryParams)}/>

                case E_ScenerySpotItem.LOADING: // 加载中
                    return <Loading/>

                default:
                    return null
            }
        } catch (error) {
            reportInfo({
                code: errorCodeConstantMapping?.LOADERROR_BUSINESS_IMMEDIATE_ATTENTION,
                errorDetail: {
                    errorType: ErrortType.Info,
                    customMsg: {
                        compName: '景点渲染异常',
                        errorInfo: error?.message,
                        errorStack: error?.stack
                    }
                }
            })
            return <></>
        }

    }, [mateParams])
    const handelExpo = useCallback((visible, index, item, userAction) => {
        const {cardType, cardVoType, scenicCardVO = {}} = item ?? {}
        const {free, id, price, promotionTagListMap = {}, wareRankVO = {}} = scenicCardVO ?? {}
        if (visible && !expoEndData.current?.[id]) {
            const {scenicScore = [], scenicRightPromotion = []} = promotionTagListMap ?? {}
            let score = ''
            if (scenicScore && Array.isArray(scenicScore)) {
                scenicScore?.forEach((item) => {
                    if (item.trackId === 'scenicScore') {
                        score = item?.listShowName
                    }
                })
            }
            let tagList = []
            scenicRightPromotion?.forEach(item => {
                tagList.push({
                    trackId: item?.trackId || MTA_NONE_NUMBER,
                    labelName: item?.listShowName || MTA_NONE_NUMBER
                })
            })

            IdleQueue.add(mtaExposure, cardVoType === 'recommendCard' ? M_EVENTID.TravelSearcResultRecScenicExpo : M_EVENTID.TravelSearcResultScenicExpo, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                ...(mateParams ?? {}),
                displayName: mateParams?.keyword ?? MTA_NONE,
                index,// 位置 number
                cardType: cardType, // 卡片类型 string
                startingPrice: price, // 起始价 string
                score, // 评分 string
                free: free ? '1' : '0', // 是否免费 string
                scenicId: id, //景点ID string
                tagList,
                discountType: -100,
                rankId: wareRankVO?.rankId || MTA_NONE,
                ...userAction
            })

            expoEndData.current[id] = true
        }
    }, [mateParams])
    const onclick = useCallback((cardProps, index, cardVoType, userAction) => {
        const {free, id, price, promotionTagListMap = {}, wareRankVO = {}} = cardProps
        const {scenicScore = [], scenicRightPromotion = []} = promotionTagListMap
        let score = ''
        if (scenicScore && Array.isArray(scenicScore)) {
            scenicScore?.forEach((item) => {
                if (item.trackId === 'scenicScore') {
                    score = item?.listShowName
                }
            })
        }
        let tagList = []
        scenicRightPromotion?.forEach(item => {
            tagList.push({
                trackId: item?.trackId || MTA_NONE_NUMBER,
                labelName: item?.listShowName || MTA_NONE_NUMBER
            })
        })
        IdleQueue.add(newMta, cardVoType === 'recommendCard' ? M_EVENTID.TravelSearcResultRecScenic : M_EVENTID.TravelSearcResultScenic, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
            ...(mateParams ?? {}),
            displayName: mateParams?.keyword ?? MTA_NONE,
            index,// 位置 number
            cardType: '2', // 卡片类型 string
            startingPrice: price, // 起始价 string
            score, // 评分 string
            free: free ? '1' : '0', // 是否免费 string
            scenicId: id, //景点ID string
            tagList,
            rankId: wareRankVO?.rankId || MTA_NONE,
            discountType: -100,
            ...userAction
        })

        if (cardProps && cardProps?.jumpUrl) {
            jumpTo({to: 'web', params: {url: decodeURIComponent(cardProps?.jumpUrl)}})
        }
    }, [mateParams])
    // 渲染商品
    const renderGoods = useCallback((item, index, layoutInfo, firstCount) => {
        const {cardType, scenicCardVO, cardVoType, userAction} = item

        return (<InView onChange={(visible) => {
            handelExpo(visible, index, item, userAction)
        }} index={index}>
            <Card
                layoutInfo={layoutInfo}
                mode={cardType}
                data={scenicCardVO}
                cardWidth={cardWidth}
                onPress={(cardProps, target) => {
                    onclick(cardProps, index, cardVoType, userAction)
                }}
                style={{
                    backgroundColor: '#fff',
                    paddingTop: (index !== 0 && firstCount !== true) ? pt(10) : 0,
                    paddingBottom: pt(10),
                    borderBottomWidth: pt(1),
                    paddingLeft: pt(12),
                    paddingRight: pt(12),
                    borderColor: THEME_BASE.middleColorThree
                }}/>
        </InView>)
    }, [mateParams])

    const getItemListParam = (args, filterPanelCode) => {
        try {
            const filterItemList = _.get(args, [0, filterPanelCode], [])
            if(!Array.isArray(filterItemList) || filterItemList.length === 0) {
                return []
            }

            return filterItemList?.map(item => {
                const metaData = _.get(item, 'metaData')
                return {
                    ...metaData,
                    groupCode: _.get(item, 'parents.metaData.groupCode', -100)
                }
            })
        }catch(e) {
            console.log("getItemListParam error->", e)
            return []
        }
    }

    const insets = useSafeAreaInsets()
    // console.log('render了景点11', renderData)

    // if ((TAB_MAP[currentTabIndex] !== E_TAB.ScenerySpot) || (renderData.type === 'loading')) {
    //   console.log('进入null')

    //   return null
    // }
    const notRender = (TAB_MAP[currentTabIndex] !== E_TAB.ScenerySpot) || (renderData.type === 'loading')

    return (
        <View className={styles.wrapper}>

            {
                notRender ? null :
                    <>
                        {
                            renderData.type === 'error' && (
                                <ErrorView type={renderData.data?.type}
                                           onRetry={() => (_reFetch.current)(data.onRetryParams)}/>
                            )
                        }
                        {
                            // ((renderData.type === 'render' || renderData.type === 'innerLoading') &&
                            // (!(isNoNaturalData()) || hasFilterPanelVOList())) &&
                            (renderData.type === 'render' || renderData.type === 'innerLoading') && (!(isNoNaturalData()) || hasFilterPanelVOList()) && (
                                <>
                                    <SortBar mateData={mtaRef.current} options={data?.filterPanelVOList}/>
                                    <FilterPanel
                                        channel="search"
                                        options={data?.filterPanelVOList || []}
                                        resInfo={data?.countSummaryVO?.resultCountShowText}
                                        id={'scenery'}
                                        handleInView={(isShow, item, value) => {
                                            const {displayName} = basePageContext.getPageParams()
                                            const {sortType, orderType, index} = _.get(item, ['metaData'], {})
                                            mtaExposure(M_EVENTID.TravelSearcResultOrderExpo, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                                                ...(mtaRef?.current ?? {}),
                                                sortType,
                                                orderType: ORDER_TYPE[orderType],
                                                index,
                                                isSelect: value.some(valueItem => valueItem.sameKey === item.sameKey),
                                                displayName
                                            })
                                        }}
                                        onChange={(...args) => {
                                            const isClear = refIsClear.current
                                            refIsClear.current = false
                                            service.onFilterChange(E_TAB.ScenerySpot, args, () => {
                                                reFetch({
                                                    isResetData: true,
                                                    isShowToast: isClear ? false : true,
                                                    isRestFillter: false
                                                })
                                            })
                                            // console.log('jdfilter', ...args)
                                            // service.onFilterChange(E_TAB.ScenerySpot, args, reFetch)
                                            safeRun(SortBarRefs.get('onChange'), ...args)
                                        }} onClear={(...args) => {
                                        const {displayName} = basePageContext.getPageParams()
                                        const filterPanelCode = _.get(args, [args.length - 1], false)
                                        IdleQueue.add(newMta, M_EVENTID.TravelSearcResultFilterClear, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                                            ...(mtaRef?.current ?? {}),
                                            filterPanelName: M_PANEL_NAME[filterPanelCode],
                                            filterPanelCode,
                                            displayName
                                        })
                                        service.onFilterChange(E_TAB.ScenerySpot, args, () => {
                                            reFetch({
                                                isResetData: true,
                                                isShowToast: false,
                                                isRestFillter: false
                                            })
                                        })
                                        safeRun(SortBarRefs.get('onChange'), ...args)
                                    }}
                                        onOk={(...args) => {
                                            const filterPanelCode = _.get(args, [args.length - 1], false)
                                            const {displayName} = basePageContext.getPageParams()

                                            if (filterPanelCode.includes('SortType')) {
                                                const sortInfo = _.get(args, [0, 'sceneSortType', 0, 'metaData'], {})
                                                IdleQueue.add(newMta, M_EVENTID.TravelSearcResultOrder, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                                                    ...(mtaRef?.current ?? {}),
                                                    orderType: sortInfo?.sortType,
                                                    sortType: ORDER_TYPE[sortInfo?.orderType],
                                                    index: sortInfo?.index,
                                                    displayName
                                                })
                                            } else {
                                                IdleQueue.add(newMta, M_EVENTID.TravelSearcResultFilterConfirm, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                                                    ...(mtaRef?.current ?? {}),
                                                    filterPanelName: M_PANEL_NAME[filterPanelCode],
                                                    filterPanelCode,
                                                    itemList: getItemListParam(args, filterPanelCode),
                                                    displayName
                                                })
                                            }
                                            safeRun(SortBarRefs.get('refPanel'), false)
                                        }}
                                    />
                                </>
                            )
                        }
                        {
                            renderData.type === 'innerLoading' && <ScenerySpotSkeleton/>
                        }
                        {
                            (renderData.type === 'render' && !isEmpty(renderData.data)) && (
                                <>

                                    <IOFlatList
                                        style={{paddingBottom: insets.bottom}}
                                        // contentContainerStyle={{paddingHorizontal: pt(12)}}
                                        // scrollEnabled={!showNoData}
                                        keyboardShouldPersistTaps="handled"
                                        decelerationRate={isAndroid ? 0.985 : 0.994}
                                        windowSize={21}
                                        scrollEventThrottle={16}
                                        data={renderData.data}
                                        onScroll={onScroll}
                                        renderItem={renderItem}
                                        keyExtractor={(item, index) => index.toString()}
                                        onEndReached={loadNextPage}
                                        ref={getScrollRef}
                                    />
                                </>
                            )
                        }
                        {
                            (renderData.type === 'render' && isEmpty(renderData.data)) && (
                                <NoData className={styles.noData}/>
                            )
                        }
                        <BackTop isShow={isShowBackTop} handleClick={scrollTop}/>
                    </>

            }
            <CalcTextLayout ref={refCalc}/>
        </View>
    )
}

export default memo(ScenerySpot)

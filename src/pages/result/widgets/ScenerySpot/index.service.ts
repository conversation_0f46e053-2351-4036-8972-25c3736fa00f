import { useGetScenerySpotData } from "./useGetScenerySpotData";
import { useScrollService } from "./useScrollService";

export default function useScenerySpotService(props, expoEndData) {
    const {
        onScroll,
        getScrollRef,
        scrollTop,
        isShowBackTop,
    } = useScrollService(props)

    const {
        loadNextPage,
        reFetch,
        data,
        mateParams,
        renderData,
        currentTabIndex,
        isMatching,
        isNoNaturalData,
        refCalc,
        refLayoutInfos,
        _reFetch,
        hasFilterPanelVOList
    } = useGetScenerySpotData({ ...props, scrollTop, expoEndData });





    return {
        data,
        mateParams,
        loadNextPage,
        onScroll,
        getScrollRef,
        renderData,
        reFetch,
        currentTabIndex,
        scrollTop,
        isShowBackTop,
        isMatching,
        isNoNaturalData,
        refCalc,
        refLayoutInfos,
        _reFetch,
        hasFilterPanelVOList
    }
}
import { useRef, useState } from "react"
import { deviceHeight } from '@ltfe/ltfe-core-lib/lib/utiles';


// 滚动的service
export function useScrollService(props) {

     const scrollRef = useRef()

     const [isShowBackTop, setIsShowBackTop] = useState(false) // 是否展示返回顶部

   
     // 超过一屏展示返回顶部
     const showToTop = (e) => {
       const { y } = e.nativeEvent.contentOffset;
       if (y > deviceHeight) {
         setIsShowBackTop(true)
       } else {
         setIsShowBackTop(false)
       }
     }

     const _getScrollRef = (e) => {
          scrollRef.current = e;
     }

     // 点击回到顶部
     const scrollTop = () => {
          scrollRef.current?.scrollToOffset({ animated: true, offset: 0 });
     }


     /**
      * 滚动
      */
     const onScroll = (e) => {
          showToTop(e)
     }


     return {
          onScroll,
          getScrollRef: _getScrollRef,
          scrollTop,
          isShowBackTop,
     }
}
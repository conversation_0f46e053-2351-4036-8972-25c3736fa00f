import {Image, Text, View} from '@/BaseComponents/atoms'
import {memo, useContext, useEffect, useRef, useState} from 'react'
import styles from './index.module.scss'
import {refFilterPanelScene} from '../FilterPanel'
import _ from 'lodash'
import {safeRun, getImg} from '@/Components/Filter/utils'
import {M_EVENTID, M_PAGE, M_PAGEID, M_PANEL_NAME, newMta, ORDER_TYPE} from '@/common/mta'
import useScenerySpotService from '@/pages/result/widgets/ScenerySpot/index.service'
import IdleQueue from '@/utils/IdleQueue'
import {BasePageContext} from '@/common/withPage'
import {isAndroid} from '@/common/common'

export const SortBarRefs = new Map()

function SortBar(props) {
    const {options, mateData = {}} = props
    const [_options] = useState(_.cloneDeep(options))
    const [filterValues, setValues] = useState({})
    const [activeType, setActiveType] = useState(false)
    const basePageContext = useContext(BasePageContext)

    useEffect(() => {
        SortBarRefs.set('onChange', (value) => {
            setValues(value)
        })
        SortBarRefs.set('refPanel', refFilterPanelScene.toggle)
        SortBarRefs.set('onPanelChange', setActiveType)
    }, [])

    const getFiletCount = (key) => {
        return _.get(filterValues, key, []).length
    }

    const getListName = (list, defaultName) => {
        return Array.isArray(list) ? list.map(item => _.get(item, ['metaData', 'itemName'], _.get(item, ['metaData', 'filterName']))).join(',') : defaultName
    }

    return (
        <View className={styles.wr}>
            <View className={styles.sortBarWr}>
                <View className={styles.filterItemBox} onClick={() => {
                    const {displayName} = basePageContext.getPageParams()
                    const sortInfo = _.get(filterValues, ['sceneSortType', 0, 'metaData'], {})
                    IdleQueue.add(newMta, M_EVENTID.TravelSearcResultOrderEntrance, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                        ...mateData,
                        orderType: sortInfo?.sortType,
                        sortType: ORDER_TYPE[sortInfo.orderType],
                        displayName
                    })

                    safeRun(refFilterPanelScene.toggle, {
                        type: 'sceneSortType',
                        value: filterValues
                    })
                }}>
                    <Text
                        className={`${(_.get(filterValues, ['sceneSortType', 0, 'metaData', 'filterType'], '') === '' && activeType !== 'sceneSortType') ? styles.sortText : styles.sortTextAct} bold`}
                    >
                        {_.get(filterValues, ['sceneSortType', 0, 'metaData', 'filterName'], '综合排序')}
                        {_.get(filterValues, ['sceneSortType', 0, 'metaData', 'tips'], '')}
                    </Text>

                    {
                        activeType === 'sceneSortType' ? <Image className={styles.filterArrow}
                                                                src={getImg('filterArrowAct')}/> :
                            (_.get(filterValues, ['sceneSortType', 0, 'metaData', 'filterType'], '') === '' && activeType !== 'sceneSortType') ?
                                <Image className={styles.filterArrow}
                                       src={getImg('filterArrow')}/> : <Image className={styles.filterArrowDown}
                                                                              src={getImg('filterArrowAct')}/>
                    }
                </View>

                {
                    Array.isArray(_options) && _options.filter(item => {
                        const {filterList} = item
                        return Array.isArray(filterList) && filterList.length > 0
                    }).map((item, index) => {
                        const {filterPanelCode, filterPanelName, filterPanelTextType} = item
                        return <View className={styles.filterItemBox} key={index}
                                     onClick={() => {
                                         const {displayName} = basePageContext.getPageParams()
                                         IdleQueue.add(newMta, M_EVENTID.TravelSearcResultFilter, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                                             ...mateData,
                                             'filterPanelName': M_PANEL_NAME[filterPanelCode],
                                             'filterPanelCode': filterPanelCode,
                                             displayName
                                         })

                                         safeRun(refFilterPanelScene.toggle, {
                                             type: filterPanelCode,
                                             value: filterValues
                                         })
                                     }}>
                            <Text numberOfLines={1}
                                  className={`${(activeType === filterPanelCode || getFiletCount(filterPanelCode) > 0) ? styles.sortTextAct : styles.sortText} bold`}
                            >
                                {(filterPanelTextType !== 1 || getFiletCount(filterPanelCode) === 0) ? filterPanelName : getListName(_.get(filterValues, [filterPanelCode]), filterPanelName)}
                            </Text>
                            {
                                filterPanelTextType !== 1 && getFiletCount(filterPanelCode) > 0 ?
                                    <View className={styles.filterCountBox}><Text
                                        className={isAndroid ? styles.filterCountAndroid : styles.filterCount}>{getFiletCount(filterPanelCode)}</Text></View> : ''
                            }
                            {
                                activeType === filterPanelCode ? <Image className={styles.filterArrow}
                                                                        src={getImg('filterArrowAct')}/> : getFiletCount(filterPanelCode) === 0 ?
                                    <Image className={styles.filterArrow} src={getImg('filterArrow')}/> :
                                    <Image className={styles.filterArrowDown} src={getImg('filterArrowAct')}/>
                            }
                        </View>
                    })
                }
            </View>
        </View>
    )
}

export default memo(SortBar)

import { getWatcher } from "@/common/common";

export const AllPanelRefs = [
]

/**
 * 关闭除指定面板外的其他面板。
 * @param ref 要保留打开的面板的引用。
 */
export const closeOthers = (ref) => {
    AllPanelRefs.map((item) => {
        if (item !== ref) {
            // 执行close
            item?.closeSelf?.()
        }
    })
}

/**
 * 切换功能的导出函数
 * @param fn 要执行的函数
 * @returns 返回一个函数，执行时会关闭其他功能
 */
export function withCloseOther(ref) {
    return function (fn) {
        return function (...args) {
            closeOthers(ref);
            return fn(...args);
        };
    }
}

/**
 * 被观察者句柄
 */
export const watcher = getWatcher();


import {Image, Text, View} from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import classNames from 'classnames'
import React from 'react'
import {getImg} from '@/Components/Filter/utils'

const RecommendTitle = (props) => {

    const { data, cStyle = {}, showIcon } = props

    if (!data.title) {
        return null
    }

    return (
        <View className={styles.wrapper} style={cStyle}>
            {
                showIcon ? <Image className={styles.goodIcon} src={getImg('good')}/> : null
            }
            <Text className={classNames('bold', styles.title)}>{data.title}</Text>
        </View>
    )

}

export default RecommendTitle

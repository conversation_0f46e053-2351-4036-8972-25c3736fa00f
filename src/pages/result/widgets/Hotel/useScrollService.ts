import { useEffect, useRef, useState } from "react"
import { deviceHeight } from '@ltfe/ltfe-core-lib/lib/utiles';
import { panelWatcher, scollWatcher } from "./communication";
import { isWeb } from "@/common/common";


// 滚动的service
export function useScrollService(props) {

     const scrollRef = useRef<any>()

     const [isShowBackTop, setIsShowBackTop] = useState(false) // 是否展示返回顶部

     const [scrollEnabled, setScrollEabled] = useState(true)

     useEffect(() => {
          const subcriber = panelWatcher.observable.subscribe((result) => {
               // console.log('panelWatcher',scrollRef.current?.setNativeProps)
               if(isWeb) {
                    setScrollEabled(result.payload.enabled)
                    return
               }
               scrollRef.current?.setNativeProps?.({ scrollEnabled: result.payload.enabled })
          })

          return () => {
               subcriber.unsubscribe()
          }
     }, [])

     // 超过一屏展示返回顶部
     const showToTop = (e) => {
          const { y } = e.nativeEvent.contentOffset;
          if (y > deviceHeight) {
               setIsShowBackTop(true)
          } else {
               setIsShowBackTop(false)
          }
     }

     const _getScrollRef = (e) => {
          scrollRef.current = e;
     }

     // 点击回到顶部
     const scrollToTop = (option?: { y: number, animated: boolean }) => {
          const { y, animated } = option || {}
          scrollRef.current?.scrollToOffset({ animated: animated ?? true, offset: y || 0 });
     }


     const refScrollTop = useRef<undefined | 0>()
     /**
      * 滚动
      */
     const onScroll = (e) => {
          refScrollTop.current = e.nativeEvent.contentOffset.y
          scollWatcher.observer?.next({ action: 'scroll', payload: refScrollTop.current })
          showToTop(e)
     }


     return {
          onScroll,
          getScrollRef: _getScrollRef,
          scrollToTop,
          refScrollTop,
          isShowBackTop,
          scrollEnabled
     }
}
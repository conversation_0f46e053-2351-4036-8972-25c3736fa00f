import {Image, Loading, View} from '@/BaseComponents/atoms'
import React, {memo, useCallback, useContext, useEffect, useRef, useState} from 'react'
import {FlatList} from 'react-native'
import useHotelService from './index.service'
import BaseInfo from './BaseInfo'
import ExpandPanel from './BaseInfo/ExpandPanel'
import ExpandHotelSearchPanel from './BaseInfo/ExpandHotelSearchPanel'
import {E_TAB, TAB_MAP} from '../../store/result.model'
import ClearArea from './ClearArea'
import NoMathces from '../NoMatches'
import NoData from '../NoData'
import styles from './index.module.scss'
import SortBar, {SortBarRefs} from './SortBar'
import Bubble from './Bubble'
import _ from 'lodash'
import {isEmpty} from '@/utils/isType'
import {pt} from '@ltfe/ltfe-core-lib/lib/utiles'
import ResultContext from '../../store/ResultContext'
import FilterPanel, {refFilterPannelHotel} from './FilterPanel'
import {safeRun} from '@/Components/Filter/utils'
import {E_HotelItem} from './useGetHotelData.service'
import {useLoad} from '@tarojs/taro'
import RecommendTitle from '../RecommendTitle'
import {useSafeAreaInsets} from 'react-native-safe-area-context'
import HotelCard from './HotelCard'
import classNames from 'classnames'
import ErrorView from '../ErrorView'
import useJumpTo from '@/common/useJumpTo'
import hasLogin, {doLogin} from '@/common/Login'
import BackTop from '@/BaseComponents/BackTop'
import {QuickMatchTabRefs} from '@/pages/result/widgets/Hotel/SortBar/QuickMatchTab/index'
import CalcTextLayout from '@/BaseComponents/CalcTextLayout'
import ListBottomTip from '../ListBottomTip'
import {Dimensions} from 'react-native'
import HotelSkeleton from './HotelSkeleton'
import { isWeb } from '@/common/common'

import {
    M_EVENTID,
    M_PAGEID,
    M_PAGE,
    newMta,
    mtaExposure,
    M_PANEL_NAME,
    ORDER_TYPE,
    MTA_NONE,
    MTA_NONE_NUMBER,
    mtaEp
} from '@/common/mta'
import {InView, IOFlatList} from '@/BaseComponents/IntersectionObserver'
import IdleQueue from '@/utils/IdleQueue'
import {BasePageContext} from '@/common/withPage'
import {isAndroid} from '@/common/common'
import {errorCodeConstantMapping, ErrortType} from '@/common/reporter/errorMapping'
import {reportInfo} from '@/common/reporter'
import DiscountPop from '@/pages/VResult/DiscountPop'
import ListTips from '@/pages/result/widgets/ListBottomTip'

const {width} = Dimensions.get('window')
const cardWidth = pt(width - 24)

const Hotel = (props) => {
    const expoEndData = useRef({})
    // 使用ResultContext中的useResultStore
    const {service, useResultStore} = useContext(ResultContext)
    const basePageContext = useContext(BasePageContext)
    const [cardInfo, setCardInfo] = useState({})
    const refRootNode = useRef<any>()

    const getRefNode = useCallback(() => {
        return refRootNode.current
    }, [])

    const updateTabContentLoading = useResultStore.use.updateTabContentLoading()

    useLoad(() => {
        updateTabContentLoading(E_TAB.Hotel, true)
    })
    // 使用useHotelService hook获取数据和相关状态
    const {
        loadNextPage,
        onScroll,
        getScrollRef,
        reFetch,
        renderData,
        currentTabIndex,
        data,
        mateParams,
        scrollToTop,
        refScrollTop,
        isShowBackTop,
        refCalc,
        refLayoutInfos,
        _reFetch,
        scrollEnabled,
        reloadDataIfUserIdentityChange
    } = useHotelService(props, expoEndData)
    const mtaRef = useRef()
    mtaRef.current = {...(data?.commonUserAction ?? {}), businessType: data?.tabType ?? ''}
    const [catchObj, setCatchObj] = useState({})

    const getWidgetEventId = (trackData: any, isNutural) => {
        const {isExposure, scene} = trackData
        if(scene === 'subsidy'){
            if(isNutural) {
            return isExposure ? 'TravelSearcResult_SubsidyRoomExpo' : 'TravelSearcResult_SubsidyRoom'
            }else {
            return isExposure ? 'TravelSearcResult_RecSubsidyRoomExpo' : 'TravelSearcResult_RecSubsidyRoom'
            }
        }
    }

    const getItemListParam = (args, filterPanelCode) => {
        try {
            const filterItemList = _.get(args, [0, filterPanelCode], [])
            if(!Array.isArray(filterItemList) || filterItemList.length === 0) {
                return []
            }

            return filterItemList?.map(item => {
                const metaData = _.get(item, 'metaData')
                return {
                    ...metaData,
                    groupCode: _.get(item, 'parents.metaData.groupCode', -100)
                }
            })
        }catch(e) {
            console.log("getItemListParam error->", e)
            return []
        }
    }

    const jumpTo = useJumpTo()
    const onPress = useCallback(async (cardProps, target, index, cardInfo, tagInfo) => {
        const {data} = cardInfo
        const {cardVoType} = data ?? {}

        const {trigger, type} = target

        const {id = '', originPrice = '', price = '', promotionTagListMap = {}, wareRankVO = {}} = cardProps ?? {}
        const {hotelScore = [], hotelRightPromotion} = promotionTagListMap ?? {}

        let score = ''
        Array.isArray(hotelScore) && hotelScore.forEach(item => {
            if (item?.styleCode === 'Score') {
                score = item?.listShowName ?? ''
            }
        })
        let tagList = []
        Array.isArray(hotelRightPromotion) && hotelRightPromotion.forEach(item => {
            tagList.push({
                trackId: item?.trackId || MTA_NONE_NUMBER,
                labelName: item?.listShowName || MTA_NONE_NUMBER
            })
        })

        const discountList = _.get(cardProps, ['promotionLayerVO', 'promotionDetailList'], -100)
        const tagBeltCode = _.get(cardProps, ['beltInfoVO', 'beltCode'], '-100')

        const discountItemCount = _.get(cardProps, 'discountItemCount', 0)
        const discountPrice = _.get(cardProps, 'discountPrice', '')
        const discountText = discountItemCount ? `${discountItemCount}项优惠${discountPrice}` : `优惠${discountPrice}`
    
        const mtaInfo = {
            ...(mateParams ?? {}),
            displayName: mateParams?.keyword ?? MTA_NONE,
            index,// 位置 number
            cardType: '1', // 卡片类型 string
            firpricetype: '11', // 第一价格分类 string
            firprice: price ? price * 1 : MTA_NONE_NUMBER, // 第一价格金额 number
            secpricetype: '52', // 第二价格类型 string
            secprice: originPrice ? originPrice * 1 : MTA_NONE_NUMBER, //第二价格金额 number
            score, // 评分 string
            hotelId: id, //酒店ID string
            tagList, // 标签
            discountType: Array.isArray(discountList) ? discountList.map(item => item.type) : -100,
            rankId: wareRankVO?.rankId || MTA_NONE,
            beltCode: tagBeltCode,
            promotionName: _.get(tagInfo, 'name', discountText), // 优先使用tagInfo中的name，否则使用构建的discountText
            ..._.get(cardProps, ['userAction'], {})
        }

        if (trigger === 'discount' && _.get(cardProps, ['promotionLayerVO'])) {
            IdleQueue.add(newMta, cardVoType === 'recommendCardVOList' ? M_EVENTID.TravelSearcResult_RecDiscount : M_EVENTID.TravelSearcResultDiscount, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, mtaInfo)
            setCardInfo({
                cardProps,
                target,
                index,
                showPopup: 'discount',
                cardVoType
            })
            return
        }

        if (cardVoType === 'recommendCardVOList') {
            IdleQueue.add(newMta, type === 'popup' ? M_EVENTID.TravelSearcResult_RecDiscountLayer_Order : M_EVENTID.TravelSearcResultRecHotel, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, mtaInfo)
        } else {
            IdleQueue.add(newMta, type === 'popup' ? M_EVENTID.TravelSearcResultDiscountLayerOrder : M_EVENTID.TravelSearcResultHotel, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, mtaInfo)
        }

        // 记录访问缓存
        catchObj[cardProps.id] = true
        setCardInfo({...catchObj})

        const isLogin = await hasLogin()
        if (!isLogin) {
            const logged = await doLogin()
            if (!logged) {
                reFetch({
                    isResetData: true,
                    isRestFillter: false,
                    isShowToast: false
                })
                return
            }
        }

        if (_.get(cardProps, 'type') === 'popup') {
            setCardInfo({})
        }

        if (cardProps && cardProps?.jumpUrl) {
            // let url = cardProps?.jumpUrl.split('?')
            // let params = []
            // if(url?.[1] && url[1]?.indexOf('&') > -1) {
            //     params = url?.[1].split('&')
            //     params = params?.filter((item, index) => index !== params.length -1)
            // }
            // let _jumpUrl = params?.length ? url[0] + '?' + params.join('&') : url?.[0] ?? ''

            jumpTo({to: 'web', params: {url: decodeURIComponent(cardProps.jumpUrl)}})
        }
    }, [mateParams])

    const refIsClear = useRef(false)
    const onClear = useCallback((item, index) => {
        refIsClear.current = true
        const newFilterList = service.deleteOriginFilterList(E_TAB.Hotel, index)
        refFilterPannelHotel.setValue(newFilterList)
    }, [])

    const getScrollTop = useCallback(() => {
        return refScrollTop.current
    }, [])
    const handelExpo = useCallback((visible, index, item) => {
        const {data} = item ?? {}
        const {cardType = '', hotelCardVO, cardVoType} = data ?? {}
        const {id = '', price = '', originPrice = '', promotionTagListMap, wareRankVO = {}} = hotelCardVO ?? {}
        if (visible && !expoEndData.current?.[id]) {
            const {hotelScore = [], hotelRightPromotion = []} = promotionTagListMap ?? {}
            let score = ''
            hotelScore.forEach(item => {
                if (item?.styleCode === 'Score') {
                    score = item?.listShowName ?? ''
                }
            })
            let tagList = []
            hotelRightPromotion.forEach(item => {
                tagList.push({
                    trackId: item?.trackId || MTA_NONE_NUMBER,
                    labelName: item?.listShowName || MTA_NONE_NUMBER
                })
            })
            const discountList = _.get(hotelCardVO, ['promotionLayerVO', 'promotionDetailList'], -100)
            const tagBeltCode = _.get(hotelCardVO, ['beltInfoVO', 'beltCode'], -100)
            const discountPrice = _.get(hotelCardVO, 'discountPrice', '')
            const discountItemCount = _.get(hotelCardVO, 'discountItemCount', 0)
            let promotionName = '-100'
            if (!isEmpty(discountPrice)) {
                if (isEmpty(discountItemCount)) {
                    promotionName = `优惠${discountPrice}`
                } else {
                    promotionName = `${discountItemCount}项优惠${discountPrice}`
                }
            }
            // 到手价： 11 划线价：52
            IdleQueue.add(mtaExposure, cardVoType === 'recommendCardVOList' ? M_EVENTID.TravelSearcResultRecHotelExpo : M_EVENTID.TravelSearcResultHotelExpo, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                ...(mateParams ?? {}),
                displayName: mateParams?.keyword ?? MTA_NONE,
                index,// 位置 number
                cardType: cardType, // 卡片类型 string
                firpricetype: '11', // 第一价格分类 string
                firprice: price ? price * 1 : MTA_NONE_NUMBER, // 第一价格金额 number
                secpricetype: '52', // 第二价格类型 string
                secprice: originPrice ? originPrice * 1 : MTA_NONE_NUMBER, //第二价格金额 number
                score, // 评分 string
                hotelId: id, //酒店ID string
                tagList, // 标签
                discountType: Array.isArray(discountList) ? discountList.map(item => item.type) : -100,
                rankId: wareRankVO?.rankId || MTA_NONE,
                beltCode: tagBeltCode,
                ..._.get(item, ['data', 'userAction']),
                promotionName: promotionName
            })
            expoEndData.current[id] = true
        }
    }, [mateParams])
    // 渲染列表中的每一项
    const renderItem = useCallback(({item = {}, index}) => {
        try {
            const {_type, data, cardIndex} = item as any
            switch (_type) {
                case E_HotelItem.GOODS: {
                    const layoutInfo = refLayoutInfos.current?.filter(x => x?.cardIndex === cardIndex)
                    // 商品
                    return <InView onChange={(visible) => {
                        handelExpo(visible, index, item)
                    }} index={index}>
                        <HotelCard cardWidth={cardWidth} data={data} index={index} isFirst={index === 2 || _.get(item, 'data.isFirst', false)}
                                   onPress={(cardProps, target) => onPress(cardProps, target, index, item, target.tagInfo)}
                                   isClick={catchObj[_.get(item, ['data', 'hotelCardVO', 'id'], false)]}
                                   mtaTrack={(trackData) => {
                                        const {isExposure, param} = trackData
                                        if(isExposure) {
                                            IdleQueue.add(mtaEp,
                                                getWidgetEventId(trackData, data?.cardVoType === 'recommendCardVOList' ? false : true), 
                                                M_PAGEID.TravelSearcResult, 
                                                M_PAGE.SearchResult, 
                                                {...param, ...(mateParams ?? {}), cardType: data?.cardType}
                                            )
                                        }else {
                                            IdleQueue.add(newMta, getWidgetEventId(trackData, data?.cardVoType === 'recommendCardVOList' ? false : true), 
                                                M_PAGEID.TravelSearcResult, 
                                                M_PAGE.SearchResult, 
                                                {...param, ...(mateParams ?? {}), cardType: data?.cardType}
                                            )
                                        }
                                    }}
                                   layoutInfo={layoutInfo}/>
                    </InView>
                }

                case E_HotelItem.NO_MATCHES: // 没匹配上筛选
                    return <NoMathces data={data} onClear={onClear} index={index} businessType={E_TAB.Hotel}
                                      commonUserAction={mateParams}/>

                case E_HotelItem.TIPS:
                    return <ListTips title={'未找到符合条件的结果，请更改条件重新搜索'} className={'bold'}
                                     style={{color: '#1A1A1A'}}/>

                case E_HotelItem.RECOMMEND_TITLE: // 推荐的标题
                    return <RecommendTitle showIcon={true} data={data}/>

                case E_HotelItem.LOADING: // 加载中
                    return <Loading/>

                case E_HotelItem.BASEINFO:
                    return <BaseInfo data={data} reFetch={_reFetch.current} onClear={onClear} baseInfoNotChangeAction={reloadDataIfUserIdentityChange}/>

                case E_HotelItem.CLEAR_AREA:
                    return <ClearArea data={data}/>

                case E_HotelItem.SORTBAR:
                    console.log('SORTBAR data', data, mtaRef.current);
                    return <SortBar data={data} mateData={mtaRef.current} getRefNode={getRefNode}
                                    getScrollTop={getScrollTop}/>

                case E_HotelItem.LIST_BOTTOM_TIP: // 列表底部的内容 没有更多 或者异常提示
                    return <ListBottomTip type={data.type} onRetry={() => (_reFetch.current)(data.onRetryParams)}/>

                case E_HotelItem.FUll_LOADING:
                    return <HotelSkeleton/>
                case E_HotelItem.NO_DATAS:
                    return <NoData style={{height: pt(300)}}/>

                default:
                    return null
            }
        } catch (error) {
            reportInfo({
                code: errorCodeConstantMapping?.LOADERROR_BUSINESS_IMMEDIATE_ATTENTION,
                errorDetail: {
                    errorType: ErrortType.Info,
                    customMsg: {
                        compName: '酒店渲染异常',
                        errorInfo: error?.message,
                        errorStack: error?.stack
                    }
                }
            })
            return <></>
        }

    }, [mateParams])




    // 渲染商品
    // const renderGoods = useCallback((item) => {
    //     const { cardType, scenicCardVO, hotelCardVO } = item
    //     console.log('cardType', cardType)
    //     return <ErrorBoundary fallback={<Text>1222</Text>}>
    //         <Card mode={cardType} data={{ ...scenicCardVO, ...hotelCardVO }} style={{
    //             backgroundColor: '#fff',
    //             paddingTop: pt(10),
    //             paddingBottom: pt(10),
    //             borderBottomWidth: pt(1),
    //             paddingLeft: pt(12),
    //             paddingRight: pt(12),
    //             borderColor: THEME_BASE.middleColorThree
    //         }} />
    //     </ErrorBoundary>
    // }, [])

    // // 用于判断是否已经显示过
    // const refHasShow = useRef(false)

    // // 获取当前的tab索引
    // const currentTabIndex = useResultStore.use.currentTabIndex()
    // const tabContentLoading = useResultStore.use.tabContentLoading();
    const insets = useSafeAreaInsets()

    // 如果当前tab不是酒店且未显示过，则返回null
    // if ((TAB_MAP[currentTabIndex] !== E_TAB.Hotel) || (renderData.type === 'loading')) {
    //     return null
    // }
    const notRender = (TAB_MAP[currentTabIndex] !== E_TAB.Hotel) || (renderData.type === 'loading')

    return (
        <View ref={refRootNode} className={classNames('flex1', styles.wr)}>
            {
                notRender ? null :
                    <>
                        {
                            renderData.type === 'error' && (
                                <ErrorView type={renderData.data?.type}
                                           onRetry={() => (_reFetch.current)(renderData.data?.onRetryParams)}/>
                            )
                        }
                        {
                            (renderData.type === 'render' && renderData.data?.length >= 1) && (
                                <>
                                    <IOFlatList
                                        scrollEnabled={isWeb ?  true : scrollEnabled}
                                        style={{paddingBottom: insets.bottom}}
                                        // 根据showNoData决定是否允许滚动
                                        // scrollEnabled={!showNoData}
                                        keyboardShouldPersistTaps="handled"
                                        stickyHeaderIndices={[1]}
                                        decelerationRate={isAndroid ? 0.985 : 0.994}
                                        windowSize={21}
                                        initialNumToRender={10}
                                        scrollEventThrottle={16}
                                        onEndReachedThreshold={0.2}
                                        // 数据源包括基本信息和排序栏以及mockList中的数据
                                        data={renderData.data}
                                        onScroll={onScroll}
                                        renderItem={renderItem}
                                        keyExtractor={(item, index) => index.toString()}
                                        // @ts-ignore
                                        onEndReached={loadNextPage}
                                        ref={getScrollRef}
                                    />
                                    <Bubble/>
                                </>
                            )
                        }
                        <ExpandPanel onOpened={() => {
                        }}/>
                        <FilterPanel
                            id={'hotel-filter'}
                            options={data?.filterPanelVOList}
                            address={data?.selectedAreaId}
                            resInfo={data?.countSummaryVO?.resultCountShowText}
                            handleInView={(isShow, item, value) => {
                                const {sortType, orderType, index} = _.get(item, ['metaData'], {})
                                const {displayName} = basePageContext.getPageParams()
                                mtaExposure(M_EVENTID.TravelSearcResultOrderExpo, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                                    ...(mtaRef?.current ?? {}),
                                    sortType,
                                    orderType: ORDER_TYPE[orderType],
                                    index,
                                    isSelect: value.some(valueItem => valueItem.sameKey === item.sameKey),
                                    displayName
                                })
                            }}
                            onChange={(...args) => {
                                safeRun(SortBarRefs.get('onChange'), ...args)
                                setTimeout(() => {
                                    safeRun(QuickMatchTabRefs.get('onChange'), ...args)
                                    const isClear = refIsClear.current
                                    refIsClear.current = false
                                    service.onFilterChange(E_TAB.Hotel, args, () => {
                                        reFetch({
                                            isResetData: true,
                                            isShowToast: isClear ? false : true,
                                            isRestFillter: false
                                        })
                                    })
                                }, 0)
                            }} onClear={(...args) => {
                                // console.log('aabbcc', 'onclear')
                            const filterPanelCode = _.get(args, [args.length - 1], false)
                            const {displayName} = basePageContext.getPageParams()
                            IdleQueue.add(newMta, M_EVENTID.TravelSearcResultFilterClear, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                                ...(mateParams ?? {}),
                                filterPanelName: M_PANEL_NAME[filterPanelCode],
                                filterPanelCode,
                                displayName
                            })
                            safeRun(QuickMatchTabRefs.get('onClear'), ...args)
                            // service.onFilterChange(E_TAB.Hotel, args, () => {
                            //     reFetch({
                            //         isResetData: true,
                            //         isShowToast: false,
                            //         isRestFillter: false
                            //     })
                            // })
                            safeRun(SortBarRefs.get('onChange'), ...args)
                        }} onOk={(...args) => {
                            const filterPanelCode = _.get(args, [args.length - 1], false)
                            const {displayName} = basePageContext.getPageParams()
                            if (filterPanelCode.includes('SortType')) {
                                const sortInfo = _.get(args, [0, 'hotelSortType', 0, 'metaData'], {})
                                IdleQueue.add(newMta, M_EVENTID.TravelSearcResultOrder, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                                    ...(mateParams ?? {}),
                                    orderType: sortInfo?.sortType,
                                    sortType: ORDER_TYPE[sortInfo?.orderType],
                                    index: sortInfo?.index,
                                    displayName
                                })
                            } else {
                                IdleQueue.add(newMta, M_EVENTID.TravelSearcResultFilterConfirm, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                                    ...(mateParams ?? {}),
                                    filterPanelName: M_PANEL_NAME[filterPanelCode],
                                    filterPanelCode,
                                    itemList: getItemListParam(args, filterPanelCode),
                                    displayName
                                })
                            }
                            safeRun(SortBarRefs.get('refPanel'), false)
                        }}
                        />
                        <ExpandHotelSearchPanel onOpened={() => {
                        }}/>
                        <BackTop isShow={isShowBackTop} handleClick={scrollToTop}/>
                    </>
            }
            <CalcTextLayout ref={refCalc}/>
            <DiscountPop cardInfo={cardInfo} showPopup={_.get(cardInfo, ['showPopup'])} onClose={() => {
                setCardInfo({})
            }}
                         toPage={(cardProps, target, index) => onPress(cardProps, target, index, {data: {cardVoType: _.get(cardInfo, ['cardVoType'])}}, target.tagInfo)}/>
        </View>
    )
}

export default memo(Hotel)

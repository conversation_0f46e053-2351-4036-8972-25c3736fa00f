import { refExpandHotelSearchPanel } from "./BaseInfo/ExpandHotelSearchPanel";
import { refExpandPanel } from "./BaseInfo/ExpandPanel";
import { getWatcher } from "@/common/common";
import { refBubble } from "./Bubble";
// import { refFilterPannel } from '../Comprehensive/FilterPanel'
import { refFilterPannelHotel } from '../Hotel/FilterPanel'
// import { refFilterPanelScene } from '../ScenerySpot/FilterPanel'
import { isEmpty, isFunction, isObject } from "@/utils/isType";

export const AllPanelRefs = [
    refExpandHotelSearchPanel,
    refExpandPanel,
    refBubble,
    refFilterPannelHotel
]


/**
 * 关闭除指定面板外的其他面板。
 * @param ref 要保留打开的面板的引用。
 */
export const closeOthers = (ref) => {
    AllPanelRefs.map((item) => {
        if (item !== ref) {
            // console.log('[ closeOthers ] >', item, ref)
            // 执行close
            // @ts-ignore
            item?.closeSelf?.({ closeByOther: true })
        }
    })
}

/**
 * 切换功能的导出函数
 * @param fn 要执行的函数
 * @returns 返回一个函数，执行时会关闭其他功能
 */
export function withCloseOther(ref, isShown: boolean) {
    return function (fn) {
        return function (...args) {
            // if (!isShown) {
            //     panelWatcher.observer?.next({ action: 'panelShow', payload: { enabled: false } })
            // }
            closeOthers(ref);
            return fn(...args);
        };
    }
}

export function witchCloseSelf(fn) {
    return function (...args) {
        setTimeout(() => {
            const hasPanelShown = AllPanelRefs.some(x => {
                const queryState = (x as any)?.queryState;
                if (isFunction(queryState)) {
                    const { isShown } = queryState() || {}
                    if (isShown) {
                        return true
                    }
                }
                return false;
            })
            panelWatcher.observer?.next({ action: 'panelsClosed', payload: { enabled: !hasPanelShown } })
        }, 20);
        return fn(...args)
    }
}

/**
 * 被观察者句柄 - 观察酒店列表滚动变化
 */
export const scollWatcher = getWatcher();

/**
 * 监听面板状态变更
 */
export const panelWatcher = getWatcher();

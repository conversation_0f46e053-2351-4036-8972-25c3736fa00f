import React, { useRef } from 'react';
import { View, ScrollView, PanResponder, StyleSheet, Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

const DraggableView = () => {
  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        return Math.abs(gestureState.dx) > 10 || Math.abs(gestureState.dy) > 10;
      },
      onPanResponderMove: (evt, gestureState) => {
        const { dx, dy } = gestureState;
        setPosition({
          x: initialPosition.x + dx,
          y: initialPosition.y + dy,
        });
      },
      onPanResponderRelease: () => {
        setInitialPosition(position);
      },
    })
  ).current;

  const [position, setPosition] = React.useState({ x: 0, y: 0 });
  const [initialPosition, setInitialPosition] = React.useState({ x: 0, y: 0 });

  return (
      <View style={styles.box} {...panResponder.panHandlers}>
        <View style={[styles.draggable, { transform: [{ translateX: position.x }, { translateY: position.y }] }]}>
          <View style={styles.innerBox} />
        </View>
      </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  box: {
    height: height * 2, // 提供足够的滚动空间
  },
  draggable: {
    width: 100,
    height: 100,
    backgroundColor: 'lightblue',
    position: 'absolute',
  },
  innerBox: {
    flex: 1,
    backgroundColor: 'blue',
  },
  placeholder: {
    height: height * 2,
  },
});

export default DraggableView;

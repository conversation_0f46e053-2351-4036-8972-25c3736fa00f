.bubble {
    position: absolute;
    left: 16px;
    top: 54px;
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 4px;
    height: 30px;

    padding-left: 10px;
    padding-right: 6px;
    z-index: 999;
}

.bubbleInner {
    width: 100%;
    height: 100%;
}

.bubleContent {
    flex-direction: row;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.bubbleTxt {
    color: #FFFFFF;
    margin-left: 2px;
}

.bubuleCloseWr {
    width: 19.96px;
    height: 19.96px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bubuleClose {
    width: 10px;
    height: 10px;
}

// .bubleArrow {
//     width: 11px;
//     height: 11px;
//     border-top-left-radius: 4px;
//     transform: rotate(45deg);
//     background-color: rgba($color: #000000, $alpha: 0.8);
//     top: -5px;
//     left: 80px;
//     position: absolute;
//     z-index: 10;
// }

.bubleArrowImg {
    width: 10px;
    height: 5px;
    top: -5px;
    left: 80px;
    position: absolute;
    z-index: 10;
}
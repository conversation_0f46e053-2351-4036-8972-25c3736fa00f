import {Image, Text, View, AnimatedView} from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import {getImageUrl} from '@/assets/imgs'
import {memo, useEffect, useRef, useState} from 'react'
import {scollWatcher} from '../communication'
import {pt} from '@ltfe/ltfe-core-lib/lib/utiles'
import {Animated} from 'react-native'
import globalInfoModel from '@/store/model/globalInfo.model'
import _ from 'lodash'
import {setShareData} from '@/utils/bridge'

import {
    checkValidDate,
    getDefaultDate
} from '@/Components/utils'


type TYPE_RefBubble = {
    closeSelf?: () => void,
    showSelf?: (text) => void
}

export const refBubble = {} as TYPE_RefBubble

function Bubble() {

    const translateY = useRef(new Animated.Value(0)).current

    const [showBubble, setShowBubble] = useState(false)
    const [showText, setShowText] = useState('请确认入离日期、城市是否正确')

    async function init() {
        const baseInfo = await globalInfoModel.getHotelBaseInfo()
        const {checkInDate} = baseInfo

        // 检查日期
        if (!checkValidDate(checkInDate)) {
            setShowBubble(true)
            setShowText('入住日期已变化，即将刷新')
        }
    }

    const hideBubble = () => {
        setShowBubble(false)
    }

    const _showBubble = (text) => {
        if (typeof text === 'string') {
            setShowText(text)
        }
        setShowBubble(true)
    }

    refBubble.closeSelf = hideBubble
    refBubble.showSelf = _showBubble

    useEffect(() => {
        const st = setTimeout(() => {
            setShowBubble(false)
            setShowText('请确认入离日期、城市是否正确')
        }, 3000)
        return () => {
            clearTimeout(st)
        }
    }, [showBubble])

    useEffect(() => {
        init()
    }, [])

    useEffect(() => {
        if (!showBubble) {
            return
        }
        const subcriber = scollWatcher.observable.subscribe((result) => {
            // console.log(result)
            translateY.setValue(result.payload)
        })
        return () => {
            subcriber.unsubscribe()
        }
    }, [showBubble])

    if (!showBubble) {
        return null
    }

    return (
        // @ts-ignore
        <AnimatedView className={styles.bubble} style={{
            // @ts-ignore
            transform: [{
                translateY: translateY.interpolate({
                    inputRange: [-pt(62), 0, pt(62)],
                    outputRange: [pt(62), 0, -pt(100)]
                })
            }]
        }}>
            <View className={styles.bubbleInner}>
                {/* <View className={styles.bubleArrow}></View> */}
                {/* @ts-ignore */}
                <Image rn className={styles.bubleArrowImg} src={getImageUrl('bubleArrowImg')}/>
                <View className={styles.bubleContent}>
                    <Text className={styles.bubbleTxt}>{showText}</Text>
                    {/*<View className={styles.bubuleCloseWr} onClick={hideBubble}>*/}
                    {/*    /!* @ts-ignore *!/*/}
                    {/*    <Image rn className={styles.bubuleClose} src={getImageUrl('bubuleClose')}/>*/}
                    {/*</View>*/}
                </View>
            </View>
        </AnimatedView>
    )
}


export default memo(Bubble)

import { E_TAB } from "@/pages/result/store/result.model";
import Skeleton from "../../FullLoading/Skeleton";
import { skeletonImgs, skeletonStyle } from "../../FullLoading/skeletonUtils";
import { Dimensions } from "react-native";
import styles from './index.module.scss'

export default function HotelSkeleton() {

    const { height } = Dimensions.get('window')

    return <Skeleton
        className={styles.wr}
        style={{ height }}
        useCustomStyle={true}
        loading={true}
        skeletonStyle={skeletonStyle[E_TAB.Hotel]}
        skeletonImg={skeletonImgs[E_TAB.Hotel]}
    />
}
// import ErrorBoundary from "@/BusinessComponents/ErrorBoundary"
import Card from '@/Components/Card'
import { THEME_BASE } from '@ltfe/ltfe-core-lib/lib/utiles/theme'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import { View } from '@/BaseComponents/atoms'
import styles from "./index.module.scss"
import { ErrorBoundary } from'@ltfe/ltfe-core-lib';

export default function HotelCard(props) {
    const { cardType, scenicCardVO, hotelCardVO, userAction ,eventData , isNatural, priceStyle} = props.data || {}
    
    return (
        <ErrorBoundary>
            <View className={styles.wr}>
                <Card
                    {...props}
                    mode={cardType}
                    data={{ ...scenicCardVO, ...hotelCardVO, userAction, eventData, isNatural, index: props.index, priceStyle}}
                    style={{
                        backgroundColor: '#fff',
                        paddingTop: 0,
                        paddingBottom: pt(16),
                    }} />
            </View>
        </ErrorBoundary>
    )
}

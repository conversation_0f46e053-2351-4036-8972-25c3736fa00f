import {View, Text, Image} from '@/BaseComponents/atoms'
import {memo, useContext, useEffect, useRef, useState} from 'react'
import QuickMatchTab from './QuickMatchTab'
import styles from './index.module.scss'
import {refFilterPannelHotel} from '../FilterPanel'
import {safeRun, getImg} from '@/Components/Filter/utils'
import _ from 'lodash'
import classNames from 'classnames'
import IdleQueue from '@/utils/IdleQueue'
import {M_EVENTID, M_PAGE, M_PAGEID, M_PANEL_NAME, newMta, ORDER_TYPE} from '@/common/mta'
import {BasePageContext} from '@/common/withPage'
import {isAndroid, isWeb} from '@/common/common'
import {pt} from '@ltfe/ltfe-core-lib/lib/utiles'

export const SortBarRefs = new Map()

function SortBar(props) {
    const {mateData} = props
    const refSortBarWr = useRef<any>()
    const [_options] = useState(_.cloneDeep(_.get(props, ['data', 'filterPanelVOList'])))
    const [filterValues, setValues] = useState({})
    const [activeType, setActiveType] = useState(false)

    const basePageContext = useContext(BasePageContext)

    useEffect(() => {
        SortBarRefs.set('onChange', setValues)
        SortBarRefs.set('refPanel', refFilterPannelHotel.toggle)
        SortBarRefs.set('onPanelChange', setActiveType)
    }, [])
    const hasQuickMatch = props?.data?.outsideFilterPanelVOList?.length > 0

    const getFiletCount = (key) => {
        return _.get(filterValues, key, []).length
    }

    const getListName = (list, defaultName) => {
        return Array.isArray(list) ? list.map(item => _.get(item, ['metaData', 'itemName'])).join(',') : defaultName
    }

    const getPanelTop = () => {
        return new Promise(resolve => {
            refSortBarWr.current?.measureLayout(props.getRefNode(), (x, y, w, h) => {
                if(isWeb) {
                    const scrollTop = ~~props.getScrollTop()
                    let filterTop = pt(100)
                    // todo 找顺哥确认
                    if (scrollTop >= y) {
                        filterTop = h
                    } else {
                        filterTop = filterTop - pt(scrollTop)
                    }
                    resolve(filterTop)
                } else {
                    const scrollTop = ~~props.getScrollTop()
                    let filterTop = y + pt(42)
                    // todo 找顺哥确认
                    if (scrollTop >= y) {
                        filterTop = h
                    } else {
                        filterTop = filterTop - scrollTop
                    }
                    resolve(filterTop)
                }
            })
        })
    }

    return (
        <View className={classNames(styles.wr, hasQuickMatch && styles.wrPdb)}>
            {/* @ts-ignore */}
            <View ref={refSortBarWr} className={styles.sortBarWr}>
                {/* 筛选项 */}
                <View className={styles.filterItemBox} onClick={async () => {
                    const panelTop = await getPanelTop()
                    safeRun(refFilterPannelHotel.toggle, {
                        type: 'hotelSortType',
                        top: panelTop
                    })
                    const {displayName} = basePageContext.getPageParams()
                    const sortInfo = _.get(filterValues, ['hotelSortType', 0, 'metaData'], {})
                    IdleQueue.add(newMta, M_EVENTID.TravelSearcResultOrderEntrance, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                        ...mateData,
                        orderType: sortInfo?.sortType,
                        sortType: ORDER_TYPE[sortInfo.orderType],
                        displayName
                    })
                }}>
                    <Text numberOfLines={1}
                          className={`${(_.get(filterValues, ['hotelSortType', 0, 'metaData', 'filterType'], '') === '' && activeType !== 'hotelSortType') ? styles.sortText : styles.sortTextAct} bold`}>
                        {_.get(filterValues, ['hotelSortType', 0, 'metaData', 'filterName'], '智能排序')}
                        {_.get(filterValues, ['hotelSortType', 0, 'metaData', 'tips'], '')}
                    </Text>
                    {
                        activeType === 'hotelSortType' ? <Image className={styles.filterArrow}
                                                                src={getImg('filterArrowAct')}/> :
                            (_.get(filterValues, ['hotelSortType', 0, 'metaData', 'filterType'], '') === '' && activeType !== 'hotelSortType') ?
                                <Image className={styles.filterArrow}
                                       src={getImg('filterArrow')}/> : <Image className={styles.filterArrowDown}
                                                                              src={getImg('filterArrowAct')}/>
                    }
                </View>

                {
                    Array.isArray(_options) && _options.map((item, index) => {
                        const {filterPanelCode, filterPanelName, filterPanelTextType} = item
                        return <View className={styles.filterItemBox} key={index} onClick={async () => {
                            const panelTop = await getPanelTop()
                            const {displayName} = basePageContext.getPageParams()
                            const hotelFilterObj = {}
                            if (filterPanelCode === 'hotel_filter') {
                                hotelFilterObj.isunfold = ~~!(activeType === filterPanelCode)
                            }
                            IdleQueue.add(newMta, M_EVENTID.TravelSearcResultFilter, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                                ...mateData,
                                'filterPanelName': M_PANEL_NAME[filterPanelCode],
                                'filterPanelCode': filterPanelCode,
                                displayName,
                                ...hotelFilterObj
                            })
                            safeRun(refFilterPannelHotel.toggle, {
                                type: filterPanelCode,
                                top: panelTop
                            })
                        }}>
                            <Text numberOfLines={1}
                                  className={`${(activeType === filterPanelCode || getFiletCount(filterPanelCode) > 0) ? styles.sortTextAct : styles.sortText} bold`}>
                                {(filterPanelTextType !== 1 || getFiletCount(filterPanelCode) === 0) ? filterPanelName : getListName(_.get(filterValues, [filterPanelCode]), filterPanelName)}
                            </Text>
                            {
                                filterPanelTextType !== 1 && getFiletCount(filterPanelCode) > 0 ?
                                    <View className={styles.filterCountBox}><Text
                                        className={isAndroid ? styles.filterCountAndroid : styles.filterCount}>{getFiletCount(filterPanelCode)}</Text></View> : ''
                            }
                            {
                                activeType === filterPanelCode ? <Image className={styles.filterArrow}
                                                                        src={getImg('filterArrowAct')}/> : getFiletCount(filterPanelCode) === 0 ?
                                    <Image className={styles.filterArrow} src={getImg('filterArrow')}/> :
                                    <Image className={styles.filterArrowDown} src={getImg('filterArrowAct')}/>
                            }
                        </View>
                    })
                }
            </View>
            {
                hasQuickMatch &&
                <QuickMatchTab data={props?.data?.outsideFilterPanelVOList}/>
            }
        </View>
    )
}

export default memo(SortBar)

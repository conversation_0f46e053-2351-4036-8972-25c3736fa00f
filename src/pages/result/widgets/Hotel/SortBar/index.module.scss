@import "@/assets/theme.scss";

.wr {
    background-color: #FFFFFF;

}

.wrPdb {
    padding-bottom: 6px;
}

.sortBarWr {
    height: 46px;
}

.sortBarWr {
    height: 44px;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-left: 10px;
    padding-right: 10px;
}

.sortText {
    font-size: 14px;
    text-align: center;
    font-weight: var(--fontActWeight);
}

.sortTextAct {
    font-size: 14px;
    text-align: center;
    font-weight: var(--fontActWeight);
    color: var(--primaryHLTextColor);
}

.filterText {
    font-size: 14px;
}

.act {
    color: var(--primaryHLTextColor);
    font-weight: 600;
}

.filterIcon {
    width: 12px;
    height: 12px;
    margin-right: 4px;
}

.filterBox {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.filterLine {
    width: 0.5px;
    height: 20px;
    margin-right: 16px;
}

.filterCountBox {
    width: 14px;
    height: 14px;
    border-radius: 7px;
    margin-left: 4px;
    background-color: var(--primaryHLTextColor);
}

.filterCount {
    color: #fff;
    text-align: center;
    font-size: 10px;
    line-height: 14px;
}


.filterCountAndroid {
    color: #fff;
    text-align: center;
    font-size: 10px;
    line-height: 13px;
}

.filterTextWord {
    line-height: 16px;
}

.iconIcon {
    width: 10px;
    height: 6px;
}

.filterArrow {
    width: 6px;
    height: 6px;
    margin-left: 4px;
}

.filterArrowDown {
    width: 6px;
    height: 6px;
    margin-left: 4px;
    transform: rotate(180deg);
}

.filterItemBox {
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding-left: 6px;
    padding-right: 6px;
    max-width: 110px;
}

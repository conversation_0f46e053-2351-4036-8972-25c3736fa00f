import {AdaptiveImage, ScrollView, View, Image} from '@/BaseComponents/atoms'
import {useCallback, useContext, useEffect, useRef, useState} from 'react'
import {pt, px} from '@ltfe/ltfe-core-lib/lib/utiles'
import {StyleSheet} from 'react-native'
import styles from './index.module.scss'
import classNames from 'classnames'
import {hairlineWidth} from '@/utils/cross'
import SelectItem from '../../BaseInfo/SelectItem'
import {
    compareArrays,
    findDifferences,
    findIntersection,
    moveElementsToFront,
    moveElementsToFrontWithoutMutation
} from '@/utils'
import {useDebounce} from '@/utils/hooks'
import {refFilterPannelHotel} from '../../FilterPanel'
import {SortBarRefs} from '../index'
import Filter from '@/Components/Filter/index'
import _ from 'lodash'
import {InView, IOScrollView} from '@/BaseComponents/IntersectionObserver'
import {BasePageContext} from '@/common/withPage'
import IdleQueue from '@/utils/IdleQueue'
import {M_EVENTID, M_PAGE, M_PAGEID, MTA_NONE, mtaExposure, mtaPv, newMta} from '@/common/mta'
import {E_TAB} from '@/pages/result/store/result.model'
import {isWeb} from '@/common/common'

// function extractItemList(obj, result: any[] = []) {
//     function traverse(node, prefix) {
//         if (node.itemList) {
//             node.itemList.forEach(item => {
//                 item.prefix = prefix ? prefix + "|" + item.filterType : item.filterType;
//                 result.push(item);
//                 if (item.subItemList) {
//                     traverse({ itemList: item.subItemList }, item.filterType);
//                 }
//             });
//         }
//         if (node.filterList) {
//             node.filterList.forEach(filter => traverse(filter, prefix));
//         }
//     }

//     traverse(obj, 'hotel_filter');
//     return result;
// }

function extractItemList(filterList, result: any[] = []) {
    function traverse(node, prefix) {
        if (node.itemList) {
            node.itemList.forEach(item => {
                // item.prefix = prefix ? prefix + "|" + item.filterType : item.filterType;
                result.push(item)
                if (item.subItemList) {
                    traverse({itemList: item.subItemList}, item.filterType)
                }
            })
        }
        if (node.filterList) {
            node.filterList.forEach(filter => traverse(filter, prefix))
        }
    }

    for (let index = 0; index < filterList.length; index++) {
        const obj = filterList[index]

        traverse(obj, 'hotel_filter')
    }

    return result
}

export const QuickMatchTabRefs = new Map()


function QuickMatchTab(props) {
    const {data = {}} = props
    const activeIndexArr = useRef<any[]>([]).current
    const [imgWidth, setImgWidth] = useState({})

    const basePageContext = useContext(BasePageContext)

    const filterList = useRef(Filter.formatData(data)?.filter(item => item.isLeaf)).current

    // console.log(filterList, 'quick')

    const [list, setList] = useState(filterList)

    const refSv = useRef<any>()

    const refLastChangeValues = useRef<any[]>([])
    useEffect(() => {
        QuickMatchTabRefs.set('onChange', (objInfo, _value) => {
            // setSelectList(value)
            const value = [...(_value)]
            // 想对比onChange的增量和减量
            const {added, removed} = compareArrays(value || [], refLastChangeValues.current, 'sameKey')

            // 增量需要在list中查找是否存在，存在的再做下一步动作
            const intersections = findIntersection(added, list, 'sameKey')
            intersections.map(x => {
                const index = activeIndexArr.findIndex(y => y.sameKey === x.sameKey)
                if (index < 0) {
                    activeIndexArr.push(x)
                }
            })

            // 减量直接从选中的list中移除
            removed.map(x => {
                const index = activeIndexArr.findIndex(y => y.sameKey === x.sameKey)
                if (index > -1) {
                    activeIndexArr.splice(index, 1)
                }
            })

            refLastChangeValues.current = value

            // const newList = moveElementsToFront(filterList, activeIndexArr, 'sameKey')
            // // console.log('QuickMatchTabRefs onChange value newList', newList)
            // setList(newList)
        })

        QuickMatchTabRefs.set('onClear', () => {
            refLastChangeValues.current = []
            activeIndexArr.splice(0, activeIndexArr.length)
            setList([...filterList])
        })
    }, [])

    const scrollTo = useDebounce(
        useCallback((x) => {
            refSv.current?.scrollTo({x, animated: true})
        }, [])
        , 3000)

    const onItemPress = (item, index) => {

        let isChoose = false
        const _index = activeIndexArr.findIndex(x => item.sameKey === x.sameKey)
        if (_index > -1) {
            activeIndexArr.splice(_index, 1)
        } else {
            isChoose = true
            activeIndexArr.push(item)
        }

        IdleQueue.add(newMta, M_EVENTID.TravelSearcResult_QickFilter, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, getMtaParams(data, item, index, isChoose))

        const _index1 = refLastChangeValues.current.findIndex(x => item.sameKey === x.sameKey)
        if (_index1 > -1) {
            refLastChangeValues.current.splice(_index1, 1)
        } else {
            refLastChangeValues.current.push(item)
        }

        refFilterPannelHotel.setValue([...refLastChangeValues.current])

        // const newList = moveElementsToFront(filterList, activeIndexArr, 'sameKey')
        // setList(newList)
    }

    const getMtaParams = (data, item, index, isChoose?: boolean) => {
        const {displayName, realName} = basePageContext.getPageParams()
        const {filterType, itemId, itemName, itemType} = item?.metaData || {}
        const params = {
            displayName: displayName || MTA_NONE,
            keyword: realName || MTA_NONE,
            search_o2o_coordinates: MTA_NONE,
            search_fouraddrid: MTA_NONE,
            businessType: E_TAB.Hotel,
            groupCode: MTA_NONE,
            filterType,
            itemId,
            itemName,
            itemType,
            isSelect: false,
            ...(data?.commonUserAction || {}),
            index: index + 1,
            isChoose,
            'itemValue': itemName
        }
        return params
    }

    const mtaFn = (data, item, index) => {
        // console.log('visibleFn item, index', data, item, index)
        const params = getMtaParams(data, item, index)
        mtaExposure(M_EVENTID.TravelSearcResult_QuickFilterExpo, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, params)
    }

    const refVisibledList = useRef<any[]>([]).current
    const visibleFn = (item, index) => {
        if (refVisibledList.includes(index)) {
            return
        }
        IdleQueue.add(mtaFn, data, item, index)
    }

    const renderIcon = (item, isActive) => {
        const { imageWidth = 75, imageHeight = 30, activeImage, listImage } = item?.metaData?.style || {}
        const showHeight = 30;
        let showWidth = 75;
        if (imageHeight > 0 && imageWidth > 0) {
          showWidth = showHeight * imageWidth / imageHeight;
        }
        return (
          <View style={{ 
                position: 'relative',
                height: pt(showHeight),
                width: pt(showWidth), 
                display: 'flex'
                 }}>
            <Image
              src={activeImage}
              mode="aspectFit"
              style={{
                height: pt(showHeight),
                width: pt(showWidth),
                position: 'absolute',
                top: 0,
                left: 0,
                opacity: isActive ? 1 : 0
              }}
            />
            <Image
              src={listImage}
              mode="aspectFit"
              style={{
                height: pt(showHeight),
                width: pt(showWidth),
                position: 'absolute',
                top: 0,
                left: 0,
                opacity: isActive ? 0 : 1
              }}
            />
          </View>
        )
    }

    return (
        <IOScrollView
            ref={refSv}
            onTouchStart={() => {
                scrollTo(0)
            }}
            horizontal
            contentContainerStyle={_styles.svStyle}
            showsHorizontalScrollIndicator={false}
        >
            {
                list.map((item, index) => {
                    const isSeleted = activeIndexArr.some(x => x.sameKey === item.sameKey)
                    const isIcon = item?.metaData?.style?.type === 'icon'

                    return (

                        isIcon ? (
                            <InView
                                onChange={(visible) => {
                                    visible && visibleFn(item, index)
                                }}
                                key={index}
                                className={styles.item_image}
                                // @ts-ignore
                                onClick={() => {
                                    onItemPress(item, index)
                                }}
                                collapsable={false}
                            >
                                {renderIcon(item, isSeleted)}
                            
                            </InView>
                        ) : (
                            <SelectItem
                                showCheckedIcon={true}
                                onChange={(visible) => {
                                    visible && visibleFn(item, index)
                                }}
                                style={{height: 'auto'}}
                                key={index}
                                onClick={() => {
                                onItemPress(item, index)
                            }} selected={isSeleted}
                                text={_.get(item, 'metaData.itemName')}></SelectItem>
                        )
                    )
                })

            }
        </IOScrollView>
    )
}

export default QuickMatchTab

const _styles = StyleSheet.create({
    svStyle: {
        paddingLeft: pt(12),
        paddingRight: pt(4),
        height: pt(31)
    },
    item: {
        height: pt(30),
        paddingHorizontal: pt(10),
        backgroundColor: '#F5F7FA'
    }
})

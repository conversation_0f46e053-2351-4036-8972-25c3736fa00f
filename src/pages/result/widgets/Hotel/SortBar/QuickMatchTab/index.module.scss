
@import "@/assets/theme.scss";


.sv {
    margin-top: 6px;
    margin-bottom: 5px;
}

.item {
    padding: 0 10px;
    margin-right: 8px;
    height: 30px;
    background-color: var(--primaryBgColor);
    box-sizing: border-box;
    border-radius: 4px;
    overflow: hidden;
}

.noPd  {
    padding: 0;
}

.noMgLeft {
    margin-left: 0;
}

.icon {
    background-color: #FFF7F7;
}

.item_image {
    position: relative;
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    height: 30px;
  }

import {useEffect, useState} from 'react'
import {View} from '@/BaseComponents/atoms'
import {witchCloseSelf, withCloseOther} from '../Hotel/communication'
import Filter from '@/Components/Filter'
import Styles from './FilterPanel.module.scss'
import {SortBarRefs} from '@/pages/result/widgets/Hotel/SortBar'
import {objToList, safeRun, listToObjByKey} from '@/Components/Filter/utils'

type TYPE_RefExpandPanel = {
    setValue: (value: any) => void
    setTop: (value: number) => void
    toggle?: (option?: {
        show?: boolean,
        data?: any[],
        activeIndex?: number
    }) => boolean
    onClosed?: () => void
    onSeleted?: ({item, index}) => void
    closeSelf?: () => void
    queryState: () => { isShown: boolean }
}

export const refFilterPannelHotel: TYPE_RefExpandPanel = {} as TYPE_RefExpandPanel

const ExpandFilter = (props) => {
    const [showFilterType, setShowFilterType] = useState<string | boolean>(false)
    const [value, setValue] = useState<object | boolean>(false)
    const [top, setTop] = useState(0)

    const _setValue = (value) => {
        const _value = listToObjByKey(value, 'filterKey')
        setValue(_value)
        safeRun(SortBarRefs.get('onChange'), _value)
    }

    refFilterPannelHotel.queryState = () => {
        return {
            isShown: !!showFilterType
        }
    }

    refFilterPannelHotel.toggle = withCloseOther(refFilterPannelHotel, !!showFilterType)((option: {
        type: string,
        value?: any,
        top?: number
    }) => {
        const {type, value, top} = option
        if (showFilterType === type) {
            setShowFilterType(false)
            safeRun(SortBarRefs.get('onPanelChange'), false)
        } else {
            setShowFilterType(type)
            safeRun(SortBarRefs.get('onPanelChange'), type)
        }
        if (value) {
            _setValue(value)
        }
        if (top) {
            setTop(top)
        }
        return type
    })

    const closeSelf = witchCloseSelf(() => {
        setShowFilterType(false)
        safeRun(SortBarRefs.get('onPanelChange'), false)
        refFilterPannelHotel.onClosed?.()
    })


    useEffect(() => {
        refFilterPannelHotel.closeSelf = closeSelf
        refFilterPannelHotel.setValue = _setValue
        refFilterPannelHotel.setTop = setTop

        return () => {
            for (const key in refFilterPannelHotel) {
                delete refFilterPannelHotel[key]
            }
        }
    }, [])

    return <View className={typeof showFilterType === 'string' ? Styles.filterBox : ''} style={{top}}>
        {
            typeof showFilterType === 'string' ? <View className={Styles.mask} onClick={() => closeSelf()}/> : null
        }
        <Filter {...props} value={value} showFilterType={showFilterType}
                formatHooks={(value) => {
                    return objToList(value, ['hotelSortType', 'sortType'])
                }}/>
    </View>
}

export default ExpandFilter

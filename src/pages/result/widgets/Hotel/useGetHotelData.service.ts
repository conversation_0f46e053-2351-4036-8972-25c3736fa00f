import {useContext, useEffect, useRef, useState} from 'react'
import {isArray, isEmpty, isNumber} from '@/utils/isType'
import {E_TAB, TAB_MAP} from '../../store/result.model'
import ResultContext from '../../store/ResultContext'
import {pt} from '@ltfe/ltfe-core-lib/lib/utiles'
import cloneDeep from 'lodash-es/cloneDeep'
import {showToast} from '@/BaseComponents/atoms/utils/toast'
import {E_ErrorStyle} from '../ErrorView'
import get from 'lodash-es/get'
import {TIP_TYPE} from '../ListBottomTip'
import {ERROR_CODE} from '@/common/useFetch'
import {reportInfo} from '@/common/reporter'
import {errorCodeConstantMapping, ErrortType} from '@/common/reporter/errorMapping'
import addressModel from "@/store/model/address.model";
import _ from 'lodash';

export enum E_HotelItem {
    /**
     * 基础信息
     */
    BASEINFO,
    /**
     * 排序条
     */
    SORTBAR,
    /**
     * 商卡
     */
    GOODS,
    /* 扩大搜索范围
     */
    CLEAR_AREA,
    /**
     * 筛选后：无/少结果
     */
    NO_MATCHES,
    /**
     * 自然流量无结果
     */
    NO_DATAS,
    /**
     * 推荐的标题
     */
    RECOMMEND_TITLE,
    /**
     * loading
     */
    LOADING,
    /**
     * 列表底部的tip
     */
    LIST_BOTTOM_TIP,
    /**
     * 骨架屏
     */
    FUll_LOADING,
    /**
     * 提示
     */
    TIPS
}

const PAGESIZE = 20

export function useGetHotelData({data: _pData, error: _pError, scrollToTop, refScrollTop, currentTabIndex, expoEndData}) {
    const refPageNo = useRef(1)
    const refLoading = useRef(false)
    const refNoMore = useRef(false)

    const [data, setData] = useState<any>({})
    const [renderData, setRenderData] = useState({type: 'loading', data: null as any})
    const {service, useResultStore} = useContext(ResultContext)
    const updateTabContentLoading = useResultStore.use.updateTabContentLoading()


    const _reFetch = useRef<any>()

    const isNoMoreData = () => {
        return !data?.hasNextPage
    }

    const subsAddress = () => {
        // 订阅地址 如果地址返回了才请求接口
        addressModel.subscribe((result) => {
            if (TAB_MAP[currentTabIndex] === E_TAB.Hotel) {
                reFetch()
            }
        })
    }

    useEffect(() => {
        let st1, st2
        // const hideTypes = [E_ErrorStyle.NetError, E_ErrorStyle.OtherError]
        const hideTypes = [...Object.values(E_ErrorStyle), 'error']

        if (renderData.data?.length > 0 || hideTypes.includes(renderData.type)) {
            // console.log('hideTypes', hideTypes, renderData.type, renderData.data)
            // st1 = hideTabSkeleton()
            service.hideTabSkeleton(E_TAB.Hotel)
        }
        if (refLoading.current) {
            st2 = setTimeout(() => {
                // console.log('loadNextPage ==== 2')
                refLoading.current = false
            }, 20)
        }
        return () => {
            // clearTimeout(st1)
            clearTimeout(st2)
        }
    }, [data, renderData])

    
  // 重新加载数据
  const reloadDataIfUserIdentityChange = async () => {
    // 获取身份字段
    const currentUserIdentityList = refUserIdentityList.current

    const response = await service.getUserIdentityList()

    if (response?.code === '0') {
      const userIdentityList = response?.result || []
      if (!isEmpty(userIdentityList)) {
        // 对身份列表进行排序后比较，确保顺序不影响比较结果
        const sortedUserIdentityList = (Array.isArray(userIdentityList) ? userIdentityList : []).sort((a, b) => a - b)
        const sortedCurrentUserIdentityList = (currentUserIdentityList as number[]).sort((a, b) => a - b)
        // 如果排序后的身份列表不相等，说明用户身份发生了变化，需要清空当前身份列表
        if (!_.isEqual(sortedUserIdentityList, sortedCurrentUserIdentityList)) {
            refUserIdentityList.current = []
            reFetch()
        }
      }
    } 
}

    const resetState = () => {
        service.setIsDefaultMddInfo(false)
        refLoading.current = false
        refNoMore.current = false
        refPageNo.current = 1
    }

    const reFetch = (params = {
        isResetData: true, // 默认参数对象，用于控制是否重置数据、重置过滤器、显示提示信息
        isRestFillter: false,
        isShowToast: false,
    }) => {
        if (params.isResetData) { // 如果需要重置数据
            resetState() // 重置状态
            // scrollToTop();

            // console.log('refScrollTop.current', refScrollTop.current)
            if (refScrollTop.current >= pt(64)) { // 如果滚动条位置大于等于62
                const toY = 64 + Math.random()
                // console.log('refScrollTop.current ===1', toY)
                scrollToTop({y: toY, animated: false}) // 滚动到顶部位置（62 + 随机数）
            }

            const newData = renderData.data?.slice?.(0, params.isRestFillter ? 1 : 2) || [] // 根据条件切片数据
            newData.push({ // 添加新数据
                _type: E_HotelItem.FUll_LOADING // 数据类型为FUll_LOADING
            })
            // console.log('newData', newData) // 输出新数据
            setRenderData({ // 设置渲染数据
                type: 'render',
                data: newData
            })

        } else { // 如果不需要重置数据
            const _renderData = cloneDeep(renderData.data) // 深拷贝渲染数据
            if (isArray(_renderData) && _renderData.length > 0 && _renderData[_renderData.length - 1]._type === E_HotelItem.LIST_BOTTOM_TIP) { // 如果数据为数组且最后一个元素类型为LIST_BOTTOM_TIP
                _renderData.pop() // 移除最后一个元素
                _renderData.push({ // 添加新元素
                    _type: E_HotelItem.LOADING,
                    data: {}
                })
                setRenderData({ // 设置渲染数据
                    type: 'render',
                    data: _renderData
                })
            }
        }
        fetchData({ // 获取数据
            pageSize: PAGESIZE, // 每页大小
            page: refPageNo.current // 当前页数
        }, params) // 传递参数
    }

    _reFetch.current = reFetch

    useEffect(() => {

        async function init() {
            if (service.getHasLoadedMap(E_TAB.Hotel)) {
                return
            }
            if (renderData.type === 'loading') {
                updateTabContentLoading(E_TAB.Hotel, true)
            }

            if (TAB_MAP[currentTabIndex] === E_TAB.Hotel) {
                service.setHasLoadedMap(E_TAB.Hotel)
                if (!isEmpty(_pError)) {
                    handleError(_pError)
                    // hideTabSkeleton()
                } else if (!isEmpty(_pData)) {
                    if (_pData?.result) {
                        await handleResponse(_pData.result, {}, 1)
                    } else {
                        setRenderData({
                            type: 'error',
                            data: {
                                type: E_ErrorStyle.OtherError,
                                onRetryParams: {
                                    isResetData: true,
                                    isRestFillter: true,
                                    isShowToast: false
                                }
                            }
                        })
                    }
                    // hideTabSkeleton()
                } else {
                    await fetchData({
                        pageSize: PAGESIZE,
                        page: refPageNo.current
                    })
                }
                subsAddress()
            }
        }

        init().catch(() => {
            service.hideTabSkeleton(E_TAB.Hotel)
        })

    }, [currentTabIndex, _pData, renderData])

    const refFetchNo = useRef(0)
    const refUserIdentityList = useRef<any[]>([])

    // 请求数据
    const fetchData = async (params = {}, other = {} as any) => {
        refLoading.current = true
        const businessType = E_TAB.Hotel
        const _params = {
            businessType,
            ...params
        } as any
        if (other?.hotelBaseSearchParam) {
            _params.hotelBaseSearchParam = other?.hotelBaseSearchParam
        }

        const currentPage = refPageNo.current
        if (currentPage === 1) {
            _params.userIdentityList = []
        } else {
            _params.userIdentityList = refUserIdentityList.current || []
        }


        let result
        try {
            refFetchNo.current++
            const fetchNo = refFetchNo.current
            const pageIndex = refPageNo.current
            const [error, res] = await service.fetchResult(_params)
            if (fetchNo < refFetchNo.current) {
                return
            }

            if (error) {
                handleError(error)
            } else if (res?.result) {
                result = res.result
                await handleResponse(result, other, pageIndex)
            } else {
                setErrorByType({
                    errorType: E_ErrorStyle.OtherError,
                    renderType: E_HotelItem.LIST_BOTTOM_TIP,
                    tipType: TIP_TYPE.RETRY_BY_JSERROR
                })
            }
        } catch (error) {
            setErrorByType({
                errorType: E_ErrorStyle.OtherError,
                renderType: E_HotelItem.LIST_BOTTOM_TIP,
                tipType: TIP_TYPE.RETRY_BY_JSERROR
            })
        }
        // hideTabSkeleton()
    }

    // 异常处理
    const handleError = (errorInfo) => {
        if (errorInfo?.code == ERROR_CODE.NETWORK_TIMEOUT) {
            setErrorByType({
                errorType: E_ErrorStyle.NetError,
                renderType: E_HotelItem.LIST_BOTTOM_TIP,
                tipType: TIP_TYPE.RETRY_BY_NETERROR
            })
        } else {
            setErrorByType({
                errorType: E_ErrorStyle.OtherError,
                renderType: E_HotelItem.LIST_BOTTOM_TIP,
                tipType: TIP_TYPE.RETRY_BY_JSERROR
            })
        }
    }

    const popBottomElement = (_renderData) => {
        if (_renderData[_renderData.length - 1]._type === E_HotelItem.LOADING || _renderData[_renderData.length - 1]._type === E_HotelItem.LIST_BOTTOM_TIP) {
            _renderData.pop()
        }
    }

    // 通过类型处理错误内容
    const setErrorByType = ({errorType, renderType, tipType}) => {
        if (refPageNo.current === 1) { // 如果是第一页 就展示全屏的错误提示
            setRenderData({
                type: 'error',
                data: {
                    type: errorType,
                    onRetryParams: {
                        isResetData: true,
                        isRestFillter: true,
                        isShowToast: false
                    }
                }
            })
        } else { // 有数据的情况 异常了
            const _renderData = cloneDeep(renderData.data)
            popBottomElement(_renderData)
            _renderData.push({
                _type: renderType,
                data: {
                    type: tipType,
                    onRetryParams: {
                        isResetData: false,
                        isRestFillter: false,
                        isShowToast: false
                    }
                }
            })
            setRenderData({
                type: 'render',
                data: _renderData
            })
        }
    }

    const resultIsFewer = () => {
        return refNaturalCardVOList.current.length < 20
    }
    const refLayoutInfos = useRef<any[]>([])
    const refRequestCount = useRef(0)
    const refNeedAutoLoadNextPage = useRef(0)
    const refLastNeedAutoLoadNextPage = useRef(0)
    const refNaturalCardVOList = useRef<any[]>([])

    function withDataHandle(originalFunction) {
        return async function (...args) {
            let needAutoLoadNextPage = false
            /**
             * 如果pageIndex === 1，且此时result.naturalCardVOList是空，则继续请求下一页，
             * 并累计每页下发result.naturalCardVOList的数量，同时累计请求的次数：
             * 如果小于20条，且次数>5，停止下一页请求；如果大于等于20条，停止下一页请求
             */
            const [result, , pageIndex] = args

            if (result.hasNextPage) {
                if (pageIndex === 1) {
                    refRequestCount.current++
                    if (isEmpty(result.naturalCardVOList)) {
                        refPageNo.current++

                        loadNextPage({force: true})
                        return
                    } else if (result.naturalCardVOList?.length < 20) {
                        needAutoLoadNextPage = true
                    }
                } else if (resultIsFewer()) {
                    refRequestCount.current++
                    /** 已加载的商卡数量 */
                    needAutoLoadNextPage = true
                } else if (isEmpty(result.naturalCardVOList)) {
                    refRequestCount.current++
                    /** 如果非第1页，且当前页naturalCardVOList下发为空并且有下一页 */
                    needAutoLoadNextPage = true
                }
                if (refRequestCount.current > 4 && needAutoLoadNextPage) {
                    needAutoLoadNextPage = false
                    result.hasNextPage = false
                    service.hideTabSkeleton(E_TAB.Comprehensive)
                }
                if (!needAutoLoadNextPage) {
                    refRequestCount.current = 0
                }
            }
            if (needAutoLoadNextPage) {
                refNeedAutoLoadNextPage.current = Date.now()
            }
            await originalFunction(...args)
            refPageNo.current++
        }
    }

    useEffect(() => {
        if (refNeedAutoLoadNextPage.current !== refLastNeedAutoLoadNextPage.current) {
            refLastNeedAutoLoadNextPage.current = refNeedAutoLoadNextPage.current
            const st = setTimeout(() => {
                loadNextPage({force: true})
            }, 20)
            return () => {
                clearTimeout(st)
            }
        }
    }, [renderData])

    const handleResponse = withDataHandle(async (result = {}, other = {} as any, pageIndex: number) => {
        const {isResetData = false, isRestFillter = false, isShowToast = false} = other
        let _data = cloneDeep(result)

        // if (!isResetData && !isEmpty(data.naturalCardVOList) && !isEmpty(_data.naturalCardVOList)) {
        //     const naturalCardVOList = ([] as any[]).concat(data.naturalCardVOList).concat(_data.naturalCardVOList)
        //     _data['naturalCardVOList'] = naturalCardVOList
        // }

        // if (!isResetData && !isEmpty(data.naturalCardVOList) && isEmpty(_data.naturalCardVOList)) {
        //     _data['naturalCardVOList'] = data.naturalCardVOList
        // }


        // 1.75新增身份认证出参
        if (pageIndex === 1 && !isEmpty(_data.userIdentityList)) {
            refUserIdentityList.current = _data.userIdentityList
        }

        if ((isEmpty(data.filterPanelVOList) || isRestFillter)) {
            _data['filterPanelVOList'] = _data.filterPanelVOList || []
        } else {
            _data['filterPanelVOList'] = data.filterPanelVOList
        }
        if (isEmpty(data.mddInfoList) && pageIndex === 1) {
            _data['mddInfoList'] = _data.mddInfoList || []
        } else {
            _data['mddInfoList'] = data.mddInfoList
        }

        try {

            if (isShowToast && isEmpty(_data.naturalCardVOList)) {
                showToast({
                    title: '没有找到匹配的结果，请修改筛选条件试试',
                    icon: 'none',
                    duration: 2000
                })
            }

            let _result = renderData.data || []
            const isFirstPage = pageIndex === 1

            if (isResetData) {
                refLayoutInfos.current = []
                refNaturalCardVOList.current = []
                expoEndData.current = {}
            }

            const commonUserAction = _data.commonUserAction
            if (!isFirstPage && !isResetData && !isEmpty(renderData.data)) {
                popBottomElement(renderData.data)
            } else {
                if (!isEmpty(_data.outsideFilterPanelVOList)) {
                    _data.outsideFilterPanelVOList.commonUserAction = commonUserAction
                }
                _result = <any>[
                    {
                        _type: E_HotelItem.BASEINFO,
                        data: {
                            mddInfoList: _data.mddInfoList,
                            commonUserAction
                        }
                    },
                    {
                        _type: E_HotelItem.SORTBAR,
                        data: {
                            filterPanelVOList: _data.filterPanelVOList,
                            outsideFilterPanelVOList: _data.outsideFilterPanelVOList
                        }
                    }
                ]
            }

            if (!isEmpty(_data.naturalCardVOList)) {
                refNaturalCardVOList.current = refNaturalCardVOList.current.concat(_data.naturalCardVOList)
                _result = _result.concat(handleData(_data.naturalCardVOList, E_HotelItem.GOODS, _data))
            }

            if (!_data.hasNextPage && !isEmpty(_data.tipVOList)) {
                _data.tipVOList[0].commonUserAction = _data.commonUserAction
                _result = _result.concat(handleData(_data.tipVOList, E_HotelItem.CLEAR_AREA))
            }


            // 如果没有下一页了 自然流量数据小于20条 有进行筛选处理
            if (!_data.hasNextPage && resultIsFewer() && isMatching()) {
                _result.push({
                    _type: E_HotelItem.NO_MATCHES,
                    data: service.getOriginFilterList(E_TAB.Hotel)
                })
                //  推荐流量有数据

            }

            // 没有自然流量，有推荐，没有筛选
            if (isEmpty(_data.naturalCardVOList) && !isEmpty(_data.recommendCardVOList) && !isMatching()) {
                _result.push({
                    _type: E_HotelItem.TIPS,
                    data: {}
                })
            }

            if (!_data.hasNextPage && !isEmpty(_data.recommendCardVOList)) {
                _result.push({
                    _type: E_HotelItem.RECOMMEND_TITLE,
                    data: {
                        title: '为你推荐更多结果'
                    }
                })
                _result = _result.concat(handleData(_data.recommendCardVOList.map((item, index) => {
                    item.cardVoType = 'recommendCardVOList'
                    item.isFirst = index === 0
                    return item
                }), E_HotelItem.GOODS, _data))
            }

            // 没有下一页了
            if (!_data.hasNextPage) {
                if (_result.length === 2) {
                    _result.pop() // 把sort去掉
                    _result.push({
                        _type: E_HotelItem.NO_DATAS,
                        data: {}
                    })
                }

                if ((_result.length > 2 && _result.length <= 20 && !isMatching()) || _result.length > 20 || !isEmpty(_data.recommendCardVOList)) {
                    _result.push({
                        _type: E_HotelItem.LIST_BOTTOM_TIP,
                        data: {}
                    })
                }
                refNoMore.current = true
            } else {
                _result.push({
                    _type: E_HotelItem.LOADING,
                    data: {}
                })
            }

            if (isShowToast && _data.length <= 1) {
                showToast({
                    title: '没有找到匹配的结果，请修改筛选条件试试',
                    icon: 'none',
                    duration: 2000
                })
            }

            const layoutInfos = await measureLayout(_result)
            refLayoutInfos.current = refLayoutInfos.current.concat(layoutInfos)

            setData(_data)
            setRenderData({
                type: 'render',
                data: _result
            })

        } catch (error) {
            // 这里加个监控
            reportInfo({
                code: errorCodeConstantMapping?.TRYCATCHERROR_CATCH_CAPTURE_CODE_EXCEPTION,
                errorDetail: {
                    errorType: ErrortType.Info,
                    customMsg: {
                        errorDescription: '酒店数据处理catch',
                        errorInfo: error?.message,
                        errorStack: error?.stack
                    }
                }
            })
        }
    })

    // 处理数据 给数据包一层业务映射
    const handleData = (data, type, resultData = {}) => {
        let result = <any>[]
        for (let i = 0; i < data.length; i++) {
            let item = { ...data[i] }
            // 身份价-处理商品卡片中的数据
            if (type === E_HotelItem.GOODS && !isEmpty(resultData) && !isEmpty(resultData?.priceStyleVOMap)) {
                if (!isEmpty(item?.hotelCardVO) && !!item?.hotelCardVO?.priceStyleCode) {
                    item.priceStyle = resultData?.priceStyleVOMap[item?.hotelCardVO?.priceStyleCode]
                }
            }
            result.push({ _type: type, data: item })
        }

        return result
    }


    const refCalc = useRef<any>()
    const measureLayout = (data) => {
        if (isEmpty(data)) {
            return []
        }
        const titleStyle = {fontSize: pt(16)}
        const tagStyle = {fontSize: pt(11)}
        const tagPathItem = {
            nameFullPath: 'hotelCardVO.name',
            tagFullPath: 'hotelCardVO.promotionTagListMap.hotelTitleAfter'
        }

        const textObjs: any[] = []
        for (let index = 0; index < data.length; index++) {
            const item = data[index]
            const itemData = item.data

            if (isNumber(item.cardIndex)) {
                continue
            }
            const cardIndex = index
            item.cardIndex = index

            if (item._type !== E_HotelItem.GOODS) {
                continue
            }


            textObjs.push({
                text: get(itemData, tagPathItem.nameFullPath),
                textStyle: titleStyle,
                fullPath: tagPathItem.nameFullPath,
                cardIndex
            })

            const titleAfters = get(itemData, tagPathItem.tagFullPath)
            if (titleAfters) {
                const tagIndex = titleAfters.findIndex(x => x.styleCode === 'Tag')
                if (tagIndex > -1) {
                    textObjs.push({
                        text: titleAfters[tagIndex].listShowName,
                        textStyle: tagStyle,
                        fullPath: tagPathItem.tagFullPath,
                        index: tagIndex,
                        cardIndex
                    })
                }
            }
        }

        if (isEmpty(textObjs)) {
            return []
        }

        return new Promise((resolve) => {
            refCalc.current?.getLayoutList?.({
                texts: textObjs,
                callback: (val) => {
                    // console.log('val++++', val)
                    resolve(val)
                }
            })
        })

    }


    const loadNextPage = (option?: { force: boolean }) => {
        // console.log('loadNextPage', refLoading.current, isNoMoreData(), refPageNo.current)
        if (!option?.force) {
            if (refLoading.current || isNoMoreData()) {
                return
            }
        }
        // console.log('loadNextPage ==== 1', refLoading.current)
        fetchData({
            pageSize: PAGESIZE,
            page: refPageNo.current
        })
    }

    /**
     * 筛选项 (带有搜筛选项的搜索结果)
     */
    const isMatching = () => {
        return service.hasFilterItems(E_TAB.Hotel)
    }


    return {
        loadNextPage,
        reFetch,
        data,
        mateParams: {...(data?.commonUserAction ?? {}), businessType: data?.tabType ?? ''},
        isMatching,
        renderData,
        currentTabIndex,
        refCalc,
        refLayoutInfos,
        _reFetch,
        reloadDataIfUserIdentityChange
    }
}

@import "@/assets/theme.scss";

.wr {
    height: 62px;
    background-color: #f3f2f5;
}

.bg {
    background-color: #FFFFFF;
    height: 58px;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: -1;
}

.rowWr{
    align-items: center;
    justify-content: space-between;
}
.left {
    flex-direction: column;
    flex-grow: 0;
    flex-shrink: 0;
}

.leftCol {
    padding-left: 16px;
    margin-right: 14px;

}
.vCenter {
    align-items: center;
}

.day {
    margin-right: 2px;
}

.txtSize {
    font-size: 12px;
    line-height: 13px;
}

.mgTop2 {
    margin-top: 2px;
}

.vLine {
    width: 1px;
    height: 30px;
    margin: 0 14px;
}

.bubble {
    position: absolute;
    left: 16px;
    top: 54px;
    background-color: rgba($color: #000000, $alpha: 0.8);
    border-radius: 4px;
    height: 30px;

    padding-left: 10px;
    padding-right: 6px;
    z-index: 999;
}

.bubbleInner {
    width: 100%;
    height: 100%;
}
.bubleContent {
    flex-direction: row;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.bubbleTxt{
    color: #FFFFFF;
    margin-left: 2px;
}
.bubuleCloseWr {
    width: 19.96px;
    height: 19.96px;
}
.bubuleClose {
  width: 100%;
  height: 100%;
}

.bubleArrow {
    width: 11px;
    height: 11px;
    border-top-left-radius: 4px;
    transform: rotate(45deg);
    background-color: rgba($color: #000000, $alpha: 0.8);
    top: -5px;
    left: 80px;
    position: absolute;
    z-index: 10;
}

.positionAreaWr {
    width: 100%;
}
.positionArea {
    flex-grow: 0;
    width: 100%;
}



.showMoreWr {
    position: absolute;
    right: 0;
    height: 100%;
    align-items: center;
    justify-content: center;
    background-color: #FFFFFF;
    padding: 0 12px;
    flex-direction: row;
}


.showmTxt {
    color: var(--primaryTextColor);
    font-size: 12px;
}
.acColor {
    color: var(--primaryHLTextColor);
}


.showMoreImg {
    width: 7px;
    height: 4px;
    margin-left: 4px;
}

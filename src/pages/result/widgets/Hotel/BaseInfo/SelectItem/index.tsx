import { Image, Text, View } from "@/BaseComponents/atoms";
import styles from './index.module.scss'
import { getImageUrl } from "@/assets/imgs";
import classNames from "classnames";
import { memo } from "react";
import withClassName from "@/BaseComponents/atoms/utils/withClassName";
import { isFunction } from "@/utils/isType";
import { InView } from "@/BaseComponents/IntersectionObserver";

/**
 * 选择项组件，用于显示可选项并处理点击事件
 * @param {Object} props - 组件属性对象
 * @param {any} props.selected - 是否选中
 * @param {Function} props.onPress - 点击事件处理函数
 * @param {string} props.text - 显示文本
 */
function SelectItem(props: { selected: any; onClick: any; text: any; style?: any; onChange: (visible: boolean) => void, showCheckedIcon: boolean }) {
    const { selected, onClick, text, style, onChange, showCheckedIcon = true } = props;

    return (
        <InView
            onChange={onChange}
            className={classNames('center', styles.selectItemWr, selected && styles.active)}
            style={style}
            onClick={isFunction(onClick) && onClick}
        >
            <View>
                <Text className={classNames(styles.selectItem, selected && styles.actTxt)}>{text}</Text>
            </View>
            {/* {
                showCheckedIcon && selected && (
                    <View className={styles.activeImgWr}>
                        <Image mode="aspectFit" src={getImageUrl("blueChecked")} className={styles.activeImg} />
                    </View>
                )
            } */}
        </InView>
    );
}

export default withClassName()(memo(SelectItem))

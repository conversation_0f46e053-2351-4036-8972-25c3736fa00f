import {Image, Text, View} from '@/BaseComponents/atoms'
import {memo, useCallback, useContext, useEffect, useRef, useState} from 'react'
import { formatTime, getTimeZone } from '@ltfe/ltfe-core-lib/lib/utiles'
import { useMorningCheckInTip, TIP_TYPE } from '@ltfe/ltfe-core-lib/lib/MorningCheckInTip';
import styles from './index.module.scss'
import classNames from 'classnames'
import {getImageUrl} from '@/assets/imgs'
import PositionArea from './PositionArea'
import {refExpandHotelSearchPanel} from './ExpandHotelSearchPanel'
import {arraysHaveDifferences} from '@/utils'
import {getBaseInfo} from '@/common/hotelBaseInfo'
import {BasePageContext} from '@/common/withPage'
import {isEmpty} from '@/utils/isType'
import {M_EVENTID, M_PAGE, M_PAGEID, MTA_NONE, newMta} from '@/common/mta'
import {E_TAB} from '@/pages/result/store/result.model'
import IdleQueue from '@/utils/IdleQueue'
import globalInfoModel from '@/store/model/globalInfo.model'
import {checkValidDate, getDefaultDate} from '@/Components/utils'
import {safeRun} from '@/Components/Filter/utils'
import {refBubble} from '@/pages/result/widgets/Hotel/Bubble'
import _ from 'lodash'
import { setShareData } from '@/utils/bridge'
import { isWeb } from '@/common/common'

const BaseInfo = (props) => {

    const {reFetch, data, onClear, baseInfoNotChangeAction} = props
    const basePageContext = useContext(BasePageContext)

    const [state, setState] = useState({
        checkInDate: getTimeZone().format('YYYY-MM-DD'),
        checkOutDate: getTimeZone().add(1, 'days').format('YYYY-MM-DD'),
        roomNum: 1,
        personNum: 1
    })

    const {registerDidShow, offDidShow} = useContext(BasePageContext)

    async function init(params?) {
        const baseInfo = await globalInfoModel.getHotelBaseInfo()
        changeState(baseInfo, params)

        if (!checkValidDate(_.get(baseInfo, ['checkInDate']))) {
            const timer1 = setTimeout(() => {
                changeState({
                    ...baseInfo,
                    ...getDefaultDate()
                }, {reFetch: true})
                const timer2 = setTimeout(() => {
                    safeRun(refBubble.showSelf)
                    clearTimeout(timer2)
                }, 1000)
                clearTimeout(timer1)
            }, 1000)
        }
    }

    const didShow = useCallback(async () => {
        const baseInfo = await getBaseInfo({needFixDate: false}).catch(() => {
        })
        // console.log('aabbcc!!!')
        // 先对比日期是否有效
        if (!checkValidDate(_.get(baseInfo, ['checkInDate']))) {
            safeRun(refBubble.showSelf, '入住日期已变化，即将刷新')
            const timer1 = setTimeout(() => {
                changeState({
                    ...baseInfo,
                    ...getDefaultDate()
                }, {reFetch: true})
                const timer2 = setTimeout(() => {
                    safeRun(refBubble.showSelf)
                    clearTimeout(timer2)
                }, 1000)
                clearTimeout(timer1)
            }, 1000)
        } else {
            let changed = false
            if (!changed) {
                // 比对和上一次的baseInfo是否相同
                if (!isEmpty(refLastBaseInfo.current)) {
                    // console.log('aabbcc1')
                    if (
                        refLastBaseInfo.current?.adultNum?.value !== baseInfo?.adultNum?.value ||
                        refLastBaseInfo.current?.roomNum?.value !== baseInfo?.roomNum?.value ||
                        refLastBaseInfo.current?.childNum?.value !== baseInfo?.childNum?.value ||
                        arraysHaveDifferences(refLastBaseInfo.current?.childNum?.age || [], baseInfo?.childNum?.age || []) ||
                        refLastBaseInfo.current?.checkInDate !== baseInfo?.checkInDate ||
                        refLastBaseInfo.current?.checkOutDate !== baseInfo?.checkOutDate
                    ) {
                        // console.log('aabbcc2')
                        changed = true
                    }
                } else {
                    // console.log('aabbcc3')
                    changed = true
                }
            }

            // console.log('aabbcc4')
            if (changed) {
                // console.log('aabbcc5')
                changeState(baseInfo, {reFetch: true})
            } else {
                baseInfoNotChangeAction?.()
            }
        }
    }, [])

    useEffect(() => {
        registerDidShow(didShow)
        init()
        return () => {
            offDidShow(didShow)
        }
    }, [])

    const refLastBaseInfo = useRef<any>()
    const changeState = async (args, params?) => {
        refLastBaseInfo.current = args
        // alert('changeState|||'+JSON.stringify(args))
        globalInfoModel.setHotelBaseInfo(args)

        setState(prev => {
            const newState = Object.assign({}, prev, {
                checkInDate: args.checkInDate,
                checkOutDate: args.checkOutDate,
                roomNum: args.roomNum?.value || 1,
                personNum: Number(args.adultNum?.value || 1) + Number(args.childNum?.value || 0)
            })
            // 修改完成后同步jdShare
            if (!isWeb) {
                setShareData({
                    hotelCheckInDate: args.checkInDate,
                    hotelCheckOutDate: args.checkOutDate
                })
            }

            // console.log(`newState${JSON.stringify(newState)}`)

            // 这个不需要设置，再获取的时候可以直接取到
            // resultModel.setFilterItemsMap(E_TAB.Hotel, {
            //     hotelBaseSearchParam: {
            //         checkInDate: args.checkInDate,
            //         checkOutDate: args.checkOutDate,
            //         roomNum: args.roomNum.value,
            //         grownNum: args.adultNum.value || 0,
            //         childrenNum: args.childNum.value || 0,
            //         childrenAges: args.childNum.age || []
            //     }
            // })
            // console.log('getFilterItemsMap', resultModel.getFilterItemsMap(E_TAB.Hotel))
            return newState
        })
        if (params?.reFetch) {
            reFetch({
                isResetData: true,
                isRestFillter: false,
                isShowToast: false,
                hotelBaseSearchParam: {
                    checkInDate: args.checkInDate,
                    checkOutDate: args.checkOutDate,
                    roomNum: args.roomNum?.value || 1,
                    grownNum: args.adultNum?.value || 1,
                    childrenNum: args.childNum?.value || 0,
                    childrenAges: args.childNum?.age || []
                }
            })
        }
    }

    const {
        checkInDate,
        checkOutDate,
        roomNum,
        personNum
    } = state

    const { tipType } = useMorningCheckInTip(checkInDate, checkOutDate);
    const isEarly = tipType === TIP_TYPE.EARLY_YESTERDAY;
    const fromDate = isEarly ? getTimeZone().format('MM-DD') : formatTime(checkInDate, 'MM-DD');


    // console.log('BaseInfo', state)
    const getMtaParams = (cParams?: any) => {
        const commonMtaParams = basePageContext.getCommonMtaParams()
        const params = {
            ...commonMtaParams,
            businessType: E_TAB.Hotel,
            ...(cParams || {}),
            ...(data?.commonUserAction || {})
        }
        // console.log('BaseInfo getMtaParams', params)
        return params
    }

    const toggleClick = async () => {
        IdleQueue.add(newMta, M_EVENTID.TravelSearcResult_RoomEntrance, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, getMtaParams())
        const baseInfo = await getBaseInfo()
        refExpandHotelSearchPanel.toggle?.({
            data: {
                ...baseInfo,
                checkInDate: checkInDate,
                checkOutDate: checkOutDate
            },
            changeState: (...args) => {

                const baseInfo = args[0] || {}
                const params = getMtaParams({
                    adultNum: isEmpty(baseInfo.adultNum?.value) ? MTA_NONE : baseInfo.adultNum?.value,
                    childrenNum: isEmpty(baseInfo.childNum?.value) ? MTA_NONE : baseInfo.childNum?.value,
                    startDate: isEmpty(baseInfo.checkInDate) ? MTA_NONE : baseInfo.checkInDate,
                    endDate: isEmpty(baseInfo.checkOutDate) ? MTA_NONE : baseInfo.checkOutDate,
                    roomNum: isEmpty(baseInfo.roomNum?.value) ? MTA_NONE : baseInfo.roomNum?.value
                })
                IdleQueue.add(newMta, M_EVENTID.TravelSearcResult_RoomConfirm, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, params)

                // @ts-ignore
                changeState(...args)
            }
        })
    }

    return (
        <View className={classNames('row center', styles.wr)}>
            <View className={styles.bg}></View>
            <View className={styles.left} onClick={() => toggleClick()}>
                <View className={classNames('row', styles.rowWr)}>
                    <View className={classNames('row', styles.leftCol, styles.vCenter)}>
                        {/* @ts-ignore */}
                        <Text type="regular"
                              className={classNames('primaryHLTextColor', styles.txtSize, styles.day)}>{fromDate}</Text>
                        <Text className={classNames('primaryTextColor', styles.txtSize)}>住</Text>
                    </View>
                    <View className={classNames('row', styles.vCenter)}>
                        <Text className={classNames('primaryHLTextColor', styles.txtSize, styles.day)}>{roomNum}</Text>
                        <Text className={classNames('primaryTextColor', styles.txtSize)}>间</Text>
                    </View>
                </View>
                <View className={classNames('row', styles.mgTop2, styles.rowWr)}>
                    <View className={classNames('row', styles.leftCol)}>
                        {/* @ts-ignore */}
                        <Text type="regular"
                              className={classNames('primaryHLTextColor', styles.txtSize, styles.day)}>{formatTime(checkOutDate, 'MM-DD')}</Text>
                        <Text className={classNames('primaryTextColor', styles.txtSize)}>离</Text>
                    </View>
                    <View className={'row'}>
                        <Text
                            className={classNames('primaryHLTextColor', styles.txtSize, styles.day)}>{personNum}</Text>
                        <Text className={classNames('primaryTextColor', styles.txtSize)}>人</Text>
                    </View>
                </View>
            </View>
            <Image src={getImageUrl('vLine')} className={styles.vLine}/>
            <View className={'flex1 center'}>
                {
                    <PositionArea data={props.data?.mddInfoList} commonUserAction={data.commonUserAction}
                                  reFetch={reFetch} onClear={onClear}/>
                }
            </View>
        </View>
    )
}

export default memo(BaseInfo)

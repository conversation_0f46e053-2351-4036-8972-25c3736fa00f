import { Image, ScrollView, Text, View } from "@/BaseComponents/atoms"
import styles from './index.module.scss'
import { useContext, useEffect, useRef, useState } from "react"
import classNames from "classnames";
import { getImageUrl } from "@/assets/imgs";
import SelectItem from "./SelectItem";
import { refExpandPanel } from "./ExpandPanel";
import { moveElementToFrontWithoutMutation } from "@/utils";
import { E_TAB } from "@/pages/result/store/result.model";
import ResultContext from "@/pages/result/store/ResultContext";
import { isArray } from "@/utils/isType";
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import { closeOthers } from "../communication";
import { BasePageContext } from "@/common/withPage";
import IdleQueue from "@/utils/IdleQueue";
import { M_EVENTID, M_PAGE, M_PAGEID, newMta } from "@/common/mta";

const PositionArea = (props) => {
    const { reFetch, commonUserAction, onClear } = props
    const { service } = useContext(ResultContext)
    const basePageContext = useContext(BasePageContext)

    // const { data } = props;
    const data = useRef(props.data).current
    const [list, setList] = useState(props.data)
    const [activeIndex] = useState(0)
    const [showMore, setShowMore] = useState(false)
    const [arrowDirect, setArrowDirect] = useState('down')
    const layoutInfo = useRef({ contentWidth: undefined, width: undefined })
    const refLastSelectedIndex = useRef(-1)

    useEffect(() => {
        service.setMddInfo(E_TAB.Hotel, {
            mddInfo: list?.[0],
            isDefaultMddInfo: true
        })
    }, [])

    const getMoreMtaParams = () => {
        const commonMtaParams = basePageContext.getCommonMtaParams();
        const params = {
            ...commonMtaParams,
            businessType: E_TAB.Hotel,
            ...(commonUserAction || {}),
        }
        // console.log('PositionArea getMoreMtaParams', params)
        return params
    }


    const getMtaParams = (item, isChoose) => {
        const commonMtaParams = basePageContext.getCommonMtaParams();
        const params = {
            ...commonMtaParams,
            businessType: E_TAB.Hotel,
            cityId: item.city,
            isChoose,
            ...(commonUserAction || {}),
        }
        // console.log('PositionArea getMtaParams', params)
        return params
    }

    /**
     * 将指定项移动到列表最前端的点击事件处理函数。
     * @param item 要移动的项
     */
    const onClick = (item, _index) => {
        const index = data.indexOf(item)
        const isChoose = index === refLastSelectedIndex.current
        IdleQueue.add(newMta, M_EVENTID.TravelSearcResult_City, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, getMtaParams(item, isChoose))

        if (index === refLastSelectedIndex.current) {
            return;
        }
        onClear(null, -1)
        closeOthers(null)
        refLastSelectedIndex.current = index;
        const newList = moveElementToFrontWithoutMutation(data, index)
        service.setMddInfo(E_TAB.Hotel, {
            mddInfo: item,
            isDefaultMddInfo: false
        })
        // console.log('newList', newList)
        setList(newList)

        /**
         * 切换城市，需要重新刷新酒店所有数据，不包括
         */
        reFetch({
            isResetData: true,
            isRestFillter: true,
            isShowToast: false,
        })
    }

    const showMoreClick = () => {
        IdleQueue.add(newMta, M_EVENTID.TravelSearcResult_CityMore, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, getMoreMtaParams())
        // console.log('----------111')
        // onChange('filter')
        // console.log('执行到了showMoreClick')
        const show = refExpandPanel.toggle?.({ data: list, commonUserAction })
        setArrowDirect(!show ? 'down' : 'up')
    }

    refExpandPanel.onClosed = () => {
        // onChange(false)
        setArrowDirect('down')
    }

    refExpandPanel.onSeleted = ({ item, index }) => {
        onClick(item, index)
    }

    const needShowMoreBtn = () => {
        setShowMore(layoutInfo.current.contentWidth! > layoutInfo.current.width!)
    }

    // useUpdateOverFirst(() => {
    //     setList(data)
    // }, [data])
    if (!isArray(list)) {
        return null
    }

    // console.log('list======', list, arrowDirect === 'up' && styles.acColor)
    return (
        <View className={styles.positionAreaWr}>
            <View>
                <ScrollView
                    scrollX
                    // @ts-ignore
                    contentContainerStyle={[{ alignItems: 'center' }, showMore && { paddingRight: pt(57) }]}
                    className={styles.positionArea}
                    onContentSizeChange={(width) => {
                        layoutInfo.current.contentWidth = width
                        needShowMoreBtn();
                    }}
                    onLayout={(event) => {
                        layoutInfo.current.width = event.nativeEvent.layout.width
                        needShowMoreBtn();
                    }}
                >
                    {
                        list.map((item, index) => {
                            const seleted = activeIndex === index
                            return (
                                <SelectItem key={index} text={item.showName} onClick={() => onClick(item, index)} selected={seleted} />
                            )
                        })
                    }
                </ScrollView>
            </View>
            {
                showMore &&
                <View onClick={() => showMoreClick()} className={styles.showMoreWr}>
                    <View className={styles.showMore}>
                        <Text className={classNames(styles.showmTxt, arrowDirect === 'up' && styles.acColor)}>更多</Text>
                    </View>
                    <Image src={getImageUrl(arrowDirect === 'up' ? "positionAreaArrowBlue" : 'positionAreaArrow')} mode="aspectFit" className={styles.showMoreImg} />
                </View>
            }

        </View>
    )
}

export default PositionArea

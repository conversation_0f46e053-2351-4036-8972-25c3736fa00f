import { memo, useContext, useEffect, useRef, useState } from "react"
import { Text, View } from "@/BaseComponents/atoms"
import styles from './index.module.scss'
import SelectItem from "../SelectItem"
import classNames from "classnames"
import { witchCloseSelf, withCloseOther } from "../../communication"
import { BasePageContext } from "@/common/withPage"
import { E_TAB } from "@/pages/result/store/result.model"
import IdleQueue from "@/utils/IdleQueue"
import { M_EVENTID, M_PAGE, M_PAGEID, newMta } from "@/common/mta"

type TYPE_RefExpandPanel = {
    toggle?: (option?: { show?: boolean, data?: any[], activeIndex?: number, commonUserAction }) => boolean
    onClosed?: () => void
    onSeleted?: ({ item, index }) => void
    closeSelf?: () => void
    queryState: () => { isShown: boolean }
}

export const refExpandPanel: TYPE_RefExpandPanel = {} as TYPE_RefExpandPanel;

const ExpandPanel = (props: { onOpened: () => void }) => {
    const { onOpened } = props
    const [state, setState] = useState<{ show: boolean, data: any[], activeIndex: number }>({ show: false, data: [], activeIndex: 0 })

    const { getPageParams } = useContext(BasePageContext)
    const params = getPageParams();

    refExpandPanel.queryState = () => {
        return {
            isShown: state.show
        }
    }
    const refCommonUserAction = useRef<any>()
    const basePageContext = useContext(BasePageContext)

    const getMtaParams = (item, isChoose) => {
        const commonMtaParams = basePageContext.getCommonMtaParams();
        const params = {
            ...commonMtaParams,
            businessType: E_TAB.Hotel,
            cityId: item.city,
            isChoose,
            ...(refCommonUserAction.current || {}),
        }
        // console.log('PositionArea getMtaParams', params)
        return params
    }

    refExpandPanel.toggle = withCloseOther(refExpandPanel, state.show)((option?: { show?: boolean, data?: any[], activeIndex?: number, commonUserAction }) => {
        const { show, data, activeIndex, commonUserAction } = option || {}

        setState(pre => {
            refCommonUserAction.current = commonUserAction
            const _show = show ?? !pre.show
            // console.log('refExpandPanel toggle', _show, refExpandPanel.onClosed)
            _show ? onOpened() : refExpandPanel.onClosed?.();
            return { show: _show, data: data || pre.data, activeIndex: activeIndex ?? (pre.activeIndex || 0) }
        })
        return show ?? !state.show
    })

    const closeSelf = witchCloseSelf(() => {
        setState({ show: false } as any)
        // console.log('refExpandPanel 执行这里了， closeSelf')
        refExpandPanel.onClosed?.();
    })
    refExpandPanel.closeSelf = closeSelf

    const onClick = ({ item, index }) => {
        IdleQueue.add(newMta, M_EVENTID.TravelSearcResult_RecCity, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, getMtaParams(item, true))
        refExpandPanel.onSeleted?.({ item, index })
        closeSelf()
    }

    useEffect(() => {
        return () => {
            delete refExpandPanel.toggle;
            delete refExpandPanel.onClosed;
            delete refExpandPanel.onSeleted;
            delete refExpandPanel.closeSelf;
        }
    }, [])

    const { data, show, activeIndex } = state
    if (!show) {
        return null;
    }

    // console.log('render ExpandPanel', styles.item)

    return (
        <View className={styles.wr} >
            <View className={classNames(styles.wr, styles.mask)} onClick={() => closeSelf()} />
            <View className={styles.wrInner}>
                <Text className="bold">{`以下城市有“${params.displayName}”相关结果，请选择`}</Text>
                <View className={styles.content}>
                    {
                        data.map((item, index) => {
                            const selected = index === activeIndex
                            // console.log('props selected', selected, activeIndex)
                            return (
                                <SelectItem className={styles.item} key={index} text={item.showName} onClick={() => onClick({ item, index })} selected={selected} />
                            )
                        })
                    }
                </View>
                <Text className={styles.btmTxt}>没有想要的城市？请尝试搜索“城市+关键词”</Text>
            </View>
        </View>
    )
}

export default memo(ExpandPanel)
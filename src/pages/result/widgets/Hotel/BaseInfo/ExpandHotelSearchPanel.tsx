import { memo, useEffect, useRef, useState } from "react"
import { HotelSearchPanel } from '@ltfe/ltfe-core-lib'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import { witchCloseSelf, withCloseOther } from "../communication";
import { isFunction } from "@/utils/isType";
import {setShareData} from '@/utils/bridge'
import _ from 'lodash'
import {isWeb} from '@/common/common'

type TYPE_RefExpandHotelSearchPanel = {
    toggle?: (options?: { show?: boolean, data?: any, changeState }) => void
    onClosed?: () => void
    closeSelf?: () => void
    queryState: () => { isShown: boolean }
}

const initialState = {
    checkInDate: '2024-11-11',
    checkOutDate: '2024-12-11',
    // keyword: { id: '', name: '北京', type: '' },
    // 房间信息
    roomNum: {
        value: 1,
        minNum: 1,
        maxNum: 10
    },
    // 成人信息
    adultNum: {
        value: 1,
        minNum: 1,
        maxNum: 30
    },
    // 儿童信息
    childNum: {
        value: 0,
        minNum: 0,
        maxNum: 30,
        age: null // 年龄，数组类型
    },
    // hotelCity: {
    //     geoId: 36,
    //     geoName: '北京'
    // }
};

// store.asyncSave('hotelRoomNum', initialState)

export const refExpandHotelSearchPanel = {} as TYPE_RefExpandHotelSearchPanel

const ExpandHotelSearchPanel = (props: { onOpened?: () => void }) => {
    const { onOpened } = props
    const [state, setState] = useState<{ show: boolean, data: any }>(() => {
        return {
            show: false, data: Object.assign({}, initialState)
        }
    })

    const refChangeBaseInfo = useRef((_newState, _params) => { })

    refExpandHotelSearchPanel.queryState = () => {
        return {
            isShown: state.show
        }
    }

    refExpandHotelSearchPanel.toggle = withCloseOther(refExpandHotelSearchPanel, state.show)(async (options) => {
        const { show, data, changeState } = options || {};
        refChangeBaseInfo.current = changeState
        setState(pre => {
            const _show = show ?? !pre.show
            _show ? onOpened?.() : refExpandHotelSearchPanel.onClosed?.();
            // console.log('{ ...pre.data, ...(data || {}) }', { ...pre.data, ...(data || {}) })
            return { show: _show, data: { ...pre.data, ...(data || {}) } }
        })
    })

    const closeSelf = witchCloseSelf(() => {
        setState(pre => ({ ...pre, show: false }) as any)
        refExpandHotelSearchPanel.onClosed?.();
    })
    refExpandHotelSearchPanel.closeSelf = closeSelf


    useEffect(() => {
        return () => {
            delete refExpandHotelSearchPanel.toggle
            delete refExpandHotelSearchPanel.onClosed
        }
    }, [])

    const { show, data } = state

    if (!show) {
        return null
    }

    const {
        checkInDate,
        checkOutDate,
        roomNum,
        adultNum,
        childNum
    } = data;

    return (
        <HotelSearchPanel
            style={{ top: pt(62 - 4) }} onClose={closeSelf}
            checkInDate={checkInDate}
            checkOutDate={checkOutDate}
            roomNum={roomNum}
            adultNum={adultNum}
            childNum={childNum}
            onUpdate={(args) => {
                const {checkInDate, checkOutDate, adultNum, childNum, roomNum = {}} = args
                isFunction(refChangeBaseInfo.current) && refChangeBaseInfo.current(args, { reFetch: true })

                if(!isWeb) {
                    setShareData({
                        hotelRoomNum: {
                            roomNum: _.pick(roomNum, ['value', 'minNum', 'maxNum']),
                            adultNum: _.pick(adultNum, ['value', 'minNum', 'maxNum']),
                            childNum: _.pick(childNum, ['value', 'minNum', 'maxNum', 'age']),
                        },
                        hotelCheckInDate: checkInDate,
                        hotelCheckOutDate: checkOutDate
                    })
                }
                closeSelf();
            }}
        // toCity={toCity}
        />
    )
}

export default memo(ExpandHotelSearchPanel)

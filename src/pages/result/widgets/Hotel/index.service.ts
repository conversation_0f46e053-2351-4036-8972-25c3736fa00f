import { useGetHotelData } from "./useGetHotelData.service";
import { useScrollService } from "./useScrollService";

export default function useScenerySpotService(props, expoEndData) {
    const {
        onScroll,
        getScrollRef,
        scrollToTop,
        refScrollTop,
        isShowBackTop,
        scrollEnabled
    } = useScrollService(props)

    const {
        loadNextPage,
        reFetch,
        data,
        renderData,
        currentTabIndex,
        refCalc,
        mateParams,
        refLayoutInfos,
        _reFetch,
        reloadDataIfUserIdentityChange
    } = useGetHotelData({ ...props, scrollToTop, refScrollTop, expoEndData });

    return {
        data,
        loadNextPage,
        mateParams,
        onScroll,
        getScrollRef,
        renderData,
        reFetch,
        currentTabIndex,
        scrollToTop,
        isShowBackTop,
        refCalc,
        refLayoutInfos,
        _reFetch,
        refScrollTop,
        scrollEnabled,
        reloadDataIfUserIdentityChange
    }
}
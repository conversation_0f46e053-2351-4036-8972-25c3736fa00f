import {View, Text} from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import classNames from 'classnames'
import {hairlineWidth} from '@/utils/cross'
import {TouchableWithoutFeedback} from 'react-native'
import {safeRun} from '@/Components/Filter/utils'
import Filter from '@/Components/Filter'
import {InView} from '@/BaseComponents/IntersectionObserver'
import IdleQueue from '@/utils/IdleQueue'
import {M_EVENTID, M_PAGE, M_PAGEID, MTA_NONE, mtaExposure, newMta} from '@/common/mta'
import {useContext, useEffect} from 'react'
import {BasePageContext} from '@/common/withPage'
import {E_TAB} from '@/pages/result/store/result.model'

export default function ClearArea(props) {
    const {data = {}, mtaKey, mtaPage, mtaPageName, filterType = 'location_distance', mtaClick} = props
    const {tips, commonUserAction} = data

    const basePageContext = useContext(BasePageContext)

    const getMtaParams = () => {
        const {displayName, realName} = basePageContext.getPageParams()
        const params = {
            displayName: displayName || MTA_NONE,
            keyword: realName || MTA_NONE,
            search_o2o_coordinates: MTA_NONE,
            search_fouraddrid: MTA_NONE,
            businessType: E_TAB.Hotel,
            ...(commonUserAction || {})
        }
        return params
    }

    const mtaEpFn = () => {
        const params = getMtaParams()
        mtaExposure(M_EVENTID.TravelSearcResult_TipExpo, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, params)
    }

    useEffect(() => {
        safeRun(props?.onLoad)
    }, [])

    return (
        <InView onChange={(visible) => {
            visible && IdleQueue.add(mtaEpFn)
        }} className={classNames('row vCenter', styles.wr)}>
            <View className={classNames('flex1')}>
                <Text className={styles.txtLeft} numberOfLines={1}>{tips}</Text>
            </View>
            <TouchableWithoutFeedback onPress={() => {
                if (typeof mtaClick !== 'function') {
                    const params = getMtaParams()
                    IdleQueue.add(newMta, mtaKey ? mtaKey : M_EVENTID.TravelSearcResult_Tip, mtaPage ? mtaPage : M_PAGEID.TravelSearcResult, mtaPageName ? mtaPageName : M_PAGE.SearchResult, params)
                } else {
                    safeRun(mtaClick, 'location_distance', filterType)
                }
                if (Filter.Instance.get('hotel-filter-filterClearByKey')) {
                    safeRun(Filter.Instance.get('hotel-filter-filterClearByKey'), 'gis_distance', filterType)
                } else {
                    safeRun(Filter.Instance.get('filterClearByKey'), 'gis_distance', filterType)
                }
                
            }}>
                <View className={classNames('center', styles.btn)} style={{borderWidth: hairlineWidth}}>
                    <Text className={styles.txt}>扩大搜索范围</Text>
                </View>
            </TouchableWithoutFeedback>
        </InView>
    )
}

import { JDNetworkErrorView } from '@jdreact/jdreact-core-lib'

export enum E_ErrorStyle {
    NetError = 0, // 网络错误
    OtherError = 1 // js 异常等error
}

/**
 * 异常兜底组件
 * @param props 
 * @returns 
 */
const ErrorView = (props) => {
    const { type, title = '页面走丢了', subTitle = '请稍后再试', retryText, onRetry } = props

    return (
        <>
            {
                type === E_ErrorStyle.NetError && (
                    <JDNetworkErrorView
                        uiStyle={E_ErrorStyle.NetError}
                        retryText={retryText}
                        onRetry={onRetry}
                    />
                )
            }
            {
                type === E_ErrorStyle.OtherError && (
                    <JDNetworkErrorView
                        uiStyle={E_ErrorStyle.OtherError}
                        title={title}
                        subTitle={subTitle}
                        onRetry={onRetry}
                    />
                )
            }
        </> 
    )
}

export default ErrorView
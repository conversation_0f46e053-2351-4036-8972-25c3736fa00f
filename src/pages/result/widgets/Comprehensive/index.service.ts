import { useGetComprehensiveData } from "./useGetComprehensiveData";
import { useViewRuleService } from "./useViewRuleService";
import { useScrollService } from "./useScrollService";

export default function useComprehensiveSerice(props, {cardWidth, expoEndData}) {

    const {
        onScroll,
        getScrollRef,
        scrollTop,
        isShowBackTop,
        scrollRef,
    } = useScrollService(props)

    const {
        loadNextPage,
        loading,
        noMore,
        reFetch,
        data,
        getFillterList,
        // tabContentLoading,
        currentTabIndex,
        error,
        renderType,
        refCalc,
        refCalcEl,
        refLayoutInfos,
        refLayoutRecommendInfos,
        innerError,
        dataProvider
    } = useGetComprehensiveData({ ...props, scrollTop, cardWidth, expoEndData });


    const {
        isNoNaturalData,
        isNoMatches,
        isNoRecommendData,
        isMatching,
        hasFilterPanelVOList,
        mateParams,
    } = useViewRuleService(data)

    return {
        data,
        loading,
        mateParams,
        noMore,
        isNoNaturalData,
        hasFilterPanelVOList,
        isNoMatches,
        loadNextPage,
        onScroll,
        getScrollRef,
        isNoRecommendData,
        reFetch,
        getFillterList,
        // tabContentLoading,
        currentTabIndex,
        error,
        renderType,
        scrollTop,
        isShowBackTop,
        scrollRef,
        refCalc,
        refCalcEl,
        refLayoutInfos,
        refLayoutRecommendInfos,
        isMatching,
        innerError,
        dataProvider
    }

}
import { IOScrollView } from "@/BaseComponents/IntersectionObserver";
import { forwardRef, useMemo } from "react";
import { useSafeAreaInsets } from "react-native-safe-area-context";


export default function useWithListHeader() {
    const insets = useSafeAreaInsets()
    const _ScrollView = useMemo(() => {
        // console.log('走了这里了');
        return forwardRef(({ children, headerComponent, ...props }, ref) => {
            return (
                <IOScrollView
                    contentContainerStyle={{ paddingBottom: insets.bottom }}
                    ref={ref}
                    {...props}
                >
                    {/* // @ts-ignore */}
                    {headerComponent && headerComponent()}
                    {children}
                </IOScrollView>
            );
        });
    }, [])

    return _ScrollView;
}

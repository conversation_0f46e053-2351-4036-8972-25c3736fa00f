
import { useContext, useEffect, useRef, useState } from "react";
import { isEmpty } from "@/utils/isType";
import { E_TAB, TAB_MAP } from "../../store/result.model";
import ResultContext from "../../store/ResultContext";
import cloneDeep from 'lodash-es/cloneDeep';
import { showToast } from "@/BaseComponents/atoms/utils/toast";
import { E_ErrorStyle } from "../ErrorView";
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles';
import get from "lodash-es/get";
import { TIP_TYPE } from "../ListBottomTip";
import { ERROR_CODE } from "@/common/useFetch";
import { DataProvider, MasonryLayoutProvider } from "@jd/recyclerlistview";
import { unstable_batchedUpdates } from "react-native";
import { isWeb } from "@/common/common";
import addressModel from "@/store/model/address.model"

// paddingLeft: 12
// cardType	否	string
// 卡片类型 TRAFFIC("0", "交通出行")
// HOTEL("1", "酒店")
// SCENIC("2", "景点")
export enum E_CARD_TYPE {
    HOTEL = "1",
    SCENIC = "2"
}

const PAGESIZE = 20; // 网关固定的，不能改
export function useGetComprehensiveData({ data: _pData, error: _pError, currentTabIndex, cardWidth, expoEndData }: any) {
    const refPageNo = useRef(1)
    const refLoading = useRef(false)
    const refNoMore = useRef(false)


    const [dataProvider, setDataProvider] = useState(new DataProvider((r1, r2) => {
        return r1.cardIndex !== r2.cardIndex;
    }))


    const [error, setError] = useState({} as any)
    const [innerError, setInnerError] = useState({} as any)
    const [data, setData] = useState<any>({})
    const [renderType, setRenderType] = useState('loading')
    const [loading, setLoading] = useState(false)
    const { service, useResultStore } = useContext(ResultContext)
    const updateTabContentLoading = useResultStore.use.updateTabContentLoading()
    // const tabContentLoading = useResultStore.use.tabComprehensiveLoading()

    const isNoMoreData = () => {
        return !data?.hasNextPage
    }

    const subsAddress  = () => {
        console.log('zmm---subsAddress---综合')
        // 订阅地址 如果地址返回了才请求接口
        addressModel.subscribe((result) => {
            // alert(`综合--${JSON.stringify(result)}`)
            console.log('请求-zmm----综合-----addressModel', result)
            reFetch()
        });
    }

    useEffect(() => {
        let st1, st2;
        const hideTypes = ['render', 'error', 'innerLoading']
        if (hideTypes.includes(renderType)) {
            service.hideTabSkeleton(E_TAB.Comprehensive)
        }
        if (refLoading.current) {
            st2 = setTimeout(() => {
                refLoading.current = false;
                setLoading(false)
            }, 20);

        }
        return () => {
            // clearTimeout(st1)
            clearTimeout(st2)
        }
    }, [data, renderType])

    const resetState = () => {
        refLoading.current = false;
        refNoMore.current = false;
        refPageNo.current = 1;
    }

    function reFetch(params = {
        isResetData: true,
        isRestFillter: false,
        isShowToast: false
    }) {
        if (params.isResetData) {
            resetState();
            // scrollTop();
            setRenderType('innerLoading')
        } else {
            setLoading(true)
            setInnerError({})
        }

        fetchData({
            pageSize: PAGESIZE,
            page: refPageNo.current
        }, params)
    }

    useEffect(() => {
        async function init() {
            if (service.getHasLoadedMap(E_TAB.Comprehensive)) {
                return
            }
            if (renderType === 'loading') {
                updateTabContentLoading(E_TAB.Comprehensive, true)
            }
            if (TAB_MAP[currentTabIndex] === E_TAB.Comprehensive) {
                service.setHasLoadedMap(E_TAB.Comprehensive)
                if (!isEmpty(_pError)) {
                    handleError(_pError)
                    service.hideTabSkeleton(E_TAB.Comprehensive)
                } else if (!isEmpty(_pData)) {
                    if (_pData?.result) {
                        setRenderType('innerLoading')
                        await handleResponse(_pData.result, { isRestFillter: true, isResetData: true }, 1)
                    } else {
                        setRenderType('error')
                        setError({
                            type: E_ErrorStyle.OtherError,
                            onRetryParams: {
                                isResetData: true,
                                isRestFillter: true,
                                isShowToast: false
                            }
                        })
                    }
                    service.hideTabSkeleton(E_TAB.Comprehensive)
                } else {
                    fetchData({
                        pageSize: PAGESIZE,
                        page: refPageNo.current,
                    }, {
                        isRestFillter: true
                    })
                }
                subsAddress()
            }
        }
        init();

    }, [currentTabIndex, _pData, renderType])

    const refFetchNo = useRef(0)
    // 请求数据
    const fetchData = async (params = {}, other = {} as any) => {
        refLoading.current = true;
        const _params = {
            businessType: E_TAB.Comprehensive,
            ...params
        }

        let result
        try {
            refFetchNo.current++;
            const fetchNo = refFetchNo.current
            const pageIndex = refPageNo.current
            const [error, res] = await service.fetchResult(_params)
            if (fetchNo < refFetchNo.current) {
                return;
            }
            if (error) {
                handleError(error)
            } else if (res?.result) {
                result = res.result;
                await handleResponse(result, other, pageIndex)
            } else {
                setErrorByType({
                    errorType: E_ErrorStyle.OtherError,
                    innerErrorType: TIP_TYPE.RETRY_BY_JSERROR
                })
            }
        } catch (error) {
            setErrorByType({
                errorType: E_ErrorStyle.OtherError,
                innerErrorType: TIP_TYPE.RETRY_BY_JSERROR
            })
        }
        // updateTabContentLoading(false);
    }

    const handleError = (errorInfo) => {
        if (errorInfo?.code == ERROR_CODE.NETWORK_TIMEOUT) {
            setErrorByType({
                errorType: E_ErrorStyle.NetError,
                innerErrorType: TIP_TYPE.RETRY_BY_NETERROR
            })
        } else {
            setErrorByType({
                errorType: E_ErrorStyle.OtherError,
                innerErrorType: TIP_TYPE.RETRY_BY_JSERROR
            })
        }
    }

    // 通过类型处理错误内容
    const setErrorByType = ({ errorType, innerErrorType }) => {
        if (refPageNo.current === 1) { // 如果是第一页 就展示全屏的错误提示
            setRenderType('error')
            setError({
                type: errorType,
                onRetryParams: {
                    isResetData: true,
                    isRestFillter: true,
                    isShowToast: false
                }
            })
        } else { // 有数据的情况 异常了
            setRenderType('render')
            // TODO setLoading 是否设置为false
            setLoading(false)
            setInnerError({
                type: innerErrorType,
                onRetryParams: {
                    isResetData: false,
                    isRestFillter: false,
                    isShowToast: false
                }
            })
        }
    }

    const refGetData = useRef<any>()
    refGetData.current = () => {
        return data;
    }
    const refLayoutInfos = useRef<any[]>([])
    const refLayoutRecommendInfos = useRef<any[]>([])
    const refRequestCount = useRef(0)

    const refNeedAutoLoadNextPage = useRef(0)
    const refLastNeedAutoLoadNextPage = useRef(0)
    function withDataHandle(originalFunction) {
        return async function (...args) {
            let needAutoLoadNextPage = false;
            /**
             * 如果pageIndex === 1，且此时result.naturalCardVOList是空，则继续请求下一页，
             * 并累计每页下发result.naturalCardVOList的数量，同时累计请求的次数：
             * 如果小于20条，且次数>5，停止下一页请求；如果大于等于20条，停止下一页请求
             */
            const [result, , pageIndex] = args
            const data = refGetData.current();
            if (result.hasNextPage) {
                if (pageIndex === 1) {
                    refRequestCount.current++;
                    if (isEmpty(result.naturalCardVOList)) {
                        refPageNo.current++;
                        loadNextPage({ force: true })
                        return
                    } else if (result.naturalCardVOList?.length < 20) {
                        needAutoLoadNextPage = true;
                    }
                } else if (data.naturalCardVOList?.length < 20) {
                    refRequestCount.current++;
                    /** 已加载的商卡数量 */
                    needAutoLoadNextPage = true;
                } else if (isEmpty(result.naturalCardVOList)) {
                    refRequestCount.current++;
                    /** 如果非第1页，且当前页naturalCardVOList下发为空并且有下一页 */
                    needAutoLoadNextPage = true;
                }
                if (refRequestCount.current > 4 && needAutoLoadNextPage) {
                    needAutoLoadNextPage = false
                    result.hasNextPage = false
                }
                if (!needAutoLoadNextPage) {
                    refRequestCount.current = 0;
                }
            }
            if (needAutoLoadNextPage) {
                refNeedAutoLoadNextPage.current = Date.now()
            }
            await originalFunction(...args);
            refPageNo.current++;
        };
    }

    useEffect(() => {
        if (refNeedAutoLoadNextPage.current !== refLastNeedAutoLoadNextPage.current) {
            refLastNeedAutoLoadNextPage.current = refNeedAutoLoadNextPage.current
            const st = setTimeout(() => {
                loadNextPage({ force: true })
            }, 0);
            return () => {
                clearTimeout(st)
            }
        }
    }, [data])
    const handelLayoutScenic = (item, layouts) => {
        const { scenicCardVO } = item
        const { promotionTagListMap, price, name } = scenicCardVO
        const { mainScenicComment, mainScenicLocationDistance, mainScenicPromotion, mainScenicScore, mainScenicTitleAfter } = promotionTagListMap
        let imageHeight = pt(172), titleHeight = 0, commentHeight = 0, tagHeight = 0, locationHeight = 0, priceHeight = 0
        let titleAndTagWidth = 0
        layouts.forEach(item => {
            titleAndTagWidth += item?.layout?.width
        })
        if (titleAndTagWidth > (cardWidth)) {
            titleHeight = pt(20) * 2 + pt(2)
        } else {
            titleHeight = pt(20) + pt(2)
        }
        if (mainScenicLocationDistance && Array.isArray(mainScenicLocationDistance) && mainScenicLocationDistance.length) {
            locationHeight = pt(18) // height: 12 marginTop: 4
        }
        if (mainScenicPromotion && Array.isArray(mainScenicPromotion) && mainScenicPromotion.length) {
            tagHeight = pt(24) // height: 16 marginTop: 6
        }
        if (mainScenicComment && Array.isArray(mainScenicComment) && mainScenicComment.length) {
            commentHeight = pt(14) // height: 12 marginTop: 2
        }
        if (!!price) {
            priceHeight = pt(28)
        } else if (mainScenicScore && Array.isArray(mainScenicScore) && mainScenicScore.length) {
            priceHeight = pt(22)
        }
        return imageHeight + titleHeight + commentHeight + tagHeight + locationHeight + priceHeight + pt(18)
    }
    const handelLayoutHotel = (item, layouts) => {
        const { hotelCardVO } = item
        const { promotionTagListMap, price, name } = hotelCardVO
        const { mainHotelLocationDistance, mainHotelPromotion, mainHotelScore, mainHotelTitleAfter } = promotionTagListMap
        let imageHeight = (172), titleHeight = 0, tagHeight = 0, locationHeight = 0, priceHeight = 0
        let starWidth = 0, tagWidth = 0
        if (mainHotelTitleAfter && Array.isArray(mainHotelTitleAfter) && mainHotelTitleAfter.length) {
            mainHotelTitleAfter.forEach((item) => {
                if (item?.styleCode === "Stars") {
                    starWidth = (item?.listShowName ?? 0) * (14) + (4)
                }
            })
        }
        let titleAndTagWidth = (18)
        layouts.forEach(item => {
            titleAndTagWidth += item?.layout?.width
            if (item?.text !== name) {
                tagWidth = item?.layout?.width
            }
        })
        if ((titleAndTagWidth + starWidth) > cardWidth) {
            if ((titleAndTagWidth + starWidth) > cardWidth * 2) {
                titleHeight = (20) * 2 + (20)
            } else if (cardWidth - (titleAndTagWidth + starWidth) < tagWidth) {
                titleHeight = (20) + (20)
            } else {
                titleHeight = (20) * 2
            }
        } else {
            titleHeight = (20)
        }

        if (mainHotelLocationDistance && Array.isArray(mainHotelLocationDistance) && mainHotelLocationDistance.length) {
            locationHeight = (22)
        }
        if ((mainHotelScore && Array.isArray(mainHotelScore) && mainHotelScore.length) || (mainHotelPromotion && Array.isArray(mainHotelPromotion) && mainHotelPromotion.length)) {
            tagHeight = (22)
        }
        if (!!price) {
            priceHeight = (28)
        } else {
            priceHeight = (22)
        }
        return pt(imageHeight + titleHeight + tagHeight + locationHeight + priceHeight + (18))
    }
    const handelLayout = (item, layout) => {
        const { cardType } = item
        if (cardType === '2') {
            return handelLayoutScenic(item, layout)
        } else if (cardType === '1') {
            return handelLayoutHotel(item, layout)
        } else if (cardType === '0') {
            return pt(132)
        }
    }
    const refCountEl = useRef(0)

    // 处理数据
    const handleResponse = withDataHandle(async (result = {} as any, other = {} as any, pageIndex: number) => {
        const { isResetData = false, isRestFillter = false, isShowToast = false } = other

        let newDataProvider = dataProvider;
        if (isResetData) {
            newDataProvider = dataProvider.cloneWithRows([])
            refCountEl.current = 0;
            expoEndData.current = {};

        }
        let _data = cloneDeep(result)
        const layoutInfos = await measureLayout(_data.naturalCardVOList || []) as any[]
        refLayoutRecommendInfos.current = await measureLayout(_data.recommendCardVOList || []) as any[]
        if (isResetData) {
            refLayoutInfos.current = layoutInfos as any[] || []

            if (isEmpty(_data.naturalCardVOList) && !isEmpty(_data.recommendCardVOList)) {
                newDataProvider = newDataProvider.cloneWithRows(_data.recommendCardVOList)
            } else if (!isEmpty(_data.naturalCardVOList)) {
                newDataProvider = newDataProvider.cloneWithRows(_data.naturalCardVOList)
            }
        }

        if (!isResetData && !isEmpty(_data.naturalCardVOList)) {
            const naturalCardVOList = ([] as any[]).concat(data.naturalCardVOList || []).concat(_data.naturalCardVOList)
            _data['naturalCardVOList'] = naturalCardVOList
            newDataProvider = newDataProvider.cloneWithRows(naturalCardVOList)
            refLayoutInfos.current = refLayoutInfos.current.concat(layoutInfos);
        }

        if (!isResetData && !isEmpty(data.naturalCardVOList) && isEmpty(_data.naturalCardVOList)) {
            _data['naturalCardVOList'] = data.naturalCardVOList
        }

        if (isEmpty(data.filterPanelVOList) && isRestFillter && !isEmpty(_data['naturalCardVOList'])) {
            _data['filterPanelVOList'] = _data.filterPanelVOList
        } else {
            _data['filterPanelVOList'] = data.filterPanelVOList
        }

        if (isShowToast && isEmpty(_data.naturalCardVOList)) {
            showToast({
                title: '没有找到匹配的结果，请修改筛选条件试试',
                icon: 'none',
                duration: 2000,
            });
        }

        // 没有下一页了
        if (!_data.hasNextPage) {
            refNoMore.current = true
        }
        if (_data.naturalCardVOList) {
            _data.naturalCardVOList = _data.naturalCardVOList?.map(item => {
                let layouts = refLayoutInfos.current?.filter(_item => _item.cardIndex === item.cardIndex)
                item._layouts = layouts
                return item
            })
        }

        if (_data.recommendCardVOList) {
            _data.recommendCardVOList = _data.recommendCardVOList?.map(item => {
                let layouts = refLayoutRecommendInfos.current?.filter(_item => _item.cardIndex === item.cardIndex)
                item._layouts = layouts
                return item
            })
        }
        const elLayouts = await measureElLayout(_data.naturalCardVOList || [])
        const elRecLayouts = await measureElLayout(_data.recommendCardVOList || [])
        setTimeout(() => {
            // measureLayout(_data.naturalCardVOList, pageIndex)
            const conFn = isWeb ? ((fn) => { fn() }) : unstable_batchedUpdates;
            conFn(() => {
                setDataProvider(newDataProvider)
                setData(_data)
                setRenderType('render')
                setError({})
            })
        }, 500)


    })

    const refCalcEl = useRef<any>()
    const measureElLayout = (datas) => {
        if (isEmpty(datas)) {
            return []
        }
        return new Promise((resolve) => {
            refCalcEl.current?.getLayoutList?.({
                datas,
                callback: (val) => {
                    resolve(val)
                }
            });
        })
    }

    const refCalc = useRef<any>();
    const measureLayout = (data) => {
        if (isEmpty(data)) {
            return []
        }
        const titleStyle = { fontSize: pt(15) }
        const tagStyle = { fontSize: pt(11) }
        const tagPath = {
            [E_CARD_TYPE.HOTEL]: {
                nameFullPath: 'hotelCardVO.name',
                tagFullPath: 'hotelCardVO.promotionTagListMap.mainHotelTitleAfter',
                parentPathName: 'mainHotelTitleAfter'
            },
            [E_CARD_TYPE.SCENIC]: {
                nameFullPath: 'scenicCardVO.name',
                tagFullPath: 'scenicCardVO.promotionTagListMap.mainScenicTitleAfter',
                parentPathName: 'mainScenicTitleAfter'
            }
        }
        const textObjs: any[] = []
        for (let index = 0; index < data.length; index++) {
            const item = data[index];
            const cardIndex = refCountEl.current++;
            item.cardIndex = cardIndex;
            const tagPathItem = tagPath[item.cardType] || {}
            const name = get(item, tagPathItem.nameFullPath)
            if (!!name) {
                textObjs.push({
                    text: get(item, tagPathItem.nameFullPath),
                    textStyle: titleStyle,
                    fullPath: tagPathItem.nameFullPath,
                    cardIndex,
                })

                const titleAfters = get(item, tagPathItem.tagFullPath);
                if (titleAfters) {
                    const tagIndex = titleAfters.findIndex(x => x.styleCode === 'Tag');
                    if (tagIndex > -1) {
                        textObjs.push({
                            text: titleAfters[tagIndex].listShowName,
                            textStyle: tagStyle,
                            fullPath: tagPathItem.tagFullPath,
                            index: tagIndex,
                            cardIndex
                        })
                    }
                }
            }

        }
        if (isEmpty(textObjs)) {
            return []
        }

        return new Promise((resolve) => {
            refCalc.current?.getLayoutList?.({
                texts: textObjs,
                callback: (val) => {
                    resolve(val)
                }
            });
        })

    };

    const loadNextPage = (option?: { force: boolean }) => {
        if (!option?.force) {
            if (refLoading.current || isNoMoreData()) {
                return
            }
        }
        refLoading.current = true;
        refPageNo.current > 1 && setLoading(true)

        fetchData({
            pageSize: PAGESIZE,
            page: refPageNo.current
        })
    }

    // 获取筛选项信息
    const getFillterList = () => {
        return service.getOriginFilterList(E_TAB.Comprehensive)
    }


    return {
        loadNextPage,
        reFetch,
        loading,
        data,
        noMore: isNoMoreData(),
        getFillterList: getFillterList,
        // tabContentLoading,
        currentTabIndex,
        error,
        renderType,
        refCalc,
        refCalcEl,
        refLayoutInfos,
        refLayoutRecommendInfos,
        innerError,
        dataProvider
    }
}

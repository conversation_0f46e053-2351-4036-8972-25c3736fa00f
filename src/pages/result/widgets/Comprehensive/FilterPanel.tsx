import {useEffect, useState} from 'react'
import {View} from '@/BaseComponents/atoms'
import {witchCloseSelf, withCloseOther} from '../Hotel/communication'
import Filter from '@/Components/Filter'
import {objToList, safeRun} from '@/Components/Filter/utils'
import Styles from './FilterPanel.module.scss'
import {SortBarRefs} from '@/pages/result/widgets/Comprehensive/SortBar'

type TYPE_RefExpandPanel = {
    toggle?: (option?: {
        show?: boolean,
        data?: any[],
        activeIndex?: number
    }) => boolean
    onClosed?: () => void
    onSeleted?: ({item, index}) => void
    closeSelf?: () => void,
    setValue: (value) => void
    setTop: (t: number) => void
    queryState: () => { isShown: boolean }
}

export const refFilterPannel: TYPE_RefExpandPanel = {} as TYPE_RefExpandPanel

const ExpandFilter = (props) => {
    const [showFilterType, setShowFilterType] = useState<string | boolean>(false)
    const [top, setTop] = useState<number | any>(0)
    const [value, setValue] = useState<object | boolean>(false)

    const _setValue = (value) => {
        setValue(value)
        safeRun(SortBarRefs.get('onChange'), value)
    }

    refFilterPannel.queryState = () => {
        return {
            isShown: !!showFilterType
        }
    }

    refFilterPannel.toggle = withCloseOther(refFilterPannel, !!showFilterType)((option: {
        type: string,
        value?: any,
        top?: number
    }) => {
        const {type, value, top} = option
        if (showFilterType === type) {
            safeRun(SortBarRefs.get('onPanelChange'), false)
            setShowFilterType(false)
        } else {
            setShowFilterType(type)
            safeRun(SortBarRefs.get('onPanelChange'), type)
        }
        if (value) {
            _setValue(value)
        }
        if (top) {
            setTop(top)
        }
        return type
    })

    const closeSelf = witchCloseSelf(() => {
        setShowFilterType(false)
        safeRun(SortBarRefs.get('onPanelChange'), false)
        refFilterPannel.onClosed?.()
    })

    useEffect(() => {
        refFilterPannel.closeSelf = closeSelf
        refFilterPannel.setValue = _setValue
        refFilterPannel.setTop = setTop
    }, [])

    return <View className={typeof showFilterType === 'string' ? Styles.filterBox : ''}>
        {
            typeof showFilterType === 'string' ? <View className={Styles.mask} onClick={() => closeSelf()}/> : null
        }
        <Filter {...props} top={top} value={value} showFilterType={showFilterType} formatHooks={(value) => {
            return objToList(value, ['mainSortType', 'sortType'])
        }}/>
    </View>
}

export default ExpandFilter

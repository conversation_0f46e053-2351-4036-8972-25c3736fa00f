@import "@/assets/theme.scss";

.wr {
  background-color: #FFFFFF;
}

.sortBarWr {
  height: 44px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-right: 16px;
}

.sortText {
  font-size: 14px;
  width: 100px;
  text-align: center;
}

.sortTextAct {
  font-size: 14px;
  text-align: center;
  margin-left: 16px;
  font-weight: var(--fontActWeight);
  color: var(--primaryHLTextColor);
}

.filterText {
  font-size: 14px;
  font-weight: var(--fontActWeight);
}

.act {
  color: var(--primaryHLTextColor);
  font-weight: 600;
}

.filterIcon {
  width: 12px;
  height: 12px;
  margin-right: 4px;
}

.filterBox {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.filterLine {
  width: 0.5px;
  height: 20px;
  margin-right: 16px;
}

.filterCountBox {
  width: 14px;
  height: 14px;
  border-radius: 7px;
  margin-left: 4px;
  background-color: var(--primaryHLTextColor);
}

.filterCount {
  color: #fff;
  text-align: center;
  font-size: 10px;
  line-height: 14px;
}

.filterCountAndroid {
  color: #fff;
  text-align: center;
  font-size: 10px;
  line-height: 13px;
}

.filterTextWord {
  line-height: 16px;
}

.filterTextWordActive {
  line-height: 16px;
  color: var(--primaryHLTextColor);
  font-weight: var(--fontActWeight);
}

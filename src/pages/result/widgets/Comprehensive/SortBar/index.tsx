import {View, Text, Image} from '@/BaseComponents/atoms'
import {memo, useContext, useEffect, useRef, useState} from 'react'
import styles from './index.module.scss'
import {getImageUrl} from '@/assets/imgs'
import {safeRun} from '@/Components/Filter/utils'
import _ from 'lodash'

import {refFilterPannel} from '@/pages/result/widgets/Comprehensive/FilterPanel'
import {M_EVENTID, M_PAGE, M_PAGEID, M_PANEL_NAME, newMta} from '@/common/mta'
import useScenerySpotService from '@/pages/result/widgets/ScenerySpot/index.service'
import IdleQueue from '@/utils/IdleQueue'
import {BasePageContext} from '@/common/withPage'
import {isAndroid} from '@/common/common'

export const SortBarRefs = new Map()

const SortBar = (props) => {
    const [filterValues, setValues] = useState(false)
    const [active, setActive] = useState<boolean | string>(false)
    const {
        data
    } = useScenerySpotService(props)
    const basePageContext = useContext(BasePageContext)
    const mtaRef = useRef({...(data?.commonUserAction ?? {}), businessType: data?.tabType ?? ''})

    useEffect(() => {
        SortBarRefs.set('onChange', setValues)
        SortBarRefs.set('refPanel', refFilterPannel.toggle)
        SortBarRefs.set('onPanelChange', setActive)
    }, [])

    const getFiletCount = (key) => {
        return _.get(filterValues, key, []).length
    }

    return (
        <View className={styles.wr}>
            <View className={styles.sortBarWr}>
                <View onClick={() => {
                    IdleQueue.add(newMta, M_EVENTID.TravelSearcResultOrder, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                        ...(mtaRef?.current ?? {})
                    })
                    // 这里只有智能排序 不给点击事件
                    // setActive('SortType')
                    // safeRun(refFilterPannel.toggle, {
                    //     type: 'SortType'
                    // })
                }}>
                    <Text
                        className={`${styles.sortTextAct} bold`}
                    >
                        {_.get(filterValues, ['sortType', 'itemName'], '智能排序')}
                        {_.get(filterValues, ['sortType', 'tips'], '')}
                    </Text>
                </View>

                <View className={styles.filterBox} onClick={() => {
                    const panelCode = 'main_filter'
                    const {displayName} = basePageContext.getPageParams()
                    IdleQueue.add(newMta, M_EVENTID.TravelSearcResultFilter, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                        ...(mtaRef?.current ?? {}),
                        'filterPanelName': M_PANEL_NAME[panelCode],
                        'filterPanelCode': panelCode,
                        displayName
                    })
                    safeRun(refFilterPannel.toggle, {
                        type: 'main_filter',
                        value: filterValues
                    })
                }}>
                    <Image className={styles.filterLine} src={getImageUrl('line')}/>
                    {
                        (getFiletCount('main_filter') > 0 || active === 'main_filter') ?
                            <Image className={styles.filterIcon}
                                   src={getImageUrl('filterAct')}/>
                            : <Image className={styles.filterIcon}
                                     src={getImageUrl('filter')}/>
                    }
                    <Text className={styles.filterText}>
                        <Text
                            className={`${(getFiletCount('main_filter') > 0 || active === 'main_filter') ? styles.filterTextWordActive : styles.filterTextWord} bold`}>
                            筛选
                        </Text>
                        <Text> </Text>
                    </Text>
                    {
                        getFiletCount('main_filter') > 0 ?
                            <View className={styles.filterCountBox}><Text
                                className={isAndroid ? styles.filterCountAndroid : styles.filterCount}>{getFiletCount('main_filter')}</Text></View> : ''
                    }
                </View>
            </View>
        </View>
    )
}

export default memo(SortBar)

import { InView } from "@/BaseComponents/IntersectionObserver"
import { StyleSheet } from 'react-native';
import { memo } from "react"
import Card from '@/Components/Card'
import isEqual from 'lodash-es/isEqual'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'

const ComprehensiveCard = (props) => {
    const { val, cardWidth, onPress, handelExpo, isLeft, isShadow, layoutInfos } = props
    const { item, i: index } = val
    const { cardType, scenicCardVO, trafficCardVOList, hotelCardVO } = item
    return (
        <InView index={index} style={[
            !isShadow && (isLeft ? styles.cardLeftWr : styles.cardRightWr),
            // { width: cardWidth + pt(8) }
        ]}
            onChange={(visible) => {
                !isShadow && handelExpo && handelExpo(visible, index, item)
            }}
        // style={{ flexDirection: 'column' }}
        >
            <Card mode={`${cardType}`}
                style={item?.layout?.height ? {minHeight: item.layout.height} : {}}
                cardWidth={cardWidth} layoutInfo={layoutInfos} type={'line'}
                onPress={(cardProps, target) => onPress(cardProps, target, index, item)}
                data={{ ...scenicCardVO, ...hotelCardVO, infoList: trafficCardVOList }} />
        </InView>
    )
}

export default memo(ComprehensiveCard, (prev, next) => {
    // console.log('prev.val === next.val', isEqual(prev.val, next.val), prev.val, next.val)
    return isEqual(prev.val, next.val)
})
const styles = StyleSheet.create({
    cardLeftWr: {
        marginLeft: pt(12),
        marginRight: pt(4),
        marginBottom: pt(8),
    },
    cardRightWr: {
        marginRight: pt(12),
        marginLeft: pt(4),
        marginBottom: pt(8)
    },
});

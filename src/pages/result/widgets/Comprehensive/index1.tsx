import { View, Loading } from '@/BaseComponents/atoms'
import { memo, useCallback, useContext, useRef } from 'react'
import MasonryList from '@react-native-seoul/masonry-list'
import useComprehensiveSerice from './index.service'
import NoMore from '../NoMore'
import NoData from '../NoData'
import NoSieveData from '../NoSieveData'
import NoMathces from '../NoMatches'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { E_TAB, TAB_MAP } from '../../store/result.model'
import SortBar, { SortBarRefs } from './SortBar'
import _, { forEach } from 'lodash'
import { useDidHide, useDidShow } from '@tarojs/taro'
import FilterPanel, { refFilterPannel } from '@/pages/result/widgets/Comprehensive/FilterPanel'
import { InView, IOScrollView } from '@/BaseComponents/IntersectionObserver'
import ResultContext from '../../store/ResultContext'
import { safeRun } from '@/Components/Filter/utils'
import styles from './index.module.scss'
import ErrorView from '../ErrorView'
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'
import useJumpTo from '@/common/useJumpTo'
import BackTop from '@/BaseComponents/BackTop'
import CalcTextLayout from '@/BaseComponents/CalcTextLayout'
import hasLogin, { doLogin } from '@/common/Login'
import { Dimensions, StyleSheet } from 'react-native'
import ComprehensiveSkeleton from './ComprehensiveSkeleton'
import ListBottomTip from '../ListBottomTip'
import { isEmpty } from '@/utils/isType'
import LinearGradient from '@jdreact/jdreact-core-linear-gradient'
import { isWeb } from '@/common/common'
import { M_EVENTID, M_PAGEID, M_PAGE, M_PANEL_NAME, newMta, mtaExposure, MTA_NONE } from '@/common/mta'
import IdleQueue from '@/utils/IdleQueue'
import { BasePageContext } from '@/common/withPage'
import ComprehensiveCard from './ComprehensiveCard'

const { width } = Dimensions.get('window')

// const json = require('../../../../Components/Masonry/data.json')

const Comprehensive = (props) => {
    // layout: paddingLeft: px(12), paddingRight: px(12), spacing: px(8)
    const cardWidth = (width - pt(32)) / 2
    useDidShow(() => {
        console.log('didShow -- ')
    })

    useDidHide(() => {
        console.log('useDidHide -- ')
    })
    const insets = useSafeAreaInsets()

    const { service, useResultStore } = useContext(ResultContext)

    const {
        data,
        loadNextPage,
        noMore,
        isNoNaturalData,
        hasFilterPanelVOList,
        isNoMatches,
        loading,
        onScroll,
        getScrollRef,
        isNoRecommendData,
        reFetch,
        getFillterList,
        tabContentLoading,
        currentTabIndex,
        error,
        renderType,
        isShowBackTop,
        scrollTop,
        scrollRef,
        mateParams,
        refCalc,
        refLayoutInfos,
        refLayoutRecommendInfos,
        isMatching,
        innerError
    } = useComprehensiveSerice(props, cardWidth)
    const mtaRef = useRef<any>()
    mtaRef.current = mateParams

    const jumpTo = useJumpTo()
    const expoEndData = useRef({})
    const refIsClear = useRef(false)
    const onClear = useCallback((item, index) => {
        refIsClear.current = true
        const newFilterList = service.deleteOriginFilterList(E_TAB.Comprehensive, index)
        refFilterPannel.setValue({
            'main_filter': newFilterList
        })
    }, [])

    const basePageContext = useContext(BasePageContext)

    // const tabContentLoading = useResultStore.use.tabContentLoading()

    const refUsedLayoutInfos = useRef<any[]>([])
    const useRecommend = isNoNaturalData() && !isNoRecommendData()

    const _data = useRecommend ? data?.recommendCardVOList : data?.naturalCardVOList || []
    refUsedLayoutInfos.current = useRecommend ? refLayoutRecommendInfos.current : refLayoutInfos.current
    const handelExpoHotel = (index, cardItem, isRecommend) => {
        const { id, originPrice, price, promotionTagListMap = {} } = cardItem ?? {}
        const { mainHotelScore = [] } = promotionTagListMap ?? {}
        let score = ''
        if (mainHotelScore && Array.isArray(mainHotelScore)) {
            mainHotelScore.forEach(item => {
                if (item.trackId === 'hotelScore') {
                    score = item.listShowName
                }
            })
        }
        console.log('aabbcc', cardItem)
        // 如果useRecommend为true代表为推荐类型
        IdleQueue.add(mtaExposure, isRecommend ? M_EVENTID.TravelSearcResultRecHotelExpo : M_EVENTID.TravelSearcResultHotelExpo, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
            ...(mtaRef.current ?? {}),
            displayName: mtaRef.current?.keyword ?? '-100',
            index,// 位置 number
            cardType: '1', // 卡片类型 string
            firpricetype: '11', // 第一价格分类 string
            firprice: price ? price * 1 : -100, // 第一价格金额 number
            secpricetype: '52', // 第二价格类型 string
            secprice: originPrice ? originPrice * 1 : -100, //第二价格金额 number
            score, // 评分 string
            hotelId: id, //酒店ID string
            ..._.get(cardItem, ['cardItem', 'useAction'], {})
        })
        expoEndData.current[index] = true
    }
    const handelExpoScenic = (index, cardItem, isRecommend) => {
        const { free, id, price, promotionTagListMap = {} } = cardItem ?? {}
        const { mainScenicScore } = promotionTagListMap
        let score = ''
        if (mainScenicScore && Array.isArray(mainScenicScore)) {
            mainScenicScore.forEach(item => {
                if (item.trackId === 'scenicScore') {
                    score = item.listShowName
                }
            })
        }
        IdleQueue.add(mtaExposure, isRecommend ? M_EVENTID.TravelSearcResultRecScenicExpo : M_EVENTID.TravelSearcResultScenicExpo, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
            ...(mtaRef.current ?? {}),
            index,// 位置 number
            displayName: mtaRef.current?.keyword ?? '-100',
            cardType: '2', // 卡片类型 string
            startingPrice: price, // 起始价 string
            score, // 评分 string
            free: free ? '1' : '0', // 是否免费 string
            discountType: -100,
            scenicId: id, //景点ID string
            ..._.get(cardItem, ['data', 'userAction'], {})
        })
        expoEndData.current[index] = true
    }
    const handelExpoTraffic = (index, cardList) => {
        if (cardList && Array.isArray(cardList)) {
            cardList.forEach((item) => {
                const { fromCityId = '', fromCityName = '', toCityId = '', toCityName = '', type = 0 } = item
                IdleQueue.add(mtaExposure, M_EVENTID.TravelSearcResultTrafficExpo, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                    ...(mtaRef.current ?? {}),
                    displayName: mtaRef.current?.keyword ?? '-100',
                    index,// 位置 number
                    cardType: '0', // 卡片类型 string
                    trafficType: type * 1, // 交通类型 number
                    fromCityId, // 起点城市Id string
                    fromCityName, // 起点城市名称 string
                    toCityId, // 终点城市Id string
                    toCityName // 终点城市名称 string
                })
            })
        }
        expoEndData.current[index] = true
    }
    const handelExpo = useCallback((visible, index, item) => {
        if (visible && !expoEndData.current?.[index]) {
            const { cardType, trafficCardVOList, hotelCardVO, scenicCardVO } = item
            if (cardType === '0') {
                handelExpoTraffic(index, trafficCardVOList)
            }
            if (cardType === '1') {
                handelExpoHotel(index, hotelCardVO, useRecommend)
            }
            if (cardType === '2') {
                handelExpoScenic(index, scenicCardVO, useRecommend)
            }
        }
    }, [useRecommend])
    const onPressTraffic = useCallback((data, index) => {
        const { fromCityId = '', fromCityName = '', toCityId = '', toCityName = '', type = 0 } = data ?? {}
        IdleQueue.add(newMta, M_EVENTID.TravelSearcResultTraffic, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
            ...(mtaRef.current ?? {}),
            displayName: mtaRef.current?.keyword ?? '-100',
            index,// 位置 number
            cardType: '0', // 卡片类型 string
            trafficType: type * 1, // 交通类型 number
            fromCityId, // 起点城市Id string
            fromCityName, // 起点城市名称 string
            toCityId, // 终点城市Id string
            toCityName // 终点城市名称 string
        })
        if ((data && data?.jumpUrl)) {
            jumpTo({ to: 'web', params: { url: decodeURIComponent(data?.jumpUrl) } })
        }
    }, [])
    const onPressScenic = useCallback((cardProps, index) => {
        const { id, free, price, promotionTagListMap = {} } = cardProps ?? {}
        const { mainScenicScore = [] } = promotionTagListMap ?? {}
        let score = ''
        if (mainScenicScore && Array.isArray(mainScenicScore)) {
            mainScenicScore.forEach(item => {
                if (item.trackId === 'scenicScore') {
                    score = item.listShowName
                }
            })
        }
        IdleQueue.add(newMta, useRecommend ? M_EVENTID.TravelSearcResultRecScenic : M_EVENTID.TravelSearcResultScenic, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
            ...(mtaRef.current ?? {}),
            displayName: mtaRef.current?.keyword ?? '-100',
            index,// 位置 number
            cardType: '2', // 卡片类型 string
            startingPrice: price, // 起始价 string
            score, // 评分 string
            discountType: -100,
            free: free ? '1' : '0', // 是否免费 string

            scenicId: id, //景点ID string
            ..._.get(cardProps, ['data', 'userAction'], {})
        })
        if (cardProps && cardProps?.jumpUrl) {
            jumpTo({ to: 'web', params: { url: decodeURIComponent(cardProps?.jumpUrl) } })
        }
    }, [useRecommend])
    const onPressHotel = useCallback(async (cardProps, index) => {
        const { id, originPrice, price, promotionTagListMap = {} } = cardProps ?? {}
        const { mainHotelScore = [] } = promotionTagListMap ?? {}
        let score = ''
        if (mainHotelScore && Array.isArray(mainHotelScore)) {
            mainHotelScore.forEach(item => {
                if (item.trackId === 'hotelScore') {
                    score = item.listShowName
                }
            })
        }

        IdleQueue.add(newMta, useRecommend ? M_EVENTID.TravelSearcResultRecHotel : M_EVENTID.TravelSearcResultHotel, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
            ...(mtaRef.current ?? {}),
            displayName: mtaRef.current?.keyword ?? '-100',
            index,// 位置 number
            cardType: '1', // 卡片类型 string
            firpricetype: '11', // 第一价格分类 string
            firprice: price ? price * 1 : -100, // 第一价格金额 number
            secpricetype: '52', // 第二价格类型 string
            secprice: originPrice ? originPrice * 1 : -100, //第二价格金额 number
            score, // 评分 string
            hotelId: id, //酒店ID string
            ..._.get(cardProps, ['data', 'userAction'], {})
        })
        const isLogin = await hasLogin()
        if (!isLogin) {
            const logged = await doLogin()
            if (!logged) {
                reFetch({
                    isResetData: true,
                    isRestFillter: false,
                    isShowToast: false
                })
                return
            }
        }
        if (cardProps && cardProps?.jumpUrl) {
            jumpTo({ to: 'web', params: { url: decodeURIComponent(cardProps.jumpUrl) } })
        }
    }, [])
    const onPress = useCallback((cardProps, target, index) => {
        const { trigger, data } = target
        if (trigger === 'traffic-item') {
            onPressTraffic(data, index)
        } else if (trigger === 'scenic-card') {
            onPressScenic(cardProps, index)
        } else if (trigger === 'hotel-card') {
            onPressHotel(cardProps, index)
        }
    }, [])
    const renderCustomBlock = useCallback((val) => {
        const { i: index } = val
        // const {cardType, scenicCardVO, trafficCardVOList, hotelCardVO, cardIndex} = item
        // const layoutInfo = refUsedLayoutInfos.current?.filter(x => x?.cardIndex === cardIndex)
        return <ComprehensiveCard
            key={index}
            val={val}
            refUsedLayoutInfos={refUsedLayoutInfos}
            cardWidth={cardWidth}
            onPress={onPress}
            handelExpo={handelExpo}
        />
        // return (
        //     <InView index={index} className={index % 2 === 0 ? styles.cardLeftWr : styles.cardRightWr}
        //             onChange={(visible) => {
        //                 handelExpo(visible, index, item)
        //             }}>
        //         <Card mode={`${cardType}`} cardWidth={cardWidth} layoutInfo={layoutInfo} type={'line'}
        //               onPress={(cardProps, target) => onPress(cardProps, target, index)}
        //               data={{...scenicCardVO, ...hotelCardVO, infoList: trafficCardVOList}}/>
        //     </InView>
        // )
    }, [useRecommend])

    const renderFooter = useCallback(() => {
        // console.log('data---1-1--1', data)
        const filterList = getFillterList()
        if ((data.naturalCardVOList?.length || 0) < 20 && isEmpty(data.recommendCardVOList) && !isEmpty(filterList)) {
            return <NoMathces onClear={onClear} data={filterList} businessType={E_TAB.Comprehensive} commonUserAction={mateParams} />
        } else if (!isEmpty(innerError)) {
            return <ListBottomTip type={innerError.type} onRetry={() => reFetch(innerError.onRetryParams)} />
        } else if (noMore) {
            return <NoMore />
        } else {
            return null
        }
    }, [data, noMore, isNoMatches(), innerError])

    const getItemListParam = (args, filterPanelCode) => {
        try {
            const filterItemList = _.get(args, [0, filterPanelCode], [])
            if(!Array.isArray(filterItemList) || filterItemList.length === 0) {
                return []
            }

            return filterItemList?.map(item => {
                const metaData = _.get(item, 'metaData')
                return {
                    ...metaData,
                    groupCode: _.get(item, 'parents.metaData.groupCode', -100)
                }
            })
        }catch(e) {
            console.log("getItemListParam error->", e)
            return []
        }
    }

    // console.log('什么情况下', data, isNoNaturalData(), isNoRecommendData(), error)

    const notRender = (TAB_MAP[currentTabIndex] !== E_TAB.Comprehensive) || (renderType === 'loading')

    // !isNoNaturalData() ? data?.naturalCardVOList : !isNoRecommendData() ? data?.recommendCardVOList : []

    return (
        <View className={'flex1'}>
            {
                notRender ? null :
                    <>

                        <LinearGradient
                            colors={isWeb ? ['#FFFFFF', '#F5F7FA', '#F5F7FA'] : ['#FFFFFF', '#F5F7FA']}
                            className={'flex1'}
                            style={rnStyles.flex1}
                        >
                        </LinearGradient>
                        {
                            renderType === 'error' && (
                                <ErrorView type={error?.type} onRetry={() => {
                                    reFetch(error.onRetryParams)
                                }} />
                            )
                        }
                        {
                            (renderType === 'render' || renderType === 'innerLoading') && (!(isNoNaturalData()) || hasFilterPanelVOList()) &&
                            <>
                                {/* 排序区域 */}
                                <SortBar />
                                <FilterPanel channel={'search'} options={_.get(data, 'filterPanelVOList')}
                                    id={'comprehensive'}
                                    resInfo={data?.countSummaryVO?.resultCountShowText}
                                    onChange={(...args) => {
                                        safeRun(SortBarRefs.get('onChange'), ...args)
                                        const isClear = refIsClear.current
                                        refIsClear.current = false
                                        service.onFilterChange(E_TAB.Comprehensive, args, () => {
                                            reFetch({
                                                isResetData: true,
                                                isShowToast: isClear ? false : true,
                                                isRestFillter: false
                                            })
                                        })
                                    }} onClear={(...args) => {
                                        const { displayName } = basePageContext.getPageParams()
                                        const filterPanelCode = _.get(args, [args.length - 1], false)
                                        IdleQueue.add(newMta, M_EVENTID.TravelSearcResultFilterClear, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                                            ...(mtaRef.current ?? {}),
                                            filterPanelName: M_PANEL_NAME[filterPanelCode],
                                            filterPanelCode,
                                            displayName
                                        })
                                        safeRun(SortBarRefs.get('onChange'), ...args)
                                        service.onFilterChange(E_TAB.Comprehensive, args, () => {
                                            reFetch({
                                                isResetData: true,
                                                isShowToast: false,
                                                isRestFillter: false
                                            })
                                        })
                                    }} onOk={(...args) => {
                                        const { displayName } = basePageContext.getPageParams()
                                        const filterPanelCode = _.get(args, [args.length - 1], false)
                                        IdleQueue.add(newMta, M_EVENTID.TravelSearcResultFilterConfirm, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                                            ...(mtaRef.current ?? {}),
                                            filterPanelName: M_PANEL_NAME[filterPanelCode],
                                            filterPanelCode,
                                            itemList: getItemListParam(args, filterPanelCode),
                                            displayName
                                        })
                                        safeRun(SortBarRefs.get('refPanel'), false)
                                    }}
                                />
                            </>
                        }

                        {
                            renderType === 'innerLoading' && <ComprehensiveSkeleton />
                        }

                        {
                            renderType === 'render' && !(isNoNaturalData() && isNoRecommendData() && !hasFilterPanelVOList()) && (
                                <>

                                    {/* 1、自然搜索时：
                                     如果没有搜到数据且没有推荐数据，则显示"未找到符合条件的结果，请更改条件重新搜索"
                                     2、自然搜索时：
                                     如果没有搜到数据,有推荐数据，则显示"没有搜到相关结果，请尝试修改搜索词" + 推荐feeds */}
                                    <MasonryList
                                        getEl={getScrollRef}
                                        ScrollContainer={IOScrollView}
                                        // containerStyle={{ paddingBottom: insets.bottom }}
                                        contentContainerStyle={{ paddingBottom: insets.bottom }}
                                        ListHeaderComponent={isNoNaturalData() && !isNoRecommendData() &&
                                            <NoSieveData
                                                onClear={onClear}
                                                noNaturalData={isNoNaturalData()}
                                                data={getFillterList()}
                                                businessType={E_TAB.Comprehensive}
                                                commonUserAction={mateParams}
                                            />
                                        }
                                        data={_data}
                                        keyExtractor={(item, index): string => index.toString()}
                                        numColumns={2}
                                        // decelerationRate={0.99}
                                        showsVerticalScrollIndicator={false}
                                        renderItem={renderCustomBlock as any}
                                        refreshing={false}
                                        loading={loading}
                                        LoadingView={<Loading />}
                                        refreshControl={false}
                                        onScroll={onScroll}
                                        innerRef={scrollRef}
                                        // onRefresh={() => refetch({ first: ITEM_CNT })}
                                        onEndReachedThreshold={0.1}
                                        onEndReached={loadNextPage}
                                        ListFooterComponent={renderFooter()}
                                    />
                                </>
                            )
                        }

                        {
                            renderType === 'render' && (isNoNaturalData() && isNoRecommendData() && !hasFilterPanelVOList()) && (
                                <NoData />
                            )
                        }
                        <BackTop isShow={isShowBackTop} handleClick={scrollTop} />
                    </>
            }

            <CalcTextLayout ref={refCalc} />
        </View>
    )
}

export default memo(Comprehensive)


const rnStyles = StyleSheet.create({
    flex1: {
        position: 'absolute',
        zIndex: 0,
        width: '100%',
        height: pt(170)
    }
})

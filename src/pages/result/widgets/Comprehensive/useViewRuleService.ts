import { isEmpty } from "@/utils/isType"
import ResultContext from "../../store/ResultContext";
import { E_TAB } from "../../store/result.model";
import { useContext } from "react";


export function useViewRuleService(data) {
    const { service } = useContext(ResultContext)

    /**
    * 自然流量
    */
    const isNaturalFlow = () => {
        if (isEmpty(data.naturalCardVOList)) {
            return false
        }
        return true
    }

    /**
    * 推荐流量
    */
    const isRecommendFlow = () => {
        if (isEmpty(data.recommendCardVOList)) {
            return false
        }
        return true
    }

    /**
     * 筛选项 (带有搜筛选项的搜索结果)
     */
    const isMatching = () => {
        return service.hasFilterItems(E_TAB.Comprehensive)
    }

    /**
     * 自然流量：没有数据 
     * */
    const isNoNaturalData = () => {
        if (!isNaturalFlow()) {
            return true;
        }
        return false;
    }

    /**
     * 推荐流量：没有数据
     */
    const isNoRecommendData = () => {
        if (!isRecommendFlow()) {
            return true;
        }
        return false
    }

    /**
     * 搜索流量：判断是否没有匹配到数据
     */
    const isNoMatches = () => {
        // 如果没有匹配项
        if (!isMatching()) {
            return false;
        }
        // 这里要根据有没有筛选项来判断
        // 如果有筛选项，则返回 true
        return true;
    }

    /**
     * 判断是否存在过滤面板VO列表
     * @returns 是否存在过滤面板VO列表
     */
    const hasFilterPanelVOList = () => {
        return !isEmpty(data.filterPanelVOList)
    }

    const mateParams = {...(data.commonUserAction ?? {}), businessType: data?.tabType ?? ''}

    return {
        isNoNaturalData,
        isNoMatches,
        isNoRecommendData,
        isMatching,
        mateParams,
        hasFilterPanelVOList
    }
}
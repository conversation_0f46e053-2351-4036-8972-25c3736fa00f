export const mockData = [
  {
    "filterPanelName": "筛选",
    "filterPanelCode": "main_filter",
    "showFilter": true,
    "filterList": [
      {
        "groupCode": "hotel_filter",
        "groupName": "酒店",
        "filterName": "全部酒店",
        "filterType": "hotel_main",
        "multi": 1,
        "filterBehavior": "normal",
        "itemList": [
          {
            "filterType": "hotel_main",
            "itemId": "-909090",
            "itemName": "全部酒店",
            "itemType": "button",
            "group": false,
            "itemBehavior": "all"
          }
        ],
        "exposeItemCount": 6
      },
      {
        "groupCode": "hotel_filter",
        "groupName": "酒店",
        "filterName": "星级",
        "filterType": "hotel_grade",
        "multi": 0,
        "filterBehavior": "normal",
        "itemList": [
          {
            "filterType": "hotel_grade",
            "itemId": "2",
            "itemName": "经济型",
            "itemType": "button",
            "group": false,
            "itemBehavior": "normal"
          },
          {
            "filterType": "hotel_grade",
            "itemId": "3",
            "itemName": "3星/舒适",
            "itemType": "button",
            "group": false,
            "itemBehavior": "normal"
          },
          {
            "filterType": "hotel_grade",
            "itemId": "4",
            "itemName": "4星/高档",
            "itemType": "button",
            "group": false,
            "itemBehavior": "normal"
          },
          {
            "filterType": "hotel_grade",
            "itemId": "5",
            "itemName": "5星/豪华",
            "itemType": "button",
            "group": false,
            "itemBehavior": "normal"
          }
        ],
        "exposeItemCount": 6
      },
      {
        "groupCode": "hotel_filter",
        "groupName": "酒店",
        "filterName": "酒店评分",
        "filterType": "hotel_score",
        "multi": 1,
        "filterBehavior": "normal",
        "itemList": [
          {
            "filterType": "hotel_meal",
            "itemId": "4.5,",
            "itemName": "4.5分以上",
            "itemType": "button",
            "group": false,
            "itemBehavior": "normal"
          },
          {
            "filterType": "hotel_meal",
            "itemId": "4.0,",
            "itemName": "4.0分以上",
            "itemType": "button",
            "group": false,
            "itemBehavior": "normal"
          },
          {
            "filterType": "hotel_meal",
            "itemId": "3.5,",
            "itemName": "3.5分以上",
            "itemType": "button",
            "group": false,
            "itemBehavior": "normal"
          },
          {
            "filterType": "hotel_meal",
            "itemId": "3.0,",
            "itemName": "3.0分以上",
            "itemType": "button",
            "group": false,
            "itemBehavior": "normal"
          }
        ],
        "exposeItemCount": 6
      },
      {
        "groupCode": "hotel_filter",
        "groupName": "酒店",
        "filterName": "点评数",
        "filterType": "hotel_comment_count",
        "multi": 1,
        "filterBehavior": "normal",
        "itemList": [
          {
            "filterType": "hotel_comment_count",
            "itemId": "500,",
            "itemName": "500条以上",
            "itemType": "button",
            "group": false,
            "itemBehavior": "normal"
          },
          {
            "filterType": "hotel_comment_count",
            "itemId": "200,",
            "itemName": "200条以上",
            "itemType": "button",
            "group": false,
            "itemBehavior": "normal"
          },
          {
            "filterType": "hotel_comment_count",
            "itemId": "100,",
            "itemName": "100条以上",
            "itemType": "button",
            "group": false,
            "itemBehavior": "normal"
          }
        ],
        "exposeItemCount": 6
      },
      {
        "groupCode": "scenic_filter",
        "groupName": "景点",
        "filterName": "全部景点",
        "filterType": "scenic_main",
        "multi": 1,
        "filterBehavior": "normal",
        "itemList": [
          {
            "filterType": "scenic_main",
            "itemId": "-909090",
            "itemName": "全部景点",
            "itemType": "button",
            "group": false,
            "itemBehavior": "all"
          }
        ],
        "exposeItemCount": 6
      }
    ]
  }
]

export const mockList = {
  "code": "0",
  "msg": "成功",
  "result": {
    "tabType": "0",
    "curPage": 1,
    "hasNextPage": false,
    "countSummaryVO": {
      "hotelCount": 0,
      "scenicCount": 0,
      "resultCount": 11,
      "resultCountShowText": "11"
    },
    "filterPanelVOList": [
      {
        "filterPanelName": "筛选",
        "filterPanelCode": "main_filter",
        "showFilter": true,
        "filterList": [
          {
            "groupCode": "hotel_filter",
            "groupName": "酒店",
            "filterName": "全部酒店",
            "filterType": "hotel_main",
            "multi": 1,
            "filterBehavior": "normal",
            "itemList": [
              {
                "filterType": "hotel_main",
                "itemId": "-909090",
                "itemName": "全部酒店",
                "itemType": "button",
                "group": false,
                "itemBehavior": "all"
              }
            ],
            "exposeItemCount": 6
          },
          {
            "groupCode": "hotel_filter",
            "groupName": "酒店",
            "filterName": "星级",
            "filterType": "hotel_grade",
            "multi": 0,
            "filterBehavior": "normal",
            "itemList": [
              {
                "filterType": "hotel_grade",
                "itemId": "2",
                "itemName": "经济型",
                "itemType": "button",
                "group": false,
                "itemBehavior": "normal"
              },
              {
                "filterType": "hotel_grade",
                "itemId": "3",
                "itemName": "3星/舒适",
                "itemType": "button",
                "group": false,
                "itemBehavior": "normal"
              },
              {
                "filterType": "hotel_grade",
                "itemId": "4",
                "itemName": "4星/高档",
                "itemType": "button",
                "group": false,
                "itemBehavior": "normal"
              },
              {
                "filterType": "hotel_grade",
                "itemId": "5",
                "itemName": "5星/豪华",
                "itemType": "button",
                "group": false,
                "itemBehavior": "normal"
              }
            ],
            "exposeItemCount": 6
          },
          {
            "groupCode": "hotel_filter",
            "groupName": "酒店",
            "filterName": "酒店评分",
            "filterType": "hotel_score",
            "multi": 1,
            "filterBehavior": "normal",
            "itemList": [
              {
                "filterType": "hotel_meal",
                "itemId": "4.5,",
                "itemName": "4.5分以上",
                "itemType": "button",
                "group": false,
                "itemBehavior": "normal"
              },
              {
                "filterType": "hotel_meal",
                "itemId": "4.0,",
                "itemName": "4.0分以上",
                "itemType": "button",
                "group": false,
                "itemBehavior": "normal"
              },
              {
                "filterType": "hotel_meal",
                "itemId": "3.5,",
                "itemName": "3.5分以上",
                "itemType": "button",
                "group": false,
                "itemBehavior": "normal"
              },
              {
                "filterType": "hotel_meal",
                "itemId": "3.0,",
                "itemName": "3.0分以上",
                "itemType": "button",
                "group": false,
                "itemBehavior": "normal"
              }
            ],
            "exposeItemCount": 6
          },
          {
            "groupCode": "hotel_filter",
            "groupName": "酒店",
            "filterName": "点评数",
            "filterType": "hotel_comment_count",
            "multi": 1,
            "filterBehavior": "normal",
            "itemList": [
              {
                "filterType": "hotel_comment_count",
                "itemId": "500,",
                "itemName": "500条以上",
                "itemType": "button",
                "group": false,
                "itemBehavior": "normal"
              },
              {
                "filterType": "hotel_comment_count",
                "itemId": "200,",
                "itemName": "200条以上",
                "itemType": "button",
                "group": false,
                "itemBehavior": "normal"
              },
              {
                "filterType": "hotel_comment_count",
                "itemId": "100,",
                "itemName": "100条以上",
                "itemType": "button",
                "group": false,
                "itemBehavior": "normal"
              }
            ],
            "exposeItemCount": 6
          },
          {
            "groupCode": "scenic_filter",
            "groupName": "景点",
            "filterName": "全部景点",
            "filterType": "scenic_main",
            "multi": 1,
            "filterBehavior": "normal",
            "itemList": [
              {
                "filterType": "scenic_main",
                "itemId": "-909090",
                "itemName": "全部景点",
                "itemType": "button",
                "group": false,
                "itemBehavior": "all"
              }
            ],
            "exposeItemCount": 6
          }
        ]
      }
    ],
    "naturalCardVOList": [
      {
        "cardType": "0",
        "trafficCardVOList": [
          {
            "type": 2,
            "fromCityId": "1",
            "fromCityName": "北京",
            "toCityId": "2",
            "toCityName": "上海",
            "jumpUrl": "https://jipiao.m.jd.com/?initRoute=flightList&fromtype=104&depDate=2024-11-07&arrDate=2024-11-08&queryModule=1&depCity=北京&arrCity=上海&jdreactkey=JDReactAirtickets&jdreactapp=JDReactAirtickets&transparentenable=true&jdreactAppendPath=flightList"
          },
          {
            "type": 1,
            "fromCityId": "1",
            "fromCityName": "北京",
            "toCityId": "2",
            "toCityName": "上海",
            "jumpUrl": "https://train.m.jd.com/train?routerName=train&trainDate=2024-11-07&fromStationCode=&toStationCode=&fromStationName=北京&toStationName=上海&train_source=jipiao&jdreactkey=JDReactTrain&jdreactapp=JDReactTrain&transparentenable=true&jdreactAppendPath=train"
          }
        ]
      },
      {
        "cardType": "2",
        "scenicCardVO": {
          "id": "2004",
          "name": "芒砀山地质公园-凉亭",
          "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/177812/20/16232/702098/60ff6c50E9d1e4c89/d42c9ce593354012.png",
          "price": "9.9",
          "free": false,
          "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=2004&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
          "promotionTagListMap": {
            "mainScenicLocationDistance": [
              {
                "styleCode": "Text",
                "trackId": "scenicDistance",
                "listShowName": "距景点直线624公里",
                "template": "距景点直线%s"
              }
            ],
            "mainScenicPromotion": [
              {
                "styleCode": "TagList",
                "trackId": "scenicBooking",
                "listShowName": "今日可定"
              }
            ]
          }
        }
      },
      {
        "cardType": "1",
        "hotelCardVO": {
          "id": "87027",
          "name": "梨树戴维斯宾馆",
          "picUrl": "https://img12.360buyimg.com/hotel/jfs/t1/147310/18/29945/111508/634f5bfaEda8ebfe9/d9080032e904414f.jpg",
          "price": "300",
          "originPrice": "300",
          "discountPrice": "300",
          "jumpUrl": "https://hotel.m.jd.com?routerName=detail&hotelId=87027&cityId=138&cityName=梨树&checkInDate=2024-11-07&checkOutDate=2024-11-08&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detail",
          "promotionTagListMap": {
            "mainHotelTitleAfter": [
              {
                "styleCode": "Tag",
                "trackId": "hotelGrade",
                "listShowName": "经济"
              }
            ],
            "mainHotelPromotion": [
              {
                "styleCode": "TagList",
                "trackId": "hotelTodayBookable",
                "listShowName": "今日可订",
                "template": "%s"
              },
              {
                "styleCode": "TagList",
                "trackId": "hotelFacilities",
                "listShowName": "免费停车",
                "template": "%s"
              }
            ],
            "mainHotelLocationDistance": [
              {
                "styleCode": "Text",
                "trackId": "hotelLocationDistanceDoubleColumn",
                "listShowName": "四平市"
              }
            ],
            "mainHotelScore": [
              {
                "styleCode": "Text",
                "trackId": "hotelScoreWithFenLimit",
                "listShowName": "4.1分",
                "template": "%s分"
              }
            ]
          }
        }
      },
      {
        "cardType": "2",
        "scenicCardVO": {
          "id": "2003",
          "name": "东拉山大峡谷",
          "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/217619/36/30399/244632/64a26435F6ce4d7ab/8aaf538e836aae05.jpg",
          "free": false,
          "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=2003&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
          "promotionTagListMap": {
            "mainScenicLocationDistance": [
              {
                "styleCode": "Text",
                "trackId": "scenicDistance",
                "listShowName": "距景点直线1640公里",
                "template": "距景点直线%s"
              }
            ],
            "mainScenicTitleAfter": [
              {
                "styleCode": "Tag",
                "trackId": "scenicLevel",
                "listShowName": "4A",
                "template": "%sA"
              }
            ],
            "mainScenicComment": [
              {
                "styleCode": "Text",
                "trackId": "scenicDistance",
                "listShowName": "距景点直线1640公里",
                "template": "距景点直线%s"
              }
            ]
          }
        }
      },
      {
        "cardType": "1",
        "hotelCardVO": {
          "id": "87046",
          "name": "四平朗月宾馆",
          "picUrl": "https://img12.360buyimg.com/hotel/jfs/t1/44578/31/23047/28321/63c44625F0113782c/fe0c7fba18fa6ee5.jpg",
          "price": "400",
          "originPrice": "500",
          "discountPrice": "400",
          "jumpUrl": "https://hotel.m.jd.com?routerName=detail&hotelId=87046&cityId=119&cityName=四平&checkInDate=2024-11-07&checkOutDate=2024-11-08&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detail",
          "promotionTagListMap": {
            "mainHotelTitleAfter": [
              {
                "styleCode": "Tag",
                "trackId": "hotelGrade",
                "listShowName": "经济"
              }
            ],
            "mainHotelPromotion": [
              {
                "styleCode": "TagList",
                "trackId": "hotelTodayBookable",
                "listShowName": "今日可订",
                "template": "%s"
              },
              {
                "styleCode": "TagList",
                "trackId": "hotelFacilities",
                "listShowName": "公用区wifi",
                "template": "%s"
              }
            ],
            "mainHotelLocationDistance": [
              {
                "styleCode": "Text",
                "trackId": "hotelLocationDistanceDoubleColumn",
                "listShowName": "四平市"
              }
            ],
            "mainHotelScore": [
              {
                "styleCode": "Text",
                "trackId": "hotelScoreWithFenLimit",
                "listShowName": "4.6分",
                "template": "%s分"
              }
            ]
          }
        }
      },
      {
        "cardType": "2",
        "scenicCardVO": {
          "id": "2002",
          "name": "雾灵西峰高山滑水",
          "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/173385/27/39556/197271/64e9261eF3c6f6f79/9dc58aae3f5e8480.png",
          "free": false,
          "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=2002&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
          "promotionTagListMap": {
            "mainScenicScore": [
              {
                "styleCode": "Text",
                "trackId": "scenicScore",
                "listShowName": "5.0分",
                "template": "%s分"
              }
            ],
            "mainScenicLocationDistance": [
              {
                "styleCode": "Text",
                "trackId": "scenicDistance",
                "listShowName": "距景点直线115公里",
                "template": "距景点直线%s"
              }
            ]
          }
        }
      },
      {
        "cardType": "1",
        "hotelCardVO": {
          "id": "86998",
          "name": "舒美达宾馆(四平宏信店)",
          "picUrl": "https://img12.360buyimg.com/hotel/jfs/t1/231355/30/6158/52139/656df1a3Ff51e1853/5b138e4352b16b7e.jpg",
          "price": "300",
          "originPrice": "300",
          "discountPrice": "300",
          "jumpUrl": "https://hotel.m.jd.com?routerName=detail&hotelId=86998&cityId=119&cityName=四平&checkInDate=2024-11-07&checkOutDate=2024-11-08&channel=1000&jdreactkey=JDReactHotel&jdreactapp=JDReactHotel&transparentenable=true&jdreactAppendPath=detail",
          "promotionTagListMap": {
            "mainHotelTitleAfter": [
              {
                "styleCode": "Tag",
                "trackId": "hotelGrade",
                "listShowName": "经济"
              }
            ],
            "mainHotelPromotion": [
              {
                "styleCode": "TagList",
                "trackId": "hotelTodayBookable",
                "listShowName": "今日可订",
                "template": "%s"
              },
              {
                "styleCode": "TagList",
                "trackId": "hotelFacilities",
                "listShowName": "免费停车",
                "template": "%s"
              }
            ],
            "mainHotelLocationDistance": [
              {
                "styleCode": "Text",
                "trackId": "hotelLocationDistanceDoubleColumn",
                "listShowName": "四平市"
              }
            ],
            "mainHotelScore": [
              {
                "styleCode": "Text",
                "trackId": "hotelScoreWithFenLimit",
                "listShowName": "4.5分",
                "template": "%s分"
              }
            ]
          }
        }
      },
      {
        "cardType": "2",
        "scenicCardVO": {
          "id": "2001",
          "name": "上海爱琴海购物公园冰雪大世界",
          "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/26498/17/4480/74670/5c31c2afEeb40d194/5788f87fc580f748.jpg",
          "free": false,
          "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=2001&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
          "promotionTagListMap": {
            "mainScenicLocationDistance": [
              {
                "styleCode": "Text",
                "trackId": "scenicDistance",
                "listShowName": "距景点直线1052公里",
                "template": "距景点直线%s"
              }
            ]
          }
        }
      },
      {
        "cardType": "2",
        "scenicCardVO": {
          "id": "1213",
          "name": "黄山尖",
          "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/136065/35/40412/77078/65a97624Fb80a60b7/f28632ecfe222a19.jpg",
          "price": "55",
          "free": false,
          "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=1213&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
          "promotionTagListMap": {
            "mainScenicLocationDistance": [
              {
                "styleCode": "Text",
                "trackId": "scenicDistance",
                "listShowName": "距景点直线1159公里",
                "template": "距景点直线%s"
              }
            ],
            "mainScenicPromotion": [
              {
                "styleCode": "TagList",
                "trackId": "scenicBooking",
                "listShowName": "今日可定"
              }
            ]
          }
        }
      },
      {
        "cardType": "2",
        "scenicCardVO": {
          "id": "1212",
          "name": "云蒙山皇家森林公园",
          "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/9187/10/11992/38087/5c31c441E5152024c/ce6bd3a585ad9e32.jpg",
          "free": false,
          "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=1212&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
          "promotionTagListMap": {
            "mainScenicScore": [
              {
                "styleCode": "Text",
                "trackId": "scenicScore",
                "listShowName": "5.0分",
                "template": "%s分"
              }
            ],
            "mainScenicLocationDistance": [
              {
                "styleCode": "Text",
                "trackId": "scenicDistance",
                "listShowName": "距景点直线85公里",
                "template": "距景点直线%s"
              }
            ]
          }
        }
      },
      {
        "cardType": "2",
        "scenicCardVO": {
          "id": "878",
          "name": "大理苍山石门关景区",
          "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/127347/11/29161/107290/63fbc8b8Fa6205667/e33b85d11e6f44ea.jpg",
          "price": "20",
          "free": false,
          "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=878&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
          "promotionTagListMap": {
            "mainScenicLocationDistance": [
              {
                "styleCode": "Text",
                "trackId": "scenicDistance",
                "listShowName": "距景点直线2206公里",
                "template": "距景点直线%s"
              }
            ],
            "mainScenicPromotion": [
              {
                "styleCode": "TagList",
                "trackId": "scenicBooking",
                "listShowName": "今日可定"
              }
            ],
            "mainScenicTitleAfter": [
              {
                "styleCode": "Tag",
                "trackId": "scenicLevel",
                "listShowName": "4A",
                "template": "%sA"
              }
            ]
          }
        }
      },
      {
        "cardType": "2",
        "scenicCardVO": {
          "id": "999",
          "name": "民国大杂院",
          "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/176206/24/28502/48702/62fcbc39E436748e4/91d8ee43f97c2b30.jpg",
          "price": "25",
          "free": false,
          "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=999&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
          "promotionTagListMap": {
            "mainScenicLocationDistance": [
              {
                "styleCode": "Text",
                "trackId": "scenicDistance",
                "listShowName": "距景点直线1203公里",
                "template": "距景点直线%s"
              }
            ],
            "mainScenicPromotion": [
              {
                "styleCode": "TagList",
                "trackId": "scenicBooking",
                "listShowName": "今日可定"
              }
            ]
          }
        }
      }
    ],
    "recommendCardVOList": [
      {
        "cardType": "2",
        "scenicCardVO": {
          "id": "3001",
          "name": "《春江花月夜·唯美扬州》实景演出",
          "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/16519/28/4506/173642/5c31c2aaE6ff15d8c/b5e9b22aa4650510.jpg",
          "free": false,
          "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=3001&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
          "promotionTagListMap": {
            "mainScenicLocationDistance": [
              {
                "styleCode": "Text",
                "trackId": "scenicDistance",
                "listShowName": "距景点直线860公里",
                "template": "距景点直线%s"
              }
            ]
          }
        }
      },
      {
        "cardType": "2",
        "scenicCardVO": {
          "id": "3002",
          "name": "承德小布达拉宫",
          "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/19022/14/4475/108576/5c31c361Ed3989f66/7c606098f40882cd.jpg",
          "free": false,
          "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=3002&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
          "promotionTagListMap": {
            "mainScenicLocationDistance": [
              {
                "styleCode": "Text",
                "trackId": "scenicDistance",
                "listShowName": "距景点直线241公里",
                "template": "距景点直线%s"
              }
            ]
          }
        }
      },
      {
        "cardType": "2",
        "scenicCardVO": {
          "id": "3004",
          "name": "上海玛雅海滩水公园",
          "picUrl": "https://img30.360buyimg.com/mptrip/jfs/t1/84622/5/23412/140857/63cd6cfcF16565d9d/b7ec795a08977edf.jpg",
          "free": false,
          "jumpUrl": "https://m3-ptrip.jd.com/?initRoute=scenicSpotDetail&scenicId=3004&channel=0&jdreactkey=JDReactWorldOfTomorrow&jdreactapp=JDReactWorldOfTomorrow&transparentenable=true&jdreactAppendPath=scenicSpotDetail",
          "promotionTagListMap": {
            "mainScenicScore": [
              {
                "styleCode": "Text",
                "trackId": "scenicScore",
                "listShowName": "5.0分",
                "template": "%s分"
              }
            ],
            "mainScenicLocationDistance": [
              {
                "styleCode": "Text",
                "trackId": "scenicDistance",
                "listShowName": "距景点直线1054公里",
                "template": "距景点直线%s"
              }
            ]
          }
        }
      }
    ]
  },
  "umpTraceId": "4843826.75724.17309718332700015",
  "success": true
}

import { E_TAB } from "@/pages/result/store/result.model";
import Skeleton from "../../FullLoading/Skeleton";
import { skeletonImgs, skeletonStyle } from "../../FullLoading/skeletonUtils";
import styles from './index.module.scss'

export default function ComprehensiveSkeleton() {
    
    

    return <Skeleton
        className={styles.wr}
        useCustomStyle={true}
        loading={true}
        skeletonStyle={skeletonStyle[E_TAB.Comprehensive]}
        skeletonImg={skeletonImgs[E_TAB.Comprehensive]}
    />
}
import { AnimatedView, Image } from '@/BaseComponents/atoms'
import { useEffect, useRef } from 'react';
import { Animated } from 'react-native';
import classNames from 'classnames'
import styles from './index.module.scss'

export default function Skeleton(props) {

    const { useCustomStyle, style, loading, skeletonStyle, skeletonImg } = props
    console.log('useCustomStyle',props.abc, useCustomStyle, style, skeletonStyle)
    const data = Array.from({ length: 20 }).fill(1)


    const opacity = useRef(new Animated.Value(1)).current;

    function _pointAnimate() {
        Animated.sequence([
            Animated.timing(opacity, {
                useNativeDriver: true,
                toValue: 0.3,
                duration: 1000,
            }),
            Animated.timing(opacity, {
                useNativeDriver: true,
                toValue: 0.5,
                duration: 1000,
            })
        ]).start(({ finished }) => {
            // console.log('在执行吗', finished)
            if (!finished) { return }
            _pointAnimate()
        });
    }

    useEffect(() => {
        if (!loading) {
            // console.log('动画关闭')
            opacity.stopAnimation()
            return;
        }
        _pointAnimate();
    }, [loading])

    if (!loading) {
        return null
    }

    // console.log('skeleton[tab]', skeletonImg, data, skeletonStyle)

    return (
        // @ts-ignore 
        <AnimatedView className={classNames(!useCustomStyle && styles.maskWr, styles.wrPadding)} style={[{ opacity }, style]}>
            {
                data.map((item, index) => {
                    return (
                        // @ts-ignore 
                        <Image rn key={index} mode={"aspectFit"} src={skeletonImg} className={classNames(styles.skeleton)}  style={skeletonStyle}/>
                    )
                })
            }

        </AnimatedView>
    )
}
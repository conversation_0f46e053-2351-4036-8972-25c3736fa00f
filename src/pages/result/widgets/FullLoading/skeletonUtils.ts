import { getImageUrl } from "@/assets/imgs";
import { E_TAB } from "../../store/result.model";
import { pt } from '@ltfe/ltfe-core-lib/lib/utiles'

export const skeletonImgs = {
    [E_TAB.Comprehensive]: getImageUrl("comprehensiveSkeleton"),
    [E_TAB.ScenerySpot]: getImageUrl("scenerySkeleton"),
    [E_TAB.Hotel]: getImageUrl("hotelSkeleton")
}

export const skeletonStyle = {
    [E_TAB.Comprehensive]: {height: pt(265)},
    [E_TAB.Hotel]: {height: pt(138)}
}

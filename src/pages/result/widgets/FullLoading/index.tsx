import { View } from '@/BaseComponents/atoms'
import styles from './index.module.scss'
import { E_TAB, TAB_MAP } from '../../store/result.model';
import { memo, useContext } from 'react';
import { JDLoadingView } from '@jdreact/jdreact-core-lib';
import classNames from 'classnames'
import ResultContext from '../../store/ResultContext';
import Skeleton from './Skeleton';
import { skeletonImgs, skeletonStyle } from './skeletonUtils';


export default memo(function FullLoading(props: any) {
    const { businessType } = props;
    const { useResultStore } = useContext(ResultContext)
    const tabComprehensiveLoading = useResultStore.use.tabComprehensiveLoading();
    const tabHotelLoading = useResultStore.use.tabHotelLoading();
    const tabScenerySpot = useResultStore.use.tabScenerySpot();
    const tabContentLoading = useResultStore.use.tabContentLoading();
    // const tabContentLoading = useResultStore.use.tabContentLoading();

    const tab = businessType //TAB_MAP[currentTabIndex]

    if(businessType === E_TAB.NONE && !tabContentLoading) {
        return null
    }
    if(businessType === E_TAB.Comprehensive && !tabComprehensiveLoading) {
        return null
    }
    if(businessType === E_TAB.Hotel && !tabHotelLoading) {
        return null
    }
    if(businessType === E_TAB.ScenerySpot && !tabScenerySpot) {
        return null
    }


    if (tab === E_TAB.NONE) {
        return (
            <View className={classNames(styles.maskWr, styles.pdTop, styles.loadingWr)}>
                <JDLoadingView />
            </View>
        )
    }


    return <Skeleton
        loading={true}
        skeletonStyle={skeletonStyle[tab]}
        skeletonImg={skeletonImgs[tab]}
    />

})
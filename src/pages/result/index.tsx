import { View } from '@/BaseComponents/atoms'
import { pxTransform, useDidHide, useDidShow, useLoad } from '@tarojs/taro'
import { Tabs } from '@ltfe/ltfe-core-lib'
import { pt, } from '@ltfe/ltfe-core-lib/lib/utiles';
import styles from './index.module.scss'
import { Dimensions, PixelRatio } from 'react-native';
import { useEffect, useMemo, useRef } from 'react';
import TabItem from './widgets/TabItem';
import classNames from "classnames";
import FullLoading from './widgets/FullLoading';
import { E_TAB, TAB_MAP } from './store/result.model';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import SearchBar from './widgets/searchBar';
import { getCurrentPageInstance } from '@/common/common';
import withPage, { withBasePageInfo } from '@/common/withPage';
import { isEmpty } from '@/utils/isType';
import ResultContext, { T_ResultContext } from './store/ResultContext';
import ResultService from './store/result.service';
import { createSelectors } from '@/store/tools';
import { getResultStoreBase } from './store';
import useIndexService from './index.service';
import { STYLE_TYPE } from '@/BusinessComponents/PositionTip/index.model';
import PositionTip from '@/BusinessComponents/PositionTip';
import { isWeb } from '@/common/common'
import useFetch from '@/common/useFetch';
import { urlDecode } from '@/utils/urlDecode';
import { M_EVENTID, M_PAGE, M_PAGEID, MTA_NONE, mtaPv, newMta } from '@/common/mta';
import { reportInfo } from '@/common/reporter';
import { errorCodeConstantMapping, ErrortType } from '@/common/reporter/errorMapping';


function Index(props) {
  const refCurrentTab = useRef(-1)

  const service = useRef(new ResultService()).current
  const { apiFetch } = useFetch()
  service.apiFetch = apiFetch

  service.hideTabSkeleton = (tab: E_TAB) => {
    return setTimeout(() => {
      // if (refCurrentTab.current === tab) {
      updateTabContentLoading(tab, false)
      // }
    }, isWeb ? 60 : 20);
  }


  const resultStoreBase = useRef(getResultStoreBase(service)).current
  const resultStore = useRef({
    service,
    // resultStoreBase,
    useResultStore: createSelectors(resultStoreBase)
  } as T_ResultContext).current;


  const { fetchData, itemDatas, getTabMtaParams } = useIndexService(props, { service, useResultStore: resultStore.useResultStore })
  const updateTabContentLoading = resultStore.useResultStore.use.updateTabContentLoading()

  useLoad(() => {
    // console.log('did Page loaded.', pxTransform(44))
    updateTabContentLoading(E_TAB.NONE, true)
    fetchData().then(() => {
      service.hideTabSkeleton(E_TAB.NONE)

    });
    // apiFetch('QUERY_COUPON_AD', params, true)
    //         .then(res => {
    //             if (res && res.head && +res.head.code === 200 && res.body) {
    //                 resolve(res.body);
    //             } else {
    //                 resolve();
    //             }
    //         }).catch(() => {
    //             resolve();
    //         });

  })

  useEffect(() => {
    console.log('did useEffect')
  }, [])

  useDidShow(withBasePageInfo(() => {
    // console.log('didShow withBasePageInfo props.basePageInfo.pvId', props.basePageInfo.pvId)
    const { displayName, realName } = props.getPageParams();
    // alert(props.basePageInfo.pvId)
    mtaPv(M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
      pvid: props.basePageInfo.pvId,
      logid: props.basePageInfo.pvId,
      displayName: displayName || MTA_NONE,
      keyword: realName || MTA_NONE,
      search_o2o_coordinates: MTA_NONE,
      search_fouraddrid: MTA_NONE
    })
  }))

  useDidHide(() => {
    // console.log('useDidHide')
  })

  const pageParams: any = getCurrentPageInstance().router?.params || {};
  // try {
  //   reportInfo({
  //       code: errorCodeConstantMapping?.PAGE_RESULT_SEARCH_ROUTE_PARAMS,
  //       errorDetail: {
  //           errorType: ErrortType.Info,
  //           customMsg: {
  //               errorDescription: `大搜结果页面路由入参: ${JSON.stringify(pageParams)}`
  //           },
  //       }
  //   })
  // } catch (error) {

  // }

  const displayName = urlDecode(pageParams.displayName)
  const realName = urlDecode(pageParams.realName)
  const searchWord = {
    displayName,
    realName
  }

  // setTimeout(() => {
  //   addressModel.lng = 'tln1111'
  //   console.log('addressModel', JSON.stringify(addressModel))
  // }, 2000);

  const { width: winWidth } = Dimensions.get('window');
  const gutterWidth = 12; // 两边各留12pt的间距
  const totalGutterWidth = pt(gutterWidth * 2 + 20 * 4);
  const contentWidth = winWidth - totalGutterWidth;
  const segmentWidth = PixelRatio.roundToNearestPixel(contentWidth / 5);
  const tabStyle = useMemo(() => ({
    height: pt(30),
    justifyContent: 'space-between',
    paddingHorizontal: pt(12),
  }), [])


  const tabItemStyle = useMemo(() => ({ minWidth: segmentWidth }), [])

  const updateTabIndex = resultStore.useResultStore.use.updateTabIndex()
  const tabIndex = resultStore.useResultStore.use.currentTabIndex()
  const initData = service.getInitTabDatas() || {}
  // const initData = resultStore.useResultStore.use.initData();

  // const [tabIndex, setTabIndex] = useState(-1);
  // const [initData, setInitData] = useState<any>({});

  useEffect(() => {
    //   const dispose = resultStore.useResultStore.subscribe(
    //     (state) => state.initData,
    //     (initData) => {
    //       const { currentTabIndex } = resultStore.useResultStore.getState()
    //       console.log('xxxxxxxxx', initData, currentTabIndex)
    //       if (!isEmpty(initData)) {
    //         setInitData(initData)
    //         setTabIndex(currentTabIndex)
    //       }
    //     }
    //   )

    const dispose1 = resultStore.useResultStore.subscribe(
      (state) => state.currentTabIndex,
      (currentTabIndex) => {
        // const { initData } = resultStore.useResultStore.getState()
        // if (!isEmpty(initData)) {
        //   setTabIndex(currentTabIndex)
        // }
        refCurrentTab.current = TAB_MAP[currentTabIndex];
      }
    )

    return () => {
      // dispose();
      dispose1();
    }
  }, [])

  useEffect(() => {

    // setTimeout(() => {
    //   updateTabIndex(itemDatas.findIndex(x => x.businessType === E_TAB.Comprehensive))
    // }, 20);

    return () => {
      updateTabIndex(E_TAB.NONE)
      // console.log('结果页销毁了')
    }
  }, [])

  const insets = useSafeAreaInsets();

  // const scrollRef = useRef(null); // scroll ref
  // const [isShowBackTop, setIsShowBackTop] = useState(false) // 是否展示返回顶部

  // // 获取 scroll
  // const getScrollRef = (e) => {
  //   scrollRef.current = e
  // }

  // // 超过一屏展示返回顶部
  // const showToTop = (e) => {
  //   const { y } = e.nativeEvent.contentOffset;
  //   if (y > deviceHeight) {
  //     setIsShowBackTop(true)
  //   } else {
  //     setIsShowBackTop(false)
  //   }
  // }

  // // 点击回到顶部
  // const handleBackTopClick = () => {
  //   if (scrollRef.current) {
  //     if (TAB_MAP[tabIndex] == E_TAB.Comprehensive) {
  //       scrollRef.current?.scrollTo({ y: 0, animated: true });
  //     } else if (TAB_MAP[tabIndex] == E_TAB.ScenerySpot || TAB_MAP[tabIndex] == E_TAB.Hotel) {
  //       scrollRef.current?.scrollToOffset({ animated: true, offset: 0 });
  //     }
  //   }
  // }

  // console.log('ItemDatas++++', itemDatas, insets, tabIndex, initData)
  return (
    <ResultContext.Provider value={resultStore}>
      <View className={styles.container}>
        <SearchBar searchWord={searchWord}></SearchBar>
        <View className={classNames('flex1 relative', isWeb ? styles.topMargin : '')}>
          <Tabs
            tabStyle={tabStyle}
            activeColor='#F2270C'
            scrollEnabled={false}
            changeTabWithAnimated={false}
            needLine={false}
            needAnimated={false}
            initialPage={tabIndex}
            changeTab={(index) => {
              // console.log('changeTab', index, E_TAB.Comprehensive, E_TAB.Hotel, E_TAB.ScenerySpot)
              if (tabIndex === E_TAB.NONE) {
                return
              }

              requestAnimationFrame(() => {
                newMta(M_EVENTID.TravelSearcResult_Tab, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, getTabMtaParams(TAB_MAP[tabIndex]))
              })

              updateTabIndex(index)
            }}
          >
            {
              itemDatas.map((item, index) => {
                const initTabData = initData[item.businessType!];
                return (
                  // @ts-ignore
                  <View key={index} className={styles.tabContainer} style={{ width: winWidth }} tabLabel={(
                    <TabItem style={tabItemStyle} data={item} isBlank={item.blank} />
                  )} isBlank={item.blank}>
                    {/* <Text>{item.text}</Text> */}
                    {
                      item.Comp &&
                      <>
                        <item.Comp currentTabIndex={tabIndex} data={initTabData?.data} error={initTabData?.error} businessType={item.businessType} />
                        <FullLoading businessType={item.businessType} />
                      </>
                    }

                  </View>
                )
              })
            }
          </Tabs>
          <FullLoading businessType={E_TAB.NONE}/>
          {/* <BackTop isShow={isShowBackTop} handleClick={handleBackTopClick} /> */}
        </View>
        <View className={styles.positionTip}>
          <PositionTip styleType={STYLE_TYPE.MODEL} />
        </View>
      </View>
    </ResultContext.Provider>
  )
}


export default withPage({ pageName: M_PAGE.SearchResult })(Index)

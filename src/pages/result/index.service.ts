import { useContext, useEffect, useRef, useState } from "react";
import { E_TAB } from "./store/result.model";
import { getImageUrl } from "@/assets/imgs";
import Comprehensive from "./widgets/Comprehensive";
import ScenerySpot from "./widgets/ScenerySpot";
import Hotel from "./widgets/Hotel";
import { BasePageContext } from "@/common/withPage";
import { M_EVENTID, M_PAGE, M_PAGEID, MTA_NONE, mtaExposure, mtaEp, newMta } from "@/common/mta";
import IdleQueue from "@/utils/IdleQueue";
import _ from 'lodash'

export default function useIndexService(props, { service, useResultStore }) {
    const updateTabIndex = useResultStore.use.updateTabIndex()
    // const updateInitDataAndTabIndex = useResultStore.use.updateInitDataAndTabIndex()

    const basePageContext = useContext(BasePageContext)

    const [itemDatas] = useState(() => {
        return [
            { text: '综合', businessType: E_TAB.Comprehensive, iconUri: getImageUrl('plus'), activeBgColor: '#EDF6FF',activeColor: '#006EEB', Comp: Comprehensive },
            { text: '景点', businessType: E_TAB.ScenerySpot, iconUri: getImageUrl('scenerySpotTab'), activeBgColor: '#FFEEE9', activeColor: '#FF5800', Comp: ScenerySpot },
            { text: '酒店', businessType: E_TAB.Hotel, iconUri: getImageUrl('hotelTab'), activeBgColor: '#FFEEE9', activeColor: '#FF5800', Comp: Hotel },
            { blank: true },
            { blank: true }
        ]
    })

    // const refItemIndex = useRef(0)

    // useUpdateOverFirst(() => {
    //     updateTabIndex(refItemIndex.current);
    // }, [itemDatas])

    const refPageMtaParams = useRef<any>({})
    const getTabMtaParams = (tab: E_TAB) => {
        return refPageMtaParams.current[tab];
    }

    const tabMta = (res, businessType) => {
        res = res || {}
        const pvId = basePageContext.basePageInfo.pvId
        const { displayName, realName } = basePageContext.getPageParams();
        // console.log('props.basePageInfo.pvId', basePageContext.basePageInfo.pvId)

        itemDatas.forEach((tabItem, index) => {
            if (tabItem.text) {

                const jsonParams = {
                    pvid: pvId,
                    logid: pvId,
                    displayName: displayName || MTA_NONE,
                    keyword: realName || MTA_NONE,
                    search_o2o_coordinates: MTA_NONE,
                    search_fouraddrid: MTA_NONE,
                    ...(res.result?.commonUserAction || {}),
                    businessType: tabItem.businessType,
                    index: index + 1
                }

                refPageMtaParams.current[tabItem.businessType] = jsonParams
                IdleQueue.add(mtaExposure, M_EVENTID.TravelSearcResult_TabExpo, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
                    ...jsonParams,
                    isSelect: businessType === tabItem.businessType
                })
            }
        })
    }

    const resultMta = (res, businessType) => {
        res = res || {}
        const pvId = basePageContext.basePageInfo.pvId
        const { displayName, realName } = basePageContext.getPageParams();
        const mapKeyword = ["综合", "景点", "酒店"]
        // console.log('props.basePageInfo.pvId', basePageContext.basePageInfo.pvId)
        IdleQueue.add(mtaEp, M_EVENTID.TravelSearcResult_ResultExpo, M_PAGEID.TravelSearcResult, M_PAGE.SearchResult, {
            ..._.get(res, ['result', 'commonUserAction'], {}),
            displayName: _.get(res, ['result', 'commonUserAction', 'keyword'], "-100"),
            tabId: _.get(res, ['result', 'tabType'], "-100"),
            tabName: mapKeyword[_.get(res, ['result', 'tabType'], "-100")] || "-100",
            ..._.get(res, ['result', 'naturalCardVOList', 0, 'userAction'], {})
        })
    }


    async function fetchData() {
        const [error, res] = await service.fetchResult({ needSearchItemId: true })
        // console.log('res0000', error, res)
        let businessType = E_TAB.Comprehensive
        if (res?.result?.tabType) {
            // 这里要设置全局兜底页面
            businessType = res?.result?.tabType
        }
        const itemIndex = error ? 0 : itemDatas.findIndex((item) => item.businessType === businessType);
        // console.log('itemIndex===', itemIndex)

        tabMta(res, businessType)
        resultMta(res, businessType)
        service.setInitTabData(businessType, {
            error,
            data: res
        });
        updateTabIndex(itemIndex);
        // const item = itemDatas[itemIndex];
        // (item as any).error = error;
        // (item as any).data = res?.result;
        // setItemDatas([...itemDatas])
        // updateInitDataAndTabIndex(itemIndex, { data: itemDatas, error })
    }


    return {
        fetchData,
        itemDatas,
        getTabMtaParams
    }
}

diff --git a/node_modules/react-native-i18n/index.js b/node_modules/react-native-i18n/index.js
index 6402f5a..e153fb4 100644
--- a/node_modules/react-native-i18n/index.js
+++ b/node_modules/react-native-i18n/index.js
@@ -7,9 +7,10 @@ const I18nJs = require('i18n-js');
 
 if (typeof RNI18n !== 'undefined') {
   I18nJs.locale = RNI18n.languages[0];
-} else if (__DEV__) {
-  console.warn('react-native-i18n module is not correctly linked');
 }
+// else if (__DEV__) {
+//   console.warn('react-native-i18n module is not correctly linked');
+// }
 
 export const getLanguages = () => RNI18n.getLanguages();
 export default I18nJs;

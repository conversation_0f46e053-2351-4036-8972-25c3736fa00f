diff --git a/node_modules/@jdtaro/plugin-platform-jdrn/lib/router/jdrn/rootNavigation.js b/node_modules/@jdtaro/plugin-platform-jdrn/lib/router/jdrn/rootNavigation.js
index 0d52d93..4a57540 100644
--- a/node_modules/@jdtaro/plugin-platform-jdrn/lib/router/jdrn/rootNavigation.js
+++ b/node_modules/@jdtaro/plugin-platform-jdrn/lib/router/jdrn/rootNavigation.js
@@ -59,6 +59,10 @@ export function navigate(option, method) {
         }
         else if (method === 'redirectTo') {
             updateJumpAnimate(false);
+            // navigationRef.current.dispatch(NavigationActions.deleteRoutes({
+            //     start: 1,
+            //     length: 1
+            // }));
             navigationRef.current.dispatch(NavigationActions.replace({
                 key: navigationRef.current.state.nav.routes[navigationRef.current.state.nav.routes.length - 1].key,
                 routeName: routeParam.pageName,
@@ -77,6 +81,9 @@ export function navigate(option, method) {
             }
         }
         else if (method === 'navigateBack') {
+            if(option.animate === false) {
+                updateJumpAnimate(false);
+            }
             const currIndex = navigationRef.current.state.nav.index;
             if (currIndex < 1) {
                 JDDevice.exitApp();
diff --git a/node_modules/@jdtaro/plugin-platform-jdrn/lib/runtime/scale2dp.js b/node_modules/@jdtaro/plugin-platform-jdrn/lib/runtime/scale2dp.js
index d7f4047..8a134e4 100644
--- a/node_modules/@jdtaro/plugin-platform-jdrn/lib/runtime/scale2dp.js
+++ b/node_modules/@jdtaro/plugin-platform-jdrn/lib/runtime/scale2dp.js
@@ -3,11 +3,11 @@ import { Dimensions, Platform } from 'react-native';
 const deviceWidthDp = Dimensions.get('window').width;
 const deviceHeightDp = Dimensions.get('window').height;
 let uiWidthPx = 375;
-if (Platform.OS === 'ios') {
-    if (Platform.isPad) {
-        uiWidthPx = 750;
-    }
-}
+// if (Platform.OS === 'ios') {
+//     if (Platform.isPad) {
+//         uiWidthPx = 750;
+//     }
+// }
 export function scalePx2dp(uiElementPx) {
     return uiElementPx * deviceWidthDp / uiWidthPx;
 }

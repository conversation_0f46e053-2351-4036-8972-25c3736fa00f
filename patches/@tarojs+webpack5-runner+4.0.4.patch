diff --git a/node_modules/@tarojs/webpack5-runner/dist/webpack/WebpackModule.js b/node_modules/@tarojs/webpack5-runner/dist/webpack/WebpackModule.js
index 5b6842f..3cf6ef0 100644
--- a/node_modules/@tarojs/webpack5-runner/dist/webpack/WebpackModule.js
+++ b/node_modules/@tarojs/webpack5-runner/dist/webpack/WebpackModule.js
@@ -63,6 +63,8 @@ class WebpackModule {
             implementation: require('sass'),
             sassOptions: {
                 outputStyle: 'expanded',
+                // https://github.com/sass/dart-sass/blob/main/CHANGELOG.md#js-api
+                silenceDeprecations: ['legacy-js-api'],
                 importer(url, prev, done) {
                     // 让 sass 文件里的 @import 能解析小程序原生样式文体，如 @import "a.wxss";
                     const extname = node_path_1.default.extname(url);

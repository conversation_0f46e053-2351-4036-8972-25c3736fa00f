diff --git a/node_modules/@react-native-seoul/masonry-list/lib/index.d.ts b/node_modules/@react-native-seoul/masonry-list/lib/index.d.ts
index 051bd1e..a907bbe 100644
--- a/node_modules/@react-native-seoul/masonry-list/lib/index.d.ts
+++ b/node_modules/@react-native-seoul/masonry-list/lib/index.d.ts
@@ -3,6 +3,8 @@ import type { MutableRefObject, ReactElement } from 'react';
 import React from 'react';
 import { ScrollView } from 'react-native';
 interface Props<T> extends Omit<ScrollViewProps, 'refreshControl'> {
+    ScrollContainer?: any;
+    getEl?: (el: any) => any;
     innerRef?: MutableRefObject<ScrollView | undefined>;
     loading?: boolean;
     refreshing?: RefreshControlProps['refreshing'];
diff --git a/node_modules/@react-native-seoul/masonry-list/lib/index.js b/node_modules/@react-native-seoul/masonry-list/lib/index.js
index 39a96d9..e6df3bc 100644
--- a/node_modules/@react-native-seoul/masonry-list/lib/index.js
+++ b/node_modules/@react-native-seoul/masonry-list/lib/index.js
@@ -18,51 +18,59 @@ const isCloseToBottom = ({ layoutMeasurement, contentOffset, contentSize }, onEn
 };
 function MasonryList(props) {
     const [isRefreshing, setIsRefreshing] = useState(false);
-    const { refreshing, data, innerRef, ListHeaderComponent, ListEmptyComponent, ListFooterComponent, ListHeaderComponentStyle, containerStyle, contentContainerStyle, renderItem, onEndReachedThreshold, onEndReached, onRefresh, loading, LoadingView, numColumns = 2, horizontal, onScroll, removeClippedSubviews = false, keyExtractor, keyboardShouldPersistTaps = 'handled', refreshControl = true, refreshControlProps, } = props;
+    let { ScrollContainer, getEl, refreshing, data, innerRef, ListHeaderComponent, ListEmptyComponent, ListFooterComponent, ListHeaderComponentStyle, containerStyle, contentContainerStyle, renderItem, onEndReachedThreshold, onEndReached, onRefresh, loading, LoadingView, numColumns = 2, horizontal, onScroll, removeClippedSubviews = false, keyExtractor, keyboardShouldPersistTaps = 'handled', refreshControl = true, refreshControlProps, } = props;
     const { style } = props, propsWithoutStyle = __rest(props, ["style"]);
-    return (<ScrollView {...propsWithoutStyle} ref={innerRef} style={[{ flex: 1, alignSelf: 'stretch' }, containerStyle]} contentContainerStyle={contentContainerStyle} keyboardShouldPersistTaps={keyboardShouldPersistTaps} removeClippedSubviews={removeClippedSubviews} refreshControl={refreshControl ? (<RefreshControl refreshing={!!(refreshing || isRefreshing)} onRefresh={() => {
-                setIsRefreshing(true);
-                onRefresh === null || onRefresh === void 0 ? void 0 : onRefresh();
-                setIsRefreshing(false);
-            }} {...refreshControlProps}/>) : null} scrollEventThrottle={16} onScroll={(e) => {
-            const nativeEvent = e.nativeEvent;
-            if (isCloseToBottom(nativeEvent, onEndReachedThreshold || 0.0)) {
-                onEndReached === null || onEndReached === void 0 ? void 0 : onEndReached();
-            }
-            onScroll === null || onScroll === void 0 ? void 0 : onScroll(e);
-        }}>
-      <>
-        <View style={ListHeaderComponentStyle}>{ListHeaderComponent}</View>
-        {data.length === 0 && ListEmptyComponent ? (React.isValidElement(ListEmptyComponent) ? (ListEmptyComponent) : (
-        // @ts-ignore
-        <ListEmptyComponent />)) : (<View style={[
-                {
-                    flex: 1,
-                    flexDirection: horizontal ? 'column' : 'row',
-                },
-                style,
-            ]}>
-            {Array.from(Array(numColumns), (_, num) => {
-                return (<View key={`masonry-column-${num}`} style={{
-                        flex: 1 / numColumns,
-                        flexDirection: horizontal ? 'row' : 'column',
-                    }}>
-                  {data
-                        .map((el, i) => {
-                        if (i % numColumns === num) {
-                            return (<View key={(keyExtractor === null || keyExtractor === void 0 ? void 0 : keyExtractor(el, i)) || `masonry-row-${num}-${i}`}>
-                            {renderItem({ item: el, i })}
-                          </View>);
-                        }
-                        return null;
-                    })
-                        .filter((e) => !!e)}
-                </View>);
-            })}
-          </View>)}
-        {loading && LoadingView}
-        {ListFooterComponent}
-      </>
-    </ScrollView>);
+    const _ScrollContainer = ScrollContainer || ScrollView
+    return (<_ScrollContainer {...propsWithoutStyle} ref={el => {
+        if(!innerRef) return;
+        if (!ScrollContainer) {
+            innerRef = el;
+        }else {
+            innerRef = typeof getEl === 'function' ? getEl(el) : el;
+        }
+    }} style={[{ flex: 1, alignSelf: 'stretch' }, containerStyle]} contentContainerStyle={contentContainerStyle} keyboardShouldPersistTaps={keyboardShouldPersistTaps} removeClippedSubviews={removeClippedSubviews} refreshControl={refreshControl ? (<RefreshControl refreshing={!!(refreshing || isRefreshing)} onRefresh={() => {
+        setIsRefreshing(true);
+        onRefresh === null || onRefresh === void 0 ? void 0 : onRefresh();
+        setIsRefreshing(false);
+    }} {...refreshControlProps} />) : null} scrollEventThrottle={16} onScroll={(e) => {
+        const nativeEvent = e.nativeEvent;
+        if (isCloseToBottom(nativeEvent, onEndReachedThreshold || 0.0)) {
+            onEndReached === null || onEndReached === void 0 ? void 0 : onEndReached();
+        }
+        onScroll === null || onScroll === void 0 ? void 0 : onScroll(e);
+    }}>
+        <>
+            <View style={ListHeaderComponentStyle}>{ListHeaderComponent}</View>
+            {data.length === 0 && ListEmptyComponent ? (React.isValidElement(ListEmptyComponent) ? (ListEmptyComponent) : (
+                // @ts-ignore
+                <ListEmptyComponent />)) : (<View style={[
+                    {
+                        flex: 1,
+                        flexDirection: horizontal ? 'column' : 'row',
+                    },
+                    style,
+                ]}>
+                    {Array.from(Array(numColumns), (_, num) => {
+                        return (<View key={`masonry-column-${num}`} style={{
+                            flex: 1 / numColumns,
+                            flexDirection: horizontal ? 'row' : 'column',
+                        }}>
+                            {data
+                                .map((el, i) => {
+                                    if (i % numColumns === num) {
+                                        return (<View key={(keyExtractor === null || keyExtractor === void 0 ? void 0 : keyExtractor(el, i)) || `masonry-row-${num}-${i}`}>
+                                            {renderItem({ item: el, i })}
+                                        </View>);
+                                    }
+                                    return null;
+                                })
+                                .filter((e) => !!e)}
+                        </View>);
+                    })}
+                </View>)}
+            {loading && LoadingView}
+            {ListFooterComponent}
+        </>
+    </_ScrollContainer>);
 }
 export default memo(MasonryList);

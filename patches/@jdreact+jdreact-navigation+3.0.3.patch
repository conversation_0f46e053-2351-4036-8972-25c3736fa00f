diff --git a/node_modules/@jdreact/jdreact-navigation/src/views/TransitionConfigs.js b/node_modules/@jdreact/jdreact-navigation/src/views/TransitionConfigs.js
index 3e28a32..788d118 100644
--- a/node_modules/@jdreact/jdreact-navigation/src/views/TransitionConfigs.js
+++ b/node_modules/@jdreact/jdreact-navigation/src/views/TransitionConfigs.js
@@ -82,6 +82,14 @@ function defaultTransitionConfig(
   if (isModal) {
     return ModalSlideFromBottomIOS;
   }
+  if(!global.__taroJumpAnimate) {
+    SlideFromRightIOS.transitionSpec.duration = 0;
+    setTimeout(() => {
+      global.__taroJumpAnimate = true;
+    }, 0);
+  } else {
+    SlideFromRightIOS.transitionSpec.duration = 500
+  }
   return SlideFromRightIOS;
 }
 

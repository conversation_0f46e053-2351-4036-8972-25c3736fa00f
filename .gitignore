dist/

# OSX
.DS_Store
config/harmony.json

# Xcode
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
ios/.xcode.env.local

# Android/IntelliJ
build/
.idea
.gradle
local.properties
*.iml
*.hprof
.cxx/
*.keystore
!debug.keystore

# node.js
node_modules/
npm-debug.log
yarn-error.log

# fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://docs.fastlane.tools/best-practices/source-control/

**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Bundle artifact
*.jsbundle

# Ruby / CocoaPods
/ios/Pods/
/vendor/bundle/

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*
# testing
/coverage

/android/app/src/main/java/com/tarodemo/generated

## taro-react-native-release
!release/**

# taro
.swc
src - 副本 (2)/app.config.ts
src - 副本 (2)/app.scss
src - 副本 (2)/app.tsx
src - 副本 (2)/index.html
src - 副本 (2)/BaseComponents/.gitkeep
src - 副本 (2)/BaseComponents/BackTop/index.module.scss
src - 副本 (2)/BaseComponents/BackTop/index.tsx
src - 副本 (2)/BaseComponents/CalcElementLayout/index.js
src - 副本 (2)/BaseComponents/CalcTextLayout/index.js
src - 副本 (2)/BaseComponents/Dialog/index.rn.tsx
src - 副本 (2)/BaseComponents/Dialog/index.tsx
src - 副本 (2)/BaseComponents/Dialog/index.web.tsx
src - 副本 (2)/BaseComponents/FloatLayout/dom.ts
src - 副本 (2)/BaseComponents/FloatLayout/index.tsx
src - 副本 (2)/BaseComponents/IntersectionObserver/global.d.ts
src - 副本 (2)/BaseComponents/IntersectionObserver/index.ts
src - 副本 (2)/BaseComponents/IntersectionObserver/IntersectionObserver.ts
src - 副本 (2)/BaseComponents/IntersectionObserver/InView.tsx
src - 副本 (2)/BaseComponents/IntersectionObserver/IOAnimatedScrollView.ts
src - 副本 (2)/BaseComponents/IntersectionObserver/IOContext.ts
src - 副本 (2)/BaseComponents/IntersectionObserver/IOFlatList.ts
src - 副本 (2)/BaseComponents/IntersectionObserver/IOManager.ts
src - 副本 (2)/BaseComponents/IntersectionObserver/IOScrollView.ts
src - 副本 (2)/BaseComponents/IntersectionObserver/IOSectionList.ts
src - 副本 (2)/BaseComponents/IntersectionObserver/Readme.md
src - 副本 (2)/BaseComponents/IntersectionObserver/withIO.h5.tsx
src - 副本 (2)/BaseComponents/IntersectionObserver/withIO.tsx
src - 副本 (2)/BaseComponents/MarkDown/index.rn.tsx
src - 副本 (2)/BaseComponents/MarkDown/index.tsx
src - 副本 (2)/BaseComponents/MarkDown/index.web.css
src - 副本 (2)/BaseComponents/MarkDown/index.web.tsx
src - 副本 (2)/BaseComponents/NavBar/index.rn.tsx
src - 副本 (2)/BaseComponents/NavBar/index.tsx
src - 副本 (2)/BaseComponents/NavBar/index.web.tsx
src - 副本 (2)/BaseComponents/NetWorkError/index.rn.tsx
src - 副本 (2)/BaseComponents/NetWorkError/index.tsx
src - 副本 (2)/BaseComponents/NetWorkError/index.web.tsx
src - 副本 (2)/BaseComponents/atoms/.gitkeep
src - 副本 (2)/BaseComponents/atoms/index.tsx
src - 副本 (2)/BaseComponents/atoms/AdaptiveImage/index.rn.tsx
src - 副本 (2)/BaseComponents/atoms/AdaptiveImage/index.tsx
src - 副本 (2)/BaseComponents/atoms/AnimatedView/index.rn.tsx
src - 副本 (2)/BaseComponents/atoms/AnimatedView/index.tsx
src - 副本 (2)/BaseComponents/atoms/AnimatedView/index.web.tsx
src - 副本 (2)/BaseComponents/atoms/Image/index.rn.tsx
src - 副本 (2)/BaseComponents/atoms/Image/index.tsx
src - 副本 (2)/BaseComponents/atoms/Image/index.web.tsx
src - 副本 (2)/BaseComponents/atoms/LinearGradient/index.rn.tsx
src - 副本 (2)/BaseComponents/atoms/LinearGradient/index.tsx
src - 副本 (2)/BaseComponents/atoms/LinearGradient/index.web.tsx
src - 副本 (2)/BaseComponents/atoms/Loading/index.module.scss
src - 副本 (2)/BaseComponents/atoms/Loading/index.rn.tsx
src - 副本 (2)/BaseComponents/atoms/Loading/index.tsx
src - 副本 (2)/BaseComponents/atoms/Loading/index.web.tsx
src - 副本 (2)/BaseComponents/atoms/ScrollView/index.rn.tsx
src - 副本 (2)/BaseComponents/atoms/ScrollView/index.tsx
src - 副本 (2)/BaseComponents/atoms/ScrollView/index.web.ts
src - 副本 (2)/BaseComponents/atoms/ScrollView/index.web.tsx
src - 副本 (2)/BaseComponents/atoms/Text/index.rn.tsx
src - 副本 (2)/BaseComponents/atoms/Text/index.tsx
src - 副本 (2)/BaseComponents/atoms/Text/index.web.tsx
src - 副本 (2)/BaseComponents/atoms/TextInput/index.rn.tsx
src - 副本 (2)/BaseComponents/atoms/TextInput/index.tsx
src - 副本 (2)/BaseComponents/atoms/TextInput/index.web.tsx
src - 副本 (2)/BaseComponents/atoms/Tip/index.rn.ts
src - 副本 (2)/BaseComponents/atoms/Tip/index.ts
src - 副本 (2)/BaseComponents/atoms/Tip/index.web.ts
src - 副本 (2)/BaseComponents/atoms/Toast/index.rn.tsx
src - 副本 (2)/BaseComponents/atoms/Toast/index.tsx
src - 副本 (2)/BaseComponents/atoms/Toast/index.web.tsx
src - 副本 (2)/BaseComponents/atoms/View/index.rn.tsx
src - 副本 (2)/BaseComponents/atoms/View/index.tsx
src - 副本 (2)/BaseComponents/atoms/View/index.web.tsx
src - 副本 (2)/BaseComponents/atoms/View/type.d.ts
src - 副本 (2)/BaseComponents/atoms/utils/boldStyle.rn.ts
src - 副本 (2)/BaseComponents/atoms/utils/boldStyle.ts
src - 副本 (2)/BaseComponents/atoms/utils/boldStyle.web.ts
src - 副本 (2)/BaseComponents/atoms/utils/index.rn.ts
src - 副本 (2)/BaseComponents/atoms/utils/index.ts
src - 副本 (2)/BaseComponents/atoms/utils/index.web.ts
src - 副本 (2)/BaseComponents/atoms/utils/toast.ts
src - 副本 (2)/BaseComponents/atoms/utils/withClassName.tsx
src - 副本 (2)/BaseComponents/atoms/utils/withClassName.web.tsx
src - 副本 (2)/BaseComponents/atoms/utils/withStyleForWeb.tsx
src - 副本 (2)/BusinessComponents/DegradePage/index.tsx
src - 副本 (2)/BusinessComponents/ErrorBoundary/index.tsx
src - 副本 (2)/BusinessComponents/PositionTip/index.model.ts
src - 副本 (2)/BusinessComponents/PositionTip/index.module.scss
src - 副本 (2)/BusinessComponents/PositionTip/index.service.ts
src - 副本 (2)/BusinessComponents/PositionTip/index.tsx
src - 副本 (2)/Components/CatchAsyncData.tsx
src - 副本 (2)/Components/EventCatch.tsx
src - 副本 (2)/Components/index.tsx
src - 副本 (2)/Components/Scene.js
src - 副本 (2)/Components/Card/index.js
src - 副本 (2)/Components/Card/element/BookIcon.js
src - 副本 (2)/Components/Card/element/HotelTitleRender.tsx
src - 副本 (2)/Components/Card/element/index.js
src - 副本 (2)/Components/Card/element/JdTag.js
src - 副本 (2)/Components/Card/element/price.js
src - 副本 (2)/Components/Card/element/price.module.scss
src - 副本 (2)/Components/Card/element/promos.js
src - 副本 (2)/Components/Card/element/Rank.tsx
src - 副本 (2)/Components/Card/element/RightBottomCornerTag.js
src - 副本 (2)/Components/Card/element/SearchScore.js
src - 副本 (2)/Components/Card/element/Stars.js
src - 副本 (2)/Components/Card/element/Tag.js
src - 副本 (2)/Components/Card/element/TagRender.tsx
src - 副本 (2)/Components/Card/element/Text.js
src - 副本 (2)/Components/Card/element/TextTag.js
src - 副本 (2)/Components/Card/element/TimeOffer.js
src - 副本 (2)/Components/Card/element/Title.js
src - 副本 (2)/Components/Card/element/TitleRender.tsx
src - 副本 (2)/Components/Card/element/utils.js
src - 副本 (2)/Components/Card/mode/CardList.js
src - 副本 (2)/Components/Card/mode/Collapse.js
src - 副本 (2)/Components/Card/mode/HotelSearch.js
src - 副本 (2)/Components/Card/mode/LineHotel.js
src - 副本 (2)/Components/Card/mode/LineScenic.js
src - 副本 (2)/Components/Card/mode/LIneTraffic.js
src - 副本 (2)/Components/Card/mode/Scenic.js
src - 副本 (2)/Components/Filter/filter.json
src - 副本 (2)/Components/Filter/index.js
src - 副本 (2)/Components/Filter/utils.js
src - 副本 (2)/Components/Filter/element/_FilterSelect.js
src - 副本 (2)/Components/Filter/element/FilterCascader.js
src - 副本 (2)/Components/Filter/element/FilterCustom.js
src - 副本 (2)/Components/Filter/element/FilterItem.js
src - 副本 (2)/Components/Filter/element/FilterOption.js
src - 副本 (2)/Components/Filter/element/FilterSelect.js
src - 副本 (2)/Components/Filter/element/FilterTabCascader.js
src - 副本 (2)/Components/Filter/element/FilterTag.js
src - 副本 (2)/Components/Filter/element/FilterTagList.js
src - 副本 (2)/Components/Filter/element/Location.js
src - 副本 (2)/Components/Filter/element/SearchList.js
src - 副本 (2)/Components/Filter/element/SearchPanel.js
src - 副本 (2)/Components/Filter/element/SearchSelect.js
src - 副本 (2)/Components/Filter/element/SearchTabList.js
src - 副本 (2)/Components/Filter/element/SearchTabListGroup.js
src - 副本 (2)/Components/Filter/element/SearchTabPanel.js
src - 副本 (2)/Components/Filter/element/SearchTabScorll.js
src - 副本 (2)/Components/Filter/element/SearchTabSelect.js
src - 副本 (2)/Components/Filter/element/NoData/index.module.scss
src - 副本 (2)/Components/Filter/element/NoData/index.tsx
src - 副本 (2)/Components/Filter/element/price/index.hook.js
src - 副本 (2)/Components/Filter/element/price/index.js
src - 副本 (2)/Components/Filter/element/price/slider.js
src - 副本 (2)/Components/utils/index.js
src - 副本 (2)/Components/utils/lru.js
src - 副本 (2)/Components/utils/theme.js
src - 副本 (2)/assets/imgs.ts
src - 副本 (2)/assets/imgUrlList.ts
src - 副本 (2)/assets/theme.android.scss
src - 副本 (2)/assets/theme.ios.scss
src - 副本 (2)/assets/theme.scss
src - 副本 (2)/common/common.ts
src - 副本 (2)/common/hotelBaseInfo.ts
src - 副本 (2)/common/init-api-middleware.ts
src - 副本 (2)/common/init-global-middleware.ts
src - 副本 (2)/common/mixin.ts
src - 副本 (2)/common/reportInfo.ts
src - 副本 (2)/common/useAppState.ts
src - 副本 (2)/common/useFetch.ts
src - 副本 (2)/common/useJumpTo.ts
src - 副本 (2)/common/withLogging.tsx
src - 副本 (2)/common/withPage.tsx
src - 副本 (2)/common/JDShareDataUtil/index.rn.ts
src - 副本 (2)/common/JDShareDataUtil/index.ts
src - 副本 (2)/common/LBS/getLatLngPos.ts
src - 副本 (2)/common/LBS/getLBS.rn.ts
src - 副本 (2)/common/LBS/getLBS.ts
src - 副本 (2)/common/LBS/getLBS.web.ts
src - 副本 (2)/common/LBS/locationPermission.ts
src - 副本 (2)/common/Login/index.ts
src - 副本 (2)/common/mta/index.ts
src - 副本 (2)/common/reporter/errorMapping.ts
src - 副本 (2)/common/reporter/index.ts
src - 副本 (2)/pages/VResult/constance.ts
src - 副本 (2)/pages/VResult/defaultSchema.json
src - 副本 (2)/pages/VResult/index.config.ts
src - 副本 (2)/pages/VResult/index.module.scss
src - 副本 (2)/pages/VResult/index.tsx
src - 副本 (2)/pages/VResult/mock.json
src - 副本 (2)/pages/VResult/Dialog/index.module.scss
src - 副本 (2)/pages/VResult/Dialog/index.tsx
src - 副本 (2)/pages/VResult/DiscountPop/index.css
src - 副本 (2)/pages/VResult/DiscountPop/index.module.scss
src - 副本 (2)/pages/VResult/DiscountPop/index.tsx
src - 副本 (2)/pages/VResult/HotelSkeleton/index.module.scss
src - 副本 (2)/pages/VResult/HotelSkeleton/index.tsx
src - 副本 (2)/pages/VResult/List/index.module.scss
src - 副本 (2)/pages/VResult/List/index.tsx
src - 副本 (2)/pages/VResult/NoMatches/index.module.scss
src - 副本 (2)/pages/VResult/NoMatches/index.tsx
src - 副本 (2)/pages/VResult/PriceTag/index.module.scss
src - 副本 (2)/pages/VResult/PriceTag/index.tsx
src - 副本 (2)/pages/VResult/SortBar/FilterPanel.module.scss
src - 副本 (2)/pages/VResult/SortBar/FilterPanel.tsx
src - 副本 (2)/pages/VResult/SortBar/index.module.scss
src - 副本 (2)/pages/VResult/SortBar/index.tsx
src - 副本 (2)/pages/VResult/SortBar/mockdata.ts
src - 副本 (2)/pages/VResult/SortBar/SortItem.tsx
src - 副本 (2)/pages/VResult/SortBar/QuickMatchTab/index.module.scss
src - 副本 (2)/pages/VResult/SortBar/QuickMatchTab/index.tsx
src - 副本 (2)/pages/VResult/SortBar/QuickMatchTab/mockdata.ts
src - 副本 (2)/pages/VResult/SortBar/QuickMatchTab/SelectItem/index.module.scss
src - 副本 (2)/pages/VResult/SortBar/QuickMatchTab/SelectItem/index.tsx
src - 副本 (2)/pages/VResult/WebView/index.module.scss
src - 副本 (2)/pages/VResult/WebView/index.tsx
src - 副本 (2)/pages/VResult/searchBar/index.module.scss
src - 副本 (2)/pages/VResult/searchBar/index.service.ts
src - 副本 (2)/pages/VResult/searchBar/index.tsx
src - 副本 (2)/pages/hotelSearch/index.config.ts
src - 副本 (2)/pages/hotelSearch/index.module.scss
src - 副本 (2)/pages/hotelSearch/index.tsx
src - 副本 (2)/pages/hotelSearch/components/HotelList/index.module.scss
src - 副本 (2)/pages/hotelSearch/components/HotelList/index.tsx
src - 副本 (2)/pages/hotelSearch/components/HotelList/components/areaClear/index.module.scss
src - 副本 (2)/pages/hotelSearch/components/HotelList/components/areaClear/index.tsx
src - 副本 (2)/pages/hotelSearch/components/HotelList/components/empty/index.module.scss
src - 副本 (2)/pages/hotelSearch/components/HotelList/components/empty/index.tsx
src - 副本 (2)/pages/hotelSearch/components/HotelList/components/noData/index.tsx
src - 副本 (2)/pages/hotelSearch/components/HotelList/components/noMatch/index.module.scss
src - 副本 (2)/pages/hotelSearch/components/HotelList/components/noMatch/index.tsx
src - 副本 (2)/pages/hotelSearch/components/HotelList/components/recommandTitle/index.module.scss
src - 副本 (2)/pages/hotelSearch/components/HotelList/components/recommandTitle/index.tsx
src - 副本 (2)/pages/hotelSearch/components/SearchBar/index.module.scss
src - 副本 (2)/pages/hotelSearch/components/SearchBar/index.tsx
src - 副本 (2)/pages/hotelSearch/components/SearchBar/types.ts
src - 副本 (2)/pages/hotelSearch/components/SearchBar/components/mddInfoView/index.module.scss
src - 副本 (2)/pages/hotelSearch/components/SearchBar/components/mddInfoView/index.tsx
src - 副本 (2)/pages/hotelSearch/components/SearchBar/components/searchInputView/index.module.scss
src - 副本 (2)/pages/hotelSearch/components/SearchBar/components/searchInputView/index.tsx
src - 副本 (2)/pages/hotelSearch/components/SearchBar/hooks/useSearchBar.ts
src - 副本 (2)/pages/hotelSearch/components/SortBar/index.module.scss
src - 副本 (2)/pages/hotelSearch/components/SortBar/index.tsx
src - 副本 (2)/pages/hotelSearch/components/SortBar/QuickMatchTab/index.module.scss
src - 副本 (2)/pages/hotelSearch/components/SortBar/QuickMatchTab/index.tsx
src - 副本 (2)/pages/hotelSearch/components/SortBar/QuickMatchTab/SelectItem.tsx
src - 副本 (2)/pages/hotelSearch/components/SortBar/SortItem/index.module.scss
src - 副本 (2)/pages/hotelSearch/components/SortBar/SortItem/index.tsx
src - 副本 (2)/pages/hotelSearch/components/filter/index.ts
src - 副本 (2)/pages/hotelSearch/components/filter/HotelFilter/index.module.scss
src - 副本 (2)/pages/hotelSearch/components/filter/HotelFilter/index.tsx
src - 副本 (2)/pages/hotelSearch/components/filter/LocationFilter/index.module.scss
src - 副本 (2)/pages/hotelSearch/components/filter/LocationFilter/index.tsx
src - 副本 (2)/pages/hotelSearch/components/filter/LocationFilter/LocationFilterContainer.tsx
src - 副本 (2)/pages/hotelSearch/components/filter/PriceStarFilter/index.module.scss
src - 副本 (2)/pages/hotelSearch/components/filter/PriceStarFilter/index.tsx
src - 副本 (2)/pages/hotelSearch/components/filter/SortFilter/index.module.scss
src - 副本 (2)/pages/hotelSearch/components/filter/SortFilter/index.tsx
src - 副本 (2)/pages/hotelSearch/constants/config.ts
src - 副本 (2)/pages/hotelSearch/hooks/useHotelSearch.ts
src - 副本 (2)/pages/hotelSearch/services/api.ts
src - 副本 (2)/pages/hotelSearch/services/defaultRequestParams.js
src - 副本 (2)/pages/hotelSearch/utils/shareDataUtils.ts
src - 副本 (2)/pages/hotelSearch/utils/utils.ts
src - 副本 (2)/pages/index/index.config.ts
src - 副本 (2)/pages/index/index.module.scss
src - 副本 (2)/pages/index/index.service.ts
src - 副本 (2)/pages/index/index.tsx
src - 副本 (2)/pages/index/widgets/HistoricalSearch/index.model.ts
src - 副本 (2)/pages/index/widgets/HistoricalSearch/index.module.scss
src - 副本 (2)/pages/index/widgets/HistoricalSearch/index.service.ts
src - 副本 (2)/pages/index/widgets/HistoricalSearch/index.tsx
src - 副本 (2)/pages/index/widgets/LexicalChunks/index.module.scss
src - 副本 (2)/pages/index/widgets/LexicalChunks/index.service.ts
src - 副本 (2)/pages/index/widgets/LexicalChunks/index.tsx
src - 副本 (2)/pages/index/widgets/NoResultTip/index.module.scss
src - 副本 (2)/pages/index/widgets/NoResultTip/index.tsx
src - 副本 (2)/pages/index/widgets/Rank/index.module.scss
src - 副本 (2)/pages/index/widgets/Rank/index.tsx
src - 副本 (2)/pages/index/widgets/Rank/rankItem.module.scss
src - 副本 (2)/pages/index/widgets/Rank/rankItem.tsx
src - 副本 (2)/pages/index/widgets/Recommend/index.module.scss
src - 副本 (2)/pages/index/widgets/Recommend/index.service.ts
src - 副本 (2)/pages/index/widgets/Recommend/index.tsx
src - 副本 (2)/pages/index/widgets/SearchBar/index.module.scss
src - 副本 (2)/pages/index/widgets/SearchBar/index.service.ts
src - 副本 (2)/pages/index/widgets/SearchBar/index.tsx
src - 副本 (2)/pages/result/index.config.ts
src - 副本 (2)/pages/result/index.module.scss
src - 副本 (2)/pages/result/index.service.ts
src - 副本 (2)/pages/result/index.tsx
src - 副本 (2)/pages/result/store/index.ts
src - 副本 (2)/pages/result/store/mock.json.ts
src - 副本 (2)/pages/result/store/result.model.ts
src - 副本 (2)/pages/result/store/result.service.ts
src - 副本 (2)/pages/result/store/ResultContext.ts
src - 副本 (2)/pages/result/widgets/Test.tsx
src - 副本 (2)/pages/result/widgets/Comprehensive/data.json
src - 副本 (2)/pages/result/widgets/Comprehensive/FilterPanel.module.scss
src - 副本 (2)/pages/result/widgets/Comprehensive/FilterPanel.tsx
src - 副本 (2)/pages/result/widgets/Comprehensive/index.module.scss
src - 副本 (2)/pages/result/widgets/Comprehensive/index.service.ts
src - 副本 (2)/pages/result/widgets/Comprehensive/index.tsx
src - 副本 (2)/pages/result/widgets/Comprehensive/index1.tsx
src - 副本 (2)/pages/result/widgets/Comprehensive/mockdata.ts
src - 副本 (2)/pages/result/widgets/Comprehensive/Test.tsx
src - 副本 (2)/pages/result/widgets/Comprehensive/useGetComprehensiveData copy.ts
src - 副本 (2)/pages/result/widgets/Comprehensive/useGetComprehensiveData.ts
src - 副本 (2)/pages/result/widgets/Comprehensive/useScrollService.ts
src - 副本 (2)/pages/result/widgets/Comprehensive/useViewRuleService.ts
src - 副本 (2)/pages/result/widgets/Comprehensive/useWithListHeader.tsx
src - 副本 (2)/pages/result/widgets/Comprehensive/ComprehensiveCard/index.tsx
src - 副本 (2)/pages/result/widgets/Comprehensive/ComprehensiveSkeleton/index.module.scss
src - 副本 (2)/pages/result/widgets/Comprehensive/ComprehensiveSkeleton/index.tsx
src - 副本 (2)/pages/result/widgets/Comprehensive/RecyclerListViewWithHeader/index.tsx
src - 副本 (2)/pages/result/widgets/Comprehensive/SortBar/index.module.scss
src - 副本 (2)/pages/result/widgets/Comprehensive/SortBar/index.tsx
src - 副本 (2)/pages/result/widgets/ErrorView/index.tsx
src - 副本 (2)/pages/result/widgets/FullLoading/index.module.scss
src - 副本 (2)/pages/result/widgets/FullLoading/index.tsx
src - 副本 (2)/pages/result/widgets/FullLoading/Skeleton.tsx
src - 副本 (2)/pages/result/widgets/FullLoading/skeletonUtils.ts
src - 副本 (2)/pages/result/widgets/Hotel/communication.ts
src - 副本 (2)/pages/result/widgets/Hotel/DraggableView.tsx
src - 副本 (2)/pages/result/widgets/Hotel/FilterPanel.module.scss
src - 副本 (2)/pages/result/widgets/Hotel/FilterPanel.tsx
src - 副本 (2)/pages/result/widgets/Hotel/index.module.scss
src - 副本 (2)/pages/result/widgets/Hotel/index.service.ts
src - 副本 (2)/pages/result/widgets/Hotel/index.tsx
src - 副本 (2)/pages/result/widgets/Hotel/useGetHotelData.service.ts
src - 副本 (2)/pages/result/widgets/Hotel/useScrollService.ts
src - 副本 (2)/pages/result/widgets/Hotel/BaseInfo/ExpandHotelSearchPanel.tsx
src - 副本 (2)/pages/result/widgets/Hotel/BaseInfo/index.module.scss
src - 副本 (2)/pages/result/widgets/Hotel/BaseInfo/index.tsx
src - 副本 (2)/pages/result/widgets/Hotel/BaseInfo/PositionArea.tsx
src - 副本 (2)/pages/result/widgets/Hotel/BaseInfo/ExpandPanel/index.module.scss
src - 副本 (2)/pages/result/widgets/Hotel/BaseInfo/ExpandPanel/index.tsx
src - 副本 (2)/pages/result/widgets/Hotel/BaseInfo/SelectItem/index.module.scss
src - 副本 (2)/pages/result/widgets/Hotel/BaseInfo/SelectItem/index.tsx
src - 副本 (2)/pages/result/widgets/Hotel/Bubble/index.module.scss
src - 副本 (2)/pages/result/widgets/Hotel/Bubble/index.tsx
src - 副本 (2)/pages/result/widgets/Hotel/ClearArea/index.module.scss
src - 副本 (2)/pages/result/widgets/Hotel/ClearArea/index.tsx
src - 副本 (2)/pages/result/widgets/Hotel/HotelCard/index.module.scss
src - 副本 (2)/pages/result/widgets/Hotel/HotelCard/index.tsx
src - 副本 (2)/pages/result/widgets/Hotel/HotelSkeleton/index.module.scss
src - 副本 (2)/pages/result/widgets/Hotel/HotelSkeleton/index.tsx
src - 副本 (2)/pages/result/widgets/Hotel/SortBar/index.module.scss
src - 副本 (2)/pages/result/widgets/Hotel/SortBar/index.tsx
src - 副本 (2)/pages/result/widgets/Hotel/SortBar/mockdata.ts
src - 副本 (2)/pages/result/widgets/Hotel/SortBar/QuickMatchTab/index.module.scss
src - 副本 (2)/pages/result/widgets/Hotel/SortBar/QuickMatchTab/index.tsx
src - 副本 (2)/pages/result/widgets/Hotel/SortBar/QuickMatchTab/mockdata.ts
src - 副本 (2)/pages/result/widgets/ListBottomTip/index.module.scss
src - 副本 (2)/pages/result/widgets/ListBottomTip/index.tsx
src - 副本 (2)/pages/result/widgets/NoData/index.module.scss
src - 副本 (2)/pages/result/widgets/NoData/index.tsx
src - 副本 (2)/pages/result/widgets/NoMatches/index.module.scss
src - 副本 (2)/pages/result/widgets/NoMatches/index.tsx
src - 副本 (2)/pages/result/widgets/NoMore/index.module.scss
src - 副本 (2)/pages/result/widgets/NoMore/index.tsx
src - 副本 (2)/pages/result/widgets/NoSieveData/index.module.scss
src - 副本 (2)/pages/result/widgets/NoSieveData/index.tsx
src - 副本 (2)/pages/result/widgets/RecommendTitle/index.module.scss
src - 副本 (2)/pages/result/widgets/RecommendTitle/index.tsx
src - 副本 (2)/pages/result/widgets/ScenerySpot/communication.ts
src - 副本 (2)/pages/result/widgets/ScenerySpot/FilterPanel.module.scss
src - 副本 (2)/pages/result/widgets/ScenerySpot/FilterPanel.tsx
src - 副本 (2)/pages/result/widgets/ScenerySpot/index.module.scss
src - 副本 (2)/pages/result/widgets/ScenerySpot/index.service.ts
src - 副本 (2)/pages/result/widgets/ScenerySpot/index.tsx
src - 副本 (2)/pages/result/widgets/ScenerySpot/mockdata.ts
src - 副本 (2)/pages/result/widgets/ScenerySpot/useGetScenerySpotData.ts
src - 副本 (2)/pages/result/widgets/ScenerySpot/useScrollService.ts
src - 副本 (2)/pages/result/widgets/ScenerySpot/ScenerySpotSkeleton/index.module.scss
src - 副本 (2)/pages/result/widgets/ScenerySpot/ScenerySpotSkeleton/index.tsx
src - 副本 (2)/pages/result/widgets/ScenerySpot/SortBar/index.module.scss
src - 副本 (2)/pages/result/widgets/ScenerySpot/SortBar/index.tsx
src - 副本 (2)/pages/result/widgets/TabItem/index.module.scss
src - 副本 (2)/pages/result/widgets/TabItem/index.tsx
src - 副本 (2)/pages/result/widgets/searchBar/index.module.scss
src - 副本 (2)/pages/result/widgets/searchBar/index.service.ts
src - 副本 (2)/pages/result/widgets/searchBar/index.tsx
src - 副本 (2)/pages/search/defaultSchema.json
src - 副本 (2)/pages/search/index.config.ts
src - 副本 (2)/pages/search/index.module.scss
src - 副本 (2)/pages/search/index.tsx
src - 副本 (2)/pages/search/mock.ts
src - 副本 (2)/pages/search/Block/index.module.scss
src - 副本 (2)/pages/search/Block/index.tsx
src - 副本 (2)/pages/search/CardBox/index.module.scss
src - 副本 (2)/pages/search/CardBox/index.tsx
src - 副本 (2)/pages/search/DelIcon/index.module.scss
src - 副本 (2)/pages/search/DelIcon/index.tsx
src - 副本 (2)/pages/search/Expend/index.module.scss
src - 副本 (2)/pages/search/Expend/index.tsx
src - 副本 (2)/pages/search/Group/index.module.scss
src - 副本 (2)/pages/search/Group/index.tsx
src - 副本 (2)/pages/search/SelectItem/index.module.scss
src - 副本 (2)/pages/search/SelectItem/index.tsx
src - 副本 (2)/pages/search/SugList/index.tsx
src - 副本 (2)/router/index.ts
src - 副本 (2)/store/index.ts
src - 副本 (2)/store/tools.ts
src - 副本 (2)/store/useGlobalAddress.ts
src - 副本 (2)/store/model/address.model.ts
src - 副本 (2)/store/model/base.model.ts
src - 副本 (2)/store/model/base.service.ts
src - 副本 (2)/store/model/baseMore.model.ts
src - 副本 (2)/store/model/globalInfo.model.ts
src - 副本 (2)/store/model/globalTypes.d.ts
src - 副本 (2)/store/model/locationPermission.model.ts
src - 副本 (2)/utils/hooks.ts
src - 副本 (2)/utils/IdleQueue.ts
src - 副本 (2)/utils/index.ts
src - 副本 (2)/utils/stringify.ts
src - 副本 (2)/utils/taroApi.ts
src - 副本 (2)/utils/urlDecode.ts
src - 副本 (2)/utils/useProcessActionQueue.ts
src - 副本 (2)/utils/Json/index.ts
src - 副本 (2)/utils/LocalStorage/index.ts
src - 副本 (2)/utils/LocalStorage/index.web.ts
src - 副本 (2)/utils/LocalStorage/Interface/ILocalStorage.ts
src - 副本 (2)/utils/bridge/index.rn.ts
src - 副本 (2)/utils/bridge/index.ts
src - 副本 (2)/utils/bridge/index.web.ts
src - 副本 (2)/utils/cross/index.rn.ts
src - 副本 (2)/utils/cross/index.ts
src - 副本 (2)/utils/exit/index.rn.ts
src - 副本 (2)/utils/exit/index.ts
src - 副本 (2)/utils/exit/index.web.ts
src - 副本 (2)/utils/isType/index.ts
src - 副本 (2)/utils/jump/index.ts
src - 副本 (2)/utils/jump/jumppath.ts
src - 副本 (2)/utils/jump/jumpAPI/index.ts
src - 副本 (2)/utils/jump/jumpAPI/index.web.ts
src - 副本 (2)/utils/keyboard/index.rn.ts
src - 副本 (2)/utils/keyboard/index.ts
src - 副本 (2)/utils/keyboard/index.web.ts

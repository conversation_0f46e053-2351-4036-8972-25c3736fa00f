{"name": "JDReactLifeTravelSearch", "version": "1.0.0", "private": true, "description": "JDReactLifeTravelSearch", "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:h5": "taro build --type h5", "build:h5-beta": "NET_ENV=beta taro build --type h5", "build:harmony": "taro build --type harmony", "build:jdrn": "taro build --type jdrn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:h5": "export NODE_OPTIONS=--openssl-legacy-provider && npm run build:h5 -- --watch --reset-cache", "dev:h5-beta": "NET_ENV=beta export NODE_OPTIONS=--openssl-legacy-provider && npm run build:h5 -- --watch --reset-cache", "dev:harmony": "npm run build:harmony -- --watch", "dev:jdrn": "npm run build:jdrn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "postinstall": "patch-package"}, "browserslist": {"development": ["defaults and fully supports es6-module", "maintained node versions"], "production": ["last 3 versions", "Android >= 4.1", "ios >= 8"]}, "dependencies": {"@babel/runtime": "^7.20.0", "@jd/recyclerlistview": "^1.0.8", "@jd/sgm-web": "^3.3.1", "@jdreact/jdreact-core-web": "6.0.7", "@jdreact/jdreact-jsbundle-commonpack": "2.1.48-spm7", "@jdreact/jdreact-navigation": "3.0.3", "@jdtaro/plugin-platform-jdrn": "2.0.4", "@ltfe/ltfe-core-lib": "5.3.24", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.13", "@react-native-seoul/masonry-list": "^1.4.2", "@tarojs/components": "4.0.4", "@tarojs/helper": "4.0.4", "@tarojs/plugin-framework-react": "4.0.4", "@tarojs/plugin-html": "4.0.4", "@tarojs/plugin-platform-alipay": "4.0.4", "@tarojs/plugin-platform-h5": "4.0.4", "@tarojs/plugin-platform-harmony-ets": "4.0.4", "@tarojs/plugin-platform-jd": "4.0.4", "@tarojs/plugin-platform-qq": "4.0.4", "@tarojs/plugin-platform-swan": "4.0.4", "@tarojs/plugin-platform-tt": "4.0.4", "@tarojs/plugin-platform-weapp": "4.0.4", "@tarojs/react": "4.0.4", "@tarojs/runtime": "4.0.4", "@tarojs/shared": "4.0.4", "@tarojs/taro": "4.0.4", "@tarojs/taro-rn": "4.0.4", "@test/jl-test-card": "^1.0.14", "babel-plugin-import": "^1.13.8", "dayjs": "^1.11.13", "immutable": "^5.0.3", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "patch-package": "^8.0.0", "pubsub-js": "^1.9.5", "query-string": "8.2.0", "react": "18.2.0", "react-dom": "18.2.0", "react-markdown": "^10.0.0", "react-native": "0.72.3", "react-native-markdown-display": "^7.0.2", "react-native-safe-area-context": "4.7.1", "react-refresh": "^0.14.2", "resize-observer-polyfill": "^1.5.1", "tsconfig-paths-webpack-plugin": "^4.1.0", "uuid": "3.4.0", "yarn": "^1.22.22", "zen-observable-ts": "^1.1.0", "zustand": "^5.0.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@tarojs/cli": "4.0.4", "@tarojs/taro-loader": "4.0.4", "@tarojs/vite-runner": "4.0.4", "@tarojs/webpack5-runner": "4.0.4", "@types/node": "^22.7.5", "@types/react": "^18.2.6", "@types/webpack-env": "^1.13.6", "@vitejs/plugin-react": "^4.2.1", "babel-preset-taro": "4.0.4", "eslint": "^9.12.0", "eslint-plugin-simple-import-sort": "^12.1.0", "markdown-loader": "^8.0.0", "postcss": "^8.4.38", "stylelint": "^16.4.0", "ts-node": "^10.9.1", "typescript": "^5.6.3", "typescript-eslint": "^8.8.1", "unique-commit-id": "^1.0.0", "vite": "^4.2.0", "webpack": "5.95.0", "webpack-bundle-analyzer": "^4.10.2"}}
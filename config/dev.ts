import type { UserConfigExport } from '@tarojs/cli'
// const SgmWebWebpackPlugin = require('@jd/sgm-web/webpack')
process.env.BABEL_ENV = 'development'
export default {
  logger: {
    quiet: false,
    stats: true
  },
  mini: {},
  h5: {
    webpackChain(chain) {
      // chain
      //   .plugin('SgmWebWebpackPlugin')
      //   .use(SgmWebWebpackPlugin, [{
      //     sid: 'a0aefce5e8cc46acac21e0f79dd4f11d',
      //     pid: '<EMAIL>'
      //   }]);
    }
  },
} satisfies UserConfigExport

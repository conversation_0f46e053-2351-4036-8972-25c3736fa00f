import { defineConfig, type UserConfigExport } from '@tarojs/cli';
import path from 'path';
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin';
import SgmWebWebpackPlugin from '@jd/sgm-web/webpack';
import devConfig from './dev';
import prodConfig from './prod';
process.env.BROWSERSLIST_ENV = process.env.NODE_ENV

function formatDate(date) {
  const year = date.getFullYear().toString().padStart(4, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hour = date.getHours().toString().padStart(2, '0');
  const minute = date.getMinutes().toString().padStart(2, '0');
  const second = date.getSeconds().toString().padStart(2, '0');

  return `${year}${month}${day}${hour}${minute}${second}`;
}
const cleanedDate = formatDate(new Date())

const plugins: any[] = []

switch (process.env.TARO_ENV) {
  case 'weapp':
    plugins.push(['@tarojs/plugin-html'])
    break
  case 'harmony':
    plugins.push('@tarojs/plugin-platform-harmony-ets')
    break
  case 'jdrn':
    plugins.push('@jdtaro/plugin-platform-jdrn')
    break
  default:
    break
}

const _routers = require('../src/router/index').routers;
const routers = _routers.sort(x => x.root ? -1 : 1);
let customRoutes = {}

for (let i = 0; i < routers.length; i++) {
  if (routers[i].customRouter) {
    customRoutes['/' + routers[i].position] = routers[i].customRouter
  }
}

function resolve(...args) {
  return path.resolve(__dirname, '../', ...args)
}


// https://taro-docs.jd.com/docs/next/config#defineconfig-辅助函数
export default defineConfig(async (merge, { command, mode }) => {
  console.log('command, mode', command, mode, process.env.NET_ENV)
  const baseConfig: UserConfigExport = {
    projectName: 'JDReactLifeTravelSearch',
    date: '2024-10-12',
    designWidth: 375,
    deviceRatio: {
      375: 2,
      640: 2.34 / 2,
      750: 1,
      828: 1.81 / 2
    },
    sourceRoot: 'src',
    outputRoot: 'dist',
    plugins,
    defineConstants: {
      'process.env.NET_ENV': `"${process.env.NET_ENV || ''}"`,
      'process.env.BUILD_TIME': `"${cleanedDate}"`,
      '__DEV__': `${process.env.NODE_ENV === 'development'}`
    },
    copy: {
      patterns: [],
      options: {}
    },
    framework: 'react',
    compiler: {
      type: 'webpack5',
      prebundle: {
        enable: false
      }
    },
    cache: {
      enable: false // Webpack 持久化缓存配置，建议开启。默认配置请参考：https://docs.taro.zone/docs/config-detail#cache
    },
    alias: {
      // '@/BaseComponents': path.resolve(__dirname, '..', 'src/BaseComponents'),
      // '@/': path.resolve(__dirname, '..', 'src'),
      '@': path.resolve(__dirname, '../src'),
      '@jdreact/jdreact-core-lib/Libraries/TrafficMapComponent': '@jdreact/jdreact-core-lib/Libraries/JDTrafficMap/TrafficMapComponent',
      '@jdreact/jdreact-core-lib/Libraries/RNTrafficMapView': '@jdreact/jdreact-core-lib/Libraries/JDTrafficMap/RNTrafficMapView',
      '@jdreact/jdreact-core-lib/Libraries/RNTrafficMapImage': '@jdreact/jdreact-core-lib/Libraries/JDTrafficMap/RNTrafficMapImage',
    },
    mini: {
      postcss: {
        pxtransform: {
          enable: true,
          config: {}
        },
        url: {
          enable: true,
          config: {
            limit: 1024 // 设定转换尺寸上限
          }
        },
        cssModules: {
          enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
          config: {
            namingPattern: 'module', // 转换模式，取值为 global/module
            generateScopedName: '[name]__[local]___[hash:base64:5]'
          }
        }
      },
      webpackChain(chain) {
        chain.resolve.plugin('tsconfig-paths').use(TsconfigPathsPlugin)
      }
    },
    h5: {
      router: {
        mode: 'browser', // 或者是 'hash'
        customRoutes,
        // basename: 'search'
      },
      publicPath: '/',
      staticDirectory: 'static',
      output: {
        filename: `js/[name].[hash:8].${cleanedDate}.js`,
        chunkFilename: 'js/[name].[chunkhash:8].js'
      },
      miniCssExtractPluginOption: {
        ignoreOrder: true,
        filename: 'css/[name].[hash].css',
        chunkFilename: 'css/[name].[chunkhash].css'
      },
      postcss: {
        autoprefixer: {
          enable: true,
          config: {}
        },
        cssModules: {
          enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
          config: {
            namingPattern: 'module', // 转换模式，取值为 global/module
            generateScopedName: '[name]__[local]___[hash:base64:5]'
          }
        }
      },
      imageUrlLoaderOption: {
        esModule: false,
        limit: 8192,
        name: 'assets/[name].[hash:8].[ext]',
      },
      webpackChain(chain) {
        chain.resolve.plugin('tsconfig-paths').use(TsconfigPathsPlugin)
        chain.resolve.alias
          .set('react-native', '@jdreact/jdreact-core-web')
          .set('react/jsx-runtime', path.resolve(__dirname, '../', 'node_modules/react/jsx-runtime.js'))
          .set('@tarojs/plugin-framework-react/dist/runtime', path.resolve(__dirname, '../', 'node_modules/@tarojs/plugin-framework-react/dist/runtime.js'))
        chain.resolve.extensions
          .prepend('.js')
          .prepend('.tsx')
          .prepend('.ts')
          .prepend('.web.js')
          .prepend('.web.ts')
          .prepend('.web.tsx')
          .prepend('.jsx')
          .prepend('.json')
        chain.resolve.mainFields.prepend('client')
        // chain.resolve.modules.add('node_modules')
        // chain.resolve.modules
        // .add(path.resolve(__dirname, '../', 'node_modules/@jdreact/jdreact-core-lib'))
        // .add(path.resolve(__dirname, '../../', 'node_modules'))
        // .add('taro/node_modules')
        // chain.module
        // .rule('compile')
        // .test(/\.js$/)
        // .include
        // .add(path.resolve(__dirname, '../node_modules/@jdreact/jdreact-core-lib'))
        // .end()
        // .use('babel-loader')
        // .loader('babel-loader');
        // chain.merge({
        //     module: {
        //       rule: {
        //         include: [
        //           // 添加这一行，确保 @jdreact 被包含在编译范围内
        //           path.resolve(__dirname, '../node_modules/@jdreact/jdreact-core-lib')
        //         ]
        //       }
        //     }
        //   });
        // chain.resolve.plugin('MultiPlatformPlugin')
        //     .tap(args => {
        //         args[2]["include"] = ['@jdreact', '@ltfe']
        //         return args
        //     }),
        // chain.resolve.alias.set('react-native', '@jdreact/jdreact-core-web')

        chain.module
          .rule('compile')
          .test(/\.(js|ts)x?$/)
          .include
          .add(resolve('node_modules/@test/jl-test-card'))
          .add(resolve('node_modules/@jdreact'))
          .add(resolve('node_modules/@ares'))
          .add(resolve('node_modules/@areslabs'))
          .add(resolve('node_modules/@ltfe/ltfe-core-lib'))
          .add(resolve('node_modules/react-native-webview'))
          .add(resolve('node_modules/react-native-keyboard-aware-scroll-view'))
          .add(resolve('node_modules/react-native-storage'))
          .add(resolve('node_modules/@react-native-seoul'))
          .end()
          .use('babel')
          .loader('babel-loader')
          .options({
            presets: ['module:metro-react-native-babel-preset', '@babel/preset-env'],
            plugins: [
              [
                '@babel/plugin-transform-runtime', {
                  'corejs': 3
                }
              ],
              '@babel/plugin-syntax-dynamic-import',
              '@babel/plugin-transform-modules-commonjs',
              ['@babel/plugin-proposal-decorators', { 'legacy': true }],
              ['@babel/plugin-proposal-class-properties']
            ]
          })
        chain.module
          .rule('json')
          .test(/\.json$/)
          .include
          .add('/node_modules/')
          .end()
          .use('json-loader')
          .loader('json-loader');


        chain
          .plugin('SgmWebWebpackPlugin')
          .use(SgmWebWebpackPlugin, [{
            sid: 'a0aefce5e8cc46acac21e0f79dd4f11d',
            pid: '<EMAIL>'
          }]);

        // 添加对 filter-obj 等包的处理
        chain.module
          .rule('node-modules-babel')
          .test(/\.js$/)
          .include
          .add(/node_modules\/filter-obj/)
          .add(/node_modules\/query-string/)
          .add(/node_modules\/split-on-first/)
          .add(/node_modules\/strict-uri-encode/)
          .add(/node_modules\/react-native-safe-area-context/)
          .add(/node_modules\/@react-native-async-storage/)
          .add(/node_modules\/devlop/) // 添加 devlop 包
          .add(/node_modules\/micromark-util-symbol/) // 添加 micromark-util-symbol 包
          .add(/node_modules\/micromark/) // 可能需要添加相关包
          .end()
          .use('babel-loader')
          .loader('babel-loader')
          .options({
            presets: [
              ['@babel/preset-env', {
                targets: {
                  browsers: ['> 1%', 'last 2 versions', 'iOS >= 9', 'not dead']
                }
              }]
            ],
            plugins: [
              '@babel/plugin-proposal-class-properties',
              '@babel/plugin-proposal-object-rest-spread',
              '@babel/plugin-proposal-numeric-separator' // 添加数字分隔符插件
            ]
          });
      },
      //   compile: {
      //     include: [
      //         /node_modules[\\/]@jdreact/
      //         // path.resolve(__dirname, '..', 'node_modules/@jdreact/jdreact-core-lib/Libraries/jdreact-core-lib.web.js'),
      //         // path.resolve(__dirname, '..', 'node_modules/react-native-webview')
      //     ]
      //   }
    },
    rn: {
      appName: 'JDReactLifeTravelSearch',
      postcss: {
        cssModules: {
          enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
        }
      }
    },
    harmony: {
      // 将编译方式设置为使用 Vite 编译
      compiler: 'vite',
      // 【必填】鸿蒙主应用的绝对路径，例如：
      projectPath: process.env.HARMONY_PROJECT_PATH || 'dist',
      // 【可选】HAP 的名称，默认为 'entry'
      hapName: 'entry',
      // 【可选】modules 的入口名称，默认为 'default'
      name: 'default',
    },
  }

  if (process.env.NODE_ENV === 'development') {
    // 本地开发构建配置（不混淆压缩）
    return merge({}, baseConfig, devConfig)
  }
  // 生产构建配置（默认开启压缩混淆等）
  return merge({}, baseConfig, prodConfig)
})

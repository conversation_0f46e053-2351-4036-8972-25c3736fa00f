const path = require('path');
// const webpack = require('webpack');
// const ImageSuffixPlugin = require('@jdreact/image-suffix-webpack-plugin');
// const HtmlWebpackPlugin = require('html-webpack-plugin');
// const UglifyJsPlugin = require('uglifyjs-webpack-plugin');
// const { CleanWebpackPlugin } = require('clean-webpack-plugin');
// const LodashModuleReplacementPlugin = require('lodash-webpack-plugin');
// const HardSourceWebpackPlugin = require('hard-source-webpack-plugin');
// const { SgmWebWebpackPlugin } = require('@jd/sgm-web');
// var hyphenateRE = /\B([A-Z])/g;
// var hyphenate = function(str) {
//     return str.replace(hyphenateRE, '-$1').toLowerCase();
// };
const babelPlugins = [
    [
        '@babel/plugin-transform-runtime', {
            'corejs': 3
        }
    ],
    // [
    //     require.resolve('babel-plugin-lodash')
    // ],
    '@babel/plugin-syntax-dynamic-import',
    '@babel/plugin-transform-modules-commonjs',
    ['@babel/plugin-proposal-decorators', { 'legacy': true }],
    ['@babel/plugin-proposal-class-properties']
];
// if (process.env.NODE_ENV !== 'development') {
//     babelPlugins.push('transform-remove-console');
// }
// if (process.env.NODE_ENV !== 'production') {
//     babelPlugins.push(require.resolve('../plugins/babel-plugin-react-auto-prop'));
// }

function getWebpackCommonConfig(config) {
    const alias = {};
    // config.alias && Object.keys(config.alias).forEach(key => {
    //     alias[key] = resolve(config.alias[key]);
    // });
    const baseWebpackConfig = {
        context: path.resolve(__dirname, '..'),
        // entry: [
        //     require.resolve('../web/polyfills'),
        //     // resolve(config.entry)
        // ],
        output: {
            // path: resolve(config.assetsRoot),
            filename: '[name].js',
            // publicPath: config.publicPath
        },
        // optimization: {
        //     usedExports: true, // 开启tree shaking，标记未使用代码
        //     minimizer: [new UglifyJsPlugin({
        //         sourceMap: true
        //     })], // 代码压缩，删除未使用代码
        //     concatenateModules: false, // 用于将模块合并到一个闭包中，以减少最终打包文件的体积
        //     splitChunks: {// 分割代码块
        //         maxInitialRequests: 6,
        //         cacheGroups: {
        //             'vendor': {
        //                 // 第三方依赖
        //                 priority: 1,
        //                 name: 'vendor',
        //                 test: /node_modules/,
        //                 chunks: 'initial',
        //                 minSize: 100,
        //                 minChunks: 1 // 重复引入了几次
        //             },
        //             'jdreact-core-lib': {
        //                 name: 'jdreact-core-lib', // 单独将 @jdreact/jdreact-core-lib 拆包
        //                 priority: 6, // 权重需大于其它缓存组
        //                 test: /[\/]node_modules[\/]@jdreact[\/]jdreact-core-lib[\/]/,
        //                 chunks: 'initial',
        //                 minSize: 100,
        //                 minChunks: 1 // 重复引入了几次
        //             },
        //             'jdreact-core-web': {
        //                 name: 'jdreact-core-web', // 单独将 @jdreact/jdreact-core-web 拆包
        //                 priority: 6, // 权重需大于其它缓存组
        //                 test: /[\/]node_modules[\/]@jdreact[\/]jdreact-core-web[\/]/,
        //                 chunks: 'initial',
        //                 minSize: 100,
        //                 minChunks: 1 // 重复引入了几次
        //             },
        //             'lottie-web': {
        //                 name: 'lottie-web', // 单独将 lottie-web 拆包
        //                 priority: 7, // 权重需大于其它缓存组
        //                 test: /[\/]node_modules[\/]lottie-web[\/]/,
        //                 chunks: 'initial',
        //                 minSize: 100,
        //                 minChunks: 1 // 重复引入了几次
        //             }
        //         }
        //     },
        //     runtimeChunk: {
        //         name: 'manifest'
        //     }
        // },
        resolve: {
            extensions: ['.js', '.tsx', '.ts', '.web.js', '.web.ts', '.web.tsx', '.jsx', '.json'],
            mainFields: ['client', 'main'],
            modules: [
                // resolve(config.src),
                'node_modules'
            ],
            alias: {
                'react-native': '@jdreact/jdreact-core-web',
                'dismissKeyboard': '@jdreact/jdreact-core-web/Libraries/Utilties/dismissKeyboard.web.js',
                'ReactNativeART': 'react-art',
                'react/jsx-runtime': 'react/jsx-runtime.js',
                'react-native-pager-view': '@jdreact/jdreact-core-web'
            }
        },
        module: {
            rules: [
                {
                    test: /\.(js|ts)x?$/,
                    use: [{
                        loader: 'babel-loader',
                        options: {
                            cacheDirectory: true,
                            presets: ['module:metro-react-native-babel-preset', '@babel/preset-env'],
                            plugins: babelPlugins
                        }
                    }],
                    include: [
                        // resolve(config.src),
                        'node_modules/@jdreact/jdreact-core-lib',
                        'node_modules/@jdreact/jdreact-core-web',
                        'node_modules/@jdreact/jdreact-core-linear-gradient',
                        'node_modules/@jdreact/with-color-theme',
                        'node_modules/@ares',
                        'node_modules/@areslabs',
                        'node_modules/@apl',

                        'node_modules/@ltfe/ltfe-core-lib',
                        'node_modules/@jd/jd-travel-call',
                        'node_modules/@jd/jd-travel-fetch',
                        'node_modules/@jd/air-m-page',

                        'node_modules/react-native-swiper',
                        'node_modules/react-native-keyboard-aware-scroll-view',
                        'node_modules/react-native-image-zoom-viewer',
                        'node_modules/react-native-image-pan-zoom',
                        'node_modules/@jdreact/jdreact-plugin-fingerprint',
                        'node_modules/react-native-bouncy-checkbox',
                        'node_modules/react-native-storage',
                        'node_modules/react-native-webview',
                        'node_modules/@react-native-async-storage'
                    ]
                },
                {
                    test: /\.(png|gif|jpe?g)$/,
                    use: [{
                        loader: 'url-loader',
                        options: {
                            esModule: false,
                            limit: 8192 // 8K
                        }
                    }],
                    include: [
                        // resolve(config.src),
                        'node_modules/@jdreact/jdreact-core-lib',
                        'node_modules/@jdreact/jdreact-core-web',
                        'node_modules/@ares',
                        'node_modules/@areslabs',
                        'node_modules/@apl',
                        'node_modules/react-native-bouncy-checkbox',
                        'node_modules/@ltfe/ltfe-core-lib'
                    ]
                },
                {
                    test: /\.css$/,
                    use: [
                        {
                            loader: 'style-loader'
                        },
                        {
                            loader: 'css-loader'
                        },
                        {
                            loader: 'postcss-loader',
                            options: {
                                plugins: function() {
                                    return [
                                        require('autoprefixer')({
                                            overrideBrowserslist: ['> 0.25%', 'not dead']
                                        })
                                    ];
                                }
                            }
                        }
                    ],
                    include: [
                        // resolve(config.src),
                        'node_modules/@jdreact/jdreact-core-lib',
                        'node_modules/@jdreact/jdreact-core-web',
                        'node_modules/@ltfe/ltfe-core-lib'
                    ]
                }
            ]
        },
        // plugins: [
        //     new LodashModuleReplacementPlugin(),
        //     new ImageSuffixPlugin(),
        //     new HtmlWebpackPlugin({
        //         filename: 'index.html',
        //         hash: true,
        //         // template: config.index ? resolve(config.index) : './web/index.tpl.vm',
        //         // config: config.template,
        //         minify: {
        //             html5: true, // 根据HTML5规范解析输入
        //             collapseWhitespace: true, // 折叠空白区域
        //             preserveLineBreaks: false,
        //             minifyCSS: true, // 压缩文内css
        //             minifyJS: true, // 压缩文内js
        //             removeComments: true // 移除注释
        //         }
        //     }),
        //     new CleanWebpackPlugin({
        //         cleanOnceBeforeBuildPatterns: ['**/*', '!dll', '!dll/**']
        //     }),
        //     new HardSourceWebpackPlugin()
        // ]
    };

    // if (config.includeJDShare) {
    //     baseWebpackConfig.plugins.push(
    //         new webpack.ProvidePlugin({
    //             JdShare: require.resolve('../web/jdShare')
    //         })
    //     );
    // }
    // if (config.sgm) {
    //     baseWebpackConfig.plugins.push(
    //         // 说明：webpack4中需将它做为最后一个插件，https://npm.m.jd.com/package/@jd/sgm-web
    //         new SgmWebWebpackPlugin(config.sgm)
    //     );
    // }

    return baseWebpackConfig;
}

module.exports = {
    getWebpackCommonConfig
};

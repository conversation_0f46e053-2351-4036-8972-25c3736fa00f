import eslint from '@eslint/js'
import stylistic from '@stylistic/eslint-plugin'
import simpleImportSort from 'eslint-plugin-simple-import-sort'
import tseslint from 'typescript-eslint'

export default tseslint.config(
  eslint.configs.recommended,
  ...tseslint.configs.recommended,
  {
    plugins: {
      '@stylistic': stylistic,
      'simple-import-sort': simpleImportSort,
    },
    rules: {
      'prefer-rest-params': 0,
      'no-prototype-builtins': 0,
      '@typescript-eslint/no-unused-vars': 0,
      '@typescript-eslint/no-explicit-any': 0,
      '@typescript-eslint/no-var-requires': 0,
      '@typescript-eslint/ban-ts-comment': 0,
      '@stylistic/quotes': ['error', 'single'],
      '@stylistic/semi': ['error', 'never'],
      '@stylistic/arrow-spacing': 2,
      '@stylistic/brace-style': 2,
      '@stylistic/comma-spacing': 2,
      '@stylistic/no-multi-spaces': 2,
      '@stylistic/eol-last': 2,
      '@stylistic/indent': ['error', 2],
      '@stylistic/keyword-spacing': ['error', { 'before': true, 'after': true }],
      '@stylistic/no-multiple-empty-lines': 2,
      '@stylistic/space-before-function-paren': 2,
      '@stylistic/no-trailing-spaces': 2,
      '@stylistic/space-before-blocks': 2,
      '@stylistic/space-infix-ops': 2,
      '@stylistic/space-unary-ops': 2,
      '@stylistic/spaced-comment': [
        2,
        'always',
        { 'block': { 'exceptions': ['*'] } },
      ],
      '@stylistic/function-call-spacing': ['error', 'never'],
      '@stylistic/type-annotation-spacing': 2,
      '@stylistic/object-curly-spacing': ['error', 'always'],
      'simple-import-sort/imports': [2, {
        groups: [
          // Packages `react` related packages come first.
          ['^react', '^@?\\w'],
          // Node.js builtins prefixed with `node:`.
          ['^node:'],
          // Packages.
          // Things that start with a letter (or digit or underscore), or `@` followed by a letter.
          ['^@?\\w'],
          // Absolute imports and other imports such as Vue-style `@/foo`.
          // Anything not matched in another group.
          ['^'],
          // Relative imports.
          // Anything that starts with a dot.
          ['^\\.'],
          // Side effect imports.
          ['^\\u0000'],
          // Types Group
          ['^node:.*\\u0000$', '^@?\\w.*\\u0000$', '(?<=\\u0000)$', '^\\..*\\u0000$'],
          // Style imports.
          ['^.+\\.s?css$'],
        ]
      }],
      'simple-import-sort/exports': 2,
    }
  }
)
